import request from '@/axios';
//获取应办列表
export const getShouldDoList = (current:number,size:number,data:any) => {
  return request({
    url: '/staging/page',
    method: 'post',
    params:{
      current,size
    },
    data
  });
};
export const getInviteVerifyList = (current:number,size:number,data:any) => {
  return request({
    url: '/dept_invite_verify_record/page',
    method: 'get',
    params:{
      current,size
    },
    data
  });
};
//已办分页
export const getMHandleAllDone = (current:number,size:number,data:any) => {
  return request({
    url: '/done/queryDone',
    method: 'get',
    params:{
      current,
      size,
      ...data
    },
  });
};
// 微应用栏
export const getMicroPage = (current:number,size:number,data:any) => {
  return request({
    url: '/micro-app-common/getUserWaitVerifyPage',
    method: 'get',
    params:{
      current,size,...data
    },
  });
};
//获取组织下版本列表
export const getTaskVersion = (tid?: any) => {
  return request({
    url: '/scene_task_group/getVersionListWithHandledCount',
    method: 'get',
    params: {tenantId:tid},
  });
};
//获取组织下版本列表
export const getTaskTenantList = (userId?: any) => {
  return request({
    url: '/userTenant/userList',
    method: 'get',
    params: {userId},
  });
};
//应办数量统计
export const getShouldDoTaskCount = (params:any) => {
  return request({
    url: '/staging/pageCount',
    method: 'post',
    data:{
      ...params
    }
  });
};
//下线
export const doTaskOffline = (id:any) => {
  return request({
    url: '/staging/offLine',
    method: 'post',
    params:{
      id
    }
  });
};
//停
export const doTaskStop = (id:any) => {
  return request({
    url: '/staging/stop',
    method: 'post',
    params:{
      id
    }
  });
};
//下
export const doTaskOffLine= () => {
  return request({
    url: '/staging/offLine',
    method: 'post',
  });
};
//  调
export const doTaskAdjust = (params:any) => {
  return request({
    url: '/staging/adjust',
    method: 'post',
    data:{
      ...params
    }
  });
};

//  撤销
export const doTaskRevoke = (id:any) => {
  return request({
    url: '/staging/revoke',
    method: 'post',
    params:{
      id
    }
  });
};
//  应办详情
export const getTaskInfo = (id:any) => {
  return request({
    url: '/staging/detail',
    method: 'get',
    params:{
      id
    }
  });
};
//  应办表单详情
export const getTaskFormData = (id:any) => {
  return request({
    url: '/handledData/detail',
    method: 'get',
    params:{
      id
    }
  });
};
//  转
export const doTaskTrans = (params:any) => {
  return request({
    url: '/staging/change',
    method: 'post',
    data:{
      ...params
    }
  });
};
//  转-同意
export const doTaskChange = (id:any) => {
  return request({
    url: '/staging/changeAgree',
    method: 'post',
    data:{
      id
    }
  });
};
//  转-拒绝
export const changeDisagree = (id:any) => {
  return request({
    url: '/staging/changeDisagree',
    method: 'post',
    params:{
      id
    }
  });
};
//  邀
export const taskInvite = (params:any) => {
  return request({
    url: '/staging/invite',
    method: 'post',
    data:{
      ...params
    }
  });
};