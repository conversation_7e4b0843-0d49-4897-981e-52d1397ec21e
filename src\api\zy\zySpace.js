import request from '@/axios';
// 空间tree
export const getSpaceTree = (tid) => {
    return request({
        url: '/space_group/tree',
        method: 'get',
        params: {
            tid
        }
    });
};
// 新建空间
export const newSpaceWarehouse = (data) => {
    return request({
        url: '/space_group/submit',
        method: 'post',
        data: {
            ...data,
        },
    });
};
// 空间tree删除
export const removeGoodsWarehouse = (ids) => {
    return request({
        url: '/space_group/remove',
        method: 'post',
        params: {
            ids
        }
    });
};
// 空间tree删除
export const modifySpaceWarehouse = (groupName, id) => {
    return request({
        url: '/space_group/rename',
        method: 'put',
        data: {
            groupName,
            id,
        }
    });
};
// 空间分页
export const getSpaceList = (current, size, params) => {
    return request({
        url: '/space/page',
        method: 'post',
        params: {
            current,
            size,
            ...params,
        },
    });
};
// 添加空间
export const addSpace = (data) => {
    return request({
        url: '/space/add',
        method: 'post',
        data: {
            ...data
        },
    });
};
// 空间删除
export const removeSpace = (data) => {
    return request({
        url: '/space/remove',
        method: 'DELETE',
        data: {
            ...data,
        },
    });
};
// 空间编辑
export const modifySpace = (data) => {
    return request({
        url: '/space/update',
        method: 'put',
        data: {
            ...data,
        },
    });
};
// 导出空间列表
export const exportPlace = (spaceIds, tid, groupId) => {
    return request({
        url: '/space/export',
        method: 'post',
        data: {
            spaceIds,
            tid,
            groupId
        },
        responseType: 'blob',
    });
};
// 导出数据
export const exportPlaceLength = (spaceIds, tid,groupId,) => {
    return request({
        url: '/space/export-count',
        method: 'post',
        data: {
            groupId,
            spaceIds,
            tid,
        },
    });
};
// 空间导入下载模板
export const exportBlob = () => {
    return request({
        url: '/space/down-template',
        method: 'get',
        responseType: 'blob',
    });
};
// 空间导入模板校验
export const exportCheck = (data) => {
    return request({
        url: '/space/import-validate',
        method: 'post',
        data: data
    });
};
// 排序
export const sort = (data) => {
    return request({
        url: '/space_group/sort',
        method: 'put',
        data: data
    });
};
