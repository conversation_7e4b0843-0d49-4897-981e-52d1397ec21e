<template>
  <el-dropdown v-bind="props" @command="handleCommand">
    <div class="flex items-center leading-22px">
      <slot name="default" />
      <el-icon class="m-0!">
        <ArrowRight />
      </el-icon>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="item in options" :key="item.value"
          :class="{ 'is-selected': isSelect(item.value, model) }" :command="item.value">
          <div class="flex items-center justify-between">
            <span class="ml-auto">{{ item.label }}</span>
            <span class="check-mark">{{ isSelect(item.value, model) ? '✓' : '' }}</span>
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script lang="ts" setup>
import { ArrowRight } from '@element-plus/icons-vue';
import { useMemoize } from '@vueuse/core';
import { dropdownProps } from 'element-plus';

const props = defineProps({
  ...dropdownProps,
  defaultSelected: {
    type: String
  },
  options: {
    type: Array as () => DicLike[],
    required: true
  },
  popperOptions: {
    type: Object,
    default: () => ({
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [10, 20]
          }
        }
      ]
    })
  },
  placement: {
    type: String,
    default: 'right'
  }
});

const emits = defineEmits<{
  change: [value: string]
}>();

const model = defineModel<string>();

const handleCommand = (command: string) => {
  model.value = command;
  emits('change', command);
};

const isSelect = useMemoize((value: string, modelValue?: string) =>
  value === (_.isEmpty(modelValue) ? props.defaultSelected : modelValue)
);
</script>

<style lang="scss" scoped>
.check-mark {
  color: var(--el-color-primary);
  margin-left: 8px;
}
</style>