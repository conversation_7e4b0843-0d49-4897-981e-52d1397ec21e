<template>
  <Codemirror
    class="formula-mirror"
    ref="mirror"
    v-bind="$attrs"
    :options="cmOptions"
    v-model:value="v"
    @ready="handleReady"
    @change="handleChange"
  ></Codemirror>
</template>

<script setup lang="ts">
import Codemirror from 'codemirror-editor-vue3';
import 'codemirror/addon/display/placeholder.js';
import 'codemirror/addon/search/searchcursor.js';
import func from '@/utils/func.js';
import { Mark } from '../../types/CodemirrorTypes.ts';
import { Editor, EditorConfiguration, Position } from 'codemirror';

const props = withDefaults(
  defineProps<{
    templateKey?: string;
    marksKey?: string;
    options?: EditorConfiguration;
  }>(),
  {
    templateKey: () => 'template',
    marksKey: () => 'marks',
    options: () => {
      return {};
    },
  }
);

const emits = defineEmits(['change']);

const vModel = defineModel<Record<string, any>>({
  default: () => {
    return {};
  },
});

const v = computed({
  get: () => _.unescape(vModel.value?.[props.templateKey] || ''),
  set: v => {
    nextTick(() => {
      vModel.value = {
        [props.templateKey]: v,
        [props.marksKey]: getAllMarks()!,
      };
    });
  },
});
//获得所有mark
const getAllMarks = (): Mark[] | undefined => {
  return cm.value
    ?.getDoc()
    ?.getAllMarks()
    .filter(item => !item.className?.startsWith('CodeMirror'))
    .map(item => {
      return {
        ...item.find(),
        className: item.className!,
        attributes: item.attributes,
        markText: item.replacedWith?.innerText!,
      } as Mark;
    });
};

const cmOptions: EditorConfiguration = {
  lineNumbers: false,
  // line: true,
  ...props.options,
};

const cm = shallowRef<Editor>();

const handleReady = (ready: Editor) => {
  cm.value = ready;

  nextTick(() => {
    //初始化mark
    const valueElement = vModel.value?.[props.marksKey];
    if (_.isArray(valueElement)) {
      valueElement?.forEach(replaceMark);
    }
  });
};

const handleChange = (instance: string) => {
  emits('change', instance);
};

//获得当前光标位置
const getCursor: () => Position | undefined = () => {
  return cm.value?.getCursor();
};

//往光标处插入文本
const insertText = (text?: string, bracket = false): Pick<Mark, 'from' | 'to'> | undefined => {
  const startPos = getCursor();
  if (text && startPos) {
    cm.value?.getDoc().replaceRange(text, startPos);
    nextTick(() => {
      if (bracket) {
        setCursor({
          ch: startPos.ch + text.length - 1,
          line: startPos.line,
        });
      }
    });
    return {
      from: {
        ch: startPos.ch,
        line: startPos.line,
      },
      to: {
        ch: startPos.ch + text.length,
        line: startPos.line,
      },
    };
  }
};

//设置光标
const setCursor = (pos: Position) => {
  cm.value?.getDoc().setCursor(pos);
};
const clearValue = () => {
  cm.value?.setValue('');
};

//替换为mark
const replaceMark = ({ from, to, className, markText, attributes }: Mark) => {
  //创建替换物
  const replace = document.createElement('span');
  replace.className = `m-2px px-5px replaceSpan ${className}`;
  if (func.notEmpty(attributes)) {
    Object.entries(attributes!)?.forEach(value => {
      replace.setAttribute(`data-${value[0]}`, value[1]);
    });
  }
  replace.innerText = markText;

  cm.value?.getDoc().markText(from, to, {
    atomic: true,
    replacedWith: replace,
    attributes: attributes,
    handleMouseEvents: true,
    className: className,
  });
};

const insertMark = (text: string, mark: Pick<Mark, 'markText' | 'attributes' | 'className'>) => {
  const { markText } = mark;
  if (func.isEmpty(markText)) {
    mark.markText = text;
  }
  const pos = insertText(text)!;
  replaceMark({
    ...pos,
    ...mark,
  });
};

//聚焦
const focus = () => {
  cm.value?.focus();
};

defineExpose({
  focus,
  insertText,
  replaceMark,
  insertMark,
  clearValue,
});
</script>

<style lang="scss">
.formula-mirror {
  .CodeMirror {
    border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
    transition: var(--el-transition-box-shadow);
    transform: translate3d(0, 0, 0);
    padding: 1px 11px 1px 0;

    font-family: inherit;
    font-size: var(--el-font-size-base);
    color: var(--el-text-color-regular);
    --code-line-height: calc(var(--el-component-size) - 2px);
    line-height: var(--code-line-height);
  }

  .CodeMirror-focused {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }

  .CodeMirror-placeholder {
    // color: var(--el-text-color-placeholder) !important;
  }
  &:hover {
    cursor: text;
  }
  .CodeMirror-activeline-background {
    background-color: white;
  }
  .CodeMirror-gutter {
    display: none;
  }
}
</style>
