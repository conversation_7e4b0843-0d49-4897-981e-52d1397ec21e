import { AvueForm } from '@/components/form-config/types/AvueTypes';
import { isEmptyValue } from '@/utils/field';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import dayjs from 'dayjs';
export function applyFilters(data: AvueForm[], filters: AvueForm): AvueForm[] {
  if (!filters || Object.values(filters).every(isEmptyValue)) return data;

  const filterEntries = Object.entries(filters).filter(([, value]) =>
    !isEmptyValue(value)
  );

  if (filterEntries.length === 0) return data;

  const result: AvueForm[] = [];

  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    let matches = true;

    for (let j = 0; j < filterEntries.length; j++) {
      const [key, value] = filterEntries[j];

      // Skip if key is empty or value is not provided
      if (value === undefined || value === '') continue;

      const rowValue = row[key];
      // 处理数组字段
      if (Array.isArray(value)) {
        if (Array.isArray(rowValue)) {
          if (!value.every(v => rowValue.includes(v))) {
            matches = false;
            break;
          }
        } else if (!value.includes(rowValue)) {
          matches = false;
          break;
        }
        continue;
      }

      //地址
      if (key.startsWith(FixTypeEnum.ADDRESS)) {
        const addressObj = row[key];

        // 校验数据格式是否合法
        if (!addressObj || typeof addressObj !== 'object') {
          matches = false;
          break;
        }

        const { label, address } = addressObj;

        // 构造搜索关键词
        const searchLabel = Array.isArray(value?.label)
          ? value?.label.map(String).join(' ').toLowerCase()
          : String(value?.label).toLowerCase();

        const searchAddress = String(value?.address).toLowerCase();

        // 构造待匹配字符串
        const labelStr = Array.isArray(label)
          ? label.map(String).join(' ').toLowerCase()
          : String(label).toLowerCase();

        const addressStr = String(address).toLowerCase();

        // label 匹配条件
        const isLabelMatch = !searchLabel || labelStr === searchLabel;

        // address 匹配条件
        const isAddressMatch = !searchAddress || addressStr.includes(searchAddress);

        // 必须同时满足 label 和 address 的匹配（如果都有值）
        if (!isLabelMatch || !isAddressMatch) {
          matches = false;
          break;
        }
        continue;
      }

      // input模糊搜索
      if (key.startsWith(FixTypeEnum.INPUT) && value.trim() !== '') {
        const searchValue = value.toLowerCase();
        const cellValue = String(rowValue).toLowerCase();

        if (!cellValue.includes(searchValue)) {
          matches = false;
          break;
        }

        continue;
      }

       // 处理 daterange 类型
      if (key.startsWith('daterange') && Array.isArray(value)) {
        const [start, end] = value;
        const rowDate = row[key];
        const isAfterOrEqual = dayjs(rowDate).isAfter(start) || dayjs(rowDate).isSame(start);
        const isBeforeOrEqual = dayjs(rowDate).isBefore(end) || dayjs(rowDate).isSame(end);

        if (!(isAfterOrEqual && isBeforeOrEqual)) {
          matches = false;
          break;
        }
        continue;
      }


      if (rowValue !== value) {
        matches = false;
        break;
      }
    }

    if (matches) {
      result.push(row);
    }
  }

  return result;
}