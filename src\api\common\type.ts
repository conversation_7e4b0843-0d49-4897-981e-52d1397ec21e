//部门
export namespace deptType {
  export interface ReqParams {
    tid: number | string;
  }
  export interface ReqfilterParams {
    tenantId: string;
    innerType?: number;
  }
  export interface ResParams {
    deptName: string;
    id: number;
  }
  export interface ReqUserParams {
    deptId: number | string;
  }
  export interface ResUserParams {
    nickName: string;
    id: number;
  }
}

//工作组
export namespace groupType {
  export interface ReqParams {
    tid: number | string;
  }
  export interface ReqfilterParams {
    tenantId: string;
    innerType?: number;
  }
  export interface ResParams {
    groupName: string;
    id: number;
  }
  export interface ReqUserParams {
    workgroupId: number | string;
  }
  export interface ResUserParams {
    nickName: string;
    id: number;
  }
}

//空间组

export namespace spaceType {
  export interface ReqParams {
    tenantId: number | string;
  }
  export interface ResParams {
    groupName: string;
    id: number;
  }
  export interface ReqDataParams {
    ids: number | string;
  }
  export interface ResDataParams {
    spaceName: string;
    id: string;
  }
}

//物品组

export namespace articleType {
  export interface ReqParams {
    tid: number | string;
  }
  export interface ResParams {
    warehouseName: string;
    id: number;
  }
  export interface ReqDataParams {
    groupIds: number | string;
  }
  export interface ResDataParams {
    goodsName: string;
    id: string;
  }
}

// 获取客服组
export namespace customerGroupType {
  export interface ReqParams {
    tenantId: number | string;
  }
   export interface ReqFilterParams {
    tenantId: string;
    innerType?: number;
  }
  export interface ResParams {
    groupName: string;
    id: number;
  }
  export interface ReqUserParams {
    customerGroupId: number | string;
  }
  export interface ResUserParams {
    nickName: string;
    userId: number;
  }
}

export namespace userType {
  export interface ResUserParams {
    nickName: string;
    id: string;
  }
}
