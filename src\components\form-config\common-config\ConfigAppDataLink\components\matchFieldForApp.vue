<template>
  <!--  <n-card :content-style="{ padding: '10px', position: 'relative', paddingRight: '60px' }">-->
  <el-row :gutter="10">
    <el-col :span="8">
      <avue-select
        class="w-full"
        :dic="[data]"
        disabled
        :props="{ label: 'copyLabel', value: 'id', type: 'prop' }"
        v-model="data.id"
        placeholder="请选择字段"
      ></avue-select>
    </el-col>
    <el-col :span="2" class="text-center"> = </el-col>
    <el-col :span="8">
      <avue-select
        class="w-full"
        :dic="filterList"
        :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }"
        v-model="field"
        placeholder="请选择字段"
      ></avue-select>
    </el-col>
    <!--      <el-col :span="6">-->
    <!--        <el-checkbox v-model="linkage" label="空值联动" v-if="isLinkAge"/>-->
    <!--        <el-tooltip-->
    <!--          class="box-item"-->
    <!--          effect="dark"-->
    <!--          content="勾选空值联动，即空值符合当前联动采集规则的取值"-->
    <!--          placement="top-start"-->
    <!--        >-->
    <!--          <el-icon  size="16" class="ml-1 color-var"><QuestionFilled /></el-icon>-->
    <!--        </el-tooltip>-->
    <!--      </el-col>-->
  </el-row>
  <!--  </n-card>-->
</template>

<script setup lang="ts">
import {
  FieldBaseField,
  AddressAllField,
  DateAllField,
  SelectAllField,
} from '@/components/form-config/types/AvueTypes';
import {
  submitterType,
  submitterTimeType,
  triggerTimeType,
  endUpdateTimeType,
  endUpdateUserType,
  createList,
  FieldTypes,
  multipleSelectType,
  separateType,
} from '@/components/form-config/types/FieldTypes';
import {SystemTypeEnum} from '@/components/form-config/types/field'

const systemLinkTypes = [...submitterType, ...submitterTimeType, ...triggerTimeType];
const systemDataLinkTypes = [...endUpdateTimeType, ...endUpdateUserType];

const props = withDefaults(
  defineProps<{
    data: FieldBaseField;
    fieldList?: any[];
    isLinkAge?: boolean;
    pathType?: number;
  }>(),
  {
    isLinkAge: true,
    pathType: 0,
    fieldList: () => [],
  }
);

//const linkage = defineModel<boolean>('linkage', { default: false });
const field = defineModel<string>('field', { default: '' });

//系统字段
const systemFields = computed(() => {
  const types:SystemTypeEnum[] = props.pathType === 0 ? systemLinkTypes : systemDataLinkTypes;
  return createList.filter(item => types.includes(item.fixType as SystemTypeEnum));
});

const filterList = computed(() => {
  //获取依赖
  (<DateAllField>props.data).dateFormatType;
  (<SelectAllField>props.data).selectType;
  (<AddressAllField>props.data).addressPrecision;

  let result = [...props?.fieldList, ...systemFields.value];
  const filterResult = <T extends FieldTypes>(types: T) => {
    result = result.filter(i => _.includes(types, i.fixType || i.contentType));
  };
  separateType(props.data.fixType, {
    inputType: types => {
      filterResult(types);
    },
    dateType: types => {
      const dataTypes =
        props.pathType === 0 ? [...submitterTimeType, ...triggerTimeType] : endUpdateTimeType;
      const newTypes = [...types, ...dataTypes];
      filterResult(newTypes);
    },
    dateRangeType: filterResult,
    numberType: filterResult,
    // 选择
    selectType: types => {
      filterResult(types);
    },
    mulSelectType: filterResult,
    mulCascaderType: filterResult,
    addressType: filterResult,
    uploadType: filterResult,
    photoType: filterResult,
    tipsType: filterResult,
    usersType: types => {
      //展示字段那里 成员选择可以取其他任务的提交人(submitUser)?或成员选择 日期时间可以取其他任务日期时间或触发时间(triggerTime)或提交时间(submitTime) (会办和数据选择)
      //数据表 最后更新人和最后更新时间
      const userType = props.pathType === 0 ? submitterType : endUpdateUserType;
      const newTypes = [...types, ...userType];
      filterResult(newTypes);
    },
    userTagType: filterResult,
    userDeptType: filterResult,
    signatureType: filterResult,
    signatureCollectType: filterResult,
    inventoryComType: filterResult,
    interspaceComType: filterResult,
    departAdminType: filterResult,
    workingAdminType: filterResult,
    dynamicType: filterResult,
  });

  return result;
});
</script>

<style scoped lang="scss">
.color-var {
  color: var(--el-color-primary);
}
</style>
