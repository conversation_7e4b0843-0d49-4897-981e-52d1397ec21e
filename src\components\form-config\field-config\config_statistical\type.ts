import {
  FieldBaseField,
  TitleField,
} from '@/components/form-config/types/AvueTypes';
import { SubmitterEnum } from './const'
/* 统计指标 */

// 计算范围
export type StatisticalType = {
  timeRange: string[],
  versionId: string,
  submitter: string,
  submitterType: SubmitterEnum,
  usersComponentIds: string,
  taskId: string;
  statisticalRate: string
}
export type StatisticalAllType = FieldBaseField & TitleField & StatisticalType;