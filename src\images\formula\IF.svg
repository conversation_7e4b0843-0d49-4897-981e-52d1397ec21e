<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>if</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-230.000000, -350.000000)">
            <g id="if" transform="translate(230.000000, 350.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-2" transform="translate(10.000000, 42.000000)">
                    <text id="IF函数判断一个条件能否满足；如果满足返" font-family="PingFangSC-Regular, PingFang SC"
                          font-size="14" font-weight="normal" line-spacing="23">
                        <tspan x="0" y="15" fill="#3F70FF">IF</tspan>
                        <tspan x="11.396" y="15" fill="#3A3A3A">函数判断一个条件能否满足；如果满足返回一</tspan>
                        <tspan x="0" y="38" fill="#3A3A3A">个值，如果不满足则返回另外一个值。</tspan>
                        <tspan x="238" y="38" fill="#3F70FF"></tspan>
                        <tspan x="0" y="62" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="62" fill="#3F70FF">IF</tspan>
                        <tspan x="60.396" y="62" fill="#3A3A3A">(逻辑表达式,为true时返回的值,为false</tspan>
                        <tspan x="0" y="85" fill="#3A3A3A">时返回的值)</tspan>
                        <tspan x="0" y="109" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="109" fill="#3F70FF">IF</tspan>
                        <tspan x="60.396" y="109" fill="#3A3A3A">(</tspan>
                    </text>
                    <rect id="矩形" fill="#EAF3FF" x="66" y="96" width="52" height="16"></rect>
                    <text id="文成绩&gt;60时返回及格，否则返回不及格。" font-family="PingFangSC-Regular, PingFang SC"
                          font-size="14" font-weight="normal" fill="#3A3A3A">
                        <tspan x="7" y="133">文成绩&gt;60时返回及格，否则返回不及格。</tspan>
                    </text>
                    <text id="&gt;60,&quot;及格&quot;,&quot;不及格&quot;)，当语"
                          font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                          fill="#3A3A3A">
                        <tspan x="119" y="108">&gt;60,"及格","不及格")，当语</tspan>
                    </text>
                    <text id="语文成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                          font-weight="normal" line-spacing="16" fill="#3F70FF">
                        <tspan x="72" y="107">语文成绩</tspan>
                    </text>
                    <g id="编组-30" transform="translate(66.000000, 98.000000)"></g>
                </g>
                <text id="IF" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">IF</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
