<?xml version="1.0" encoding="UTF-8"?>
<svg width="160px" height="160px" viewBox="0 0 160 160" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Text Tasks</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="160" height="160"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="160" height="160" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <linearGradient x1="64.0223567%" y1="100%" x2="64.0223567%" y2="0%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="0%"></stop>
            <stop stop-color="#F2F3F5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="64.0223567%" y1="96.9562861%" x2="64.0223567%" y2="0%" id="linearGradient-4">
            <stop stop-color="#F2F3F5" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#F2F3F5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="64.0223567%" y1="100%" x2="64.0223567%" y2="0%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="0%"></stop>
            <stop stop-color="#F2F3F5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="64.0223567%" y1="100%" x2="64.0223567%" y2="0%" id="linearGradient-6">
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="0%"></stop>
            <stop stop-color="#F2F3F5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#F2F3F5" offset="0%"></stop>
            <stop stop-color="#DCDEE0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#F2F3F5" offset="0%"></stop>
            <stop stop-color="#DCDEE0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-9">
            <stop stop-color="#DCDEE0" offset="0%"></stop>
            <stop stop-color="#DCDEE0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#F2F3F5" offset="0%"></stop>
            <stop stop-color="#DCDEE0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-12">
            <stop stop-color="#F2F3F5" offset="0%"></stop>
            <stop stop-color="#DCDEE0" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="50%" cy="0%" fx="50%" fy="0%" r="100%" gradientTransform="translate(0.500000,0.000000),scale(0.131250,1.000000),rotate(90.000000),scale(1.000000,4.177872),translate(-0.500000,-0.000000)" id="radialGradient-13">
            <stop stop-color="#EBEDF0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-14">
            <stop stop-color="#F2F3F5" offset="0%"></stop>
            <stop stop-color="#DCDEE0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="3.061617e-15%" id="linearGradient-15">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#DCDEE0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="151应用-自建应用缺省" transform="translate(-107.000000, -308.000000)">
            <g id="Text-Tasks" transform="translate(107.000000, 308.000000)">
                <g id="分组-5-copy">
                    <use id="矩形" stroke="#DCDEE0" mask="url(#mask-2)" stroke-width="2" opacity="0" stroke-dasharray="5" fill-rule="nonzero" xlink:href="#path-1"></use>
                    <path d="M2,131 L2,53 L22,53 L22,73 L36,73 L36,131 L2,131 Z" id="合并形状" fill="url(#linearGradient-3)" opacity="0.800000012" transform="translate(19.000000, 92.000000) scale(-1, 1) translate(-19.000000, -92.000000) "></path>
                    <path d="M35.5,15 C39.327645,15 42.5643772,17.5299978 43.6291434,21.0089401 L43.7508207,21.0038068 L44,21 C48.418278,21 52,24.581722 52,29 C52,33.418278 48.418278,37 44,37 C43.6613564,37 43.3276273,36.9789588 43.0000487,36.9381123 L43,37 L29,37 L29.000839,36.8258699 C28.519064,36.9397414 28.0165691,37 27.5,37 C23.9101491,37 21,34.0898509 21,30.5 C21,27.0730453 23.6520344,24.2655065 27.0157184,24.0177681 C27.0052347,23.8469908 27,23.6741067 27,23.5 C27,18.8055796 30.8055796,15 35.5,15 Z" id="合并形状" fill="url(#linearGradient-4)" opacity="0.800000012"></path>
                    <path d="M133,34 L151,34 L151,48 L159,48 L159,131 L133,131 L133,34 Z" id="合并形状" fill="url(#linearGradient-5)" opacity="0.800000012"></path>
                    <path d="M95,12 L117.231445,12 L117.231445,26 L126,26 L126,132 L95,132 L95,12 Z" id="合并形状" fill="url(#linearGradient-6)" opacity="0.800000012"></path>
                </g>
                <g id="分组-9" transform="translate(41.000000, 41.000000)">
                    <rect id="矩形" fill="url(#linearGradient-7)" fill-rule="nonzero" x="0" y="12" width="78" height="86" rx="2"></rect>
                    <path d="M39,0 C41.9730632,0 44.4410745,2.16238395 44.9170737,5.00019152 L56.9000002,5 C59.7166523,5 62,7.28334768 62,10.0999998 L62,14 C62,15.1045695 61.1045695,16 60,16 L18,16 C16.8954305,16 16,15.1045695 16,14 L16,10.0999998 C16,7.28334768 18.2833477,5 21.0999998,5 L33.0829263,5.00019152 C33.5589255,2.16238395 36.0269368,0 39,0 Z" id="Combined-Shape" fill="url(#linearGradient-8)"></path>
                    <rect id="Rectangle" fill="url(#linearGradient-9)" style="mix-blend-mode: multiply;" opacity="0.300000012" x="25" y="10" width="28" height="2" rx="1"></rect>
                    <rect id="矩形" fill="url(#linearGradient-10)" fill-rule="nonzero" x="7" y="23" width="64" height="68" rx="1"></rect>
                    <path d="M30.1065538,49.6091353 L39,58.503 L47.8934462,49.6091353 C48.6744948,48.8280867 49.9408247,48.8280867 50.7218733,49.6091353 L53.3908647,52.2781267 C54.1719133,53.0591753 54.1719133,54.3255052 53.3908647,55.1065538 L44.497,64 L53.3908647,72.8934462 C54.1719133,73.6744948 54.1719133,74.9408247 53.3908647,75.7218733 L50.7218733,78.3908647 C49.9408247,79.1719133 48.6744948,79.1719133 47.8934462,78.3908647 L39,69.497 L30.1065538,78.3908647 C29.3255052,79.1719133 28.0591753,79.1719133 27.2781267,78.3908647 L24.6091353,75.7218733 C23.8280867,74.9408247 23.8280867,73.6744948 24.6091353,72.8934462 L33.503,64 L24.6091353,55.1065538 C23.8280867,54.3255052 23.8280867,53.0591753 24.6091353,52.2781267 L27.2781267,49.6091353 C28.0591753,48.8280867 29.3255052,48.8280867 30.1065538,49.6091353 Z" id="合并形状" fill="url(#linearGradient-11)" fill-rule="nonzero"></path>
                    <rect id="矩形" fill="url(#linearGradient-12)" fill-rule="nonzero" opacity="0.5" x="24" y="31" width="30" height="6" rx="1"></rect>
                </g>
                <rect id="Rectangle" fill="url(#radialGradient-13)" x="0" y="139" width="160" height="21"></rect>
                <g id="分组-3" transform="translate(15.000000, 115.000000)" fill-rule="nonzero" opacity="0.600000024">
                    <g id="分组-3-copy" transform="translate(119.000000, 0.000000)">
                        <path d="M6.45961736,3.50937356 L10.6431425,18.7350517 C10.7894694,19.2675993 10.4763757,19.8179357 9.94382812,19.9642626 C9.85750968,19.9879802 9.76839742,20 9.67887986,20 L1.3118296,20 C0.75954485,20 0.3118296,19.5522847 0.3118296,19 C0.3118296,18.9104824 0.323849448,18.8213702 0.34756697,18.7350517 L4.5310921,3.50937356 C4.67741898,2.97682602 5.22775544,2.66373231 5.76030299,2.81005919 C6.10042883,2.9035148 6.36616175,3.16924773 6.45961736,3.50937356 Z" id="三角形" fill="url(#linearGradient-14)"></path>
                        <rect id="矩形" fill="url(#linearGradient-15)" x="3.99662162" y="20" width="2.99746622" height="10"></rect>
                    </g>
                    <g id="分组-3-copy-2">
                        <path d="M6.45961736,3.50937356 L10.6431425,18.7350517 C10.7894694,19.2675993 10.4763757,19.8179357 9.94382812,19.9642626 C9.85750968,19.9879802 9.76839742,20 9.67887986,20 L1.3118296,20 C0.75954485,20 0.3118296,19.5522847 0.3118296,19 C0.3118296,18.9104824 0.323849448,18.8213702 0.34756697,18.7350517 L4.5310921,3.50937356 C4.67741898,2.97682602 5.22775544,2.66373231 5.76030299,2.81005919 C6.10042883,2.9035148 6.36616175,3.16924773 6.45961736,3.50937356 Z" id="三角形" fill="url(#linearGradient-14)"></path>
                        <rect id="矩形" fill="url(#linearGradient-15)" x="3.99662162" y="20" width="2.99746622" height="10"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>