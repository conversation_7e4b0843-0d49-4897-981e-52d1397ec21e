import request from '@/axios';
/**
 * 获取知识库列表
 */
export const getKnowledgeList = (params={}) => {
    return request({
        url: "/nbxt-service-task/knowledgeManage/knowledgeBase/list",
        method: 'post',
        data:{
            ...params
        }
    });
};
/**
 * 添加知识库列表
 */
export const addKnowledgeList = (params={}) => {
    return request({
        url: "/nbxt-service-task/knowledgeManage/knowledgeBase/submit",
        method: 'post',
        data:{
            ...params
        }
    });
};
/**
 * 编辑知识库模板
 */
export const updateKnowledgeTemplate = (params={}) => {
    return request({
        url: "/nbxt-service-task/knowledgeManage/knowledgeBase/template",
        method: 'post',
        data:{
            ...params
        }
    });
};
/**
 * 模板详情
 */
export const detailKnowledgeTemplate = (knowledgeBaseId:string) => {
    return request({
        url: `/nbxt-service-task/knowledgeManage/knowledgeBase/template/${knowledgeBaseId||''}`,
        method: 'get',
    });
};
/**
 * 模板数据列表
 */
export const templateDataList = (current:number,size:number,params={}) => {
    return request({
        url: "/nbxt-service-task/knowledgeManage/data/page",
        method: 'post',
        params:{
            current,size,
        },
        data:{
            ...params,
        }
    });
};
/**
 * 编辑知识库模板
 */
export const addTemplateData = (knowledgeBaseId:string,data:any,id?:string) => {
    return request({
        url: "/nbxt-service-task/knowledgeManage/data/submit",
        method: 'post',
        data:{
            knowledgeBaseId,
            data,
            id
        }
    });
};
/**
 * 删除知识库模板
 */
export const delTemplateData = (ids:string) => {
    return request({
        url: "/nbxt-service-task/knowledgeManage/data/remove",
        method: 'post',
        params:{
            ids
        }
    });
};
/**
 * 删除知识库
 */
export const delKnowledge = (id:string) => {
    return request({
        url: `/nbxt-service-task/knowledgeManage/knowledgeBase/${id}`,
        method: 'get',
    });
};
/**
 * 获取单挑数据信息
 */
export const getTemplateDataDetail = (id:string ) => {
    return request({
        url: "/nbxt-service-task/knowledgeManage/data/detail",
        method: 'post',
        data:{
            id,
        }
    });
};