<template>
  <config-title :data="data" />
  <config-permission :data="data" />
  <el-form-item label="类型">
    <avue-select v-model="data.type" :dic="inputTypeDic" :clearable="false" />
  </el-form-item>
  <template v-if="data.defaultType === 0">
    <el-form-item label="格式">
      <avue-select v-model="data.showFormat" :dic="inputFormatType" :clearable="false" />
    </el-form-item>
  </template>
  <config-default-type :data="data" v-if="formType!== formTypeEnum.KNOWLEDGE"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import { inputFormatType, inputTypeDic } from './const';
import { InputAllField } from './type';
import { formTypeEnum } from '@/components/form-config/const/commonDic';

const props = defineProps<{
  data: InputAllField;
}>();

const formType = inject('formType',null)

watch(
  () => props.data.defaultType, 
  (val) => {
  if (val) {
    props.data.showFormat = 0;
  }
});

</script>