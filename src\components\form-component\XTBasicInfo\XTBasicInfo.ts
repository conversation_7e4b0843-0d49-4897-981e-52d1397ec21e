
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { SelectColumnTypeEnum, InputTypeEnum, AddressPrecisionEnum } from '@/components/form-config/const/commonDic';

export type BasicInfoType = {
  tenantName: string;
  socialCreditCode: string;
  companyPhone: string;
  manageRange: string;
  manageStatus: number;
  legaler: string;
  linkman: string;
  contactNumber: string;
  registerAddress: string;
  manageAddress: string;
  depositBank: string;
  depositAccount: string;
  industrySecond: string;
  // officialSeal: Array;
  securityCode: string;
  tenantAbbreviation: string;
  legalerPhone: string;
  staffSize: string;
  logo: string;
  officialSealStatus: string;
};

export type ColExtra = {
  [P in keyof BasicInfoType]?: {
    label: string;
    display: string;
  };
};

export type ExtraInfo = {
  [P in keyof BasicInfoType]?: any;
};

const manageStatusDic = [
  { label: '存续', value: 0 },
  { label: '在业', value: 1 },
  { label: '吊销', value: 2 },
  { label: '注销', value: 3 },
  { label: '迁入', value: 4 },
  { label: '迁出', value: 5 },
  { label: '停业', value: 6 },
  { label: '清算', value: 7 },
];

const staffSizeList = [
  { label: '1-9人', value: '0' },
  { label: '10-49人', value: '1' },
  { label: '50-99人', value: '2' },
  { label: '100-299人', value: '3' },
  { label: '300-499人', value: '4' },
  { label: '500-999人', value: '5' },
  { label: '1000-2999人', value: '6' },
  { label: '3000-4999人', value: '7' },
  { label: '5000-9999人', value: '8' },
  { label: '10000-99999人', value: '9' },
  { label: '100000人及以上', value: '10' },
];
const action = '/api/blade-resource/oss/endpoint/put-file';

export const basicInfoColumn: Required<ExtraInfo> = {
  tenantName: {
    label: '组织名称',
    fixType: FixTypeEnum.INPUT
  },
  socialCreditCode: {
    label: '统一社会信用代码',
    fixType: FixTypeEnum.INPUT
  },
  legaler: {
    label: '单位法人',
    fixType: FixTypeEnum.INPUT
  },
  legalerPhone: {
    label: '法人电话',
    fixType: FixTypeEnum.INPUT
  },
  manageStatus: {
    label: '经营状态',
    fixType: FixTypeEnum.SELECT,
    type: SelectColumnTypeEnum.SELECT,
    dicData: manageStatusDic,
  },
  manageRange: {
    label: '经营范围',
    fixType: FixTypeEnum.INPUT,
    type: InputTypeEnum.TEXTAREA,
    minRows: 2,
    maxRows: 4,
  },
  registerAddress: {
    label: '注册地址',
    fixType: FixTypeEnum.ADDRESS,
    addressPrecision: AddressPrecisionEnum.STREET,
    component: 'XtArea',//兼容组件之外的地方使用
  },
  manageAddress: {
    label: '实际经营地址',
    fixType: FixTypeEnum.ADDRESS,
    addressPrecision: AddressPrecisionEnum.STREET,
    component: 'XtArea',
  },
  industrySecond: {
    label: '行业类型',
    fixType: 'input-tree',
    component: 'avue-input-tree',
    dicUrl: '/getChild',
    props: {
      label: 'industryName',
      value: 'industryName',
    },
  },
  staffSize: {
    label: '人员规模',
    fixType: FixTypeEnum.SELECT,
    type: SelectColumnTypeEnum.SELECT,
    dicData: staffSizeList,
  },
  linkman: {
    label: '单位联系人',
    fixType: FixTypeEnum.INPUT
  },
  contactNumber: {
    label: '单位联系人电话',
    fixType: FixTypeEnum.INPUT
  },
  companyPhone: {
    label: '企业电话',
    fixType: FixTypeEnum.INPUT
  },
  depositBank: {
    label: '开户银行',
    fixType: FixTypeEnum.INPUT
  },
  depositAccount: {
    label: '开户账号',
    fixType: FixTypeEnum.INPUT
  },
  tenantAbbreviation: {
    label: '组织简称',
    fixType: FixTypeEnum.INPUT
  },
  logo: {
    label: '上传logo',
    prop: 'imgUrl',
    type: FixTypeEnum.UPLOAD,
    fixType: FixTypeEnum.UPLOAD,
    listType: 'picture-img',
    accept: 'image/png, image/jpeg',
    propsHttp: {
      res: 'data',
      url: 'link',
      name: 'name',
    },
    span: 24,
    limit: 1,
    action: action,
  },
  officialSealStatus: {
    label: '公章',
    fixType: FixTypeEnum.INPUT
  },
  securityCode: {
    label: '防伪码',
    fixType: FixTypeEnum.INPUT
  },
  // officialSeal: {
  //   label: '上传公章',
  //   prop: 'imgUrl',
  //   listType: 'picture-img',
  //   accept: 'image/png, image/jpeg',
  //   propsHttp: {
  //     res: 'data',
  //     url: 'link',
  //     name: 'name',
  //   },
  //   type: 'upload',
  //   span: 24,
  //   limit: 1,
  //   action: action,
  // },
};

export const initBasicInfoColumn = (extra: ExtraInfo) => {
  const result = _.cloneDeep(basicInfoColumn);
  return _.merge(result, extra);
};
