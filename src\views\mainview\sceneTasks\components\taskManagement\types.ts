export type PackageProp = {
  id: string;
  groupName: string;
  parentId: string | number;
  tid: string;
  ancestors: string;
  relWorkGroupIds: string;
  logo?: string;
  defaultVersionFlag: number;
};

export interface taskPackageTreeProps {
  addBtn?: boolean;
  showMenu?: boolean;
  defOne?: boolean;
  other?: boolean;
}
export type rackGroupType = PackageProp & {
  relTasks: { id: string; taskName: string }[];
  sceneGroupFlag: boolean;
};
