<template>
  <n-card :content-style="{ padding: '10px', position: 'relative', paddingRight: '60px' }">
    <avue-radio
      v-model="filterColumn.filterType"
      :dic="dic"
      class="mb-3"
      @change="changeFilterType"
    ></avue-radio>
<!--    <el-row :gutter="10">-->
<!--      <el-col :span="24" v-if="filterColumn.filterType === 'TRIGGER_TASK_FIELD' && !linkTaskId" style="color: #f44;">-->
<!--        请先选择联动任务-->
<!--      </el-col>-->
<!--      &lt;!&ndash; 推送任务 &ndash;&gt;-->
<!--      <el-col :span="8">-->
<!--        <el-input v-model="pushTaskLabel" disabled></el-input>-->
<!--      </el-col>-->
<!--      <el-col :span="8">-->
<!--        <avue-select-->
<!--          class="w-full"-->
<!--          :dic="resFieldList"-->
<!--          :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }"-->
<!--          v-model="filterColumn.dataProp"-->
<!--          placeholder="请选择字段"-->
<!--        ></avue-select>-->
<!--      </el-col>-->
<!--      &lt;!&ndash; 规则 &ndash;&gt;-->
<!--      <el-col :span="8">-->
<!--        <avue-select-->
<!--          class="w-full"-->
<!--          :dic="symbolList"-->
<!--          v-model="filterColumn.operator"-->
<!--          placeholder="请选择规则"-->
<!--        ></avue-select>-->
<!--      </el-col>-->
<!--      &lt;!&ndash; 联动任务字段 &ndash;&gt;-->
<!--      <template v-if="filterColumn.filterType === 'TRIGGER_TASK_FIELD'">-->
<!--        <el-col :span="8">-->
<!--          <el-input v-model="linkTaskLabel" disabled></el-input>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <avue-select-->
<!--            class="w-full"-->
<!--            :dic="filterList"-->
<!--            :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }"-->
<!--            v-model="filterColumn.linkageProp"-->
<!--            placeholder="请选择字段"-->
<!--          ></avue-select>-->
<!--        </el-col>-->
<!--      </template>-->
<!--      &lt;!&ndash; 当前任务字段 &ndash;&gt;-->

<!--      <template v-if="filterColumn.filterType === 'CURRENT_FORM_FIELD'">-->
<!--        <el-col :span="8">-->
<!--          <avue-select-->
<!--            class="!w-full"-->
<!--            :dic="filterList"-->
<!--            :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }"-->
<!--            v-model="filterColumn.currentFormProp"-->
<!--            placeholder="请选择字段"-->
<!--          ></avue-select>-->
<!--        </el-col>-->
<!--      </template>-->
<!--      &lt;!&ndash; 自定义字段 &ndash;&gt;-->
<!--      <template v-if="filterColumn.filterType === 'CUSTOM'">-->
<!--        <el-col :span="24">-->
<!--          <component-->
<!--            :is="getFieldComponent(filterColumn.operator, fieldType, isDynamic)"-->
<!--            v-model="filterColumn.value"-->
<!--            :field="field"-->
<!--            class="!w-full"-->
<!--          ></component>-->
<!--        </el-col>-->
<!--      </template>-->
<!--    </el-row>-->
    <slot name="customBtn">
      <el-col :span="2">
        <el-button type="danger" link class="absolute right-10px top-10px" @click="$emit('delete')"
        >删除
        </el-button>
      </el-col>
    </slot>
    <template v-if="filterColumn.filterType === 'TRIGGER_TASK_FIELD'">
      <conditionOtherTaskItem :del-btn="false"
                              :push-field-list="resFieldList"
                              :link-field-list="filterList"
                              v-model:data-prop="filterColumn.dataProp"
                              v-model:symbol="filterColumn.operator"
                              v-model:linkageProp="filterColumn.linkageProp"
                              :link-task-label="linkTaskLabel"
                              :push-task-label="pushTaskLabel">
      </conditionOtherTaskItem>
    </template>
    <template v-else-if="filterColumn.filterType === 'CURRENT_FORM_FIELD'">
      <conditionCurrentTaskItem :del-btn="false"
                                :push-field-list="resFieldList"
                                :link-field-list="filterList"
                                v-model:data-prop="filterColumn.dataProp"
                                v-model:symbol="filterColumn.operator"
                                v-model:linkageProp="filterColumn.currentFormProp"
                                :push-task-label="pushTaskLabel">
      </conditionCurrentTaskItem>
    </template>
    <template v-else-if="filterColumn.filterType === 'CUSTOM'">
      <conditionCustomItem :del-btn="false"
                           :field-list="resFieldList"
                           :push-task-label="pushTaskLabel"
                           v-model:fieldId="filterColumn.dataProp"
                           v-model:symbol="filterColumn.operator"
                           v-model:result="filterColumn.value"></conditionCustomItem>
    </template>
  </n-card>
</template>

<script setup lang="ts">
import { filterConditionItemType } from '@/api/interface/configDataLink.ts';
import {
  getDynamicFieldSymbolDic,
  getFieldSymbolDic,
  getFilterFieldSymbolDic
} from '@/views/mainview/sceneTasks/detail/option/field.ts';
import { FieldBaseField } from '@/components/form-config/types/AvueTypes';
import {SystemTypeEnum} from '@/components/form-config/types/field'
import { pushTaskListType } from '@/api/interface/task';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import {
  contrastTypes,
  createList,
  dataLinkCurrentMatchField,
  dataLinksDefaultDynamicField,
  dataLinksType,
  endUpdateUserType,
  FieldTypes,
  filterCurrentField,
  getUsercodeType,
  dateType,
  separateType,
  submitterTimeType,
  submitterType,
  systemByFilterSortType,
  systemByFilterSortType1,
  systemByLinkDataType,
  systemByLinkDataType1,
  systemfilterLinkType,
  triggerTimeType,
  userCurrentType,
  usersType,
} from '@/components/form-config/types/FieldTypes';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts';
import { colsToFlatDynamic } from '@/utils/formUtils.ts';
import { editKnowType } from '@/api/interface/knowLedgeType';
import { DynamicTypeEnum } from '@/components/form-config/const/commonDic';
import { XtFieldType } from '@/components/form-config/types/FieldTypes.ts';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';
import ConditionOtherTaskItem from '@/components/conditions/conditionOtherTaskItem.vue';
import ConditionCurrentTaskItem from '@/components/conditions/conditionCurrentTaskItem.vue';
import ConditionCustomItem from '@/components/conditions/conditionCustomItem.vue';

const props = defineProps<{
  filterColumn: Partial<filterConditionItemType>;
  conditionTaskId?: string;
  data: FieldBaseField;
  taskList: pushTaskListType[];
  knowData?: editKnowType[];
  pathType: number
}>();

const emits = defineEmits(['delete', 'change']);
const { taskBase } = storeToRefs(useTaskDetailStore());

const fieldType = computed<any>(() => {
  return field.value?.fixType
});

const pathType = computed(() => {
  return [DynamicTypeEnum.KNOWLEDGE, DynamicTypeEnum.ORGKNOWLEDGE].includes(props.data.defaultType as DynamicTypeEnum) ? 3 : props.pathType;
});

//当前选中的推数路径组件
const field = computed(() => {
  return (
    resFieldList.value &&
    resFieldList.value.find((item: any) => item.prop == props.filterColumn.dataProp)
  );
});

//提交人 提交时间等系统字段
//触发任务的时候 数据表选到更新人等于/不等与 触发任务的提交人；
//更新时间等于/不等于触发任务的提交时间/日期时间/触发时间；
//当前表单字段数据 数据表选不到更新人和更新时间；
//自定义的时候更新人等于/不等于当前用户或者为空不为空；更新时间的的话同自定义的提交时间

//推数路径的系统字段
const systemField = computed(() => {
  const TypeEnumLink: Record<string, string[]> = {
    'TRIGGER_TASK_FIELD': systemByLinkDataType,
    'CURRENT_FORM_FIELD': [...systemByFilterSortType, ...submitterType],
    'CUSTOM': systemByLinkDataType
  };
  const TypeEnumData: Record<string, string[]> = {
    'TRIGGER_TASK_FIELD': systemByLinkDataType1,
    'CURRENT_FORM_FIELD': [...systemByFilterSortType1, ...endUpdateUserType],
    'CUSTOM': systemByLinkDataType1
  };

  const typeList = pathType.value === 0 && props.data.defaultType !== 4 //知识库和数据表一样
    ? TypeEnumLink[props.filterColumn.filterType as any]
    : TypeEnumData[props.filterColumn.filterType as any];

  return createList.filter(item => typeList.includes(item.fixType));
});

//联动任务/当前表单的系统字段
const systemLinkField = computed(() => {
  const typeList: SystemTypeEnum[] = pathType.value === 0 ? systemByLinkDataType : systemfilterLinkType;
  return createList.filter(item => typeList.includes(item.fixType as SystemTypeEnum));
});


const isDynamic = computed(() => {
  return props.filterColumn.filterType === 'CUSTOM' &&
    _.includes(props.filterColumn.dataProp, 'dynamic');
  ;
});

//中间的运算规则
const symbolList = computed(() => {
  let isCustom = props.filterColumn.filterType === 'CUSTOM';
  let list = isCustom ? [] : filterMethods.value;
  let retainList = isCustom ? [] : retainMethods.value;
  // 特殊类型处理
  if (_.includes(contrastTypes, fieldType.value) && !isCustom) {
    return getFilterFieldSymbolDic(fieldType.value);
  } else {
    return isDynamic.value ?
      getDynamicFieldSymbolDic(fieldType.value as XtFieldType) :
      getFieldSymbolDic(fieldType.value as XtFieldType, isCustom, list, retainList);
  }
});

const linkTaskId = defineModel<string>('linkTaskId');
const pushTaskId = defineModel<string>('pushTaskId');
const pushFieldList = defineModel<AvueColumns[]>('pushFieldList');
const linkFieldList = defineModel<AvueColumns[]>('linkFieldList');
const dic = [
  { label: '触发任务字段', value: 'TRIGGER_TASK_FIELD' },
  { label: '当前表单字段', value: 'CURRENT_FORM_FIELD' },
  { label: '自定义', value: 'CUSTOM' }
];
const filterMethods = ref<string[]>([]);
const retainMethods = ref<string[]>([]);
//当前表单字段
const fieldList1 = computed(() => {
  let column = (taskBase.value as TaskBase).column || [];
  const arr = column.map(v => {
    return {
      ...v,
      disabled: false
    };
  });
  return arr.filter(item => _.includes(dataLinkCurrentMatchField, item.fixType));
});

// 知识库列表 / 任务列表
const pushTask = computed(() => {
  return [DynamicTypeEnum.KNOWLEDGE, DynamicTypeEnum.ORGKNOWLEDGE].includes(props.data.defaultType as DynamicTypeEnum) ? props.knowData : props.taskList;
});


//推送路径名称
const pushTaskLabel = computed(() => {
  return pushTask.value?.find((item: any) => item.value === pushTaskId.value)?.label;
});

//联动任务名称
const linkTaskLabel = computed(() => {
  return props.taskList.find(item => item.value === linkTaskId.value)?.label;
});

//推送路径-组件
const resFieldList = computed(() => {
  if (props.filterColumn.filterType === 'CUSTOM') {
    const arr = pushFieldList.value?.filter(item => _.includes(filterCurrentField, item.fixType)) as AvueColumns[];
    const arr1 = colsToFlatDynamic(arr, params, true);
    return [...arr1, ...systemField.value] as AvueColumns[];
  } else {
    const arr = pushFieldList.value?.filter(item => _.includes(dataLinksType, item.fixType)) as AvueColumns[];
    return [...arr, ...systemField.value] as AvueColumns[];
  }
});

//扁平化参数
const params = {
  id: '',
  unFlatDynamic: true,
  unFlatDataCollect: true,
  unFlatDataSelect: true,
  unFlatKnowledgeSelect: true,
  unFlatClubDataCollectType: true,
  unFlatOneByTriggerType: true,
  flatFilterType: dataLinksDefaultDynamicField
};

//联动任务字段/当前任务字段匹配同类型字段
const linkField = computed(() => {
  return props.filterColumn.filterType === 'CURRENT_FORM_FIELD'
    ? [...fieldList1.value, ...systemLinkField.value]
    : [...(linkFieldList.value || []), ...systemLinkField.value];
});

const changeFilterType = () => {
};

watch(
  () => props.filterColumn.filterType,
  () => {
    delete props.filterColumn.dataProp;
    delete props.filterColumn.operator;
    delete props.filterColumn.linkageProp;
    delete props.filterColumn.currentFormProp;
  }
);

watch(
  () => props.filterColumn.dataProp,
  () => {
    delete props.filterColumn.operator;
    delete props.filterColumn.linkageProp;
    delete props.filterColumn.currentFormProp;
    delete props.filterColumn.value;
  }
);

watch(
  () => linkTaskId.value,
  val => {
    if (!val) {
      delete props.filterColumn.linkageProp;
    }
  }
);

watch(
  () => pushTaskId.value,
  val => {
    if (!val) {
      delete props.filterColumn.dataProp;
      delete props.filterColumn.operator;
    }
  }
);

watch(
  () => props.filterColumn.operator,
  () => {
    delete props.filterColumn.linkageProp;
    delete props.filterColumn.currentFormProp;
    delete props.filterColumn.value;
  }
);

const filterList = computed(() => {
  const filterType = props.filterColumn.filterType;
  // 当前表单不要提交时间
  const timesType = filterType === 'TRIGGER_TASK_FIELD'
    ? [...submitterTimeType, ...triggerTimeType, ...dateType]
    : [...triggerTimeType, ...dateType];
  //当前表单不要提交人
  const usersTypeList = filterType === 'TRIGGER_TASK_FIELD'
    ? [...usersType, ...submitterType, ...getUsercodeType]
    : [...usersType, ...getUsercodeType, ...userCurrentType];

  let result = linkField.value || [];
  //新的筛选银则会自动排除非匹配字段，但是得确保基本字段、系统字段等支型齐全
  return result;
  const filterResult = <T extends FieldTypes>(types: T) => {
    result = (result as any[]).filter(i => _.includes(types, i.fixType));
  };

  separateType(fieldType.value, {
    inputType: types => {
      filterResult(types);
    },
    dateType: () => {
      filterResult(timesType);
      filterMethods.value = [
        'gtToday',
        'geToday',
        'ltToday',
        'leToday',
        'eqToday',
        'between',
        'innovate',
        'movingBefore',
        'movingAfter',
        'eqAt',
        'gtAt',
        'ltAt'
      ];
      // result.push(createList[1]);
    },
    dateRangeType: types => {
      filterResult(types);
      filterMethods.value = [
        'gtToday',
        'geToday',
        'ltToday',
        'leToday',
        'eqToday',
        'between',
        'innovate',
        'movingBefore',
        'movingAfter',
        'eqAt',
        'gtAt',
        'ltAt',
        'gt',
        'lt',
        'ge',
        'le'
      ];
    },
    numberType: types => {
      filterResult(types);
      filterMethods.value = ['between', 'eqDay', 'eqYear', 'eqMonth'];
    },
    // 选择
    selectType: types => {
      filterResult(types);
      filterMethods.value = ['includeAll'];
    },
    mulSelectType: filterResult,
    mulCascaderType: filterResult,
    formulaEditType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    targetValueType: types => {
      filterResult(types);
      filterMethods.value = ['between'];
    },
    calculationTableType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    submitterTimeType: () => {
      filterResult(timesType);
      filterMethods.value = [
        'between',
        'includeAll',
        'ltToday',
        'gtToday',
        'leToday',
        'eqToday',
        'innovate',
        'movingBefore',
        'movingAfter'
      ];
    },
    addressType: filterResult,
    uploadType: filterResult,
    photoType: filterResult,
    tipsType: filterResult,
    usersType: () => {
      filterResult(usersTypeList);
    },
    userTagType: filterResult,
    userDeptType: filterResult,
    signatureType: filterResult,
    interspaceComType: filterResult,
    inventoryComType: filterResult,
    departAdminType: filterResult,
    workingAdminType: filterResult,
    submitterType: () => {
      filterResult(usersTypeList);
    },
    getUsercodeType: () => {
      filterResult(usersTypeList);
    },
    triggerTimeType: () => {
      filterResult(timesType);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    executorUserType: filterResult,
    noSubmitUserType: filterResult,
    submitterTheDeptType: filterResult,
    submitterTheWorkType: filterResult,
    departMentType: filterResult,
    workLeadType: filterResult,
    organizeAdminType: filterResult,
    endUpdateTimeType: () => {
      filterResult(timesType);
    },
    endUpdateUserType: () => {
      filterResult(usersTypeList);
    }
  });

  return result;
});
</script>

<style scoped lang="scss"></style>
