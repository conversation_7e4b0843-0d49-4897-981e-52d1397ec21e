<script setup lang="ts">

import { DateFormatTypeEnum, DateLimitTypeEnum} from '@/components/form-config/const/commonDic.ts';
import dayjs from "dayjs";
import { DateAllField } from '@/components/form-config/types/AvueTypes.ts';
import {formInjectKey} from '@/components/xt-el-form/constant.ts'
const { disabled, type, format, dateFormatType } = toRefs(useAttrs());
const attrs = useAttrs() as DateAllField;
const formInject = inject('allformInjectKey', null) || inject(formInjectKey,undefined)!;

const model = defineModel<any>({ default: () => '' });
const key = ref(1)
interface defaultValType{
  type:string,
  value:string
}

watchEffect(() => {
  if (_.isArray(model.value)) {
    model.value = model.value?.[0];
  }
});

const dateType = {
  'year':'year',
  'month':'month',
  'date':'date',
  'datetime':undefined,
  'time':undefined
}

const valueFormatType = {
  0:"YYYY-01-01 00:00:00",
  1:"YYYY-MM-01 00:00:00",
  2:"YYYY-MM-DD 00:00:00",
  3:"YYYY-MM-DD HH:mm:00",
  4:"YYYY-MM-DD HH:mm:ss",
  5:"YYYY-MM-DD"
}

const vBind = computed(() => {
  return {
    disabled:disabled?.value ?? false,
    picker: dateType[type.value as keyof typeof dateType],
    format:format?.value,
    valueFormat: valueFormatType[
      (dateFormatType && typeof dateFormatType.value === 'number' ? dateFormatType.value : DateFormatTypeEnum.SECONDS) as keyof typeof valueFormatType
    ],
    disabledDate,
  };
});

// 动态日期计算示例
const calculateDynamicDate = (value: string) => {
  // 将 value 转换为数字
  const num = parseInt(value, 10);

  // 根据 num 的值来决定添加或减去的单位（这里默认为 'day'）
  if (!isNaN(num)) {
    return dayjs().add(num, 'day').startOf('day');
  }

  return null;
};

// 动态时间范围计算
const dateRange = computed(() => {
  const start = attrs.limitStartDate && attrs.limitStartDate ? parseLimitDate(attrs.limitStartDateType as string, attrs.limitStartDateVal as defaultValType) : null
  const end = attrs.limitEndDate && attrs.limitEndDate ? parseLimitDate(attrs.limitEndDateType as string, attrs.limitEndDateVal as defaultValType) : null
  return { start, end }
})

// 解析日期限制值（支持所有类型）
const parseLimitDate = (limitStartDateType: string, defaultValue: defaultValType) => {
  const {value, type} = defaultValue
  switch (limitStartDateType) {
    case DateLimitTypeEnum.FIXED:
      return dayjs(value).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    case DateLimitTypeEnum.DYNAMICS:
      return calculateDynamicDate(value)?.format('YYYY-MM-DD HH:mm:ss');
    case DateLimitTypeEnum.DEFAULT:
      return dayjs().add(parseInt(value), type as any).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    case DateLimitTypeEnum.FORM:
      // 这里需要从 formInject 中获取相关日期组件的值
      return formInjectValues.value[value];
      // return rawValue ? dayjs(rawValue).startOf('day').format('YYYY-MM-DD HH:mm:ss') : null;
    default:
      return null;
  }
};

// 使用 ref 来存储 formInject.value 的值
const formInjectValues = ref<Record<string, string>>({});
// 监视 formInject.value 的变化，并保存依赖字段的值到 formInjectValues
watchEffect(() => {
  if (attrs.limitStartDateType === DateLimitTypeEnum.FORM && formInject?.value) {
    const startDateKey = attrs.limitStartDateVal?.value || '';
    formInjectValues.value[startDateKey] = formInject.value?.[startDateKey] ?? null;
  }

  if (attrs.limitEndDateType === DateLimitTypeEnum.FORM && formInject?.value) {
    const endDateKey = attrs.limitEndDateVal?.value || '';
    formInjectValues.value[endDateKey] = formInject.value?.[endDateKey] ?? null;
  }
});

const prevStartDateValue = ref<string | null>(null);
const prevEndDateValue = ref<string | null>(null);
// 监视 formInject.value 的变化
watchEffect(() => {
  const startDateKey = attrs.limitStartDateVal?.value;
  const endDateKey = attrs.limitEndDateVal?.value;

  // 获取当前选中的日期
  const selectedDate = model.value ? dayjs(model.value) : null;

  // 监听 start date 变化
  if (attrs.limitStartDateType === DateLimitTypeEnum.FORM && startDateKey) {
    const currentVal = formInjectValues.value[startDateKey];
    if (currentVal !== prevStartDateValue.value) {
      prevStartDateValue.value = currentVal;

      if (selectedDate && currentVal) {
        const startDate = dayjs(currentVal);
        if (selectedDate.isBefore(startDate.startOf('day'))) {
          model.value = '';
        }
      }
    }
  }

  // 监听 end date 变化
  if (attrs.limitEndDateType === DateLimitTypeEnum.FORM && endDateKey) {
    const currentVal = formInjectValues.value[endDateKey];
    if (currentVal !== prevEndDateValue.value) {
      prevEndDateValue.value = currentVal;

      if (selectedDate && currentVal) {
        const endDate = dayjs(currentVal);
        if (selectedDate.isAfter(endDate.endOf('day'))) {
          model.value = '';
        }
      }
    }
  }
});

// 日期禁用逻辑（整合星期+范围限制）
const disabledDate = (date: Date) => {
  const current = dayjs(date).startOf('day'); // 归一化当前日期到当天 00:00:00

  if (attrs.limitWeek && attrs.limitWeekVal?.length) {
    const day = date.getDay();
    const targetDay = day === 0 ? 7 : day;
    if (!attrs.limitWeekVal.includes(String(targetDay))) return true;
  }

  const { start, end } = dateRange.value;

  if (start && current.isBefore(dayjs(start).startOf('day'))) return true;
  if (end && current.isAfter(dayjs(end).startOf('day'))) return true;

  return false;
};
//时间段间隔计算
const disabledHours = computed(() => {
  if (!attrs.limitTime || (!attrs.limitTimeStart && !attrs.limitTimeEnd)) return () => [];

  const [startHour] = (attrs.limitTimeStart || '00:00').split(':').map(Number);
  const [endHour] = (attrs.limitTimeEnd || '23:59').split(':').map(Number);

  return () => {
    const hours = [];
    for (let i = 0; i < 24; i++) {
      if (i < startHour || i > endHour) {
        hours.push(i);
      }
    }
    console.log(hours,'hours')

    return hours;
  };
});

const disabledMinutes = computed(() => {

  if (!attrs.limitTime || (!attrs.limitTimeStart && !attrs.limitTimeEnd)) return () => [];

  const [startHour, startMinute] = (attrs.limitTimeStart || '00:00').split(':').map(Number);
  console.log(attrs.limitTimeStart,startMinute,555)
  const [endHour, endMinute] = (attrs.limitTimeEnd || '23:59').split(':').map(Number);

  return (selectedHour: number) => {
    const minutes = [];
    if (selectedHour === startHour) {
      for (let i = 0; i <= 59; i++) {
        if (i < startMinute) {
          minutes.push(i);
        }
      }
    }
    if (selectedHour === endHour) {
      for (let i = 0; i <= 59; i++) {
        if (i > endMinute) {
          minutes.push(i);
        }
      }
    }
    console.log(minutes,'minutes')
    return minutes;
  };
});


// 计算默认时间
const defaultTime = computed(() => {
  if (attrs.limitTimeStart) {
    const [hour, minute, second = 0] = attrs.limitTimeStart.split(':').map(Number);
    console.log(hour,minute,'mmmm')
    return new Date(0, 0, 0, hour, minute, second);
  }
  // return new Date(0, 0, 0, 0, 0, 0); // 默认时间为 00:00:00
});
//点击时分秒框可以直接选中当前日期，禁用会失效，再次校验
const handleTimeChange = (value: string | Date) => {
  if (value) {
    const selectedTime = dayjs(value);
    const selectedDate = selectedTime.toDate();

    if (disabledDate(selectedDate)) {
      const startDate = dateRange.value.start ? dayjs(dateRange.value.start) : null;
      const endDate = dateRange.value.end ? dayjs(dateRange.value.end) : null;
      if (startDate) {
        model.value = '';
      } else if (endDate) {
        model.value = '';
      } else {
        model.value = '';
      }
      return;
    }

    const startDate = dateRange.value.start ? dayjs(dateRange.value.start) : null;
    const endDate = dateRange.value.end ? dayjs(dateRange.value.end) : null;

    if (startDate && selectedTime.isBefore(startDate)) {
      model.value = '';
    } else if (endDate && selectedTime.isAfter(endDate)) {
      model.value = '';
    }
  }
};
// // 禁用分钟-加上时间间隔
// const disabledMinutes = (selectedHour: number) => {
//   const minutes = [];
//   const step = stepMinutes.value;

//   for (let i = 0; i < 60; i++) {
//     if (selectedHour === startHour && i < startMinute) {
//       minutes.push(i);
//     } else if (selectedHour === endHour && i > endMinute) {
//       minutes.push(i);
//     } else if (i % step !== 0) {
//       minutes.push(i);
//     }
//   }

//   return minutes;
// };
// 时分秒的defaltTime不实时更新，需要强刷一下
watch(
  () => attrs.limitTimeStart,
  () => {
    key.value += 1
  },
  { immediate: true }
)

defineOptions({
  inheritAttrs: false,
});
</script>

<template>
  <el-date-picker
    :key="key"
    v-model="model"
    class="w-full!"
    :type="!!format ? 'datetime':( vBind.picker as any)"
    v-bind="vBind"
    placeholder="请选择日期"
    :disabled-hours="disabledHours"
    :disabled-minutes="disabledMinutes"
    :default-time='defaultTime'
    @change="handleTimeChange"
  />
</template>

<style lang="scss">
.m_data_picker{
  max-width:100%;
  :where(.css-dev-only-do-not-override-1lemaw2).ant-picker-dropdown .ant-picker-panel-container{
    overflow: scroll;
  }
  .ant-picker-panel-container{
    overflow: scroll;
  }
}
</style>