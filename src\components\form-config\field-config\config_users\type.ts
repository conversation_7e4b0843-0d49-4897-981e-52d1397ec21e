 import { UserRangeEnum } from './const'
 import {
   AllDefaultValueType,
   FieldBaseField,
   TitleField,
   basePermissionType,
   singleType,
   dataValueType
 } from '@/components/form-config/types/AvueTypes';
 
 /* 成员选择 */
 export type UsersField = basePermissionType& dataValueType & singleType & {
   isShowDept: boolean;
   isShowWork: boolean;
   userRange: UserRangeEnum.ALL
 };
 export type UsersAllField = FieldBaseField & UsersField  & TitleField & AllDefaultValueType;