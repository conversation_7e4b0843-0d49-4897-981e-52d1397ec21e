<script setup lang="ts">
import { FormDesignInputType } from '@/views/mobileView/components/mFormDesign/types.ts';

defineOptions({
  name:"xt-m-input"
})

const vModel = defineModel<string>({ default: () => [] });

const props = withDefaults(defineProps<FormDesignInputType>(), {
  disabled: false,
  placeholder: '请输入文本',
});
const attrs=useAttrs()
</script>

<template>
  <div>
    <van-field v-model="vModel" v-bind="props" />
  </div>
</template>

<style scoped lang="scss">

</style>