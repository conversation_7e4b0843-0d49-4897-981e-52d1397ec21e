import { SelectDic } from './type';
import { generateId } from '@/components/form-config/utils';

export const haveRepeat = (dic?: SelectDic[]) => {
  if (!Array.isArray(dic)) {
    return false;
  }
  let repeat = _.uniqBy(dic, 'label').length !== dic.length;
  if (!repeat) {
    dic.some(item => {
      repeat = haveRepeat(item.children);
      return repeat;
    });
  }
  return repeat;
};

export function addKeys(data: SelectDic[]) {
  for (const item of data) {
    if (!item.key) {
      item.key = generateId();
    }
    if (item.children) {
      addKeys(item.children);
    }
  }
}