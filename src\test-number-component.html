<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XTNumber组件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 3px;
        }
        .expected {
            color: #28a745;
        }
        .actual {
            color: #007bff;
        }
    </style>
</head>
<body>
    <h1>XTNumber组件百分比功能测试</h1>
    
    <div class="test-case">
        <h3>测试用例1：基本百分比输入</h3>
        <p><strong>输入：</strong>20（在百分比模式下）</p>
        <div class="result">
            <div class="expected">期望vModel值：0.2</div>
            <div class="expected">期望显示值：20%</div>
        </div>
    </div>
    
    <div class="test-case">
        <h3>测试用例2：保留2位小数的百分比</h3>
        <p><strong>输入：</strong>24.56（在百分比模式下，保留2位小数）</p>
        <div class="result">
            <div class="expected">期望vModel值：0.2456</div>
            <div class="expected">期望显示值：24.56%</div>
        </div>
    </div>
    
    <div class="test-case">
        <h3>测试用例3：超过小数位数限制</h3>
        <p><strong>输入：</strong>24.567（在百分比模式下，保留2位小数）</p>
        <div class="result">
            <div class="expected">期望行为：自动截断为24.56</div>
            <div class="expected">期望vModel值：0.2456</div>
            <div class="expected">期望显示值：24.56%</div>
        </div>
    </div>
    
    <div class="test-case">
        <h3>测试用例4：无小数位设置的百分比</h3>
        <p><strong>输入：</strong>25.34（在百分比模式下，未选择保留小数）</p>
        <div class="result">
            <div class="expected">期望行为：最多允许2位小数</div>
            <div class="expected">期望vModel值：0.2534</div>
            <div class="expected">期望显示值：25.34%</div>
        </div>
    </div>
    
    <div class="test-case">
        <h3>测试用例5：千分符显示</h3>
        <p><strong>输入：</strong>1234.56（在百分比模式下，开启千分符）</p>
        <div class="result">
            <div class="expected">期望vModel值：12.3456</div>
            <div class="expected">期望显示值：1,234.56%</div>
        </div>
    </div>
    
    <h2>修改总结</h2>
    <div class="test-case">
        <h3>主要修改点：</h3>
        <ul>
            <li><strong>vModel存储：</strong>百分比模式下存储实际值（0.2而不是20）</li>
            <li><strong>显示逻辑：</strong>失去焦点时显示格式化的百分比值（20.00%）</li>
            <li><strong>输入限制：</strong>百分比模式下限制小数位数不超过precision+2位</li>
            <li><strong>千分符：</strong>仅在显示时添加，不影响实际存储值</li>
            <li><strong>聚焦状态：</strong>聚焦时显示百分比数值（20），失去焦点时显示格式化值（20.00%）</li>
        </ul>
    </div>
    
    <div class="test-case">
        <h3>关键逻辑：</h3>
        <ul>
            <li>用户输入20 → vModel存储0.2 → 显示20%</li>
            <li>用户输入24.56 → vModel存储0.2456 → 显示24.56%</li>
            <li>小数位数控制：保留n位小数时，实际允许n+2位小数输入</li>
            <li>千分符只在显示时生效，不影响数据存储</li>
        </ul>
    </div>
</body>
</html>
