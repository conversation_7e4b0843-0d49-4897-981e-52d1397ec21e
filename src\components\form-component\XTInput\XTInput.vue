<template>
  <x-t-url-input v-model="vModel" v-bind="$attrs" v-if="$attrs.showFormat == InputFormatTypeEnum.URL"></x-t-url-input>
  <el-input v-model="vModel" v-bind="$attrs" v-else></el-input>
</template>

<script setup lang="ts">
import { InputFormatTypeEnum } from '@/components/form-config/const/commonDic.ts'
import XTUrlInput from './components/XTUrlInput.vue';

// const attrs = useAttrs() as InputField;
const vModel = defineModel<string>();
</script>

<style scoped></style>
 