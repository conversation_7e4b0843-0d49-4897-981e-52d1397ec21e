<template>
  <config-title :data="data" />
  <div class="flex mt-4 mb-4" v-if="data.dynamicType === 'crud'">
    <el-checkbox v-model="data.fixedColumn">固定前</el-checkbox>
    <avue-select
      v-model="data.fixedNumber"
      :dic="fixedNumber"
      :clearable="false"
      style="margin-left: 10px"
    />
  </div>
  <config-permission :data="data">
    <el-checkbox v-model="data.batchDelete" label="批量删除"/>
  </config-permission>
  <ConfigDefaultType :data="data"/>
  <!-- <config-data-sources  v-model="data.dataSources" v-else /> -->
  <ConfigRows :data="data"></ConfigRows>
</template>

<script setup lang="ts">
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
// import ConfigDataSources from './common/ConfigDataSources.vue';
import { fixedNumber } from './const';
import { DynamicAllField } from './type';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigRows from './components/Rows.vue';

defineProps<{
  data: DynamicAllField;
  column: any
}>();
</script>
