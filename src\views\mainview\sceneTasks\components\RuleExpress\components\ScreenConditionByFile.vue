<template>
  <div class="my-20px">
    <el-button @click="addCondition" type="primary" plain>+ 添加筛选条件组</el-button>
    <div
      class="border-1 border-#ebebeb border-solid p-10px mt-10px overflow-auto max-h-500px"
      v-for="(item, index) in dataFilterList"
      :key="item.id"
    >
      <div class="flex items-center justify-between">
        <h3 class="my-10px">筛选条件组 {{ Nzh.cn.encodeS(index + 1) }}</h3>
        <el-button type="danger" link class="text-center" @click="delCondition(item.id)"
          >删除
        </el-button>
      </div>
      <el-form-item label="数据来源" required label-position="left">
        <el-radio-group v-model="item.dataPathType" @change="handleDataSource(item)" class="ml-4">
          <el-radio value="0">任务</el-radio>
          <el-radio value="1">数据表</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="数据路径" label-position="left">
        <avue-select
          :dic="dicList(item)"
          filterable
          :props="{ label: 'label', value: 'id' }"
          v-model="item.dataPath"
          class="w-280px my-5px"
          value-on-clear=""
          :placeholder="`请选择${item.dataPathType === '0' ? '任务' : '数据表'}`"
          @change="(val:any) => handleTaskChange(val,item)"
        ></avue-select>
      </el-form-item>
      <ConditionLinkItem
        :fieldList="item.fieldList"
        :push-task-label="pushTaskLabel(item)"
        :link-task-label="currentLabel"
        field-key="id"
        :pathType="Number(item.dataPathType)"
        :dataPath="item.dataPath"
        :linkFieldList="linkFieldList"
        v-model:field-rule="item.fieldRule"
        v-model:field-condition="item.fieldConditionList"
      />
      <h4 class="my-10px">极值规则</h4>
      <el-space>
        <span class="el-form-item__label">筛选后根据</span>
        <avue-select
          class="w-120px"
          v-model="item.fieldSort.fieldId"
          :dic="sortFieldList(item.fieldList)"
          :props="{ value: 'id',label:'copyLabel' }"
          @change="(val:any) => handleFieldSort(val, item.fieldSort)"
        />
        <avue-select
          class="w-100px"
          placeholder="排序规则"
          v-model="item.fieldSort.sort"
          :dic="sortDic(item)"
        />
        <span class="el-form-item__label">排</span>
      </el-space>
    </div>
  </div>
</template>
<script setup lang="ts">
import Nzh from 'nzh';
import { storeToRefs } from 'pinia';
import { conditionGroupType, fieldSortType } from '../types';
import { randomLenNum } from '@/utils/util';
import useFieldList from '../../../detail/hooks/useFieldList';
import useRuleDetailStore from '../detailStore/index';
import { getByDataTableId } from '@/api/banban/dataTable.ts';
import {
  ruleExpressByFileType,
  createList,
  dataTableDefList,
  ruleExpressByDataTableType,
  numberType,
  dateType,
  endUpdateTimeType,
  submitterTimeType,
  triggerTimeType,
} from '@/components/form-config/types/FieldTypes';
import { AvueColumns } from '@/components/form-config/types/AvueTypes';
import ConditionLinkItem from './ConditionLinkItem.vue';

const props = defineProps<{ tableList: any; currentLabel: string; linkFieldList: AvueColumns[] }>();

const { taskListByVersion } = storeToRefs(useRuleDetailStore());
const { getFiledList } = useFieldList(ruleExpressByFileType);

const dataFilterList = defineModel<conditionGroupType[]>('dataFilterList', { default: [] });

const sortDicByNumber = [
  {
    label: '最大',
    value: 1,
  },
  {
    label: '最小',
    value: 0,
  },
];

const sortDicByDate = [
  {
    label: '最新',
    value: 1,
  },
  {
    label: '最旧',
    value: 0,
  },
];

const sortFieldList = computed(() => (list: AvueColumns[]) => {
  const filterList = list.filter(f =>
    [
      ...numberType,
      ...dateType,
      ...endUpdateTimeType,
      ...submitterTimeType,
      ...triggerTimeType,
    ].includes(f.fixType as any)
  );
  return [...filterList];
});

const sortDic = computed(() => (item: conditionGroupType) => {
  const { fieldSort, fieldList } = item;
  const fieldType = fieldList.find(f => f.id === fieldSort.fieldId)?.fixType;
  return fieldType === 'number' ? sortDicByNumber : sortDicByDate;
});

const pushTaskLabel = (item: conditionGroupType) => {
  const { dataPath } = item;
  return dicList.value(item).find((f: any) => f.id === dataPath)?.label || '';
};

const dicList = computed<any>(() => (item: conditionGroupType) => {
  const { dataPathType } = item;

  // 获取其他条件组已选中的值（排除当前条件组自身）
  const usedValues = dataFilterList.value
    .filter(group => group !== item && group.dataPath)
    .map(group => group.dataPath);

  let sourceList = [];
  if (dataPathType === '0') {
    sourceList = taskListByVersion.value || [];
  } else {
    sourceList = props.tableList || [];
  }

  // 过滤选项：只显示当前条件组已选中的 或 未被其他条件组选中的
  return sourceList.filter((option: any) => !usedValues.includes(option.id));
});

const addCondition = () => {
  (dataFilterList.value ??= []).push({
    id: randomLenNum(true),
    dataPathType: '0',
    fieldRule: 1,
    dataPath: '',
    fieldConditionList: [],
    fieldList: [],
    fieldSort: {
      sort: 0,
      fieldId: '',
      defaultField: '',
    },
  });
};

const handleTaskChange = async ({ value }: { value: string }, item: conditionGroupType) => {
  let { dataPathType } = item;
  if (!value) {
    item.fieldList = [];
    return;
  } else {
    if (dataPathType === '0') {
      const res = await getFiledList({
        id: value,
        unFlatDynamic: true,
        unFlatDataCollect: true,
        unFlatDataSelect: true,
        flatFilterType: ruleExpressByFileType,
      });

      let list = (res?.length && [...res, ...createList]) || ([] as AvueColumns[]);
      item.fieldList = list
        ?.filter(f => ruleExpressByFileType.includes(f.fixType))
        .map(item => ({
          ...item,
          disabled: false,
        })) as AvueColumns[];
    } else {
      const res = await getByDataTableId(value);
      let list = res.data.data.map((item: any) => {
        delete item.value;
        return {
          ...item,
          type: item.fieldType,
          copyLabel: item.fieldName,
          fixType: item.fieldType,
          prop: item.id,
        };
      });
      item.fieldList = [...list, ...createList, ...dataTableDefList.slice(0)].filter(f =>
        ruleExpressByDataTableType.includes(f.fixType)
      );
    }
  }
};

const handleFieldSort = ({ value }: { value: string }, fieldSort: fieldSortType) => {
  const SystemField = createList.find(f => f.id === value);
  if (SystemField) {
    fieldSort.defaultField = SystemField.prop;
  } else {
    fieldSort.defaultField = '';
  }
};

const handleDataSource = (item: conditionGroupType) => {
  item.dataPath = '';
  item.fieldList = [];
  item.fieldConditionList = [];
  item.fieldSort.fieldId = ''
  item.fieldSort.sort = undefined
  item.fieldSort.defaultField = ''
};

const delCondition = (id: number) => {
  dataFilterList.value = dataFilterList.value?.filter(item => item.id !== id);
};
</script>
<style lang="scss" scoped></style>
