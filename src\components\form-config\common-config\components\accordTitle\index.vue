<template>
    <div class="color-#000 font-400 text-14px flex items-center my-10px">
      <template v-if="showRule">
        <span>符合以下</span>
        <avue-select
          class="w-110px! px-10px satisfy-select"
          v-model="vModel"
          value="1"
          :dic="isAlsoEnum1"
          :clearable="false"
          :disabled="andOrDisabled"
        ></avue-select>
        <span>筛选条件的数据</span>
      </template>
      <n-button
        v-if="showAdd"
        class="ml-10px"
        quaternary
        color="#00A870"
        @click="$emit('add')"
        :focusable="false"
      >
        <template #icon>
          <el-icon>
            <Plus />
          </el-icon>
        </template>
        {{ title }}
      </n-button>
    </div>
  </template>
  
  <script setup lang="ts">
  // import useDictStore from '@/piniaStore/useDictStore.ts';
  import { isAlsoEnum1 } from '@/components/form-config/utils/option';
  import { Plus } from '@element-plus/icons-vue';
  
  withDefaults(
    defineProps<{
      showAdd?: boolean;
      showRule?: boolean;
      title:string;
      andOrDisabled?:boolean
    }>(),
    {
      showAdd: () => true,
      showRule: () => true,
    }
  );
  
  const vModel = defineModel<string>();
  
  defineEmits<{
    add: [];
  }>();
  
  // const { getDict } = useDictStore();
  </script>
  
  <style scoped lang="scss">
  .satisfy-select {
    :deep(.el-input) {
      .el-input__wrapper {
        background: transparent;
        box-shadow: none !important;
  
        .el-input__inner,
        .el-input__suffix .el-icon {
          color: var(--el-color-primary) !important;
        }
      }
    }
  }
  </style>
  