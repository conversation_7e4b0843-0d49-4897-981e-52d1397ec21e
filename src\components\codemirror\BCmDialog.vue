<template>
  <a-drawer v-model:open="open" :title :width destroyOnClose @close="onClose">
    <template #extra>
      <a-button style="margin-right: 8px" @click="onClose">取消 </a-button>
      <a-button type="primary" @click="onSubmit">提交 </a-button>
    </template>
    <BFormula v-model="vModel" v-bind="bFormulaProp" />
  </a-drawer>
</template>

<script lang="ts" setup>
import BFormula from './BFormula.vue';
import { BCmProp } from './types.ts';

withDefaults(
  defineProps<{
    bFormulaProp: BCmProp;
    title?: string;
    width?: number;
  }>(),
  {
    title: () => '',
    width: () => 400,
  }
);

const emits = defineEmits<{
  close: [];
  submit: [];
}>();

const vModel = defineModel<any>({ default: () => ({}) });
const open = defineModel<boolean>('open');

const onSubmit = () => {
  emits('submit');
};

const onClose = () => {
  open.value = false;
  emits('close');
};
</script>

<style lang="scss" scoped></style>
