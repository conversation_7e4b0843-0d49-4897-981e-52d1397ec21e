<script setup :T="number" lang="ts">

interface DateToBeforeType{
  year:number|null
  month:number|null
  day:number|null
}

const vModel=defineModel<DateToBeforeType>({required:true,default:()=>null})

onMounted(()=>{
  if (_.isEmpty(vModel.value)){
    vModel.value={
      year:null,
      month:null,
      day:null
    }
  }
})

</script>

<template>
  <div class="flex items-center">
    <el-space class="w-full" alignment="center" :wrap="false">
      <el-input type="number" clearable placeholder="请输入" v-model="vModel.year"></el-input>
      年
      <el-input type="number" clearable placeholder="请输入" v-model="vModel.month"></el-input>
      月
      <el-input type="number" clearable placeholder="请输入" v-model="vModel.day"></el-input>
      天
    </el-space>
    <div class="ml-10px text-nowrap">
      至此刻
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>