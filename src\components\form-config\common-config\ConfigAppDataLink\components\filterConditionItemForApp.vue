<template>
  <n-card :content-style="{ padding: '10px', position: 'relative', paddingRight: '60px' }">
    <avue-radio
      v-model="filterColumn.filterType"
      :dic="dic"
      class="mb-3"
      @change="changeFilterType"
    ></avue-radio>
    <el-row :gutter="10">
<!--      <el-col :span="24" v-if="filterColumn.filterType === 'TRIGGER_TASK_FIELD' && !linkTaskId" style="color: #f44;">-->
<!--        请先选择联动任务-->
<!--      </el-col>-->
      <!-- 推送任务 -->
      <el-col :span="8">
        <el-input v-model="pushTaskLabel" disabled></el-input>
      </el-col>
      <el-col :span="8">
        <avue-select
          class="w-full"
          :dic="resFieldList"
          :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }"
          v-model="filterColumn.dataProp"
          placeholder="请选择字段"
        ></avue-select>
      </el-col>
      <!-- 规则 -->
      <el-col :span="8">
        <avue-select
          class="w-full"
          :dic="symbolList"
          v-model="filterColumn.operator"
          placeholder="请选择规则"
        ></avue-select>
      </el-col>
      <!-- 联动任务字段 -->
<!--      <template v-if="filterColumn.filterType === 'TRIGGER_TASK_FIELD'">-->
<!--        <el-col :span="8">-->
<!--          <el-input v-model="linkTaskLabel" disabled></el-input>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <avue-select-->
<!--            class="w-full"-->
<!--            :dic="filterList"-->
<!--            :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }"-->
<!--            v-model="filterColumn.linkageProp"-->
<!--            placeholder="请选择字段"-->
<!--          ></avue-select>-->
<!--        </el-col>-->
<!--      </template>-->

      <!-- 当前任务字段 -->
      <template v-if="filterColumn.filterType === 'CURRENT_FORM_FIELD'">
        <el-col :span="8">
          <avue-select
            class="!w-full"
            :dic="filterList"
            :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }"
            v-model="filterColumn.currentFormProp"
            placeholder="请选择字段"
          ></avue-select>
        </el-col>
      </template>
      <!-- 自定义字段 -->
      <template v-if="filterColumn.filterType === 'CUSTOM'">
        <el-col :span="24">
          <component
            :is="getFieldComponent(filterColumn.operator, fieldType, isDynamic)"
            v-model="filterColumn.customize"
            :field="field"
            class="!w-full"
          ></component>
        </el-col>
      </template>
    </el-row>
    <slot name="customBtn">
      <el-col :span="2">
        <el-button type="danger" link class="absolute right-10px top-10px" @click="$emit('delete')"
          >删除
        </el-button>
      </el-col>
    </slot>
  </n-card>
</template>

<script setup lang="ts">
import { filterConditionItemType } from '@/api/interface/configDataLink.ts';
import {
  getFieldSymbolDic,
  getDynamicFieldSymbolDic,
  getFieldComponent,
  getFilterFieldSymbolDic
} from '@/views/mainview/sceneTasks/detail/option/field.ts';
import { AvueColumns } from '@/components/form-config/types/AvueTypes';
import { pushTaskListType } from '@/api/interface/task';
import { storeToRefs } from 'pinia';
//import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import {
  FieldTypes,
  separateType,
  dataLinksType,
  filterCurrentField,
  dataLinkCurrentMatchField,
  contrastTypes,
  submitterType,
  submitterTimeType,
  triggerTimeType,
  dateType,
  usersType,
  userCurrentType,
  getUsercodeType,
  endUpdateUserType
} from '@/components/form-config/types/FieldTypes';
import { 
  systemByLinkDataType, 
  systemByLinkDataType1, 
  systemfilterLinkType, 
  createList,
  systemByFilterSortType,
  systemByFilterSortType1 
} from '@/components/form-config/types/FieldTypes';
import { dataLinksDefaultDynamicField } from '@/components/form-config/types/FieldTypes';
import { colsToFlatDynamic } from '@/utils/formUtils.ts';
import { editKnowType } from '@/api/interface/knowLedgeType'
import { DynamicTypeEnum } from '@/components/form-config/const/commonDic';
import { XtFieldType }from '@/components/form-config/types/FieldTypes.ts';
import {SystemTypeEnum} from '@/components/form-config/types/field'
import useApplicationDetailStore from '@/views/banban/application/flowApp/store/useApplicationDetailStore.ts';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';

const props = defineProps<{
  filterColumn: Partial<filterConditionItemType>;
  conditionTaskId?: string;
  data: AvueColumns;
  taskList: pushTaskListType[];
  knowData?: editKnowType[];
  pathType?: number
}>();

const emits = defineEmits(['delete', 'change']);
//const { taskBase } = storeToRefs(useTaskDetailStore());
const { taskBase } = storeToRefs(useApplicationDetailStore());

const fieldType = computed<any>(() => {
  return field.value?.fixType
});

const pathType = computed(() => {
  return [DynamicTypeEnum.KNOWLEDGE,DynamicTypeEnum.ORGKNOWLEDGE].includes(props.data.defaultType as DynamicTypeEnum) ? 3 : props.pathType;
});

//当前选中的推数路径组件
const field = computed(() => {
  return (
      resFieldList.value &&
      resFieldList.value.find((item: any) => item.prop == props.filterColumn.dataProp)
    );
});

//提交人 提交时间等系统字段
//触发任务的时候 数据表选到更新人等于/不等与 触发任务的提交人；
//更新时间等于/不等于触发任务的提交时间/日期时间/触发时间；
//当前表单字段数据 数据表选不到更新人和更新时间；
//自定义的时候更新人等于/不等于当前用户或者为空不为空；更新时间的的话同自定义的提交时间

//推数路径的系统字段
const systemField = computed(() => {
  const TypeEnumLink: Record<string, string[]> = {
    'TRIGGER_TASK_FIELD': systemByLinkDataType,
    'CURRENT_FORM_FIELD': [...systemByFilterSortType,...submitterType],
    'CUSTOM': systemByLinkDataType
  }
  const TypeEnumData: Record<string, string[]> = {
    'TRIGGER_TASK_FIELD': systemByLinkDataType1,
    'CURRENT_FORM_FIELD': [...systemByFilterSortType1,...endUpdateUserType],
    'CUSTOM': systemByLinkDataType1
  }

  const typeList = pathType.value === 0 && props.data.defaultType !== 4 //知识库和数据表一样
  ? TypeEnumLink[props.filterColumn.filterType as any]
  : TypeEnumData[props.filterColumn.filterType as any]

  return createList.filter(item => typeList.includes(item.fixType));
});

//联动任务/当前表单的系统字段
const systemLinkField = computed(() => {
  const typeList:SystemTypeEnum[] = pathType.value === 0 ? systemByLinkDataType : systemfilterLinkType
  return createList.filter(item => typeList.includes(item.fixType as SystemTypeEnum));
});


const isDynamic = computed(() => {
  return props.filterColumn.filterType === 'CUSTOM' &&
   _.includes(props.filterColumn.dataProp, 'dynamic');
});

//中间的运算规则
const symbolList = computed(() => {
  let isCustom = props.filterColumn.filterType === 'CUSTOM';
  let list = isCustom ? [] : filterMethods.value;
  let retainList = isCustom ? [] : retainMethods.value;
  // 特殊类型处理
  if (_.includes(contrastTypes, fieldType.value) && !isCustom) {
    return getFilterFieldSymbolDic(fieldType.value);
  } else {
    return isDynamic.value ?
    getDynamicFieldSymbolDic(fieldType.value as XtFieldType) :
    getFieldSymbolDic(fieldType.value as XtFieldType, isCustom, list, retainList);
  }
});

const linkTaskId = defineModel<string>('linkTaskId');
const pushTaskId = defineModel<string>('pushTaskId');
const pushFieldList = defineModel<AvueColumns[]>('pushFieldList');
const linkFieldList = defineModel<AvueColumns[]>('linkFieldList');
const dic = [
  // { label: '触发任务字段', value: 'TRIGGER_TASK_FIELD' },
  { label: '当前表单字段', value: 'CURRENT_FORM_FIELD' },
  // { label: '当前表单字段', value: '0' },
  { label: '自定义', value: 'CUSTOM' },
  // { label: '自定义', value: '1' },
];
const filterMethods = ref<string[]>([]);
const retainMethods = ref<string[]>([]);
//当前表单字段
const fieldList1 = computed(() => {
  let column = (taskBase.value as TaskBase).column || [];
  const arr = column.map(v => {
    return {
      ...v,

      disabled: false,
    };
  });
  return arr.filter(item => _.includes(dataLinkCurrentMatchField, item.fixType));
});

// 知识库列表 / 任务列表
const pushTask = computed(() => {
  return [DynamicTypeEnum.KNOWLEDGE,DynamicTypeEnum.ORGKNOWLEDGE].includes(props.data.defaultType as DynamicTypeEnum) ? props.knowData : props.taskList
})


//推送路径名称
const pushTaskLabel = computed(() => {
  return pushTask.value?.find((item:any) => item.value === pushTaskId.value)?.label;
});

//联动任务名称
// const linkTaskLabel = computed(() => {
//   return props.taskList.find(item => item.value === linkTaskId.value)?.label;
// })

//推送路径-组件
const resFieldList = computed(() => {
  if (props.filterColumn.filterType === 'CUSTOM') {
    const arr  = pushFieldList.value?.filter(item => _.includes(filterCurrentField, item.fixType)) ;
    const arr1 = arr && colsToFlatDynamic(arr, params, true) || [];
    return [...arr1, ...systemField.value];
  } else {
    const arr = pushFieldList.value?.filter(item => _.includes(dataLinksType, item.fixType)) || [];
    return [...arr, ...systemField.value];
  }
});

//扁平化参数
const params = {
  id: '',
  unFlatDynamic: true,
  unFlatDataCollect: true,
  unFlatDataSelect: true,
  unFlatKnowledgeSelect: true,
  unFlatClubDataCollectType: true,
  unFlatOneByTriggerType: true,
  flatFilterType: dataLinksDefaultDynamicField,
};

//联动任务字段/当前任务字段匹配同类型字段
const linkField = computed(() => {
  return props.filterColumn.filterType === 'CURRENT_FORM_FIELD'
    ? [...fieldList1.value, ...systemLinkField.value, ]
    : [...(linkFieldList.value || []), ...systemLinkField.value];
});

const changeFilterType = () => {};

watch(
  () => props.filterColumn.filterType,
  () => {
    delete props.filterColumn.dataProp
    delete props.filterColumn.operator
    delete props.filterColumn.linkageProp;
    delete props.filterColumn.currentFormProp;
  }
);

watch(
  () => props.filterColumn.dataProp,
  () => {
    delete props.filterColumn.operator
    delete props.filterColumn.linkageProp;
    delete props.filterColumn.currentFormProp;
    delete props.filterColumn.customize;
  }
);

watch(
  () => linkTaskId.value,
  val => {
    if (!val) {
      delete props.filterColumn.linkageProp;
    }
  }
);

watch(
  () => pushTaskId.value,
  val => {
    if (!val) {
      delete props.filterColumn.dataProp
      delete props.filterColumn.operator;
    }
  }
);

watch(
  () => props.filterColumn.operator,
  () => {
    delete props.filterColumn.linkageProp;
    delete props.filterColumn.currentFormProp;
    delete props.filterColumn.customize;
  }
);

const filterList = computed(() => {
  const filterType = props.filterColumn.filterType
  //当前表单不要提交时间
  const timesType = filterType === 'TRIGGER_TASK_FIELD'
    ? [...submitterTimeType, ...triggerTimeType, ...dateType]
    : [...triggerTimeType, ...dateType]
  //当前表单不要提交人
  const usersTypeList = filterType === 'TRIGGER_TASK_FIELD'
    ? [...usersType, ...submitterType, ...getUsercodeType]
    : [...usersType, ...getUsercodeType, ...userCurrentType]

  let result = linkField.value || [];
  const filterResult = <T extends FieldTypes>(types: T) => {
    result = (result as any[]).filter(i => _.includes(types, i.fixType));
  };

  separateType(fieldType.value, {
    inputType: types => {
      filterResult(types);
    },
    dateType: () => {
      filterResult(timesType);
      filterMethods.value = [
        'gtToday',
        'geToday',
        'ltToday',
        'leToday',
        'eqToday',
        'between',
        'innovate',
        'movingBefore',
        'movingAfter',
        'eqAt',
        'gtAt',
        'ltAt',
      ];
      // result.push(createList[1]);
    },
    dateRangeType: types => {
      filterResult(types);
      filterMethods.value = [
        'gtToday',
        'geToday',
        'ltToday',
        'leToday',
        'eqToday',
        'between',
        'innovate',
        'movingBefore',
        'movingAfter',
        'eqAt',
        'gtAt',
        'ltAt',
        'gt',
        'lt',
        'ge',
        'le',
      ];
    },
    numberType: types => {
      filterResult(types);
      filterMethods.value = ['between', 'eqDay', 'eqYear', 'eqMonth'];
    },
    // 选择
    selectType: types => {
      filterResult(types);
      filterMethods.value = ['includeAll'];
    },
    mulSelectType: filterResult,
    mulCascaderType: filterResult,
    formulaEditType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    targetValueType: types => {
      filterResult(types);
      filterMethods.value = ['between'];
    },
    calculationTableType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    submitterTimeType: () => {
      filterResult(timesType);
      filterMethods.value = [
        'between',
        'includeAll',
        'ltToday',
        'gtToday',
        'leToday',
        'eqToday',
        'innovate',
        'movingBefore',
        'movingAfter',
      ];
    },
    addressType: filterResult,
    uploadType: filterResult,
    photoType: filterResult,
    tipsType: filterResult,
    usersType: () => {
      filterResult(usersTypeList)
    },
    userTagType: filterResult,
    userDeptType: filterResult,
    signatureType: filterResult,
    interspaceComType: filterResult,
    inventoryComType: filterResult,
    departAdminType: filterResult,
    workingAdminType: filterResult,
    submitterType: () => {
      filterResult(usersTypeList)
    },
    getUsercodeType: () => {
      filterResult(usersTypeList)
    },
    triggerTimeType: () => {
      filterResult(timesType);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    executorUserType: filterResult,
    noSubmitUserType: filterResult,
    submitterTheDeptType: filterResult,
    submitterTheWorkType: filterResult,
    departMentType: filterResult,
    workLeadType: filterResult,
    organizeAdminType: filterResult,
    endUpdateTimeType: () => {
      filterResult(timesType)
    },
    endUpdateUserType: () => {
      filterResult(usersTypeList)
    },
  });

  return result;
});
</script>

<style scoped lang="scss"></style>
