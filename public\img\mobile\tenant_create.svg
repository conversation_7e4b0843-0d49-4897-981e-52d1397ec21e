<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>创建组织</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#73C0FF" offset="0%"></stop>
            <stop stop-color="#3F8CFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="我的-1" transform="translate(-32.000000, -402.000000)">
            <g id="编组-97" transform="translate(16.000000, 386.000000)">
                <g id="创建组织" transform="translate(16.000000, 16.000000)">
                    <rect id="矩形" x="0" y="0" width="24" height="24"></rect>
                    <path d="M0,12 L0,12 C0,18.6274223 5.37257813,24 12,24 C18.6274219,24 24,18.6274223 24,12 C24,12 24,12 24,12 L24,12 C24,5.37257812 18.6274219,-2.1531573e-15 12,-2.1531573e-15 C5.37257813,-2.1531573e-15 0,5.37257812 0,12 L0,12 Z" id="路径" fill="url(#linearGradient-1)" fill-rule="nonzero" transform="translate(12.000000, 12.000000) scale(1, -1) translate(-12.000000, -12.000000) "></path>
                    <path d="M9.44,9.94666866 L9.44,5.84000199 C9.44,5.54666866 9.68,5.33333532 9.94666667,5.33333532 L14.0533333,5.33333532 C14.3466667,5.33333532 14.56,5.57333532 14.56,5.84000199 L14.56,9.94666866 C14.56,10.240002 14.32,10.4533353 14.0533333,10.4533353 L9.94666667,10.4533353 C9.65333333,10.4533353 9.44,10.2133353 9.44,9.94666866 L9.44,9.94666866 Z M6.34666667,15.5733353 L6.34666667,13.0133353 C6.34666667,12.720002 6.58666667,12.5066687 6.85333333,12.5066687 L17.12,12.5066687 C17.4133333,12.5066687 17.6266667,12.7466687 17.6266667,13.0133353 L17.6266667,15.5733353 C17.6266667,15.8666687 17.3866667,16.080002 17.12,16.080002 C16.8533333,16.080002 16.6133333,15.840002 16.6133333,15.5733353 L16.6133333,13.520002 L7.38666667,13.520002 L7.38666667,15.5733353 C7.38666667,15.8666687 7.14666667,16.080002 6.88,16.080002 C6.58666667,16.1066687 6.34666667,15.8666687 6.34666667,15.5733353 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero" opacity="0.6"></path>
                    <path d="M11.4933333,15.760002 L11.4933333,9.94666866 C11.4933333,9.65333532 11.7333333,9.44000199 12,9.44000199 C12.2933333,9.44000199 12.5066667,9.68000199 12.5066667,9.94666866 L12.5066667,15.760002 C12.5066667,16.0533353 12.2666667,16.2666687 12,16.2666687 C11.7066667,16.2666687 11.4933333,16.0266687 11.4933333,15.760002 Z" id="路径" fill="#FFFFFF" fill-rule="nonzero" opacity="0.6"></path>
                    <path d="M15.5733333,18.160002 L15.5733333,16.1066687 C15.5733333,15.8133353 15.8133333,15.600002 16.08,15.600002 L18.1333333,15.600002 C18.4266667,15.600002 18.64,15.840002 18.64,16.1066687 L18.64,18.160002 C18.64,18.4533353 18.4,18.6666687 18.1333333,18.6666687 L16.08,18.6666687 C15.8133333,18.6666687 15.5733333,18.4266687 15.5733333,18.160002 Z M5.33333333,18.160002 L5.33333333,16.1066687 C5.33333333,15.8133353 5.57333333,15.600002 5.84,15.600002 L7.89333333,15.600002 C8.18666667,15.600002 8.4,15.840002 8.4,16.1066687 L8.4,18.160002 C8.4,18.4533353 8.16,18.6666687 7.89333333,18.6666687 L5.84,18.6666687 C5.54666667,18.6666687 5.33333333,18.4266687 5.33333333,18.160002 Z M10.4533333,18.160002 L10.4533333,16.1066687 C10.4533333,15.8133353 10.6933333,15.600002 10.96,15.600002 L13.0133333,15.600002 C13.3066667,15.600002 13.52,15.840002 13.52,16.1066687 L13.52,18.160002 C13.52,18.4533353 13.28,18.6666687 13.0133333,18.6666687 L10.96,18.6666687 C10.6933333,18.6666687 10.4533333,18.4266687 10.4533333,18.160002 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
                </g>
            </g>
        </g>
    </g>
</svg>