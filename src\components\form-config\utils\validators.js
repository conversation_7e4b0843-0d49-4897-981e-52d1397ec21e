import func from '@/utils/func';
import { deepClone } from '@/utils/util';
import { AddressPrecisionEnum } from '@/components/form-config/const/commonDic'

export const inputValid = [
  null,
  {
    errorMessage: '手机号码不正确',
    reg: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
  },
  {
    errorMessage: '电话号码不正确',
    reg: /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/,
  },
  {
    errorMessage: '邮政编码不正确',
    reg: /^[0-9]\d{5}$/,
  },
  {
    errorMessage: '身份证不正确',
    reg: /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/,
  },
  {
    errorMessage: '邮箱不正确',
    reg: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  },
  // {
  //   errorMessage: '链接地址不正确',
  //   reg: /^(https?:\/\/)([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
  // }
];
//addressRequired: 详细地址是否必填
export const isValidAddress = (address, addressRequired) => {
  return (
    Array.isArray(address?.value) && address.value.length > 0 &&
    Array.isArray(address?.label) && address.label.length > 0 &&
    (!addressRequired || address?.address)
  );
};

/**
 *  初始化rules字段
 */
export const generateRule = col => {
  const validator = {};
  if (col.required) {
    if(col?.addressPrecision === AddressPrecisionEnum.STREET){
      validator.required = {
        required: true,
        validator: (rule, value, callback) => {
          if (isValidAddress(value, col?.addressRequired)) {
            callback();
          } else {
            callback(new Error(`请填写地址`));
          }
        }
      };
    }else {
      validator.required = { required: true, message: `请填写 ${col.copyLabel}` };
    }
    
  } else {
    validator.required = null;
  }
  if (!func.isEmpty(col.showFormat)) {
    const validElement = inputValid[col.showFormat];
    if (validElement) {
      validator.pattern = { pattern: validElement.reg, message: validElement.errorMessage };
    }
  } else {
    validator.pattern = null;
  }

  const rules = [];
  Object.keys(validator).forEach(key => {
    if (validator[key]) rules.push(validator[key]);
  });
  if (rules.length === 0) delete col.rules;
  else col.rules = rules;
};

export const transRule = col => {
  const dCol = deepClone(col);
  generateRule(dCol);
  return dCol.rules;
};
