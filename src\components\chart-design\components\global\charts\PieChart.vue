<template>
  <div ref="g2Container" />
</template>

<script
  lang="ts"
  setup
>
import { ChartProps, PieStyle } from '../../../types';
import useG2Option from '../../../hooks/useG2Option.ts';
import { labelInit } from '../../../const/g2Charts/label.ts';

const props = defineProps<ChartProps<PieStyle>>();

const { g2Container } = useG2Option(props, (chart) => {
  const dimension = props.dimensions[0]?.id;
  const metrics = props.metrics[0]?.id;

  chart.coordinate({ type: 'theta', outerRadius: 0.7 });
  const mark = chart
    .interval()
    .transform({ type: 'stackY' })
    .encode('color', dimension)
    .encode('y', metrics)
    .tooltip((data) => ({
      name: data[dimension],
      value: data[metrics]
    }));

  labelInit(mark, props.chartType, props.chartStyle, metrics, metrics, dimension);
});
</script>

<style
  lang="scss"
  scoped
>

</style>