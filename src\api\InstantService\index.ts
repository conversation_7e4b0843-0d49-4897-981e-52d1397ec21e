import request from '@/axios';
import { submitCreateOrg, orgListReq,submitJionOrg } from '@/api/interface/work';
import { authType } from '@/api/interface/auth';

//创建组织
export const createOrg = (data: submitCreateOrg) => {
  return request({
    url: '/common/tenant/add',
    method: 'post',
    data,
  });
};

//加入组织//废弃
// export const joinOrg = (data: submitJionOrg) => {
//   return request({
//     url: '/userTenant/apply/add',
//     method: 'post',
//     data,
//   });
// };
//加入组织//new
export const joinOrg = (data: submitJionOrg) => {
  return request({
    url: '/staging/reviewOrganization',
    method: 'post',
    params: { ...data },
  });
};

//组织列表
export const getOrg = (params: orgListReq) => {
  return request({
    url: '/common/tenant/page',
    method: 'get',
    params,
  });
};
// 工作组树
export const getWorkGroupTree = (params: { tid: string }) => {
  return request({
    url: '/workgroup/tree',
    method: 'get',
    params,
  });
};
// 工作组添加
export const workGroupAdd = (data: { groupName: string; parentId?: string }) => {
  return request({
    url: '/workgroup/add',
    method: 'post',
    data,
  });
};
// 工作组重命名
export const workGroupRename = (data: { groupName: string; id: string }) => {
  return request({
    url: '/workgroup/rename',
    method: 'post',
    data,
  });
};

// 工作组成员列表
export const workGroupUserPage = (params: {
  current: number;
  size: number;
  tid: string;
  userName: string;
  workGroupId: string;
}) => {
  return request({
    url: '/workgroup/page/user',
    method: 'get',
    params,
  });
};
// 工作组添加成员
export const workGroupAddUser = (data: { userIds: string[]; workGroupId: string[] }) => {
  return request({
    url: '/workgroup/addUser',
    method: 'post',
    data,
  });
};
// 工作组设置负责人
export const workGroupSetLeaders = (data: { userIds: string[]; id: string }) => {
  return request({
    url: '/workgroup/set/leaders',
    method: 'put',
    data,
  });
};
// 工作组删除
export const workGroupDel = (params: { id: string }) => {
  return request({
    url: '/workgroup/delete',
    method: 'DELETE',
    params,
  });
};
// 工作组移除成员
export const workRemoveMember = (data: { userIds: string[]; workGroupId: string }) => {
  return request({
    url: '/workgroup/remove/members',
    method: 'put',
    data,
  });
};

// 工作组排序
export const workGroupSort = (data: any) => {
  return request({
    url: '/workgroup/sort',
    method: 'put',
    data,
  });
};
// 工作组导出
export const workGroupexport = (data: {workGroupId:string,userIds?:string[]}) => {
  return request({
    url: '/workgroup/export',
    method: 'post',
    data,
    responseType: 'blob',
  });
};
// 邀请外部成员
export const outsider = (data: any) => {
  return request({
    url: '/workgroup/invite/outsider',
    method: 'post',
    data,
  });
};
// 外部成员回显
export const outsiderInfo = (params:{userId:string}) => {
  return request({
    url: '/workgroup/outsider/info',
    method: 'get',
    params,
  });
};
// 外部成员编辑
export const outsiderUpdate = (data:any) => {
  return request({
    url: '/workgroup/outsider/update',
    method: 'post',
    data,
  });
};
// 外部成员编辑
export const workgroupNotMark = (data:string[]) => {
  return request({
    url: '/workgroup/not/mark',
    method: 'put',
    data,
  });
};


// 实名认证
export const authSubmit = (params:authType) => {
  return request({
    url: '/blade-auth/oauth/authentication',
    method: 'post',
    params,
  });
};
// 设置默认组织
export const installOrganization = (data: any) => {
  return request({
    url: '/userTenant/setDefault',
    method: 'post',
    data,
  });
};
// 外部人员转内部人员
export const transferUserUsers = (userId: string) => {
  return request({
    url: '/userTenant/conversion',
    method: 'post',
    params: {
      userId
    }
  });
};

