<template>
  <div class="select_dept">
    <slot :data="emitDept">
      <card-tag
        @click="disabled ? void 0 : openDialog()"
        :disabled
        :closable="!disabled && closable"
        :placeholder 
        v-model="emitDept"
        :default-prop="{id: 'id', label: 'deptName'}"
      />
    </slot>
    <el-dialog
      v-model="visible"
      title="部门(角色)列表"
      :destroy-on-close="true"
      width="50%"
    >
      <div class="select_wrapper">
        <div class="select_top">
          <card-tag
            :disabled
            closable
            :placeholder 
            v-model="modelList"
            :default-prop="{id: 'id', label: 'deptName'}"
          />
        </div>
        <div class="select_bottom">
          <select-tree
            :tree-data="treeData"
            :strictly="true"
            :defaultProps="{id: 'id', label: 'deptName'}"
            v-model="modelList"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button type="primary" @click="visible = false">
            取消
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { DeptProps } from '@/piniaStore/banban/useDeptTreeStore.ts';
import { ElMessage, ElTree } from 'element-plus';
import { taskSourceInjectKey } from '@/components/xt-el-form/constant.ts';
import { useFormItem } from 'element-plus'
import { getDepartmentTree } from '@/api/zy/zycore';
import { useStore } from 'vuex';
import { isEmptyValue } from '@/utils/field';
import { reserve } from '@/piniaStore/banban/util.ts';
import cardTag from '../component/cardTag.vue';
import selectTree from '../component/selectTree.vue';
import { flattenTreeData } from '@/utils/field';
import { useRequest} from '../hooks/useHooks'
import { handleIdInjectKey } from '@/components/xt-el-form/constant.ts'
import {selectUserByDept} from './XTUserDept'

const store = useStore();
const userMsg = store.state.user.userInfo;
const { formItem } = useFormItem()

const props = withDefaults(defineProps<selectUserByDept>(), {
  dataType: 'array',
  multiple: false,
  placeholder: '请选择',
  closable: false,
  openLoad: false,
  disabled: false,
  title: '部门列表',
  isRadio: false,
});
const isTypeArray = computed(() => {
  return props.dataType === 'array';
});
const vModel = defineModel<string[] | string>({ required: false});
const deptTreeRefByDept = ref<InstanceType<typeof ElTree>>();
const taskSource = inject(taskSourceInjectKey, null); //任务组织来源
const emitDept = ref<DeptProps[]>([]);
const visible = ref(false)
const modelList = ref<DeptProps[]>([])
//部门列表
const deptTreeData = ref<DeptProps[]>([]);
const initDeptTreeData = async (tid?: string) => {
  deptTreeData.value= (await getDepartmentTree(tid || userMsg.tid)).data.data || []; 
};
initDeptTreeData(taskSource?.value);

const handleId = inject(handleIdInjectKey, undefined);//应办id
const { getLimitDeptOrGroup, limitIds } = useRequest(handleId as string,props.item)
getLimitDeptOrGroup()

//部门的限制选择
const limitIdsArr = computed(() => {
  return (
    (Array.isArray(props.limitIds) && props.limitIds.length ? props.limitIds : null) ||
    (Array.isArray(limitIds.value) && limitIds.value.length ? limitIds.value : null) ||
    []
  );
});

const treeData = computed(() => {
  const data = deptTreeData.value;
  const flattenedData = limitIdsArr.value?.length ? flattenTreeData(data, limitIdsArr.value):data
  return Array.isArray(flattenedData) ? flattenedData : [];
});
const openDialog = () => {
  visible.value = true
  modelList.value = _.cloneDeep(emitDept.value) 
};
//确认选中
const handleSubmit = () => {
  if(props?.isRadio && modelList.value.length > 1) {
    ElMessage.warning('只能选择一个数据，请重新选择');
    return;
  }
  vModel.value = isTypeArray.value? modelList.value.map(s => s.id): modelList.value.map(s => s.id).join(',')
  visible.value = false
}

const clearValidate = () => {
  emitDept.value.length = 0;
  deptTreeRefByDept.value?.setCheckedKeys([], false);
};

watchEffect(async() => {
  formItem?.validate('change')
  if (!isEmptyValue(treeData.value)) {
    const vModelArray = props.dataType === 'array' ? vModel.value : (vModel.value as string)?.split(',').map((id:string) => id.trim());
    if(!_.isArray(vModelArray)) {
      emitDept.value = []
      return
    }
    emitDept.value = vModelArray.map(id => reserve<DeptProps>(treeData.value, id)).filter(s => s) as DeptProps[]
  }
})

export interface selectUserIns {
  clearValidate: () => void;
  modelList: any;

}
defineExpose<selectUserIns>({
  clearValidate,
  modelList: emitDept,
});
</script>

<style scoped lang="scss">
.select_dept {
  width: 100%;
}
.select_wrapper {
  display: flex;
  flex-direction: column;
  .select_bottom {
    height: 250px;
    display: flex;
    & > div {
      flex: 1;
    }
  }
}
.tree_scrollbar {
  padding: 10px;
}
.user_scrollbar {
  padding: 10px;
  width: 100%;
  border-left: 1px solid #eee;
}
.box-card {
  cursor: pointer;
  width: 100%;
  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
.select_dept_placeholder {
  color: #a8abb2;
  font-size: 14px;
}
.disabled {
  background: #f6f6f6;
}
</style>
