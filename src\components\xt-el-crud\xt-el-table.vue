<template>
    <div class="mb-10">
        <slot name="header" :selection="multipleSelection"></slot>
        <el-table
            border 
            :data="tableData"
            max-height="600" 
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >   
            <el-table-column type="selection" width="55" v-if="!operate"/>
            <el-table-column 
                v-for="item in displayColumns" 
                :key="item.prop"
                :label="item.copyLabel" 
                min-width="150" 
                align="center"
            >
                <template #default="{ row }">
                  <dynamicField 
                    :item="item" 
                    :row 
                    :originColumns="displayColumns" 
                    :formatterRowToText
                    >
                  </dynamicField>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="150" align="center" v-if="operate">
                <template #default="{ row, $index }">
                    <slot name="operation" :row="row" :$index="$index"></slot>
                </template>
            </el-table-column>
        </el-table>

    </div>
</template>

<script setup lang="ts">
import { AvueColumns, AvueForm } from '@/components/form-config/types/AvueTypes'
import { useTableOperations } from '@/components/xt-el-crud/tools/useFormat'
import { useDataList } from '@/components/xt-el-crud/tools/useData.ts'
import dynamicField from '@/components/form-component/XTDynamic/component/dynamicField.vue'

const tableData = defineModel<any[]>('tableData')

const props = withDefaults(
  defineProps<{
    columns: AvueColumns[];
    operate: boolean
  }>(),
  {
    columns: () => [],
    operate: true

  }
);

const { spaceList,articleList,deptTreeData,dutyTreeData,allUserList } = useDataList(props.columns)
const { formatterRowToText } = useTableOperations({spaceList,articleList,deptTreeData,dutyTreeData,allUserList})
const multipleSelection = ref<AvueForm[]>([])
const handleSelectionChange = (val: AvueForm[]) => {
  multipleSelection.value = val
}  

const displayColumns = computed(() => {
  return props.columns.filter(item => item.display)
})

</script>