import { getFormCol } from '@/api/taskManagement/taskDetail.ts';
import { colsToFlatDynamic } from '@/utils/formUtils.ts';
import { handleJsonByArray } from '@/views/mainview/sceneTasks/detail/utils.ts';
import { computedAsync } from '@vueuse/core';
import { getByDataTableId, getByVersionId } from '../api/banban/dataTable.ts';
import {
  getTaskListByVersionId,
  getVersionListByGroupRequest,
} from '../api/taskManagement/common.ts';
import { Field, OriginTypeEnum, Version } from '../types/OriginTypes.ts';

const originTypeHandler: {
  [key in OriginTypeEnum]: {
    getOriginList: (versionId: string) => Promise<DicLike[]>;
    getOriginFieldList: (originId: string, unFlat?: boolean) => Promise<Field[]>;
  };
} = {
  [OriginTypeEnum.TASK]: {
    getOriginList: async versionId => (await getTaskListByVersionId(versionId)).data.data ?? [],
    getOriginFieldList: async (originId, unFlat = true) => {
      const cols = (await getFormCol(originId)).data.data ?? [];
      return colsToFlatDynamic(handleJsonByArray(cols), {
        unFlatDynamic: unFlat,
        unFlatDataSelect: unFlat,
        unFlatDataCollect: unFlat,
        unFlatKnowledgeSelect: unFlat,
        unFlatClubDataCollectType: unFlat,
        unFlatOneByTriggerType: unFlat,
      }).map(
        (item: any) =>
          ({
            ...item,
            fieldProp: item.id,
            fieldLabel: item.copyLabel,
            fieldType: item.fixType,
          } as Field)
      );
    },
  },
  [OriginTypeEnum.DATATABLE]: {
    getOriginList: async versionId => (await getByVersionId(versionId)).data.data ?? [],
    getOriginFieldList: async originId =>
      (await getByDataTableId(originId)).data.data.map((item: any) => ({
        ...item,
        type: item.fieldType,
        prop: item.id,
        copyLabel: item.label,
        fixType: item.fieldType,

        fieldProp: item.id,
        fieldLabel: item.label,
        fieldType: item.fieldType,
      })),
  },
  [OriginTypeEnum.CAL_TABLE]: {
    getOriginList: async () => [],
    getOriginFieldList: async () => [],
  },
};

export const getOriginList = async (versionId?: string, originType?: OriginTypeEnum) => {
  let r: DicLike[] = [];

  if (!versionId || !originType) {
    return r;
  }
  return await originTypeHandler[originType].getOriginList(versionId);
};

type ReturnType = Field[] | (Field & DicLike)[];
export const getOriginFieldList = async (
  originId?: string,
  originType?: OriginTypeEnum,
  filterOutTypes: string[] | ((item: Field) => boolean) = [],
  options?: {
    unFlat?: boolean;
    dicLike?: boolean;
    dicKey?: keyof Field;
    dicLabel?: keyof Field;
  }
): Promise<ReturnType> => {
  let r: ReturnType = [];

  const { unFlat, dicLike = false, dicKey = 'fieldProp', dicLabel = 'fieldLabel' } = options ?? {};

  if (!originId || !originType) {
    return r;
  }
  r = (await originTypeHandler[originType].getOriginFieldList(originId, unFlat)).filter(item => {
    if (typeof filterOutTypes === 'function') {
      return filterOutTypes(item);
    }
    return !_.includes(filterOutTypes, item.fieldType);
  });
  r.forEach(item => {
    item.disabled = false;
    if (dicLike) {
      item.value = item[dicKey];
      item.label = item[dicLabel];
    }
  });
  return r;
};

const useOriginList = () => {
  const versionList = computedAsync<Version[]>(
    async () => (await getVersionListByGroupRequest()).data.data ?? [],
    []
  );

  return {
    versionList,
    getOriginList,
    getOriginFieldList,
  };
};

export default useOriginList;
