server {
    listen 80;
    server_name localhost;
    client_max_body_size 1000m;
    root html;
    index index.html index.htm;

    #路径变量
    set $web_url /usr/share/nginx/html/;

        #前端服务
        location / {
           ## 配置页面不缓存html和htm结尾的文件
           if ($request_filename ~* .*\.(?:htm|html)$) {
              add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
           }

           alias $web_url;
           index index.html;
           try_files $uri $uri/ /index.html;

           expires 30m;
        }
}
