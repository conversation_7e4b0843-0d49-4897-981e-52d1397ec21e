<template>
  <config-title :data="data" />
  <config-permission :data="data" />
  <!-- 日期组件限制配置 -->
  <config-limit-date :data></config-limit-date>
  <el-form-item label="格式">
    <avue-select v-model="data.dateFormatType" :dic="formatTypeDic" :clearable="false" />
  </el-form-item>
  <config-default-type :data="data" v-if="formType!== formTypeEnum.KNOWLEDGE"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import {
  dateFormatType,
  DateFormatTypeEnum,
  dateOption,
} from './const';
import { DateAllField } from './type';
import { CustomDefaultType } from '@/components/form-config/types/AvueTypes';
import { formTypeEnum } from '@/components/form-config/const/commonDic';
import configLimitDate from './components/limitDate.vue';

const formType = inject('formType', null)

const props = defineProps<{
  data: DateAllField;
}>();
const setDateTypes = (dateFormatType: DateFormatTypeEnum) => {
  const dateOp = dateOption[dateFormatType];
  if (dateOp) {
    props.data.type = dateOp.type;
    props.data.format = dateOp.format;
  }
};

const formatTypeDic = computed(() => {
  return dateFormatType
})

watch(
  () => props.data.dateFormatType,
  (v, ov) => {
    setDateTypes(v);
    if (!_.isNil(ov)) {
      delete (props.data as CustomDefaultType).defaultValue;
    }
  },
  {
    immediate: true,
  }
);
</script>
