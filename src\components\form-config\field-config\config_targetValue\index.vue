<template>
  <config-title :data="data" />
  <config-permission :data="data" :show-edit="false" :has-required="false" />
  <TargetValueSelect :data="data" />
  <config-span :data="data" />
</template>
<script setup lang="ts">
import TargetValueSelect from './components/configTargetValueSelect.vue'
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import { TargetValueAllType } from './type';

defineProps<{
  data: TargetValueAllType;
}>();

</script>
<style lang="scss" scoped></style>
