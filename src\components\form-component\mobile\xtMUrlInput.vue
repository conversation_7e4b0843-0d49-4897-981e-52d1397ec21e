<script setup lang="ts">
import { ElMessage } from 'element-plus';

defineOptions({
  name:"xt-m-url-input"
})

const model = defineModel<string>();

const props =  defineProps<{
  disabled: boolean;
}>();
console.log(props,"props");
const handleClick = () => {
  if(!model.value){
    ElMessage.warning('请输入链接地址');
    return
  }
  window.open(model.value, '_blank');
  // if (urlPattern.test(model.value)) {
  //     window.open(model.value, '_blank');
  // }else{
  //     ElMessage.warning('请输入正确的链接地址https://');
  // }
};
</script>

<template>
  <div>
    <van-field v-model="model" v-bind="props" label="" placeholder="请输入文本" >
      <template #right-icon>
        <van-icon name="search" @click="handleClick" />
      </template>
    </van-field>
  </div>
</template>

<style scoped lang="scss">

</style>