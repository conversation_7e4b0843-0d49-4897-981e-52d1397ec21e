<template>
  <div class="tree_container">
    <div class="tree_search">
      <el-input
        class="w-250 h-10"
        clearable
        v-model="query.groupName"
        placeholder="分组名称"
        :prefix-icon="Search"
      >
      </el-input>
    </div>
    <dialog-form
      ref="addPackageRef"
      dialogType="drawer"
      :title="addForm.id ? '编辑' : '添加场景任务集'"
      :rules="{
        groupName: { required: true, message: '请输入场景任务集名称', trigger: 'blur' },
      }"
      v-model="addForm"
    >
      <el-form-item label="场景任务集名称:" prop="groupName">
        <el-input v-model="addForm.groupName" clearable placeholder="请输入关键字" />
      </el-form-item>
      <el-form-item label="上传logo" prop="logo" v-if="isMain">
        <el-upload
          class="w-full"
          v-model="addForm.logo"
          :on-success="handleSubAvatarSuccess"
          :on-remove="handleRemove"
          :limit="1"
          drag
          action="/api/blade-resource/oss/endpoint/put-file"
          accept=".jpeg,.jpg,.png"
          :headers="headers"
        >
          <img v-if="imageUrl" :src="imageUrl" class="w-200px h-200px" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
    </dialog-form>
    <dialog-form
      ref="addPackageChildRef"
      dialogType="drawer"
      :title="isMain ? '新建场景任务集' : '新建分组'"
      :rules="{
        groupName: { required: true, message: '请输入分组名称', trigger: 'blur' },
      }"
      v-model="addChildForm"
    >
      <el-form-item :label="isMain ? '场景任务集:' : '分组名称:'" prop="groupName">
        <el-input v-model="addChildForm.groupName" clearable placeholder="请输入关键字" />
      </el-form-item>
      <el-form-item label="上传logo" prop="logo" v-if="isMain">
        <el-upload
          class="w-full"
          v-model="addChildForm.logo"
          :on-success="handleAvatarSuccess"
          :on-remove="handleRemove"
          :limit="1"
          drag
          action="/api/blade-resource/oss/endpoint/put-file"
          accept=".jpeg,.jpg,.png"
          :headers="headers"
        >
          <img v-if="imageUrl" :src="imageUrl" class="w-200px h-200px" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
    </dialog-form>

    <dialog-form
      ref="listingRef"
      dialogType="drawer"
      title="上架"
      size="50%"
      label-position="top"
      v-model="listingForm"
      :rules="{ typeId: [{ required: true, message: '请选择分类！' }] }"
    >
      <!-- <solution-config :rackingData="rackingData" /> -->
    </dialog-form>
    <el-scrollbar class="tree_scrollbar">
      <el-tree
        ref="treeRef"
        :data="data"
        :props="{
          label: 'groupName',
        }"
        :current-node-key="vModel"
        :default-expanded-keys="expandedKeys"
        node-key="id"
        draggable
        highlight-current
        check-on-click-node
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        @node-drop="handleDrop"
        :allow-drop="handleAllowDrop"
      >
        <template #default="{ data }">
          <template class="flex w-full justify-between mx-10px">
            <div
              class="tree_item"
              :title="data.groupName"
              :style="{ color: data.isEmpty == 0 && data.parentId != 0 ? '#e55959' : 'black' }"
            >
              {{ data.groupName }}
            </div>
            <el-dropdown trigger="hover" class="w-77px">
              <DotsThree style="height: 20px" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="item.handle(data)" v-for="item in mountNode(data)">{{
                    item.title
                  }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </template>
      </el-tree>
    </el-scrollbar>
    <el-dialog v-model="delGroupDialog" :close-on-click-modal="false" title="确认提示" width="500">
      <h3 class="text-center mx-50px">请确认要删除的场景任务集名称</h3>
      <avue-form
        class="px-50px"
        :option="delGroupOption"
        ref="delRef"
        @submit="delGroupConfirm"
        v-model="delGroupForm"
      ></avue-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            :disabled="delGroupForm.GroupName !== currentDelGroup.groupName"
            @click="delRef?.submit()"
            >确认</el-button
          >
          <el-button @click="delGroupDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import DialogForm, {
  DialogFormIns,
} from '@/views/mainview/sceneTasks/components/taskManagement/component/dialogForm.vue';
import {
  addTaskPackage,
  getTaskPackage,
  listingTaskPackage,
  taskDetailSort,
  getSceneTaskGroup,
} from '@/api/taskManagement/taskPackage.ts';
import { fetchShelves } from '@/api/solution/index';
import { getWorkGroupTree } from '@/api/InstantService/index';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import type { UploadProps } from 'element-plus';
import { ElLoading } from 'element-plus';
import usePackageTreeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTreeStore.ts';
import {
  taskPackageRemove,
  taskPackageAdd,
  taskPackageUpdate,
} from '@/api/taskManagement/taskPackage';
import { riseListByVersion } from '@/api/taskManagement/rise.ts';
import DotsThree from '@/images/taskManageImages/dotsThree.svg';
import { storeToRefs } from 'pinia';
import { ref, nextTick } from 'vue';
import { useStore } from 'vuex';
import useAuthHeader from '@/hooks/useAuthHeader';
import usePackageTypeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTypeStore.ts';
import solutionConfig from '@/components/solution-config/index.vue';
import { useSolutionStore } from '@/components/solution-config/store/index';
import { PackageProp, taskPackageTreeProps, rackGroupType } from '../types';

const props = withDefaults(defineProps<taskPackageTreeProps>(), {
  addBtn: true,
  showMenu: true,
  defOne: false,
  other: false,
});

const treeRef = ref();
const vModel = defineModel<string>();
const addPackageRef = ref<DialogFormIns>();
const listingRef = ref<DialogFormIns>();
const listingTreeRef = ref();
const rackingData = ref<rackGroupType[]>([]);
const listingForm = ref<
  Partial<{
    name: string;
    detail: string;
    typeId: number;
  }>
>({});

const riseList = ref([]);

const delRef = ref();
const delGroupDialog = ref(false);
const delGroupForm = ref<any>({});
const delGroupOption = ref({
  span: 24,
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 0,
  column: [
    {
      label: '',
      prop: 'GroupName',
    },
  ],
});
const currentDelGroup = ref<any>({});

const workGroupTreeData = ref([]);
const { activeTaskPackageId, versionId, expandedKeys, bindWorksByPackage, bindWorksByParentId } =
  storeToRefs(usePackageTreeStore());
const { getTypeList } = usePackageTypeStore();
getTypeList();

const {shelvesPackage,shelvesProductsDTOList} = storeToRefs(useSolutionStore())

const store = useStore();
const currentTid = computed(() => {
  const userMsg = store.getters.userInfo;
  return userMsg.tid;
});
const query = ref<Partial<PackageProp>>({
  groupName: '',
});
const addForm = ref<Partial<PackageProp>>({
  tid: currentTid.value,
});

interface ITaskPackChild {
  groupName: string;
  tid: string;
  parentId: string;
  riseId?: string;
  logo?: string;
}
const addPackageChildRef = ref<DialogFormIns>();
const addChildForm = ref<ITaskPackChild>({
  groupName: '',
  tid: '',
  parentId: '',
  riseId: '',
});
// 当前点击添加的是版本还是分组
const isMain = ref();
const data = ref<PackageProp[]>([]);
const menuData = [
  {
    title: '添加场景任务集',
    handle: (params: any) => {
      isMain.value = true;
      const { id } = params;
      const ancestorsSplitId = params.ancestors.split(',');
      addChildForm.value = { parentId: id, groupName: '', tid: currentTid.value };
      addForm.value = _.cloneDeep(params);
      initRiseList(ancestorsSplitId[1]);
      addPackageChildRef.value?.open<ITaskPackChild>(form => {
        taskPackageAdd(form).then(res => {
          res.data.success &&
            addPackageChildRef.value?.close(async () => {
              if (res.data.success) {
                ElMessage.success(res.data.msg);
                await initTreeData();
                handleNodeClick(res.data.data);
              }
            });
        });
      });
    },
  },
  {
    title: '编辑',
    handle: (data: any) => {
      isMain.value = false;
      addForm.value = _.cloneDeep(data);
      addPackageRef.value?.open<PackageProp>(form => {
        const requestApi = form.id ? taskPackageUpdate : addTaskPackage;
        requestApi(form).then(res => {
          res.data.success &&
            addPackageRef.value?.close(() => {
              if (res.data.success) {
                ElMessage.success(res.data.msg);
                initTreeData();
              }
            });
        });
      });
      console.log('编辑', data);
    },
  },
  {
    title: '上架',
    handle: async (data: any) => {
      listingForm.value.name = data.groupName;
      const res = await getSceneTaskGroup(data.id);
      rackingData.value = res.data.data[0].extendList;
      listingRef.value?.open((form: { name: string; detail: string; typeId: string }) => {
        console.log(shelvesPackage,shelvesProductsDTOList.value,'======');
        fetchShelves({shelvesPackage:shelvesPackage.value,shelvesProductsDTOList:shelvesProductsDTOList.value}).then(res => {
          console.log(res,'res');
          
        })
        // const checkedNodes = listingTreeRef.value.getCheckedNodes();
        // let childIds = checkedNodes
        //   .filter((item: rackGroupType) => !item.sceneGroupFlag)
        //   .map((item: rackGroupType) => item.id)
        //   .join();
        // if (!childIds) {
        //   ElMessage.warning('请选择要上架的场景任务集');
        //   return;
        // }
        // if (!form.typeId) {
        //   ElMessage.warning('请选择分类！');
        //   return;
        // }
        // const loading = ElLoading.service({
        //   lock: true,
        //   text: '请求中',
        // });
        // listingTaskPackage({ ...form, id: data.id, tasks: childIds })
        //   .then(res => {
        //     if (res.data.success) {
        //       listingRef.value?.close(() => {
        //         ElMessage.success(res.data.msg);
        //       });
        //     }
        //   })
        //   .finally(() => loading.close());
      });
    },
  },
  {
    title: '删除',
    handle: (data: any) => {
      delGroupDialog.value = true;
      currentDelGroup.value = _.cloneDeep(data);
    },
  },
];
const subMenu = [
  {
    title: '添加分组',
    handle: (data: any) => {
      isMain.value = false;
      const { id } = data;
      const ancestorsSplitId = data.ancestors.split(',');
      addChildForm.value = { parentId: id, groupName: '', tid: currentTid.value };
      initRiseList(ancestorsSplitId[1]);
      addForm.value = _.cloneDeep(data);
      addPackageChildRef.value?.open<ITaskPackChild>(form => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中',
        });
        taskPackageAdd(form)
          .then(res => {
            res.data.success &&
              addPackageChildRef.value?.close(async () => {
                if (res.data.success) {
                  ElMessage.success(res.data.msg);
                  await initTreeData();
                  handleNodeClick(res.data.data);
                }
              });
          })
          .finally(() => loading.close());
      });
    },
  },
  {
    title: '编辑',
    handle: (data: any) => {
      const ancestors = data.ancestors.split(',');
      if (ancestors.length === 2) {
        isMain.value = true;
      } else {
        isMain.value = false;
      }
      imageUrl.value = data.logo || '';
      addForm.value = _.cloneDeep(data);
      addForm.value.logo = data.logo || '';
      addPackageRef.value?.open<PackageProp>(form => {
        const requestApi = form.id ? taskPackageUpdate : addTaskPackage;
        requestApi(form).then(res => {
          res.data.success &&
            addPackageRef.value?.close(() => {
              if (res.data.success) {
                ElMessage.success(res.data.msg);
                initTreeData();
              }
            });
        });
      });
      console.log('编辑', data);
    },
  },
  {
    title: '删除',
    handle: (data: any) => {
      delGroupDialog.value = true;
      currentDelGroup.value = _.cloneDeep(data);
    },
  },
];

const delGroupClose = () => {
  delGroupForm.value.GroupName = '';
};
const delGroupConfirm = (_: any, done: () => void) => {
  taskPackageRemove({ id: currentDelGroup.value.id, tid: currentTid.value })
    .then(res => {
      if (res.data.success) {
        if (activeTaskPackageId.value === currentDelGroup.value.id) {
          activeTaskPackageId.value = '';
        }
        ElMessage.success(res.data.msg);
        initTreeData();
      }
    })
    .finally(() => {
      delGroupDialog.value = false;
      delGroupClose();
      done();
    });
};

watch(
  () => query.value.groupName,
  val => {
    treeRef.value.filter(val);
  }
);

const initRiseList = (id: string) => {
  riseListByVersion({ versionId: id }).then(res => {
    riseList.value = res.data.data;
  });
};
const mountNode = (data: PackageProp) => {
  if (data.parentId == '0') {
    if (data.defaultVersionFlag) {
      return menuData;
    } else {
      return menuData.slice(0, menuData.length - 1);
    }
  } else {
    return subMenu;
  }
};
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.groupName.includes(value);
};

//文件上传
const { headers } = useAuthHeader();
const imageUrl = ref('');
const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  imageUrl.value = URL.createObjectURL(uploadFile.raw!);
  addChildForm.value.logo = response.data.link;
};
const handleSubAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  imageUrl.value = URL.createObjectURL(uploadFile.raw!);
  addForm.value.logo = response.data.link;
};
const handleRemove = () => {
  imageUrl.value = '';
  addChildForm.value.logo = '';
};

const handleNodeClick = (data: PackageProp) => {
  if (data.parentId === 0) {
    vModel.value = '';
    versionId.value = '';
    bindWorksByPackage.value = [];
    return;
  }
  const ancestorsSplitId = data.ancestors.split(',');
  if (ancestorsSplitId.length >= 2) {
    versionId.value = ancestorsSplitId[1];
  }
  vModel.value = data.id;
  expandedKeys.value = [data.id, String(data.parentId)];
  bindWorksByParentId.value = data.ancestors === '0' ? data.id : ancestorsSplitId[1];
  bindWorksByPackage.value = data.relWorkGroupIds ? data.relWorkGroupIds.split(',') : [];
  activeTaskPackageId.value = data.id;
};
const handleAllowDrop = (draggingNode: any, dropNode: any, type: string) => {
  // 如果拖拽的父id为0 只能拖拽到父id也为0的层级
  if (draggingNode.data.parentId === 0 && dropNode.data.parentId !== 0) return false;
  // 如果拖拽的父id不为0(相当于子级拖拽) 不能拖到最外层
  if (draggingNode.data.parentId !== 0 && dropNode.data.parentId === 0) return false;

  if (dropNode.data.parentId === 0) {
    return type !== 'inner';
  } else {
    return true;
  }
};
const handleDrop = () => {
  taskDetailSort(data.value).then(() => {});
};
const initTreeData = async () => {
  if (!currentTid.value) {
    ElMessage.warning('请先选择组织');
    vModel.value = '';
    return;
  }
  const res = await getTaskPackage({ query: query.value, tid: currentTid.value });
  data.value = res.data.data;
  nextTick(() => {
    vModel.value = activeTaskPackageId.value;
    if (data.value[0] && props.defOne) {
      vModel.value = data.value[0].id;
    } else {
      vModel.value = activeTaskPackageId.value;
    }
  });
};
watch(
  () => currentTid.value,
  () => {
    initTreeData();
  },
  {
    deep: true,
  }
);
const initWorkTreeData = () => {
  getWorkGroupTree({ tid: currentTid.value }).then(res => {
    workGroupTreeData.value = res.data.data.map((item: any) => {
      if (item.parentId === '0') {
        return { ...item, disabled: true };
      } else {
        return { ...item };
      }
    });
  });
};
initWorkTreeData();
initTreeData();
</script>
<style scoped lang="scss">
.tree_container {
  background: #ffffff;
  font-family: PingFangSC-Medium, PingFang SC;

  .tree_search {
    padding: 40px 10px;
    display: flex;
    align-items: center;
  }

  .tree_add {
    display: flex;
    align-items: center;
    justify-content: end;
    padding: 0 15px 15px;
    font-size: 16px;
    color: #606060;
  }

  .pointer {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

.tree_scrollbar {
  height: 600px;
  overflow: scroll;
  :deep(.el-tree-node__content) {
    height: 40px;
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background: #eff3fd;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      display: block;
      height: 100%;
      width: 5px;
      background: var(--el-color-primary);
    }
  }

  .tree_item {
    flex: 1;
    overflow: auto;
  }
  .tree_item::-webkit-scrollbar {
    width: 5px;
    height: 6px;
    background-color: transparent;
  }
  .tree_item::-webkit-scrollbar-thumb {
    background: rgb(193, 222, 230);
    border-radius: 2px;
  }
}

.menu_wrapper {
  background: #ffffff;
  box-shadow: 3px 3px 6px #e0e0e0;
  padding: 10px 0;
  list-style: none;
  margin: 0;
  border-radius: 5px;

  .menu_item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #333333;
    transition: color, background 0.25s;

    &:hover {
      background: #f5f5f5;
      color: var(--el-color-primary);
    }
  }
}

.flex1 {
  flex: 1;
}

.idea_form {
  display: flex;
  flex-direction: column;

  .idea_add {
    margin-bottom: 10px;
    color: skyblue;
    display: inline;

    span {
      cursor: pointer;
    }
  }

  .idea_del {
    margin: 0 5px;
    cursor: pointer;
  }
}

.idea_join {
  margin: 0 5px;
}

.idea_list {
  display: flex;
  list-style: none;
  align-items: center;
  margin-bottom: 10px;
}
</style>
