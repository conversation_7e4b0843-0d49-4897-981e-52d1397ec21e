import { XtFieldType } from '@/components/form-config/types/FieldTypes.ts';
import { ClientComponentController } from '@/components/form-component/option/field.ts';
import { ClientTypes } from '@/components/form-component/option/types.ts';

//根据客户端类型获取对应表单组件
export const getClientComponent=(type: XtFieldType,client:ClientTypes.PC)=>{
  const clientSymbols=ClientComponentController.find(item=>_.includes(item.fixType,type))?.clientSymbols
  return clientSymbols?.find(item => _.includes(item.client, client))?.component
}