import request from '@/axios';
import { ReqPage, banbanDetailsType, allDoLinkageType } from '../interface/banban';
// 创建组织
export const getStageList = (params: ReqPage, data: any) => {
  return request({
    url: '/staging/page',
    method: 'post',
    params,
    data,
  });
};
// 调
export const stageAdjustApi = (data: any) => {
  return request({
    url: '/staging/adjust',
    method: 'post',
    data,
  });
};
// 转
export const stageChangeApi = (data: any) => {
  return request({
    url: '/staging/change',
    method: 'post',
    data,
  });
};
// 转办抄送数据
export const staginCCdata = (id: string) => {
  return request({
    url: '/staging/CCdata',
    method: 'get',
    params: { id },
  });
};
// 转办是否限制
export const staginIsTransfer = (id: string) => {
  return request({
    url: '/staging/isTransfer',
    method: 'get',
    params: { id },
  });
};
// 转办抄送数据
export const saveTransferMessage = (data: any) => {
  return request({
    url: '/transferKnowledge/saveTransferMessage',
    method: 'post',
    data,
  });
};

// 下
export const stageOffLineApi = (params: { id: string }) => {
  return request({
    url: '/staging/offLine',
    method: 'post',
    params,
  });
};
// 停
export const stageStopApi = (params: { id: string }) => {
  return request({
    url: '/staging/stop',
    method: 'post',
    params,
  });
};
// 撤
export const stageRevokeApi = (params: { id: string }) => {
  return request({
    url: '/staging/revoke',
    method: 'post',
    params,
  });
};
// 邀
export const stageInviteApi = (data: any) => {
  return request({
    url: '/staging/invite',
    method: 'post',
    data,
  });
};

//应办详情
export const getHandleDetails = (params: { handleId: string }) => {
  return request({
    url: '/handledData/detail',
    method: 'get',
    params,
  });
};
// 应办 叹号详情
export const detailTaskApi = (params: { id: string }) => {
  return request({
    url: '/staging/detail',
    method: 'get',
    params,
  });
};
// 转-同意
export const turnAgree = (params: { id: string }) => {
  return request({
    url: '/staging/changeAgree',
    method: 'post',
    params,
  });
};
// 转-不同意
export const turnDisagree = (params: { id: string }) => {
  return request({
    url: '/staging/changeDisagree',
    method: 'post',
    params,
  });
};
// 分页-数量统计
export const pageCountApi = (data: any) => {
  return request({
    url: '/staging/pageCount',
    method: 'post',
    data,
  });
};
// 用户组织列表-带版本名称和版本任务数量
export const taskNumberByUserId = (params: { userId: string }) => {
  return request({
    url: '/userTenant/userList',
    method: 'get',
    params,
  });
};

//应办暂存
export const temporaryForm = (data: any) => {
  return request({
    url: '/handledData/stagingSubmit',
    method: 'post',
    data,
  });
};

//应办保存
export const handleSubmit = (data: any, params: any) => {
  return request({
    url: '/handledData/Submit',
    method: 'post',
    data,
    params,
  });
};
//预设应办保存(审批成员加入)
export const auditHandleSubmit = (data: any) => {
  return request({
    url: '/handledData/handlePresetHandled',
    method: 'post',
    data,
  });
};
//应办条件校验
export const handleRuleCheck = (data: any, params: { taskId: string }) => {
  return request({
    url: '/task/rule/check',
    method: 'post',
    data,
    params,
  });
};

// 表格-批量删除
export const delDynamic = (params: { ids: string }) => {
  return request({
    url: '/handledData/formRemove',
    method: 'post',
    params,
  });
};

// 获取应办详情表格列表
export const banbanDetailsTablePage = (
  data: { handledId: string; parentId: string },
  params?: { current: number; size: number }
) => {
  return request({
    url: '/handledData/formPage',
    method: 'post',
    data,
    params,
  });
};

// 应办表格批量编辑
export const formEdit = (params: { parentId: string }, data: any) => {
  return request({
    url: '/handledData/formEdit',
    method: 'post',
    params,
    data,
  });
};

// 应办表格批量编辑
export const delAllDynamic = (params: { id: string }) => {
  return request({
    url: '/handledData/removeFormById',
    method: 'post',
    params,
  });
};

// 导出模板
export const exportTemp = (data: banbanDetailsType[]) => {
  return request({
    url: '/handledData/export-template',
    method: 'post',
    data,
    responseType: 'blob',
  });
};

// 导入应办表格
export const importBanbanTable = (data: any, params: any) => {
  return request({
    url: '/handledData/import-data',
    method: 'post',
    data,
    params,
  });
};

//获取应办详情
export const getHandleDetailsInfo = (params: { handleId: string }) => {
  return request({
    url: '/handledData/logicDetail',
    method: 'get',
    params,
  });
};

//应办查看文件pdf预览
export const changeFilePreviewType = (data: { name: string; size: number; url: string }) => {
  return request({
    url: '/webFile/convertToPdf',
    method: 'post',
    data,
  });
};
//应办保存文件
export const saveFileByRule = (params: { submitType: string; handledId: string }) => {
  return request({
    url: '/ruleExpress/saveFile',
    method: 'get',
    params,
  });
};

/**
 * 通过分组获取分组下一级分组
 * @param params 接口参数
 */
export const getNextLevelGroupByGroupId = (params: { groupId: number }) => {
  return request({
    url: '/scene_task_group/getNextLevelGroupByGroupId',
    method: 'get',
    params,
  });
};

// 根据分组id查即使办事 不分页
export const instantFolder = (data: { packageId: number }) => {
  return request({
    url: '/staging/folder',
    method: 'post',
    data,
  });
};
// 即时办事点击率
export const instantClicks = (params: { id: string }) => {
  return request({
    url: '/staging/clicks',
    method: 'get',
    params,
  });
};

//创建即时办事

export const getInstantHandleId = (params: { id: string }) => {
  return request({
    url: '/staging/instant',
    method: 'get',
    params,
  });
};
// 请求任务抬头信息
export const getStagingRise = (params: { id: string }) => {
  return request({
    url: '/staging/rise',
    method: 'get',
    params,
  });
};

export const getHandleDetai = (params: { id: string }) => {
  return request({
    url: '/staging/getOne',
    method: 'get',
    params,
  });
};
export const getHandleDetaiId = (params: { id: string }) => {
  return request({
    url: '/staging/getById',
    method: 'get',
    params,
  });
};

//
export const tableFieldSave = (params: { handleId: string; isTrigger: boolean }) => {
  return request({
    url: '/tableFieldValue/save',
    method: 'post',
    params,
  });
};

//应办提交后获取文件
export const getHandleFiles = (params: { handledId: string; submitType: string, taskId?: string }) => {
  return request({
    url: '/ruleExpress/showFile',
    method: 'get',
    params,
  });
};

//历史应办查看文件

export const getHisToryHandleFiles = (params: { handledId: string; submitType: string }) => {
  return request({
    url: '/handledReplaceFile/getFileList',
    method: 'get',
    params,
  });
};

//应办提交后获取文件
export const getFilesAfter = (params: { handledId: string; submitType: string }) => {
  return request({
    url: '/ruleExpress/showFile',
    method: 'get',
    params,
  });
};
//当日待办-信息审核-微应用 三合一
export const getCurrentDayThreeInOne = (params: {
  tenantId: string;
  current: number;
  size: number;
  executorId: string;
  taskSource: string;
}) => {
  return request({
    url: '/current_day_common/wait/process/threeInOne',
    method: 'get',
    params,
  });
};
//已办分页
export const getHandleAllDone = (params: any) => {
  return request({
    url: '/done/queryDone',
    method: 'GET',
    params,
  });
};
// //已办微应用
// export const getAppQueryDone = (params: ReqPage) => {
//   return request({
//     url: '/done/queryDone',
//     method: 'GET',
//     params,
//   });
// };

//整体联动
export const allDoLinkage = (data: allDoLinkageType) => {
  return request({
    url: '/linkage/doLinkages',
    method: 'post',
    data,
  });
};

//清除联动缓存
export const clearCache = (handledId: string) => {
  return request({
    url: '/linkage/clearCache',
    method: 'post',
    data: { handledId },
  });
};

//根据手机号查询用户id
export const getUserId = (data: any) => {
  return request({
    url: '/handledData/getUserIdByPhone',
    method: 'post',
    data,
  });
};

//获取当前任务的所有值与值的公式
export const getFormulaList = (taskId: string) => {
  return request({
    url: '/linkage/linkageFormulaMap',
    method: 'post',
    data: { taskId },
  });
};
