<template>
  <div class="flex justify-between">
    <div
      v-for="item in dic"
      :key="item[fieldNames.value]"
      :class="{ 'border-primary-base!': item[fieldNames.value] === model }"
      :style="{ width: size + 'px', height: size + 'px'}"
      class="border-(1px dashed transparent) bg-primary-light hover:border-primary-base cursor-pointer box-border flex-center rounded-0.5"
      @click="handleClick(item)"
    >
      <a-popover v-if="!!item[fieldNames.remark??'remark']?.length">
        <template
          #content
        >
          <div v-for="i in item[fieldNames.remark??'remark']">{{ i }}</div>
        </template>
        <img
          :height="item[fieldNames.size??'size']??(size-5)"
          :src="item[fieldNames.label]"
          :width="item[fieldNames.size??'size']??(size-5)"
          alt=""
        />
      </a-popover>
      <img
        v-else
        :height="item[fieldNames.size??'size']??(size-5)"
        :src="item[fieldNames.label]"
        :width="item[fieldNames.size??'size']??(size-5)"
        alt=""
      />
    </div>
  </div>
</template>

<script
  lang="ts"
  setup
>
import useChartDesignStore from '../store/useChartDesignStore.ts';

const props = withDefaults(defineProps<{
  dic: any[],
  fieldNames?: {
    label: string,
    value: string,
    size?: string,
    remark?: string
  },
  size?: number
}>(), {
  fieldNames: () => ({
    label: 'label',
    value: 'value'
  }),
  size: 40
});

const model = defineModel<any>();
const { resetList } = useChartDesignStore();

const handleClick = (item: any) => {
  resetList();
  model.value = item[props.fieldNames.value];
};
</script>

<style
  lang="scss"
  scoped
>

</style>