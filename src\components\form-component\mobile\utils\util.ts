// 字符串转数组
export const stringToArray=(res:any)=>{
  if (!_.isArray(res)){
    if (_.isString(res)){
      return res.split(",")
    }
    return []
  }
  return res
}// 数组转字符串
export const arrayToString=(res:any)=> {
  if (!_.isString(res)) {
    if (_.isArray(res)) {
      return _.join(res, ",")
    }
    return ''
  }
  return res
}

export const toArray=(res:any)=> {
  if (!_.isArray(res)) {
    return []
  }
  return res
}