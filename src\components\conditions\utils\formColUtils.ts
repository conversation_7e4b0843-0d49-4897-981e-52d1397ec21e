import { AvueColumns, DataFieldBase, DynamicAllField } from '@/components/form-config/types/AvueTypes.ts';
import { GetFieldListParams } from '@/components/conditions/types/types.ts';
import {
 dynamicLikeField,
  dynamicType,
} from '@/components/form-config/types/FieldTypes.ts';

export const DEEP_DELIMITER = ';';

const flatDynamic = (
  dynamic: DynamicAllField,
  filterType: string[]
): AvueColumns | AvueColumns[] => {
  if (_.isEmpty(dynamic.children)) {
    return [];
  }
  let clone = _.cloneDeep(dynamic.children || []);
  if (!_.isEmpty(filterType)) {
    clone = clone.filter(item => _.includes(filterType, item.fixType || item.fixType));
  }
  return clone.map(item => {
    item.copyLabel = `${dynamic.copyLabel}.${item.copyLabel}`;
    item.prop = `${dynamic.prop}${DEEP_DELIMITER}${item.prop}`;
    return item;
  });
};

const flatData = (col: AvueColumns, filterType: string[]): AvueColumns | AvueColumns[] => {
  // console.log(col, filterType, 'filterType');
  const dataCol = col as DataFieldBase;

  if (_.isEmpty(dataCol?.showFields)) {
    return [];
  }
  let clone = _.cloneDeep(dataCol.showFields || []);
  if (!_.isEmpty(filterType)) {
    clone = clone.filter(item => _.includes(filterType, item.fixType));
  }

  return clone
    .filter(item => !_.includes(dynamicLikeField, item.fixType))
    .map(item => {
      item.copyLabel = `${col.copyLabel}.${item.copyLabel}`;
      item.prop = `${col.prop}${DEEP_DELIMITER}${item.prop}`;
      return item;
    });
};

const flatType: {
  types: readonly string[];
  key: keyof GetFieldListParams;
  flat: (col: AvueColumns, filterType: string[]) => AvueColumns | AvueColumns[];
}[] = [
  {
    // 子表单表格
    types: dynamicType,
    key: 'unFlatDynamic',
    flat(col, filterType) {
      return flatDynamic(col as DynamicAllField, filterType);
    },
  },
  // {
  //   // 数据选择
  //   types: dataSelectType,
  //   key: 'unFlatDataSelect',
  //   flat: flatData,
  // },
  // {
  //   // 数据汇总
  //   types: dataCollectType,
  //   key: 'unFlatDataCollect',
  //   flat: flatData,
  // },
  // {
  //   // 知识库
  //   types: knowledgeSelectType,
  //   key: 'unFlatKnowledgeSelect',
  //   flat: flatData,
  // },
  // {
  //   // 数据汇总
  //   types: clubDataCollectType,
  //   key: 'unFlatClubDataCollectType',
  //   flat: flatData,
  // },
  // {
  //   // 逐一触发
  //   types: oneByTriggerType,
  //   key: 'unFlatOneByTriggerType',
  //   flat: flatData,
  // },
];

/**
 * 转换扁平化子表单
 */
export const colsToFlatDynamic = (
  cols: AvueColumns[],
  param: GetFieldListParams,
  isOriginData: boolean = false // 保留子表单/表格自身
): AvueColumns[] => {
  return _.flatten(
    _.cloneDeep(cols).map(item => {
      const fT = flatType.find(i => _.includes(i.types, item.fixType));
      if (fT && (param[fT.key] ?? false)) {
        if (!isOriginData) {
          return fT.flat(item, param.flatFilterType || []);
        } else {
          let flatRes = fT.flat(item, param.flatFilterType || []) as  AvueColumns[] || []
          return [...flatRes, item];
        }
      }
      return item;
    })
  );
};