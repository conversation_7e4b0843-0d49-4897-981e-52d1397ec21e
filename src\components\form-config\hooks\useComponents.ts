import {
  mulCascaderType,
  inputType,
  interspaceComType,
  inventoryComType,
  dateRangeType,
  selectType,
  selectPlaceholder,
  signatureType,
  userDeptType,
  usersType,
  userTagType,
  numberType,
} from '../types/FieldTypes.ts';
import { FieldBaseField } from '../types/AvueTypes.ts';
import { isMobile } from '@/utils/client';
// import { InputFormatTypeEnum } from '@/components/form-config/const/commonDic.ts';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';

const useComponents = () => {
  const getComponent = (
    type: FixTypeEnum,
  ) => {
    if (isMobile()) {
      if (_.includes(usersType, type)) {
        return 'xt-m-select-user';
      } else if (_.includes(userDeptType, type)) {
        return 'xt-m-select-dept';
      } else if (_.includes(userTagType, type)) {
        return 'xt-m-select-work-group';
      } else if (_.includes(numberType, type)) {
        return 'M-XT-Number';
      }
      // else if (_.includes(checkboxType, type)) {
      //   return 'xt-m-select-checkbox';
      // }
      else if (_.includes(inputType, type)) {
        // if (showFormat === InputFormatTypeEnum.URL) return 'xt-url-input';
        return 'M-XT-Input';
      } else if (_.includes(selectType, type)) {
        return 'M-XT-Select';
      } else if (_.includes(mulCascaderType, type)) {
        return 'xt-m-cascader';
      } else if (_.includes(interspaceComType, type)) {
        return 'xt-m-space';
      } else if (_.includes(inventoryComType, type)) {
        return 'xt-m-inventory';
      } else if (_.includes(signatureType, type)) {
        return 'xt-m-sign';
      } else if (_.includes(dateRangeType, type)) {
        return 'xt-m-daterange';
      }
      // else if ('radio' === type) {
      //   return 'singleRadio';
      // }
    }

    const fixType = capitalizeFirstLetter(type); //首字母变成大写
    // console.log(`XT${fixType}`,'4444');

    return `XT${fixType}`;
  };

  const getPlaceholder = ({ copyLabel, fixType }: FieldBaseField, label?: string) => {
    const pLabel = label || copyLabel;
    // debugger
    if (_.includes(selectPlaceholder, fixType)) return `请选择 ${pLabel}`;
    else return `请输入 ${pLabel}`;
  };

  function capitalizeFirstLetter(str: string) {
    return str.replace(/^\w/, c => c.toUpperCase());
  }

  return {
    getComponent,
    getPlaceholder,
  };
};

export default useComponents;
