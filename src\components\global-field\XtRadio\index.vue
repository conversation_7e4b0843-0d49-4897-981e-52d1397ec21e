<template>
  <n-radio-group v-model:value="vModel">
    <n-space :vertical="vertical">
      <n-radio v-for="item in dic" :key="item[props.value]" :value="item[props.value]">
        {{ item[props.label] }}
      </n-radio>
    </n-space>
  </n-radio-group>
</template>

<script setup>
import useOutVModel from '@/hooks/useOutVModel.js';

const prop = defineProps({
  dic: {},
  props: {
    default: () => {
      return {
        label: 'label',
        value: 'value',
      };
    },
  },
  vertical: {
    type: Boolean,
    default: () => false,
  },
});

const { vModel } = useOutVModel();
</script>

<style scoped lang="scss"></style>
