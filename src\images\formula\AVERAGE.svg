<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>average</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-230.000000, -670.000000)">
            <g id="average" transform="translate(230.000000, 670.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组" transform="translate(10.000000, 42.000000)">
                    <text id="AVERAGE函数可以获取一组数值的算数" font-family="PingFangSC-Regular, PingFang SC"
                          font-size="14" font-weight="normal" line-spacing="23">
                        <tspan x="0" y="15" fill="#3F70FF">AVERAGE</tspan>
                        <tspan x="65.114" y="15" fill="#3A3A3A">函数可以获取一组数值的算数平均值。</tspan>
                        <tspan x="0" y="39" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="39" fill="#3F70FF">AVERAGE</tspan>
                        <tspan x="114.114" y="39" fill="#3A3A3A">(数字1,数字2,...)</tspan>
                        <tspan x="0" y="63" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="63" fill="#3F70FF">AVERAGE</tspan>
                        <tspan x="114.114" y="63" fill="#3A3A3A">(</tspan>
                    </text>
                    <g id="编组-30" transform="translate(123.000000, 50.000000)">
                        <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="52" height="16"></rect>
                        <text id="语文成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                              font-weight="normal" line-spacing="16" fill="#3F70FF">
                            <tspan x="6" y="11">语文成绩</tspan>
                        </text>
                    </g>
                    <g id="编组-30" transform="translate(7.000000, 48.000000)">
                        <rect id="矩形" fill="#EAF3FF" x="175" y="2" width="52" height="16"></rect>
                        <text id="）" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                              fill="#3A3A3A">
                            <tspan x="229" y="15">）</tspan>
                        </text>
                        <text id="返回两门" font-family="PingFangSC-Regular, PingFang SC" font-size="14"
                              font-weight="normal" fill="#3A3A3A">
                            <tspan x="240" y="15">返回两门</tspan>
                        </text>
                        <text id="课程的平均分。" font-family="PingFangSC-Regular, PingFang SC" font-size="14"
                              font-weight="normal" fill="#3A3A3A">
                            <tspan x="0" y="38">课程的平均分。</tspan>
                        </text>
                        <text id="数学成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                              font-weight="normal" line-spacing="16" fill="#3F70FF">
                            <tspan x="181" y="13">数学成绩</tspan>
                        </text>
                    </g>
                </g>
                <text id="，" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="185" y="107">，</tspan>
                </text>
                <text id="AVERAGE" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">AVERAGE</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
