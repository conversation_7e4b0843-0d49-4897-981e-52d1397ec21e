<template>
  <el-main 
    class="widget !p-20px" 
    :style="{ background:data.column && data.column.length == 0 ? `url(${widgetEmptyImg}) no-repeat 50%` : '',}" 
    style="background-size: 293px"
    >
    <el-form 
      label-position="top" 
      label-suffix="：" 
      :model="form" 
      ref="widgetForm"
    >
      <el-row :gutter="0">
        <draggable 
          class="widget-list el-row" 
          :list="data.column" 
          :group="{ name: 'form' }" 
          ghost-class="ghost"
          :animation="300" 
          item-key="prop" 
          @add="handleWidgetAdd" 
          @end="$emit('change')"
          :disabled="configDisabled"
        >
          <template #item="{ element, index }">
            <el-col 
              :span="element.span || 24" 
              v-if="['dynamic'].includes(element.fixType)"
              @click="handleSelectWidget(index)"
            >
              <widget-dynamic 
                :class="{ active: selectWidget.prop == element.prop }"
                @click.native="handleSelectWidget(index)" 
                :data="data" 
                :column="element" 
                :dels="dels" 
                :index="index"
                v-model:select="selectWidget" 
                @change="$emit('change')" 
                :source="1" 
                :form-type="1"
              >
              </widget-dynamic>
            </el-col>
            <el-col 
              v-else 
              :md="element.span || 12" 
              :xs="24" 
              :offset="element.offset || 0"
            >
              <el-form-item 
                class="widget-item hover drag" 
                :label="element.fixType !== 'divider' && element.showLabel ? element.copyLabel : ''" 
                :prop="element.prop" 
                :class="[{ active: selectWidget.prop == element.prop, required: element.required },'avue-form__item--' + element.labelPosition || '',]" 
                :label-width="element.labelWidth" 
                @click="handleSelectWidget(index)"
              >
                <widget-item 
                  :item="element" 
                  :params="element.params" 
                  :source="1"
                >
                </widget-item>
                <widget-button 
                  :type="element.fixType" 
                  v-if="(selectWidget.prop == element.prop) && !configDisabled"
                  @delete="handleWidgetDelete(index)" 
                  @copy="handleWidgetClone(index)">
                </widget-button>
              </el-form-item>
            </el-col>
          </template>
        </draggable>
      </el-row>
    </el-form>
  </el-main>
</template>

<script>
import { noRepeatField } from '@/components/form-config/types/FieldTypes';
import { generateId, generateUniqueName } from '@/components/form-config/utils';
import Draggable from 'vuedraggable';
import widgetEmptyImg from '../assets/xt-widget-empty.png'; // 空白图片
import WidgetButton from './button.vue';
import WidgetDynamic from './dynamic.vue';
import WidgetItem from './item.vue';
import { ElMessage } from 'element-plus';
import { fieldSource, formTypeEnum } from '@/components/form-config/const/commonDic';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';

export default {
  name: 'widget',
  components: { Draggable, WidgetItem, WidgetDynamic, WidgetButton },
  props: ['data', 'select', 'delList'],
  emits: ['update:select', 'change', 'update:delList'],
  data() {
    return {
      widgetEmptyImg,
      selectWidget: this.select,
      // dels:[],
      form: {},
      configDisabled:inject('disabled',false)
    };
  },
  methods: {
    handleSelectWidget(index) {
      this.selectWidget = this.data.column[index];
    },
    handleWidgetAdd(evt) {
      const newIndex = evt.newIndex;
      const newCol = this.data.column[newIndex];
      const copyLabelList = this.data.column.filter(v => v.copyLabel === newCol.copyLabel)
      console.log(newCol, 'newCol')
      if (newCol?.mid) {
        const tempList = this.data.column.filter(v => v?.mid === newCol?.mid)
        if(tempList.length > 1){
          this.data.column.splice(newIndex, 1);
          ElMessage.warning('模板字段只能使用一次');
          return;
        }
      }
      //判断不能重复的字段
      if (
        noRepeatField.includes(newCol.fixType) &&
        (this.data.column.filter(item => item.fixType === newCol.fixType)?.length || 0) > 1
      ) {
        this.data.column.splice(newIndex, 1);
        this.$message.warning('该字段只能存在一个');
        return;
      }
      const data = this.deepClone(newCol);
      // 改名
      if (copyLabelList.length > 1) {
        data.copyLabel = generateUniqueName(data.typeLabel, copyLabelList.map(i => i.copyLabel));
      }
      if (!data.prop) data.prop = generateId(data.fixType);
      let startIndex = data.prop.indexOf('#');
      if (!data.id) data.id = data.prop.slice(startIndex + 1);
      delete data.icon;
      delete data.subfield;
      if (data.fixType == 'title') {
        this.form[data.prop] = data.value;
      }
      data.isDynamic = false;
      data.isForm = false;
      data.isCrud = false;
      data.visibleBool = true
      //字段库拖过来的表格 需要特殊处理
      if (data.fixType === FixTypeEnum.DYNAMIC && data?.mid && data.children?.length) {
        data.children.forEach(v => {
          v.prop = generateId(v.fixType);
          let startIndex = v.prop.indexOf('#');
          v.id = v.prop.slice(startIndex + 1);
          v.isDynamic = false;
          v.isForm = false;
          v.isCrud = false;
          v.visibleBool = true
        })
      }
      
      this.data.column[newIndex] = data;
      this.handleSelectWidget(newIndex);

      this.$emit('change');
    },
    handleWidgetDelete(index) {
      if (this.data.column.length - 1 === index) {
        if (index === 0) this.selectWidget = {};
        else this.handleSelectWidget(index - 1);
      } else this.handleSelectWidget(index + 1);

      this.$nextTick(() => {
        if (this.data.column[index].mid) {
          this.dels.push(this.data.column[index].mid)
        }
        this.data.column.splice(index, 1);
        this.$emit('change');
      });
    },
    handleWidgetClone(index) {
      let cloneData = this.deepClone(this.data.column[index]);
      const copyLabelList = this.data.column.filter(v => v.fixType === cloneData.fixType)
      // 改名
      cloneData.copyLabel = generateUniqueName(cloneData.typeLabel, copyLabelList.map(i => i.copyLabel));
      //判断不能重复的字段
      if (noRepeatField.includes(cloneData.fixType)) {
        this.$message.warning('该字段只能存在一个');
        return;
      }

      cloneData.prop = generateId(cloneData.fixType);
      cloneData.mid = ''
      let startIndex = cloneData.prop.indexOf('#');
      cloneData.id = cloneData.prop.slice(startIndex + 1);
      // this.data.column.push(cloneData);
      this.data.column.splice(index + 1, 0, cloneData);
      this.$nextTick(() => {
        // this.handleSelectWidget(this.data.column.length - 1);
        this.handleSelectWidget(index + 1);
        this.$emit('change');
      });
    },
  },
  computed: {
    dels: {
      get() {
        return this.delList || []
      },
      set(v) {
        this.$emit('update:delList', v);
      }
    }
  },
  watch: {
    select(val) {
      this.selectWidget = val;
    },
    selectWidget: {
      handler(val) {
        this.$emit('update:select', val);
        const index = this.data.column.findIndex(item => item.prop === val.prop);
        if (index !== -1) {
          this.data.column[index] = val; // 
        }
      },
      deep: true,
    },
    // delList(val){
    //   this.dels = val
    // },

  },
};
</script>
