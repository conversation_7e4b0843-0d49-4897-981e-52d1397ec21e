import { 
  AllDefaultValueType, 
  FieldBaseField, 
  TitleField,
  basePermissionType,
  RemarkField,
  SpecificType,

} from '@/components/form-config/types/AvueTypes';
import { InputFormatTypeEnum } from './const'
/* 文本 */
export type InputField = SpecificType & {
  showFormat: InputFormatTypeEnum;
} & basePermissionType;
export type InputAllField = FieldBaseField & InputField & TitleField & 
AllDefaultValueType & RemarkField;
