<template>
  <el-divider content-position="center" class="mt-32px">
    <div :class="dividerClass">
      {{ copyLabel }}
    </div>
  </el-divider>
</template>

<script setup lang="ts">
import { DividerAllField } from '@/components/form-config/types/AvueTypes';
 
const props = defineProps<DividerAllField>();

const dividerClass = computed(() => [props.showLabel ? '' : 'hidden']);
</script>

<style scoped lang="scss"></style>