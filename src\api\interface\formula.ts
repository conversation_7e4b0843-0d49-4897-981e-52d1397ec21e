
export interface formulaRuleType {
    formula: string;
    ruleExpressId: string;
    ruleName: string
}

export interface fieldCOnditionItemType {
    field:string;
    condition: string;
    value: string
}

export interface dataLinksRuleType {
    dataTableId: string;//数据表id
    fieldCondition: fieldCOnditionItemType[]; //条件数组
    linkageFieldId: string; //联动字段
    id: string
}

export interface dataLinksRulePreviewType {
    dataTableName: string;
    fieldConditionStr: string[];
    linkageFieldName: string;
}