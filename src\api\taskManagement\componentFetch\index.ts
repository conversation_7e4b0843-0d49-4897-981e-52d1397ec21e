import request from '../../../axios';
import { SubmitterEnum } from '@/components/form-config/const/commonDic';

// type RequestList<T> = AxiosPromise<{
//   data:T
//   code:string
//   msg:string
// }>

export const getStatisticalIndicatorsRequest =  (data:{
  timeRange:string[],
  versionId:string,
  taskId?:string,
  statisticalRate:string
  submitter?:string
  submitterType: SubmitterEnum
  usersComponentIds?:string
}) => {
  return request({
    url: '/component/statisticalIndicators',
    method: 'post',
    data
  });
};

/**
 * 逐一触发获取任务或者数据表
 *
 * @template T - 响应数据的类型
 *
 * @param {Object} data - 请求数据对象
 * @param {string} data.version - 版本ID
 * @param {0 | 1 | 2 | 3 | 4} [data.type] - 查询类型过滤（可选），可用值：0,1,2,3,4
 *  - 0: 所有
 *  - 1: 任务与数据表
 *  - 2: 仅任务
 *  - 3: 仅数据表
 *  - 4: 仅计算表
 *
 * @returns {AxiosPromise<T>} - 返回 AxiosPromise 对象
 */
export const getDataPathByVersionId =  (
  data:{
    version:string,
    type?: 0 | 1 | 2 | 3 | 4
  }
) =>{
  return request({
    url: '/linkage/dataPath',
    method: 'post',
    data
  });
}


/**
 * 逐一触发获取表格类型的字段  或者是计算表的第二个下拉框的字段
 *
 * @template T - 响应数据的类型
 *
 * @param {Object} data - 请求数据对象
 * @param {0 | 1 | 2 | 3 } data.type - 推数路径类型
 * @param {string} data.path - 	推数路径
 * @param {0 | 1 } data.filter - 查询类型过滤（可选），可用值：0,1
 *  - 0: 所有
 *  - 1: (逐一触发使用)子表单、表格、数据汇总)
 * @param {0 | 1 } [data.knowConditions] - 查询类型过滤（可选），可用值：0,1 不穿的话查所有
  - 0: 未知
  - 1: 已知

 * @returns {AxiosPromise<T>} - 返回 AxiosPromise 对象
 */
export const getDataPathChildrenByVersionId = (data:{
  type:string
  path: string
  filter?: 0 | 1
  knowConditions?:0 | 1
}) =>{
  return request({
    url: '/linkage/dataPathComp',
    method: 'post',
    data
  });
}

// 逐一触发获取同类型的字段
export const getSelectColumnByVersionId =  (params:{
  fieldType:string
  pathId:string
}) =>{
  return request({
    url: '/linkage/selectColumn',
    method: 'get',
    params
  });
}

// 逐一触发 获取值
export const getTriggerComponentData =  (data:any) =>{
  return request({
    url: '/component/triggerComponent',
    method: 'post',
    data
  });
}


// 会办数据汇总
export const getDataSummaryByTaskId =  (params:{
  taskId:string
}) =>{
  return request({
    url: '/component/dataSummary',
    method: 'get',
    params
  });
}


// 目标值
export const getNameValueList =  () =>{
  return request({
    url: '/metrics/nameValueList',
    method: 'get'
  });
}

// 获取目标值的详情
export const getTargetValueById = (metricsId:string)=>{
  return request({
    url: '/metrics/detail',
    method: 'get',
    params:{metricsId}
  });
}

// 根据计算表获得它的字段
export const getCalFieldByCalTableId =  (fieldId:string) => {
  return request({
    url:'/component/getOriginByCalFieldId',
    method:'get',
    params:{fieldId}
  })
}

// 计算表获取值
export const getCalTableComponentValue =  (data:any) => {
  return request({
    url:'/component/calTableComponent',
    method:'post',
    data
  })
}


// 具体拿条件来判断显隐是否成立
export const visibleAndHiddenRuleHttp =  (data:any) => {
  return request({
    url:'/component/visibleAndHiddenRule',
    method:'post',
    data
  })
}