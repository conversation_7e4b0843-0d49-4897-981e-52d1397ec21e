import request from '../../axios';
//创建组织
export const createOrg = (data: any) => {
    return request({
        url: '/common/tenant/add',
        method: 'post',
        data,
    });
};
// 组织分页
export const getOrganization = (current: number, size: number, data: any) => {
    return request({
        url: '/businessMenu/tenantList',
        method: 'post',
        params: {
            current,
            size
        },
        data
    });
};
// 组织编辑
export const editOrganization = (data: any) => {
    return request({
        url: '/businessMenu/edit',
        method: 'post',
        data
    });
};
// 组织查看
export const seeOrganization = (tenantId: string) => {
    return request({
        url: '/businessMenu/view',
        method: 'get',
        params: {
            tenantId
        },
    });
};
// 组织启用禁用
export const tenantSwitch = (data: any) => {
    return request({
        url: '/businessMenu/enable',
        method: 'post',
        data
    });
};
// 权限分页
export const getAuthority = (current: number, size: number, data: any) => {
    return request({
        url: '/permission/page',
        method: 'post',
        params: {
            current,
            size
        },
        data
    });
};
// 权限树
export const getAuthorityTree = () => {
    return request({
        url: '/businessMenu/buttons',
        method: 'get',
    });
};
// 权限树回显
export const getAuthorityTreeChecked = (data) => {
    return request({
        url: '/permissionMenu/menusById',
        method: 'post',
        data
    });
};
// 添加修改权限
export const addAuthority = (data: any) => {
    return request({
        url: '/permission/submit',
        method: 'post',
        data
    });
};
// 删除权限
export const delAuthority = (id: any) => {
    return request({
        url: '/permission/remove',
        method: 'post',
        params: {
            id
        }
    });
};
// 客服分页
export const getCustomer = (current: number, size: number, data: any) => {
    return request({
        url: '/customerService/page',
        method: 'post',
        params: {
            current,
            size
        },
        data
    });
};
// 资源中心客服分页
export const getCustomerZy = (current: number, size: number, data: any) => {
    return request({
        url: '/customerService/pageByTenantId',
        method: 'post',
        params: {
            current,
            size
        },
        data
    });
};
// 客服回显
export const getCustomerRelDetail = (params) => {
    return request({
        url: '/customerServiceRel/detail',
        method: 'get',
        params
    });
};
// 客服分页详情
export const getCustomerDetail = (id) => {
    return request({
        url: '/customerService/detail',
        method: 'get',
        params: {
            id,
        },
    });
};
// 添加客服
export const addCustomer = (data: any) => {
    return request({
        url: '/customerService/submit',
        method: 'post',
        data
    });
};
// 添加客服
export const addCustomerRel = (data: any) => {
    return request({
        url: '/customerServiceRel/submit',
        method: 'post',
        data
    });
};
// 删除客服
export const delCustomer = (ids: any) => {
    return request({
        url: '/customerService/remove',
        method: 'post',
        params: {
            ids
        }
    });
};
// 角色列表
export const getUserPage = (current: number, size: number, data: any) => {
    return request({
        url: '/blade-system/role/page',
        method: 'post',
        params: {
            current,
            size
        },
        data
    });
};
// 添加角色
export const addUserList = (data: any) => {
    return request({
        url: '/businessMenu/grant',
        method: 'post',
        data
    });
};
// 删除角色
export const delUserList = (ids: any) => {
    return request({
        url: '/blade-system/role/remove',
        method: 'post',
        params:{
            ids,
        }
    });
};
// 删除角色
export const getUserListDetail = (data) => {
    return request({
        url: '/businessMenu/menusByRoleId',
        method: 'get',
        params:{
            ...data,
        }
    });
};