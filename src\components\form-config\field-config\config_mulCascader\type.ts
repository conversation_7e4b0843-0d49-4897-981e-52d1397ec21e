import {
  AllDefaultValueType,
  FieldBaseField,
  TitleField,
  basePermissionType,
  RemarkField,
  SelectDic,
  SelectRules
} from '@/components/form-config/types/AvueTypes';


export type MulSelectField = basePermissionType & {
  filterable?: boolean;
  multiple?: boolean;
  props: any;
  dicData: SelectDic[];
  expandTrigger?: 'click' | 'hover';
  dicRules?: SelectRules[];
};

export type MulSelectAllField = FieldBaseField &
  MulSelectField &
  TitleField &
  AllDefaultValueType
   &
  RemarkField;
