<template>
  <el-card
    class="box-card"
    :class="{ disabled: disabled }"
  >
    <template v-if="tagList.length > 0">
      <el-tag
        v-for="item in tagList"
        :key="item[defaultProp.id]"
        :closable="!disabled && closable"
        @close="close(item)"
        style="margin: 0 5px"
        size="large"
        >{{ item[defaultProp.label] }}</el-tag
      >
    </template>
    <div v-else class="select_dept_placeholder">{{ placeholder }}</div>
  </el-card>
</template>

<script setup lang="ts">
interface propType{
  id: string,
  label:string
}

const props = withDefaults(defineProps<{
  disabled?: boolean,
  closable?: boolean,
  defaultProp: propType,
  placeholder?: string
}>(), {
  disabled: false,
  closable: false,
  placeholder: '请选择'
});


const emit = defineEmits(['close','open']);

const tagList = defineModel<any[]>({default: () => []})

const close = (v: any) => {
  tagList.value = tagList.value.filter(item => item[props.defaultProp.id] !== v[props.defaultProp.id]);
}
</script>

<style lang="scss">
.box-card {
  cursor: pointer;
  width: 100%;
  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
.disabled {
  background: #f6f6f6;
}

.select_dept_placeholder {
  color: #a8abb2;
  font-size: 14px;
}
</style>