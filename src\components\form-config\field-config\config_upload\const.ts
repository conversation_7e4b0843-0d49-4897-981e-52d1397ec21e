export const fileType = [
  {
    label: '文档',
    value: '.doc,.docx,.pdf,.xlsx,.xls,.ppt,.pptx,.txt',
  },
  {
    label: '视频',
    value: 'video/*',
  },
  {
    label: '音频',
    value: 'audio/*',
  },
  {
    label: '图片',
    value: 'image/*',
  },
  {
    label: '压缩包',
    value: '.zip,.rar,.7z',
  },
];



export const archiveFileType = [
  {
    label: '.doc,.docx,.pdf',
    value: '.doc,.docx,.pdf',
  },
] //存证存档限制的文件格式

export const isWhetherOption = [
  {
    label: '是',
    value: 1,
  },
  {
    label: '否',
    value: 0,
  },
];

export const fileClassification = [
  {
    label: '公开级',
    value: 1,
  },
  {
    label: '绝密级',
    value: 2,
  },
  {
    label: '机密级',
    value: 3,
  },
  {
    label: '秘密级',
    value: 4,
  },
  {
    label: '内部',
    value: 5,
  },
];

export const objForm = {
  isEa: 1,//存档 1:是 0: 否
  isEe: 0,//存证 1:是 0: 否
  notarization: 0,//存证+公证 1:是 0: 否
  archivalId: '',//档案门类代码
  orgId: '',//机构代码
  labelIds: '', // 标签
  level: 1, // 档案密级
  secrecyYear: '2', // 保密时间
  secrecyDept: '', // 部门
  secrecyGroup: '', // 工作组
  secrecyUser: '', // 成员
  responsibleUser: '',
  toLedger: 0,//是否转存到台账 1:是 0: 否
  dirIds: [],//电子台账目录
  isMenu: 0,//是否存文件目录 1:是 0: 否
  menuIds: [],//文件目录ids
  toRegulations: 0,//是否存规章制度 1:是 0: 否
  regulatoryData: {
    systemName: '', // 制度名称
    systemNameField: '',//选的组件prop，下面相同
    otherNumber: '', //其他编号
    otherNumberField: '',
    effectiveTime: '', // 生效日期
    effectiveTimeField: '',
    failureTime: '', // 失效日期
    failureTimeField: '',
    orgId: '',//组织 -制定单位
    orgIdName: '',
    depId: '', //部门id(制定单位)
    depIdField: '',
    workingGroupId: '', //工作组id(制定单位)
    workingGroupIdField: '',
    level: '', //制度层级
    contentSummary: '', // 发布内容
    contentSummaryField: '',
    releaseDate: '', // 发布日期
    releaseDateField: '',
    sendTime: '', // 发送时间
    sendTimeField: '',
    pubOrgId: '', // 发布对象部门兼告知范围
    pubOrgIdField: '',
    pubWorkingGroupId: '', // 发布对象工作组id
    pubWorkingGroupIdField: '',
    pubMemberId: '',//发布对象-成员
    pubMemberIdField: '',
    pubTime: '', //发布时间
    pubTimeField: '',
    serialNumber: '',//编码
    tellTime: '', // 告知时间
    tellTimeField: '',
    expiringTime: '',//// 失效日期
    expiringTimeField: '',
    notificationOrgId: '',//告知范围部门
    notificationOrgIdField: '',
    notificationWorkingGroupId: '',//告知范围工作组
    notificationWorkingGroupIdField: '',
    notificationMemberId: '',//告知范围成员
    notificationMemberIdField: '',
  },//规章制度对象
  viewUser: {//可查看人
    depts: '',//部门
    deptCode: '',//部门组件prop
    works: '',//工作组
    workCode: '',//工作组组件prop
    users: '',//成员
    userCode: '',//成员组件prop
  },
  termType: '',//可查看期限 '0':自定义 '1':依文件生成时间 '2': 取字段作为生成时间
  termRange: {
    start: '',//自定义的时间范围-开始时间
    end: '',//自定义的时间范围-结束时间
    dateRangeCode: '',//自定义时间范围prop
    day: null,//依文件生成时间-天
    dayType: 0,//0：天/ 1：工作日
    hour: null,//依文件生成时间-小时
    min: null,//依文件生成时间-分钟
    dayCode: '',//取字段作为生成时间-天，数字组件prop
    hourCode: '',//取字段作为生成时间-小时，数字组件prop
    minCode: '',//取字段作为生成时间-分钟，数字组件prop
  }


}


export const validateUsers = (rule: any, value: any, callback: any, form: any) => {
  const viewUser = form.value.viewUser;
  if (!viewUser.depts && !viewUser.works && !viewUser.users && !viewUser.deptCode && !viewUser.workCode && !viewUser.userCode) {
    callback(new Error('请选择可查看人'));
  } else {
    callback();
  }
};

export const getFileRules = (formRef: any) => ({
  isEa: [
    {
      required: true,
      message: '请选择存档',
      trigger: 'change',
    },
  ],
  isEe: [
    {
      required: true,
      message: '请选择存证',
      trigger: 'change',
    },
  ],
  archivalId: [
    {
      required: true,
      message: '请选择档案门类代码',
      trigger: 'change',
    },
  ],
  orgId: [
    {
      required: true,
      message: '请选择机构代码',
      trigger: 'change',
    },
  ],
  labelIds: [
    {
      required: true,
      message: '请选择标签',
      trigger: 'change',
    },
  ],
  level: [
    {
      required: true,
      message: '请选择档案密级',
      trigger: 'change',
    },
  ],
  secrecyYear: [
    {
      required: true,
      message: '请填写保密时间',
      trigger: 'blur', // 修改为 'blur'，因为 'blue' 不是一个有效的触发器
    },
  ],
  responsibleUser: [
    {
      required: true,
      message: '责任人员必填',
      trigger: 'change',
    },
  ],
  toLedger: [
    {
      required: true,
      message: '请选择存至电子台账',
      trigger: 'change',
    },
  ],
  dirIds: [
    {
      required: true,
      message: '请选择电子台账目录',
      trigger: 'change',
    },
  ],
  menuIds: [
    {
      required: true,
      message: '请选择文件目录',
      trigger: 'blur',
    },
  ],
  isMenu: [
    {
      required: true,
      message: '请选择存至文件',
      trigger: 'blur',
    },
  ],
  viewUser: [
    { validator: (rule: any, value: any, callback: any) => validateUsers(rule, value, callback, formRef), trigger: 'change' },
  ],
  systemNameProp: [
    {
      validator: (rule: any, value: any, callback: any) => {
        const regulatoryData = formRef.value.regulatoryData
        if (!regulatoryData.systemNameField && !regulatoryData.systemName) {
          callback(new Error('请输入制度名称'));
        } else {
          callback();
        }
      }, trigger: 'blur', require: true
    },
  ]

});