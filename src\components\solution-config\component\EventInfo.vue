<template>
  <el-form-item label="事集名称" >
    <el-input v-model="name" />
  </el-form-item>
  <el-form-item label="版本号">
    <el-input v-model="versionNumber" />
  </el-form-item>
  <el-form-item label="事集介绍">
    <el-input :rows="2" type="textarea" v-model="description" />
  </el-form-item>
  <el-form-item label="选择会员卡">
    <avue-select v-model="memberCard" />
  </el-form-item>
</template>
<script setup lang="ts">
const name = defineModel('name', { default: '' });
const versionNumber = defineModel('versionNumber', { default: '' });
const description = defineModel('description', { default: '' });
const memberCard = defineModel('memberCard', { default: '' });

</script>
<style lang="scss" scoped></style>
