
 import {
   AllDefaultValueType,
   FieldBaseField,
   TitleField,
   basePermissionType,
   singleType,
   dataValueType
 } from '@/components/form-config/types/AvueTypes';

/* 部门选择 */
export type UserDeptField = basePermissionType & dataValueType & {
  isRestrictedDept: Boolean;
  limitRange?: number,
  limitIds?: string[]
  isLimit: boolean;

};

export type UserDeptAllField = FieldBaseField  & TitleField & UserDeptField & AllDefaultValueType & 
singleType;
;