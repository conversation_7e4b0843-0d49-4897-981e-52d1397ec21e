
import request from '@/axios';
import { ReqParams, invoceHeader,invoice } from './type'
export const getInvoiceLog = (params: ReqParams) => {
  return request({
    url: '/point_order_record/page',
    method: 'get',
    params
  });
};

//发票抬头
export const getInvoiceHeader = (params: invoceHeader.ReqParams) => {
  return request({
    url: '/invoice_title/page',
    method: 'get',
    params
  });
};

//新增发票抬头

export const addInvoiceHeader = (data: invoceHeader.ResData) => {
  return request({
    url: '/invoice_title/add',
    method: 'post',
    data
  });
};

//编辑发票抬头
export const editInvoiceHeader = (data: invoceHeader.ResData) => {
  return request({
    url: '/invoice_title/edit',
    method: 'put',
    data
  });
};

//删除发票抬头
export const delInvoiceHeader = (id: string) => {
  return request({
    url: `/invoice_title/delete/${id}`,
    method: 'delete'
  });
};

//企业认证详情
export const getTentAuth = (params: { tenantid: string}) => {
  return request({
    url: '/enterprise_information/detail',
    method: 'get',
    params
  });
};


//申请开发票
export const applyInvoice = (data: invoceHeader.ResData) => {
  return request({
    url: '/point_order_record/applyInvoice',
    method: 'post',
    data
  });
};

//获取开票金额
export const getInvoiceAmount = (data: string[]) => {
  return request({
    url: '/point_order_record/getInvoiceAmount',
    method: 'post',
    data
  });
};

//查看发票详情
export const getInvoiceDetails = (params: {id:string}) => {
  return request({
    url: '/invoice_management/detail',
    method: 'get',
    params
  });
};

//发票管理列表
export const getInvoiceList = (params: invoice.ReqParams) => {
  return request({
    url: '/invoice_management/page',
    method: 'get',
    params
  });
};

//驳回发票申请
export const rejectInvoice = (data: invoice.rejectType) => {
  return request({
    url: '/invoice_management/back',
    method: 'put',
    data
  });
};


//上传发票

export const uploadInvoiceFile = (data: invoice.uploadType) => {
  return request({
    url: '/invoice_management/invoicing',
    method: 'put',
    data
  });
};

