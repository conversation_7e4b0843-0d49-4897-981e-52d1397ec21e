
const components = import.meta.glob('@/components/global-field/**/index.vue', { eager: true });

//移动端全局组件
const mComps=import.meta.glob('@/views/mobileView/components/mFormDesign/**.vue', {
  eager: true,
});
const globalComponents = app => {
  for (const [key, component] of Object.entries(Object.assign(components,mComps))) {
    
    const folderName = key.split('/').slice(-2, -1)[0]

    const value = component.default
    app.component(value.name || folderName, value);
  }
};
export default globalComponents;