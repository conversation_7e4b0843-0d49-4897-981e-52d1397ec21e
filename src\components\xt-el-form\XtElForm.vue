<template>
  <el-form
    class="xt-el-form p-32px"
    ref="formRef"
    @submit.prevent
    :model="form"
    label-position="top"
    v-if="showForm"
    v-loading="loading"
    :disabled="disabled"
    label-suffix="："
  >
    <xt-el-form-item-container :form="form" :columns="columns" :source="source" />
  </el-form>
</template>

<script setup lang="ts">
import useSyncRef from '@/hooks/useSyncRef';
import { FormInstance } from 'element-plus';
import { AvueForm, AvueOption } from '@/components/form-config/types/AvueTypes.ts';
import XtElFormItemContainer from './XtElFormItemContainer.vue';
import {
  disabledInjectKey,
  formInjectKey,
  packageInjectKey,
  previousShouldDoInjectKey,
} from './constant';

const props = defineProps<{
  disabled?: boolean;
  option: AvueOption;
  taskPackageId?: string;
  previousShouldDoId?: string;
  source?: number//组件的使用场景 1中间的组件配置 2 组件的默认值设置 3 预览 4 应办
}>();
const taskPackageId = toRef(props, 'taskPackageId');
provide(packageInjectKey, taskPackageId);
const previousShouldDoId = toRef(props, 'previousShouldDoId');
provide(previousShouldDoInjectKey, previousShouldDoId);

const vModel = defineModel<AvueForm>({ default: () => ({}) });
const form = ref<AvueForm>(_.cloneDeep(vModel.value));
const showForm = ref(false);

useSyncRef(form, vModel, { deep: true, debounce: 50 });

provide(formInjectKey, vModel);
const disabled = computed(() => (props.option.disabled ?? false) || props.disabled);
provide(disabledInjectKey, disabled);

const columns = computed(() => props.option.column || []);

const loading = ref(false);
const formRef = ref<FormInstance>();
const expose = reactive<AvueForm>({});

const validate = (callback: (valid: boolean, done: Function) => void) => {
  return formRef.value?.validate(isValid => {
    if (isValid) {
      loading.value = true;
    }
    callback(isValid, () => {
      loading.value = false;
    });
  });
};

defineExpose(expose);
onMounted(() => {
  showForm.value = true;
  nextTick(() => {
    for (const [key, value] of Object.entries(formRef.value!)) {
      expose[key] = value;
    }
    expose['validate'] = validate;
  });
});
</script>

<style lang="scss"></style>
