<template>
    <n-card class="mb-2">
        <h3>联动条件</h3>
        <fieldConditionConfig 
            :conditionTaskId="taskId" 
            v-model:andOr="linkItem.logic"
            v-model:condition="linkItem.columnList"
            :currentField="data"
            :current="true" 
            title="添加当前任务的条件" 
        />
    </n-card>

    <n-card class="mb-2">
        <h3>联动字段</h3>
        <el-form-item label="选择推数路径" required>
            <selectTask 
                v-model:taskId="linkItem.linkageField!.path" 
                v-model:fieldList="fieldList"
                v-model:type="linkItem.linkageField!.pathType" 
                :taskList="taskList"
                :taskType="2" 
                :is-filter="false"
                @click="handleChangeTask1()"
                title="选择推数路径">
            </selectTask>
        </el-form-item>
        <n-card>
            <matchField
                :pathType="linkItem.linkageField!.pathType" 
                :data="data" 
                :fieldList="fieldList" 
                v-model:linkage="linkItem.linkageField!.linkage" 
                v-model:field="linkItem.linkageField!.field">
            </matchField>
            <template  v-if="data.fixType === 'dynamic' && linkItem.linkageField!.field"  >
                <addDynamicLink
                    :data="data" 
                    :taskList="taskList"
                    v-model:pushTaskId="linkItem.linkageField!.path" 
                    :pushDynamicField="dynamicFieldList"
                    v-model:dynamicFields="linkItem.dynamicFields"
                    >
                </addDynamicLink>
            </template>
           
        </n-card>
    </n-card>

</template>

<script setup lang="ts">
import fieldConditionConfig from '../../components/fieldConditionConfig.vue';
import matchField from '../../components/matchField.vue'
import { configDataLinkType } from '../type';
import selectTask from '../../components/selectTask.vue';
import { dataLinksItem } from '@/api/interface/configDataLink.ts';
import { pushTaskListType } from '@/api/interface/task'
import addDynamicLink from '../../components/addDynamicLink/index.vue';


defineProps<{
    taskId: string;
    data: configDataLinkType['data'];
    taskList:pushTaskListType[]
}>();

const linkItem = defineModel<dataLinksItem>('linkItem', { default:  {} });
const fieldList = ref<any[]>([])

const dynamicFieldList = computed(() => {
  const filteredItem = fieldList.value.find(item => item.prop === linkItem.value.linkageField!.field);
  return filteredItem ? filteredItem.children : [];
});

//推数路径改动清除选项
const handleChangeTask1 = () => {
  linkItem.value.linkageField!.field= '';
  linkItem.value.dynamicFields= [];
}; 


</script>