<template>
  <div>
    <el-dialog
      v-model="visible"
      :title="title"
      width="800"
      :append-to-body="true"
      @close="visible = false"
    >  
      <selectDataComponent :tab-list="tabList" v-model="model" :visible="visible" :config="config" :max="max" :isFilterWork="isFilterWork">
      </selectDataComponent>
      <template #footer>
        <div class="flex justify-end">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button type="primary" @click="visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import selectDataComponent from './selectDataComponent.vue';

// const modelList = defineModel<any[]>('modelList',{default: () => []});
const model = ref<any[]>([]);
const visible = defineModel<boolean>('visible', { default: false });
const emit = defineEmits<{
  'update:modelList': [value: any[]];
}>();
interface tabType {
  name: string; //tab的name
  label: string; //tab的label
  api?: (params: any) => Promise<any>; //左侧请求的api
  apiParams?: any; //左侧请求的参数
  getDataApi?: (params: any) => Promise<any>; //请求右侧数据的api
  getDataParams?: any;
  leftProp: leftPropType; //左边tree的prop
  rightProp: rightPropType; //右边data的label和value
  dataCallBack?: (data: any) => any; //处理数据

}

interface leftPropType {
  children: string;
  label: string;
  value: string;
  leftSearchId: string;
}

interface rightPropType {
  label: string;
  id: string;
}
//配置项 单选多选之类的
interface configType {
  isRadio?: boolean;
  workIds?:string[];
  tid?: string;
}

interface DrawerProps {
  modelList: any[];
  title: string;
  tabList: tabType[];
  submitApi?: (params: any) => Promise<any>;
  getTableList?: () => void; //保存后请求表格数据
  dataCallBack?: (data: any) => any; //处理提交的数据
  config?: configType;
  isFilterWork?: boolean;
  max?: number | undefined;
}
const props = withDefaults(defineProps<DrawerProps>(), { isFilterWork: true ,max: undefined });

watch(
  () => visible,
  () => {
    model.value = [...props.modelList];
  },
  { deep: true, immediate: true }
);

const handleSubmit = async () => {
  console.log('rops.config?.isRadio',props);
  if (props.config?.isRadio) {
    if (model.value.length > 1) {
      ElMessage.warning('只能选择一个数据，请重新选择');
      return;
    }
  }
  let data = model.value;
  props.dataCallBack && (data = props.dataCallBack(data));
  if (props.submitApi) {
    try {
      await props.submitApi(data);
      props.getTableList!();
      ElMessage.success('保存成功');
      visible.value = false;
    } catch (error) {
      ElMessage.warning('保存失败');
    }
  } else {
    emit('update:modelList', data);
    visible.value = false;
    
  }
};
</script>
