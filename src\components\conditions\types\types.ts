import { MethodSymbolEnum } from '../option/field';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts';
import { Ref } from 'vue';
export type ConditionItemType = {
  fieldBank?: string;
  field?: string;
  conditions?: MethodSymbolEnum;
  value?: string;
  id:number;
};
export interface Field {
  fieldProp?: string;
  fieldLabel?: string;
  fieldType?: string;
  disabled?: boolean;

  prop: string,
  copyLabel: string,
  fixType: string,

  [key: string]: any;
}

export interface TaskLinkageTypes {
  types:readonly string[];
  symbols: MethodSymbolEnum[];
  result?: {
    types:readonly string[]
    result: string[]
  }[];
}

export interface SymbolListTypes {
  types: readonly string[];
  symbols: MethodSymbolEnum[];
  components?: {
    symbols: MethodSymbolEnum[];
    component: any;
  }[];
}

export type HookOptions = {
  self?: boolean;
  outTask?: boolean;
  defaultColumn?:AvueColumns[];
  initColumn?: Ref<AvueColumns[] | undefined>;
};

export type GetFieldListParams = {
  // 原始表单字段
  formColumn: AvueColumns[];
  // 是否扁平化子表单
  unFlatDynamic?: boolean;
  // 是否全部变为可选
  changeToAble?: boolean;
  // 是否扁平化数据选择
  unFlatDataSelect?: boolean;
  // 是否扁平化数据汇总
  unFlatDataCollect?: boolean;
  // 是否扁平化知识
  unFlatKnowledgeSelect?: boolean;
  // 是否扁平化会办数据
  unFlatClubDataCollectType?: boolean;
  // 是否扁平化逐一触发
  unFlatOneByTriggerType?: boolean;
  // 保留的类型，为空则全部保留
  flatFilterType?: string[];
  // 是否保留子表单/表格本身
  isOriginData?:boolean
};