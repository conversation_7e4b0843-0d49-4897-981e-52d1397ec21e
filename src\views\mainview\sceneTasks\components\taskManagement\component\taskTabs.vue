<template>
  <el-tabs v-model="vModel" class="task_tabs" @tab-click="handleClick">
    <el-tab-pane :label="item.label"
                 v-for="item in list"
                 :name="item.value"></el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
export interface TaskTabsProps{
  list?:Array<{label:string,value:string,handle:Function}>
}
const props=withDefaults(defineProps<TaskTabsProps>(),{
  list:()=>[],
})
const vModel=defineModel<string>({default:""})
const handleClick= (tab: any ) => {
  const item = props.list.find((item: any) => item.value === tab.paneName)
  if (item) {
    item?.handle(item)
  }
}
</script>
<style scoped>
.task_tabs{
}
:deep(.el-tabs__nav-wrap){
  background:  #fff;
}
:deep(.el-tabs__nav-wrap::after) {
  height: 0;
}
</style>