export function generateMarksFromTemplate(template: string, existingMarks: any) {
  const marks: any[] = [];
  // 按行分割模板
  const templateLines = template.split('\n');
  const regex = /@([^%]+)%/g;
  // 逐行处理
  templateLines.forEach((lineContent, lineNumber) => {
    let match;
    // 重置正则状态
    regex.lastIndex = 0;
    while ((match = regex.exec(lineContent)) !== null) {
      const [fullMatch, content] = match;
      const startCh = match.index;
      const endCh = startCh + fullMatch.length;

      // 解析内容和类型
      let prop = content;
      // 查找匹配的现有标记
      const existingMark = existingMarks.find((mark: any) => mark.attributes?.prop === prop);
      marks.push({
        ...(existingMark || {}),
        from: {
          line: lineNumber,
          ch: startCh,
          sticky: null,
        },
        to: {
          line: lineNumber,
          ch: endCh,
          sticky: null,
        },
      });
    }
  });
  return marks;
}
