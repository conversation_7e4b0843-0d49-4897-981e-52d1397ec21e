<template>
  <div class="border-1 border-#ebebeb border-solid p-20px mt-10px">
    <div class="flex items-center justify-between w-full">
      <h4>产品 {{ Nzh.cn.encodeS(index + 1) }}</h4>
      <el-icon color="#ff7070" class="ml-30px cursor-pointer" @click="$emit('delete', product.shelvesProducts.id)"
        ><DeleteFilled
      /></el-icon>
    </div>
    <el-form-item label="产品名称">
      <el-input v-model="product.shelvesProducts.name" placeholder="产品名称" />
    </el-form-item>

    <el-form-item label="功能范围">
      <MultiSelect v-model="product.shelvesProducts.taskGroupIds" :rackingData="rackingData" :options="[]" label="功能范围" />
    </el-form-item>
    <el-form-item label="产品介绍">
      <el-input type="textarea" v-model="product.shelvesProducts.description" placeholder="产品介绍"></el-input>
    </el-form-item>

    <el-button @click="solutionStore.addModel(product.shelvesProducts.id)" type="primary" size="small">+ 添加型号</el-button>
    <div v-for="(model,mInx) in product.shelvesModelDTOList" :key="model.shelvesModel.id">
      <ModelItem :model="model" :index="mInx" @delete="delModel" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import ModelItem from './ModelItem.vue';
import MultiSelect from './MultiSelect.vue';
import { Product } from '../types';
import Nzh from 'nzh';
import { DeleteFilled } from '@element-plus/icons-vue';
import { useSolutionStore } from '../store/index';
import { rackGroupType } from '@/views/mainview/sceneTasks/components/taskManagement/types';

const props = defineProps<{
  product: Product;
  index: number;
  rackingData?:rackGroupType[]
}>();

const solutionStore = useSolutionStore();

defineEmits(['delete'])

const delModel = (id:number) => {
  props.product.shelvesModelDTOList = props.product.shelvesModelDTOList.filter(item => item.shelvesModel.id !== id)
}
</script>
 