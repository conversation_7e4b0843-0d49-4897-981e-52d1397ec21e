<template>
  <div>
    <config-title :data="data"></config-title>
    <config-permission :data="data"> 
      <!-- <el-checkbox v-model="data.isRestrictedDept" label="限制当前用户可选部门" v-if="formType !== formTypeEnum.APPLICATION"/> -->
    </config-permission>
    <config-limit :data></config-limit>
    <config-default-type :data="data" v-if="data.limitRange !== 1"/>
    <config-span :data="data"></config-span>
  </div>
</template>

<script setup lang="ts">
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigLimit from '@/components/form-config/common-config/ConfigLimit/index.vue';
import { UserDeptAllField } from './type';

// const formType = inject('formType',null)

defineProps<{
  data: UserDeptAllField;
}>();
</script>

<style scoped lang="scss"></style>
