<template>
  <div class="graph_container">
    <div class="flex justify-end my-10px mr-20px" v-if="!disabled">
      <div>
<!--        <el-button type="primary" @click="openVersion">批量绑定版本</el-button>-->
        <el-button type="primary" @click="addTaskHandle">添加指标</el-button>
      </div>
    </div>
  <div id="banban_task_graph" class="banban_task_graph"></div>

  </div>
  <el-drawer
    v-model="drawer"
    title="绑定版本"
    :close-on-click-modal="false"
  >
    <el-form label-width="80" label-position="left"
             ref="boundRef"
             :model="versionForm"
             :rules="{
                metricsIds:[{required:true,message:'请选择指标',trigger:['blur','change']}],
                versionId:[{required:true,message:'请选择版本',trigger:['blur','change']}]
             }">
      <el-form-item label="选择指标" prop="metricsIds" required>
        <el-select v-model="versionForm.metricsIds" multiple placeholder="请选择">
          <el-option v-for="item in nameList" :key="item.id" :label="item.targetName" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="版本" prop="versionId" required>
        <el-select v-model="versionForm.versionId" placeholder="请选择">
          <el-option v-for="item in versionList" :key="item.id" :label="item.groupName" :value="item.id"/>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="absolute bottom-20px right-20px">
      <slot name="footer">
        <el-button type="primary" @click="bound(true)">确定</el-button>
        <el-button @click="bound(false)">取消</el-button>
      </slot>
    </div>

  </el-drawer>

  <dialog-form
    v-if="!disabled"
    dialogType="drawer"
    ref="addTaskRef"
    label-width="110"
    @closed="taskForm.addType='0'"
    :title="taskForm.id?'编辑':'添加指标'"
    :rules="{
        targetName: [{ required: true, message: '请输入目标名称', trigger: 'blur' }],
        addType: [{ required: true, message: '请输入添加方式', trigger: 'blur' }],
        targetValue: [{ required: true, message: '请输入数值', trigger: 'blur' }],
      }"
    v-model="taskForm"
  >
    <el-form-item label="版本" prop="versionId">
      <el-select v-model="taskForm.versionId" placeholder="请选择">
        <el-option v-for="item in versionList" :key="item.id" :label="item.groupName" :value="item.id"/>
      </el-select>
    </el-form-item>
    <el-form-item label="添加方式" prop="addType" v-if="taskForm.id" required>
      <el-radio-group v-model="taskForm.addType" class="ml-4">
        <el-radio value="0" size="large">自定义类型</el-radio>
        <el-radio value="1" size="large">目标配置类型</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="目标名称" prop="targetName" required>
      <el-input v-if="taskForm.addType=='0'" v-model="taskForm.targetName" placeholder="请输入"></el-input>
      <el-select v-else v-model="taskForm.targetName" placeholder="请选择">
        <el-option v-for="item in targetList" :key="item" :label="item" :value="item" />
      </el-select>
    </el-form-item>
    <el-form-item label="选择数据" required v-if="taskForm.id&&taskForm.addType=='1'">
      <select-target :target-name="taskForm.targetName" @select="getSelect"></select-target>
    </el-form-item>
    <el-form-item label="类型同步至" prop="typeSynchronization" v-if="taskForm.addType=='0'">
      <el-select v-model="taskForm.typeSynchronization" multiple placeholder="请选择">
        <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"/>
      </el-select>
    </el-form-item>
    <el-form-item label="目标值（%）" prop="targetValue" required>
      <el-input v-model="taskForm.targetValue" type="number" placeholder="请输入" :disabled="taskForm.addType=='1'"></el-input>
    </el-form-item>
    <el-form-item label="数值同步" prop="targetValueSync" v-if="taskForm.targetName&&taskForm.addType=='0'">
      <num-sync :defaultValue="taskForm.targetValueSync" :type-sync="taskForm.typeSynchronization" :target-name="taskForm.targetName" @change="setSyn"></num-sync>
    </el-form-item>
    <el-form-item label="管理要求" prop="requirements">
      <el-input type="textarea" v-model="taskForm.requirements" placeholder="请输入" :disabled="taskForm.addType=='1'"></el-input>
    </el-form-item>
  </dialog-form>
</template>
<script setup lang="ts">
import { onMounted,ref } from 'vue';
import { storeToRefs } from 'pinia';
import IGraph, {
  NodeConstant,
  TaskNodeDataProps,
  TaskTreeProps,
} from '@/views/mainview/sceneTasks/components/taskManagement/hook/useGraphHook.ts';
import DialogForm, { DialogFormIns } from '@/views/mainview/sceneTasks/components/taskManagement/component/dialogForm.vue';
import useTaskGraphStore from '@/views/mainview/sceneTasks/components/taskManagement/store/useTaskGraphStore.ts';
import { VueShape } from '@antv/x6-vue-shape';
import { offTaskEmit, taskEmitter } from '@/views/mainview/sceneTasks/components/taskManagement/event/taskEvent.ts';
import { Edge } from '@antv/x6';
import { ElMessage, ElMessageBox } from 'element-plus';
// import useTaskListStore from '@/views/mainview/sceneTasks/detail/store/useTaskListStore.ts';
import NumSync from '@/views/manageSet/components/numSync.vue';
import SelectTarget from '@/views/manageSet/components/selectTarget.vue';
import { getTargetDicAll } from '@/api/managementSetting/tagSetting.ts';
import { getTaskPackage } from '@/api/taskManagement/taskPackage.ts';
import { addUpdateTarget, bindVersion, getNameList, targetDetail } from '@/api/managementSetting/target.ts';

const targetList = ref<[]>([]);
getTargetDicAll().then(res=>{//获取左侧列表
  targetList.value = res.data.data;
})

interface TaskTreeGraphX6Props {
  disabled?: boolean;
}

const props = withDefaults(defineProps<TaskTreeGraphX6Props>(), {
  disabled: false,
});
const vModel = defineModel<string>({ default: '' });
const { initTaskTreeData, addTaskS, findNodeById, updateTaskParentS, updateTaskS } =
  useTaskGraphStore();
const { taskTreeData } = storeToRefs(useTaskGraphStore());
const addTaskRef = ref<DialogFormIns>();
// const launchRef = ref<DialogFormIns>();
// const messageViewRef = ref<DialogFormIns>();
// const launchForm = ref<any>({
//   executeDept: '',
//   executeDuty: '',
//   executeUser: '',
//   ids: [],
//   type: 0,
// });
// const { setTaskList } = useTaskListStore();
// interface TaskMsgProps {
//   id: string;
//   jumpTaskId: string;
//   changeContent: string;
// }

const drawer = ref<boolean>(false);
const boundRef = ref<any>({});

interface NameType {
  id: string,
  targetName: string
}
const nameList = ref<NameType[]>([]);
const versionForm = ref<any>({});
getNameList().then(res=>{
  nameList.value = res.data.data;
})
const bound = (type:boolean)=>{
  console.log('verisont',versionForm.value);
  if(type){
    boundRef.value.validate((valid:boolean)=>{
      if(valid){
        let data = {
          metricsIds: versionForm.value.metricsIds.join(','),
          versionId: versionForm.value.versionId
        }
        bindVersion(data).then(res=>{
          if(res.data.success){
            ElMessage.success(res.data.msg)
            boundRef.value.resetFields();
            drawer.value = false;
          }
        })
      }
    })
  }else{
    boundRef.value.resetFields();
    drawer.value = false;
  }
}

// const launchDisabled = ref<boolean>(false);
const searchKey = ref<string>('');
const graphIns = ref<IGraph>();
const taskForm = ref<Partial<TaskTreeProps>>({addType:'0'});
interface VersionType {
  id: string,
  groupName: string
}
const versionList = ref<VersionType[]>();
const store = useStore().getters['userInfo'];
//const store = useStore().getters['tenantInfo'];
getTaskPackage({tid:store.tid}).then(res=>{
  versionList.value = res.data.data;
})

const getSelect = (row:any)=>{//获取选中值
    taskForm.value.targetId = row.id;
    taskForm.value.targetValue = row.targetValue;
    taskForm.value.requirements = row.requirements;
}
watch(()=>{taskForm.value.addType},()=>{
  taskForm.value.targetValue = 0;
  taskForm.value.requirements = '';
  taskForm.value.targetId = '';
})

onMounted(() => {
  watch(
    vModel,
    async pId => {
      if (!graphIns.value) {
        initGraph();
      }
      if (pId) {
        await initTree();
        searchKey.value = '';
        graphIns.value?.centerContent();
        // graphIns.value?.zoomTo(0.6)
      }
    },
    { immediate: true }
  );
});
const typeList = [
  {label:'组织目标',value:'0'},
  {label:'版本目标',value:'1'},
  {label:'任务目标',value:'2'},
  {label:'职责目标',value:'3'}
]
function setSyn(obj:object){
  taskForm.value.targetValueSync = obj;
}
const foldTrue = (id: string) => {
  const listFalse: TaskTreeProps[] = [];
  const findFalse = (uid: string) => {
    const { parentNode } = findNodeById(uid);
    if (parentNode) {
      if (parentNode.foldLeft || parentNode.foldRight) {
        listFalse.push(parentNode);
      }
      if (parentNode.parentId) {
        findFalse(parentNode.id);
      }
    }
  };
  findFalse(id);
  Promise.all(
    listFalse.map(task => {
      return updateTaskS(task, { foldLeft: false, foldRight: false });
    })
  ).then(() => {
    resetTree(taskTreeData.value, id);
  });
};
const taskSearchHandle = (id?: string) => {
  if (!id) {
    return graphIns.value?.centerContent();
  }
  const findNode = graphIns.value?.getCellById(id);
  if (findNode) {
    graphIns.value?.centerCell(findNode);
    graphIns.value?.resetSelection([findNode]);
  } else {
    foldTrue(id);
  }
  // return findNode
};

async function initTree() {
  await initTaskTreeData(vModel.value);
  resetTree();
}

// const messageList = ref<TaskMsgProps[]>([]);

function resetTree(treeData: TaskTreeProps[] = taskTreeData.value, selectId?: string) {
  graphIns.value?.hierarchyTask(treeData);
  graphIns.value?.setSelectNodeId(selectId);
}
defineExpose({
  resetTree
})

const addTaskHandle = () => {
  addTaskRef.value?.open<TaskTreeProps>(form => {
    form.addType = '0';
    addUpdateTarget(form).then(res=>{
      if(res.data.success) {
        let resData: TaskTreeProps = res.data.data;
        resData.metricsId = resData.id;
        addTaskS(resData).then(() => {
          addTaskRef.value?.close(() => {
            resetTree();
          });
        });
      }
  });
  });
};

function initGraph() {
  //移除事件监听
  offTaskEmit();
  graphIns.value = new IGraph(
    {
      container: <HTMLElement>document.getElementById('banban_task_graph'),
      width: 100,
      height: 100,
      autoResize: true,
      scroller: {
        enabled: true,
        pannable: true,
      },
      panning: { enabled: true},
      async: true,
      virtual: true,
      grid: {
        visible: false,
        size: 5,
      },
      translating: {
        restrict: false,
      },
      mousewheel: {
        enabled: true,
        // modifiers: ["ctrl", "meta"],
      },
      interacting: cellView => {
        const { cell } = cellView;
        return cell.id !== NodeConstant.menuNode;
      },
      embedding: {
        enabled: true,
        validate: ({ parent }) => {
          if (props.disabled || parent.id === NodeConstant.menuNode) {
            return false;
          }
          return true;
        },
      },
      background: {
        color: 'white',
      },
    },
    props.disabled
  );


  //监听展开
  taskEmitter.on('fold', data => {
    updateTaskS(data, {}, props.disabled).then(() => {
      resetTree();
    });
  });
  //监听渲染完成
  graphIns.value?.onRendered((id?: string) => {
    if (id) {
      taskSearchHandle(id);
    } else {
      graphIns.value?.cleanSelection();
    }
    graphIns.value?.cleanSelect();
  });

  // async function deleteTaskHandle(node:any, isAll:any) {
  //   const data = node.getData<TaskNodeDataProps>().data;
  //   return sliceNode(
  //     getPeerCell(node.id)
  //       ?.filter((s:any) => s.id !== node.id)
  //       .map((s:any) => s.getData().data)
  //   )
  //     .then(() => {
  //       return deleteTaskS(data, isAll);
  //     })
  //     .then(() => {
  //       if (isAll) {
  //         return Promise.resolve(1);
  //       }
  //       const cls = node.getChildren();
  //       const syncCls = (cls || []).map(cl => {
  //         const dt = cl.getData<TaskNodeDataProps>();
  //         const { x: pX, y: pY } = cl.getBBox();
  //         dt.data.parentId = null;
  //         return updateTaskS(dt.data, { x: pX + dt.rootX, y: pY + dt.rootY });
  //       });
  //       return Promise.all(syncCls);
  //     });
  // }

  if (!props.disabled) {
    //设置右键菜单
    graphIns.value?.setTaskMenu([
      {
        title: '编辑',
        handle: (node: VueShape) => {
          const { data } = node.getData<TaskNodeDataProps>();
          targetDetail(data.metricsId).then(res=>{//获取详情
            taskForm.value = _.cloneDeep(res.data.data);
            if(taskForm.value.versionId=='-1'){
              taskForm.value.versionId = '';
            }
            addTaskRef.value?.open<TaskTreeProps>(form => {
              addUpdateTarget(form).then(res=> {
                if (res.data.success) {
                  let resData: TaskTreeProps = res.data.data;
                  resData.metricsId = resData.id;
                  form.id = data.id;
                  updateTaskS(resData, form).then(() => {
                    addTaskRef.value?.close(() => {
                      ElMessage({
                        type: 'success',
                        message: '操作成功',
                      });
                      resetTree();
                    });
                  });

                }
              })


            });

          })
          //taskForm.value = _.cloneDeep(data);
        },
      },
      // {
      //   title: '设置执行人',
      //   handle: (node: VueShape) => {
      //     const { data } = node.getData<TaskNodeDataProps>();
      //     launchTask(0, [data.id]);
      //   },
      // },
      // {
      //   title: '设置告知人',
      //   handle: (node: VueShape) => {
      //     const { data } = node.getData<TaskNodeDataProps>();
      //     launchTask(1, [data.id]);
      //   },
      // },
      {
        title: '删除当前',
        handle: (node: VueShape) => {
          const data = node.getData<TaskNodeDataProps>().data;
          ElMessageBox.confirm(`确认删除当前任务-${data.targetName}`, '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              // return deleteTaskHandle(node, false);
            })
            .then(() => {
              resetTree();
              ElMessage({
                type: 'success',
                message: '操作成功',
              });
            });
        },
      },
      {
        title: '删除所有',
        handle: (node: VueShape) => {
          const data = node.getData<TaskNodeDataProps>().data;
          ElMessageBox.confirm(`确认删除当前任务-${data.targetName}-及其子任务`, '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              // return deleteTaskHandle(node, true);
            })
            .then(() => {
              resetTree();
              ElMessage({
                type: 'success',
                message: '操作成功',
              });
            });
        },
      },
    ]);
  }
  //监听主节点移动
  graphIns.value?.listenRootTaskMove(({ node }) => {
    const data = node.getData<TaskNodeDataProps>();
    const { x: pX, y: pY } = node.getPosition();
    if (!graphIns.value?.isRootNode(node)) {
      // const {parentNode} = findNodeById(data.data.id)
      // const childrenCell = graphIns.value?.getNeighbors(graphIns.value?.getCellById(parentNode?.id), {outgoing: true}) || []
      // const sortChildrenCell = _.clone(childrenCell)?.sort((c, v) => c.getPosition().y - v.getPosition().y)

      const sChildrenCell = getPeerCell(node?.id);
      sliceNode(sChildrenCell.map((s:any) => s.getData().data));
      // sortChildrenCell.map((cell,idx)=>{
      //   return updateTaskS(cell.getData().data, {sort: idx})
      // })
      // updateTaskS(data.data, {x: pX + Math.abs(data.rootX), y: pY +Math.abs(data.rootY),sort:findCurrentIndex||0 })
    } else {
      updateTaskS(data.data, { x: pX - data.hX, y: pY - data.hY });
    }
  });
  //监听连线删除
  graphIns.value?.addTaskEdgeRemoveTools((edge: Edge) => {
    const targetNode = edge.getTargetNode();
    if (targetNode) {
      const { x, y } = targetNode.getPosition();
      const nodeData = targetNode.getData<TaskNodeDataProps>();
      sliceNode(
        getPeerCell(targetNode.id)
          ?.filter((t:any) => t.getData().data.id !== targetNode.id)
          .map((s:any) => s.getData().data)
      )
        .then(() => {
          return updateTaskParentS(
            nodeData.data,
            {
              x: x + Math.abs(nodeData.rootX),
              y: y + Math.abs(nodeData.rootY),
              prevName: '',
              side: '',
              root: true,
            },
            0
          );
        })
        .then(() => {
          resetTree();
        });
    }
    // updateTaskS()
  });
  //监听节点嵌入
  graphIns.value?.listenEmbed((targetCell, sourceCell) => {
    console.log('target',targetCell);
    console.log('source',sourceCell);
    const { side = 'right' } = targetCell?.getData<TaskNodeDataProps>().data;
    const sortChildrenCell = getChildrenCell(targetCell?.id!);
    let prevName = null;
    if (sortChildrenCell?.length >0&&sortChildrenCell?.at(-1)?.id!==sourceCell.id) {
      prevName = sortChildrenCell?.at(-1)?.getData<TaskNodeDataProps>().data.targetName || ''
    }
    const sChildrenCell = getPeerCell(sourceCell?.id);
    sliceNode(sChildrenCell?.filter((s:any) => s.id !== sourceCell.id).map((s:any) => s.getData().data)).then(
      () => {
        updateTaskParentS(
          sourceCell.getData<TaskNodeDataProps>().data,
          {
            side: side || 'right',
            root: false,
            x: 0,
            y: 0,
            prevName,
          },
          targetCell?.id
        ).then(() => {
          resetTree();
        });
      }
    );
  });

  //双击编辑任务
  // graphIns.value?.on('node:dblclick', ({ node }) => {
  //   const { id } = node.getData<TaskNodeDataProps>().data;
  //   // toTaskDetail(id);
  // });
  //监听新增左右
  taskEmitter.on('addLR', data => {
    console.log('ddddd',data);
    // addTaskRef.value?.open<TaskTreeProps>(form => {
    //   Object.assign(form, data);
    //   addTaskS(form, data.parentId).then(() => {
    //     addTaskRef.value?.close(() => {
    //       resetTree();
    //     });
    //   });
    // });

    addTaskRef.value?.open<TaskTreeProps>(form => {
      form.addType = '0';
      addUpdateTarget(form).then(res=>{
        if(res.data.success) {
          let resData: TaskTreeProps = res.data.data;
          form.metricsId = resData.id;
          addTaskS(form,data.parentId).then(() => {
            addTaskRef.value?.close(() => {
              resetTree();
            });
          });
        }
      });
    });
  });
  //监听新增上下
  taskEmitter.on('addBT', ({ node, type }) => {
    const nData = node.getData<TaskNodeDataProps>();
    const parentNode = graphIns.value?.getPredecessors(node)[0];
    addTaskRef.value?.open<TaskTreeProps>(form => {
      form.addType = '0';
      addUpdateTarget(form).then(res=> {
        if (res.data.success) {
          let resData: TaskTreeProps = res.data.data;
          form.metricsId = resData.id;

          Object.assign(form, {
            foldLeft: false,
            foldRight: false,
            side: nData.side,
            x: nData.data.x,
            y: type === 'top' ? nData.data.y - 50 : nData.data.y + 50,
          });
          addTaskS(form, parentNode?.id || 0)
            .then(res => {
              const childData = getPeerCell(node.id).map((s:any) => s.getData().data);
              if (type === 'bottom') {
                childData.splice(childData.findIndex(s => s.id === node.id) + 1, 0, res.data.data);
              } else if (type === 'top') {
                childData.splice(
                  childData.findIndex(s => s.id === node.id),
                  0,
                  res.data.data
                );
              }
              return sliceNode(childData);
              // if (nextNode){
              //   return updateTaskS(nextNode.getData().data,{prevName:res.data.data.taskName})
              // }
              // return 1
            })
            .then(() => {
              addTaskRef.value?.close(() => {
                resetTree();
              });
            });
        }
      })

    });
  });
}

function getPeerCell(id: string) {
  const { parentNode, node } = findNodeById(id);
  if (parentNode) {
    const childrenCell =
      graphIns.value?.getNeighbors(graphIns.value?.getCellById(parentNode?.id), {
        outgoing: true,
      }) || [];
    return _.clone(childrenCell)
      ?.filter(s => s.getData().data.side === node?.side)
      .sort((c:any, v:any) => c.getPosition().y - v.getPosition().y);
  }
  return [graphIns.value?.getCellById(id)];
}

function getChildrenCell(id: string) {
  const { node } = findNodeById(id);
  const childrenCell =
    graphIns.value?.getNeighbors(graphIns.value?.getCellById(id), { outgoing: true }) || [];
  return _.clone(childrenCell)
    ?.filter(s => s.getData().data.side === (node?.side || 'right'))
    .sort((c:any, v:any) => c.getPosition().y - v.getPosition().y);
}

async function sliceNode(newDatas: any[]) {
  console.log('newDatas',newDatas);
  const asyncTask: Promise<any>[] = [];
  newDatas.forEach((n, idx) => {
    if (idx === 0) {
      n.prevName !== '' && asyncTask.push(updateTaskS(n, { prevName: '' }));
    } else if (n.prevName !== newDatas[idx - 1].targetName) {
      asyncTask.push(updateTaskS(n, { prevName: newDatas[idx - 1].targetName }));
    }
  });
  return Promise.all(asyncTask).then(res => {
    const dataS = res.map(s => s.data.data);
    console.log('res', dataS);
    dataS.forEach(dt => {
      const cell = graphIns.value?.getCellById(dt.id);
      delete dt.children;
      cell?.updateData({ data: dt }, { silent: true });
    });
  });
}
</script>

<style scoped>
.graph_container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.graph_menu_top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 10px;

  .menu_top_search {
    display: flex;

    .search_btn {
      margin-left: 10px;
    }
  }
}

.graph_tools {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1111;
  left: 50%;
  transform: translateX(-50%);
  bottom: 25px;
}

.banban_task_graph {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-top: 10px solid #f4f9fd;
}

.m_t {
  margin-top: 5px;
}

.msg_item {
  display: flex;
  align-items: center;
  border: 1px solid #eee;
  padding: 15px 10px;
  font-size: 14px;
  border-radius: 5px;
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  }
}

.msg_del_btn {
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: red;
  }
}
</style>
