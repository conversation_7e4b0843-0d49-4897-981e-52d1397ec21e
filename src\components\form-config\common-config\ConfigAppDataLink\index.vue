<template>
  <el-button class="w-full" @click="visible = true">微应用数据联动</el-button>
  <el-dialog
    title="数据联动"
    append-to-body
    :destroy-on-close="true"
    v-model="visible"
    width="1000px"
    :close-on-click-modal="false"
  >
    <AppDataLink
      :data="data"
      :task-list="appList"
      v-model:link-item="form"
      :task-id="(route.query.id as string)"
    />
    <template #footer>
      <div class="flex justify-end">
        <el-button type="primary" @click="handleSubmit" round>保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import AppDataLink from './components/appDataLink.vue';
import {
  appDataLinkField,
  DataLinkField,
  DynamicField,
  FieldBaseField,
} from '@/components/form-config/types/AvueTypes.ts';
import { pushTaskListType } from '@/api/interface/task.ts';
//import { getPushTaskList } from '@/api/taskManagement/task.ts';
import { getAllAppList } from '@/api/application';
import { ElMessage } from 'element-plus';
import { dataType } from '@/api/interface/configDataLink.ts';

const route = useRoute();
const props = defineProps<{
  data: FieldBaseField & appDataLinkField & DataLinkField & DynamicField;
  isFormItem?: boolean;
}>();

const visible = ref<boolean>(false);
const form = ref<dataType>({} as dataType);

if(props.data.appDataLinks)form.value = _.cloneDeep(props.data.appDataLinks);

const appList = ref<pushTaskListType[]>([]);

const store = useStore();
const getList = () => {
  getAllAppList(store.state.user.tenantId).then(res => {
    appList.value = res.data.data.map((item: any) => {
      return { label: item.appName, value: item.microAppId, type: 3 };
    });
  });
};
getList();

const handleSubmit = () => {
  if (!checkData()) return;
  props.data.appDataLinks = JSON.parse(JSON.stringify(_.cloneDeep(form.value)));
  props.data.linkFields = _.cloneDeep(Array.from(new Set(linkFields.value as string[])));
  visible.value = false;
};

const checkData = () => {
  console.log('linkForm', form.value);
  if (!form.value?.linkagePath) {
    ElMessage.warning('请选择推数路径!');
    return false;
  } else if (
    !(
      form.value.microAppExtreme &&
      form.value.microAppExtreme.extremeProp &&
      form.value.microAppExtreme.rule !== undefined &&
      form.value.microAppExtreme.rule !== ''
    )
  ) {
    ElMessage.warning('请选择极值规则!');
    return false;
  }
  if (!form.value.linkageProp) {
    ElMessage.warning('请选择联动字段!');
    return false;
  }
  return true;
};
//相关组件的联动ids
const linkFields = computed(() => {
  let ids: string[] = form.value?.microAppFilter
    ?.filter((column: any) => column.filterType == 'CURRENT_FORM_FIELD')
    .filter((v: any) => v.currentFormProp)
    .map((item: any) => item.currentFormProp);
  return _.uniq(ids);
  // const currentFieldProps = linkData.value.list
  //   .filter(item => item.columnList)
  //   .flatMap(item => item.columnList?.filter(column => column.prop).map(v => v.prop));
  // const currentFilterProps = linkData.value.list
  //   .filter(item => item.linkageField?.filterColumnList)
  //   .flatMap(item => item.linkageField?.filterColumnList?.filter(column => column.currentFormProp).map(v => v.currentFormProp));
  // return _.uniq([...currentFieldProps, ...currentFilterProps]);
});
</script>
