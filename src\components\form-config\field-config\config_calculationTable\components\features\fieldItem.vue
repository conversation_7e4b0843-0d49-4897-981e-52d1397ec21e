<script setup>

import {
  getDynamicFieldSymbolDic,
  getFieldComponent, getFieldSymbolDic, getFilterFieldSymbolDic,
} from '@/views/mainview/sceneTasks/detail/option/field.ts';
import {  conditionTypeDic } from '../const/index.ts';
import { conditionEnum } from '../const/index.ts';
import { computed } from 'vue';
import {
  multipleSelectType,
  separateType,
  contrastTypes,
  submitterType,
  submitterTimeType,
  triggerTimeType,
  dateType, usersType, getUsercodeType, userCurrentType,
} from '@/components/form-config/types/FieldTypes';
import  formItem from '@/views/mainview/sceneTasks/detail/option/xtFields.ts'
import ConditionCustomItem from '@/components/conditions/conditionCustomItem.vue';
import ConditionCurrentTaskItem from '@/components/conditions/conditionCurrentTaskItem.vue';

const props = defineProps(['fieldList','column','range']);

defineEmits(['delete']);

const conditionType = defineModel('conditionType');
const fieldId = defineModel('fieldId');
const conditions = defineModel('conditions');
const result = defineModel('result');

const filterMethods = ref([]);
const retainMethods = ref([]);

const fieldType = computed(() => {
  return field.value?.fieldType || field.value?.type ;
});

const field = computed(() => {
  return props.fieldList.find(item => item.prop === fieldId.value)
});

//联动任务字段/当前任务字段匹配同类型字段
const linkField = computed(() => {
  return props.fieldList
});

const fieldContent = computed(() => {

  let  allItem = [];
  formItem.forEach(item => {
    item.list.forEach(i => {
      allItem.push(i)
    })
  })
  return field.value?.content ?? allItem.find(item => item.fixType === fieldType.value);
});

const isDynamic = computed(() => {
  return conditionType.value === conditionEnum.Custom &&
    _.includes(fieldId.value, 'dynamic');
});


//中间的运算规则
const symbolList = computed(() => {
  let isCustom = conditionType.value === conditionEnum.Custom;
  let list = isCustom ? [] : filterMethods.value;
  let retainList = isCustom ? [] : retainMethods.value;
  // 特殊类型处理
  if (contrastTypes.includes(fieldType.value) && !isCustom) {
    return getFilterFieldSymbolDic(fieldType.value);
  } else {
    return isDynamic.value ?
      getDynamicFieldSymbolDic(fieldType.value) :
      getFieldSymbolDic(fieldType.value, isCustom, list, retainList);
  }
});

// unTriggerLink.vue
const filterList = computed(() => {
  let result = linkField.value || [];

  const timesType = [...triggerTimeType, ...noRangeDateType]
  const usersTypeList = [...usersType, ...getUsercodeType, ...userCurrentType]


  const filterResult = (types) => {
    result = result.filter(i => _.includes(types, i.fieldType || i.fixType));
  };
  separateType(fieldType.value, {
    inputType: types => {
      filterResult(types);
      // filterMethods.value = ['eqAny', 'neqAny'];
      // result.push(createList[0]);
    },
    dateType: types => {
      filterResult(types);
      filterMethods.value = [
        'gtToday',
        'geToday',
        'ltToday',
        'leToday',
        'eqToday',
        'between',
        'innovate',
        'movingBefore',
        'movingAfter',
        'eqAt',
        'gtAt',
        'ltAt',
      ];
      // result.push(createList[1]);
    },
    dateRangeType: types => {
      filterResult(types);
      filterMethods.value = [
        'gtToday',
        'geToday',
        'ltToday',
        'leToday',
        'eqToday',
        'between',
        'innovate',
        'movingBefore',
        'movingAfter',
        'eqAt',
        'gtAt',
        'ltAt',
        'gt',
        'lt',
        'ge',
        'le',
      ];
    },
    numberType: types => {
      filterResult(types);
      filterMethods.value = ['between', 'eqDay', 'eqYear', 'eqMonth'];
    },
    // 选择
    selectType: types => {
      filterResult(types);
      filterMethods.value = ['includeAll'];
    },
    mulSelectType: types => {
      filterResult(types);
      filterMethods.value = ['includeAll'];
    },
    formulaEditType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    targetValueType: types => {
      filterResult(types);
      filterMethods.value = ['between'];
    },
    calculationTableType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    submitterTimeType: types => {
      filterResult(types);
      filterMethods.value = [
        'between',
        'includeAll',
        'ltToday',
        'gtToday',
        'leToday',
        'eqToday',
        'innovate',
        'movingBefore',
        'movingAfter',
      ];
    },
    addressType: filterResult,
    uploadType: filterResult,
    photoType: filterResult,
    tipsType: filterResult,
    usersType: types => {
      filterResult(usersTypeList)
    },
    userTagType: filterResult,
    userDeptType: filterResult,
    signatureType: filterResult,
    interspaceComType: filterResult,
    inventoryComType: filterResult,
    departAdminType: filterResult,
    workingAdminType: filterResult,
    submitterType: filterResult,
    getUsercodeType: filterResult,
    triggerTimeType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
    executorUserType: filterResult,
    noSubmitUserType: filterResult,
    submitterTheDeptType: filterResult,
    submitterTheWorkType: filterResult,
    departMentType: filterResult,
    workLeadType: filterResult,
    organizeAdminType: filterResult,
    endUpdateTimeType: types => {
      types = [...submitterTimeType, ...triggerTimeType, ...dateType]
      filterResult(types)
    },
    endUpdateUserType: types => {
      types = [...submitterType]
      filterResult(types)
    },
  });

  return result;
});

const matchFiledList = computed(()=> props.column.filter(res => res.fixType === fieldType.value).map(r => ({...r,disabled:false})))

function changeFilter(){
  let result = linkField.value || [];

  const timesType = [...triggerTimeType, ...dateType]
  const usersTypeList = [...usersType, ...getUsercodeType, ...userCurrentType]


  const filterResult = (types) => {
    result = result.filter(i => _.includes(types, i.fieldType || i.fixType));
  };
  separateType(fieldType.value, {
    inputType: types => {
      filterResult(types);
    },
      dateType: types => {
      filterResult(timesType);
      filterMethods.value = [
        'gtToday',
        'geToday',
        'ltToday',
        'leToday',
        'eqToday',
        'between',
        'innovate',
        'movingBefore',
        'movingAfter',
        'eqAt',
        'gtAt',
        'ltAt',
      ];
      // result.push(createList[1]);
    },
      dateRangeType: types => {
      filterResult(types);
      filterMethods.value = [
        'gtToday',
        'geToday',
        'ltToday',
        'leToday',
        'eqToday',
        'between',
        'innovate',
        'movingBefore',
        'movingAfter',
        'eqAt',
        'gtAt',
        'ltAt',
        'gt',
        'lt',
        'ge',
        'le',
      ];
    },
      numberType: types => {
      filterResult(types);
      filterMethods.value = ['between', 'eqDay', 'eqYear', 'eqMonth'];
    },
      // 选择
      selectType: types => {
      filterResult(types);
      filterMethods.value = ['includeAll'];
    },
      mulSelectType: filterResult,
      formulaEditType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
      targetValueType: types => {
      filterResult(types);
      filterMethods.value = ['between'];
    },
      calculationTableType: types => {
      filterResult(types);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
      submitterTimeType: types => {
      filterResult(timesType);
      filterMethods.value = [
        'between',
        'includeAll',
        'ltToday',
        'gtToday',
        'leToday',
        'eqToday',
        'innovate',
        'movingBefore',
        'movingAfter',
      ];
    },
      addressType: filterResult,
      uploadType: filterResult,
      photoType: filterResult,
      tipsType: filterResult,
      usersType: types => {
      filterResult(usersTypeList)
    },
      userTagType: filterResult,
      userDeptType: filterResult,
      signatureType: filterResult,
      interspaceComType: filterResult,
      inventoryComType: filterResult,
      departAdminType: filterResult,
      workingAdminType: filterResult,
      submitterType: types => {
      filterResult(usersTypeList)
    },
      getUsercodeType: types => {
      filterResult(usersTypeList)
    },
      triggerTimeType: types => {
      filterResult(timesType);
      retainMethods.value = ['eq', 'neq', 'gt', 'ge', 'lt', 'le'];
    },
      executorUserType: filterResult,
      noSubmitUserType: filterResult,
      submitterTheDeptType: filterResult,
      submitterTheWorkType: filterResult,
      departMentType: filterResult,
      workLeadType: filterResult,
      organizeAdminType: filterResult,
      endUpdateTimeType: types => {
      filterResult(timesType)
    },
      endUpdateUserType: types => {
      filterResult(usersTypeList)
    },
  });
}

onMounted(() => {
  watch(
    () => fieldId.value,
    () => {
      conditions.value = undefined;
      result.value = undefined;
      changeFilter()
    }
  );

  watch(
    () => conditions.value,
    () => {
      result.value = undefined;
    }
  );

  watch(
    () => conditionType.value,
    () => {
      result.value = undefined;
      conditions.value = undefined;
      changeFilter()
    }
  )
});


</script>

<template>
  <n-card :content-style="{ padding: '10px', position: 'relative', paddingRight: '60px', backgroundColor: '#F4F9FD;' }">

    <el-row :gutter="10">
      <!--匹配任务字段或者自定义-->
      <el-col :span="24">
        <avue-radio v-model="conditionType" :dic="conditionTypeDic"></avue-radio>
      </el-col>

<!--      <el-col :span="8">-->
<!--        <avue-input disabled :model-value="range"></avue-input>-->
<!--      </el-col>-->

<!--      <el-col :span="8">-->
<!--        <avue-select-->
<!--          class="w-full"-->
<!--          :dic="fieldList"-->
<!--          v-model="fieldId"-->
<!--          :props="{ label: 'label', value: 'prop'}"-->
<!--          placeholder="请选择字段"-->
<!--        ></avue-select>-->
<!--      </el-col>-->

<!--      <el-col :span="8" v-if="fieldId">-->
<!--        <avue-select-->
<!--          class="w-full"-->
<!--          :dic="symbolList"-->
<!--          v-model="conditions"-->
<!--          placeholder="请选择规则"-->
<!--        ></avue-select>-->
<!--      </el-col>-->
<!--      &lt;!&ndash;匹配任务字段&ndash;&gt;-->
<!--      <el-col :span="24" v-if="conditions && conditionType === conditionEnum.MatchingTaskFields">-->
<!--        <avue-select-->
<!--          class="w-full"-->
<!--          :dic="matchFiledList"-->
<!--          :props="{ label: 'label', value: 'prop', type: 'prop' }"-->
<!--          v-model="result"-->
<!--          placeholder="请选择"-->
<!--        ></avue-select>-->
<!--      </el-col>-->
<!--      &lt;!&ndash;自定义&ndash;&gt;-->
<!--      <el-col :span="24" v-if="conditions && conditionType === conditionEnum.Custom">-->
<!--        <component-->
<!--          :is="getFieldComponent(conditions, fieldType, isDynamic)"-->
<!--          v-model="result"-->
<!--          :field="fieldContent"-->
<!--          class="!w-full"-->
<!--        ></component>-->
<!--      </el-col>-->
      <template v-if="conditionType === conditionEnum.MatchingTaskFields">
        <conditionCurrentTaskItem
          :del-btn="false"
          field-label="label"
          :push-task-label="range"
          v-model:dataProp="fieldId"
          v-model:linkageProp="result"
          v-model:symbol="conditions"
          :push-field-list="fieldList"
          :link-field-list="matchFiledList">
        </conditionCurrentTaskItem>
      </template>
      <template v-if="conditionType === conditionEnum.Custom">
        <conditionCustomItem :push-task-label="range"
                             field-label="label"
                             v-model:fieldId="fieldId"
                             v-model:symbol="conditions"
                             v-model:result="result"
                             :field-list="fieldList"
                             :del-btn="false"></conditionCustomItem>
      </template>

    </el-row>
    <slot name="customBtn">
      <el-button type="danger" link class="absolute right-10px top-10px" @click="$emit('delete')"
      >删除
      </el-button>
    </slot>
  </n-card>
</template>

<style scoped lang="scss">

</style>