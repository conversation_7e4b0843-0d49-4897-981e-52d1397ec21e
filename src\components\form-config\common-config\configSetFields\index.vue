<template>
    <el-button class="w-full" size="large" @click="visible = true">配置筛选条件</el-button>
    <el-dialog
      title="筛选条件"
      append-to-body
      :destroy-on-close = true
      v-model="visible"
      width="1000px"
      :close-on-click-modal="false"
    >
        <containerContent ref="container" :data="linkData.list" @add="addDataLinks" @delete="handleGroupDel" :main-width="900">
            <template #item="{ item, index }">
                <el-form-item label="选择推数路径" required>
                    <selectTask
                        :taskList="taskList"
                        v-model:taskId="item.linkageField.path"
                        v-model:type="item.linkageField.pathType"
                        v-model:fieldList="item.pushFieldList"
                        @click="handleChangeTask1(index)"
                    />
                </el-form-item>
                <matchField 
                    v-if="data.defaultType === DynamicTypeEnum.TRIGGER"
                    :data="data" 
                    :fieldList="item.pushFieldList" 
                    :isLinkAge="false" 
                    v-model:field="item.linkageField!.field">
                </matchField>
                <addDynamicLink
                    v-if="item.linkageField.path"
                    :data="data" 
                    :taskList="taskList"
                    v-model:pushTaskId="item.linkageField!.path" 
                    :pushDynamicField="handleField(item.pushFieldList, item.linkageField!.field)"
                    v-model:dynamicFields="item.dynamicFields"
                    :isDynamic="data.defaultType === DynamicTypeEnum.HANDLEDATA ? false: true"
                    >
                </addDynamicLink>
              
            </template>
        </containerContent>
        <template #footer>
            <div class="flex justify-end">
            <el-button type="primary" @click="handleSubmit" round>保存</el-button>
            </div>
      </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { FieldBaseField, DataLinkField,linkFieldsType, AvueColumns,DynamicField,DynamicAllField } from '@/components/form-config/types/AvueTypes';
import containerContent from '../components/containerContent/index.vue';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus';
import { pushTaskListType } from '@/api/interface/task'
import { getPushTaskList } from '@/api/taskManagement/task'
import usePackageTreeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTreeStore.ts';
import addDynamicLink from '../components/addDynamicLink/index.vue';
import selectTask from '../components/selectTask.vue';
import matchField from '../components/matchField.vue';
import { addFilterItemType } from '@/api/interface/configDataLink.ts';
import { DynamicTypeEnum } from '@/components/form-config/const/commonDic';


const { versionId } = storeToRefs(usePackageTreeStore());

const props = defineProps<{
    data: FieldBaseField & DataLinkField & linkFieldsType & DynamicField;
}>();
const visible = ref(false)
const linkData = ref<addFilterItemType>({list:[]});

const handleField = (fieldList: AvueColumns[], field: string) => {
    if(props.data.defaultType === DynamicTypeEnum.TRIGGER){
        const filteredItem = fieldList?.find(item => item.prop === field) as DynamicAllField;
        return filteredItem ? filteredItem.children : [];
    }else if(props.data.defaultType === DynamicTypeEnum.HANDLEDATA){
        return fieldList
    }else {
        return []
    }
}

//校验
const checkData = () => {
    if(_.isArray(linkData.value.list)){
        const haveEmpty = linkData.value.list.some(i => {
            if(props.data.defaultType === DynamicTypeEnum.HANDLEDATA){
                return !i.dynamicFields?.length || i.dynamicFields?.some(v=> !v.currentProp || !v.pushProp) || !i.linkageField?.path
            }else if (props.data.defaultType === DynamicTypeEnum.TRIGGER){
                return !i.linkageField?.field || !i.linkageField?.path || !i.dynamicFields?.length || i.dynamicFields?.some(v=> !v.currentProp || !v.pushProp)
            }
        });
        if (haveEmpty) {
            if(props.data.defaultType === DynamicTypeEnum.HANDLEDATA){
                ElMessage.warning('推数路径、添加条件不能为空');
            }else if(props.data.defaultType === DynamicTypeEnum.TRIGGER){
                ElMessage.warning('推数路径、联动字段不能为空、添加条件不能为空');
            }
            return false; 
        }
    }
    return true
}

//添加条件组
const addDataLinks = () => {
    if(checkData()){
        (linkData.value.list ??= []).push({
            linkageField: { path: '', pathType: 0, field: ''},
            dynamicFields:[]
        });
    }
}

//删除条件组
const handleGroupDel = (index: number) => {
    linkData.value?.list.splice(index, 1);
};

//推数路径改动清除选项
const handleChangeTask1 = (index: number) => {
  linkData.value.list[index].linkageField!.field = '';
  linkData.value.list[index].dynamicFields = []
};

const handleSubmit = () => {
    if(!checkData()) return
    const arr = linkData.value.list.map((i:any) => {
        delete i.pushFieldList
        return i
    })
    props.data.dataLinks = _.cloneDeep({list: arr}) as any
    visible.value = false;
}

//任务
const taskList = ref<pushTaskListType[]>([])
const getList = () => {
  getPushTaskList({ version: versionId.value as any || '', type: '2' }).then((res) => {
    taskList.value = res.data.data
  })
}
getList()

watch(
    () => visible.value,
    (val) => {
        if(val) {
            linkData.value = _.cloneDeep(props.data.dataLinks as addFilterItemType) || {list:[]}
        }
    }
)




</script>

<style scoped lang="scss"></style>