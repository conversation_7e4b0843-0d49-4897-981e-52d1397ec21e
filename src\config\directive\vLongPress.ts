// const longpress = {
//     mounted(el, binding, vNode) {
//         // 没有绑定函数抛出错误
//         if (typeof binding.value !== 'function') {
//             throw 'longpress callback not a function'
//         }
//         // 计时器变量
//         el._timer = null
//         // 运行函数
//         el._handler = e => {
//             binding.value(e)
//         }
//         // 创建计时器（2秒后执行函数）
//         el._start = e => {
//             // 0为鼠标左键
//             if (e.type === 'click' && e.button !== 0) return

//             if (el._timer === null) {
//                 el._timer = setTimeout(_ => {
//                     el._handler(e)
//                 }, 1200)

//                 // 取消浏览器默认事件
//                 el.addEventListener('contextmenu', e => {
//                     e.preventDefault()
//                 })
//             }
//         }
//         // 两秒内松手，取消计时器
//         el._cancel = e => {
//             if (el._timer !== null) {
//                 clearTimeout(el._timer)
//                 el._timer = null
//             }
//         }

//         // 添加计时监听
//         el.addEventListener('mousedown', el._start)
//         el.addEventListener('touchstart', el._start)
//         // 添加取消监听
//         el.addEventListener('click', el._cancel)
//         el.addEventListener('mouseout', el._cancel)
//         el.addEventListener('touchend', el._cancel)
//         el.addEventListener('touchcancel', el._cancel)
//     },
//     unmounted(el) {
//         // 移除监听
//         el.removeEventListener('mousedown', el._start)
//         el.removeEventListener('touchstart', el._start)
//         el.removeEventListener('click', el._cancel)
//         el.removeEventListener('mouseout', el._cancel)
//         el.removeEventListener('touchend', el._cancel)
//         el.removeEventListener('touchcancel', el._cancel)
//     }
// }
// export default longpress