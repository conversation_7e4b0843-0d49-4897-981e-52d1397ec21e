<template>
  <div class="XtArea w-full">
    <van-field
      v-model="showText"
      is-link
      readonly
      label=""
      placeholder="选择城市"
      @click="open"
    >
      <template #right-icon>
        <van-icon v-if="showText" @click.stop="cleanArea" name="close" />
      </template>
    </van-field>
    <van-field
      v-bind="attrs"
      v-if="showAddress"
      v-model="address"
      label=""
      placeholder="请输入 详细地址"
      style="margin-top: 10px"
    ></van-field>
    <van-popup v-model:show="show" round position="bottom">
      <van-cascader
        title="请选择所在地区"
        :options="pcasDic"
        @finish="finish"
        :field-names="{
         text: 'name',
        value: 'code',
        children: 'children',
      }"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { deepClone } from '@/utils/util';
import { useVModel } from '@vueuse/core';
import { useAttrs, useFormItem } from 'element-plus';
import useAreaStore from './stores/useAreaStore';
import func from '@/utils/func';

defineOptions({
  name: 'm-xt-area',
  inheritAttrs: false
});


const { formItem } = useFormItem();

const props = defineProps({
  modelValue: {},
  addressPrecision: {
    type: Number,
    default: () => 0
  },
  props: {},
  haveAddress: {
    type: Boolean,
    default: () => true
  },
  unInitData: {
    type: Boolean
  },
  addressDisplay: {
    type: Boolean
  }
});
const emits = defineEmits(['update:modelValue']);
const attrs = useAttrs({
  excludeKeys: computed(() => ['value'])
});
const modelValue = useVModel(props, 'modelValue', emits);

const show = ref(false);

const open = () => {
  show.value = true;
};

const showText = computed({
  get: () => {
    return modelValue.value?.label?.join('/') || '';
  },
  set: v => {
    return '';
  }
});

const finish = ({ value, selectedOptions, tabIndex }) => {
  const label = selectedOptions.map(s => s.name);
  const codes = selectedOptions.map(s => s.code);
  emitValue(label, 'label');
  nextTick(() => {
    emitValue(codes, 'value');
    show.value = false;
  });
};

function validateValue() {
  formItem?.validate?.('change').catch(err => {
    console.log(err);
  });
}


const cleanArea = () => {
  console.log(func.isEmpty(address.value));
  if (func.isEmpty(address.value)) {
    modelValue.value = undefined;
  }else{
    modelValue.value = { address: '', label:[] };
  }

};

const address = computed({
  get: () => {
    const addr = modelValue.value?.address;
    return typeof addr === 'string' ? addr : '';
  },
  set: v => {
    emitValue(v, 'address');
  }
});

const emitValue = (v, key) => {
  modelValue.value = { address: '', ...props.modelValue, [key]: v };
};

// watchPostEffect(() => {
//   if (func.isEmpty(address.value)) {
//     modelValue.value = undefined;
//   }
// });

watch(
  () => modelValue.value,
  () => {
    nextTick(() => {
      validateValue(); // 主动触发校验
    });
  },
  { immediate: true, deep: true }
);

//显示相关
const areaProps = {
  expandTrigger: 'click',
  value: 'code',
  label: 'name',
  ...props.props,
  checkStrictly: true
};

const { getArea } = useAreaStore();
const options = ref([]);

onMounted(async () => {
  if (!props.unInitData) {
    options.value = await getArea();
  }
});

const showAddressPrecision = 3; //显示级数-1

// const showAddress = computed(() => props.addressPrecision > showAddressPrecision);
const showAddress = computed(() => props.addressDisplay);//详细地址是否可见


const initPrecision = (address, precision) => {
  precision--;
  address?.forEach(item => {
    if (precision < 0) {
      delete item.children;
    } else {
      initPrecision(item.children, precision);
    }
  });
};
const pcasDic = computed(() => {
  const result = deepClone(options.value);
  initPrecision(result, showAddress.value ? showAddressPrecision : props.addressPrecision);
  return result;
});
</script>

<style lang="scss">
.m_cascader_area {
  width: 90%;
  overflow: scroll;
}
</style>
