import store from '@/store';

export const vpermission={
    mounted(el,binding,vnode){
        let isShow = store.getters['permission'][binding.value];
        if(!isShow){
            const comment = document.createComment(' ');//创建一个注释节点用来替换
            // 设置value值
            Object.defineProperty(comment, 'setAttribute', {
                value: () => undefined,
            });
            // 用注释节点替换 当前页面元素
            vnode.elm = comment;
            // 下面作为初始化操作 赋值为空
            vnode.text = ' ';
            vnode.isComment = true;
            vnode.context = undefined;
            vnode.tag = undefined;
            //vnode.data.directives = undefined;

            // 判断当前元素是否是组件  如果是组件的话也替换成 注释元素
            if (vnode.componentInstance) {
                vnode.componentInstance.$el = comment;
            }

            // 判断当前元素是否是文档节点或者是文档碎片节点
            if (el.parentNode) {
                // 从 DOM 树中删除 node 节点，除非它已经被删除了。
                el.parentNode.replaceChild(comment, el);
            }
        }
    }
}