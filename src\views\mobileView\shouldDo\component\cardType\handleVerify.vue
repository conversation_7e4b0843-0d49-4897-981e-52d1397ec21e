<script setup>
import handleTaskIcon from '@/images/handle/handleTaskIcon.svg';
import IconComponent from '@/views/banban/handle/components/IconComponent.vue';
import handleTransfer from '@/images/handle/transfer.svg';
import WarningIcon from '@/images/handle/shouldDone-task-warning.svg';
import { stageChangeApi, stageRevokeApi, turnAgree, turnDisagree } from '@/api/banban';
import { ElMessage } from 'element-plus';
import { useHandleData } from '@/hooks/useHandleData';
import { HandleTypeEnum } from '@/components/form-config/const/commonDic';
import { storeToRefs } from 'pinia';
import { useShouldDoStore } from '@/views/mobileView/shouldDo/store/useShouldDoStore';
import { showConfirmDialog } from 'vant';
import { doTaskOffline, doTaskStop } from '@/api/banbanMobile/shouldDo';

const vModel = defineModel();

const tabActiveName = defineModel('tabActiveName');

const emit = defineEmits(['refresh']);

const { openTaskIns, transTaskParams } = storeToRefs(useShouldDoStore());

const router = useRouter();

const colorListProp = {
  1: '#F56C6C',
  2: '#F56C6C',
  3: '#F56C6C',
  4: '#F56C6C',
  5: '#F56C6C'
};

const iconColor = computed(() => {
  return vModel.value.operate
    ? colorListProp[vModel.value.urgency] ?? '#000'
    : colorListProp[vModel.value.urgency];
});

const TaskStatus = {
  Detail: 'Detail',
  Delay: 'Delay',
  Assignment: 'Assignment',
  Offline: 'Offline',
  Stop: 'Stop'
};

const urgencyProp = {
  0: {
    color: '',
    text: '特别紧急'
  },
  1: {
    color: '#F56C6C',
    text: '比较紧急'
  },
  2: {
    color: '#F9862B',
    text: '比较紧急'
  },
  3: {
    color: '#FFBD21',
    text: '一般紧急'
  },
  4: {
    color: '#024FE5',
    text: '不太紧急'
  },
  5: {
    color: '#4CAF1B',
    text: '不紧急'
  }
};
const getUrgency = computed(() => {
  return urgencyProp[vModel.value.urgency];
});

//同意
const agreeHandle = () => {
  turnAgree({ id: vModel.id }).then(() => {
    ElMessage.success('操作成功');
    emit('refresh');
  });
};
//拒绝
const disAgreeHandle = () => {
  turnDisagree({ id: vModel.id }).then(() => {
    ElMessage.success('操作成功');
    emit('refresh');
  });
};
//恢复
const revokeHandle = async () => {
  await useHandleData(stageRevokeApi, { id: vModel.id }, '是否确认恢复', 'warning');
  emit('refresh');
};

//详情
const reToDetail = () => {
  router.push({
    path: '/m/shouldDo/taskInfo',
    query: {
      id: vModel.value.id,
      name: vModel.value.name
    }
  });
};

//应办执行
const reToHandle = () => {
  router.push({
    path: '/m/shouldDo/taskDetail',
    query: {
      handleId: vModel.value.id,
      disabled: vModel.value.agree === 1 || tabActiveName.value === 3 || vModel.value.viewStatus === 2 ? 1 : 0,
      submitType: tabActiveName.value === 2 ? 2 : null,
      type: HandleTypeEnum.HANDLED,
      isControl: 1 // 是否要控制跳转
    }
  });
};


const commandHandle = (value) => {
  switch (value) {
    //调
    case TaskStatus.Delay:
      openTaskIns.value = vModel.value;
      return router.push({
        path: '/m/shouldDo/postpone',
        query: {
          id: vModel.value.id,
          name: vModel.value.taskName
        }
      });
    //转
    case TaskStatus.Assignment:
      transTaskParams.value = {
        row: { ...vModel.value },
        // getTableList: getList,
        api: stageChangeApi
      };
      return router.push({
        path: '/m/shouldDo/transfer',
        query: {
          id: vModel.value.id,
          taskSource: vModel.value.taskSource
        }
      });
    //下
    case TaskStatus.Offline:
      return showConfirmDialog({
        title: '提示',
        message:
          `当前任务为下线可补办任务,任务下线后可在补办中继续填写，确认下线？`
      })
        .then(() => {
          doTaskOffline(vModel.value.id).then(res => {
            if (res.data.success) {
              ElMessage.success(res.data.msg);
              emit('refresh');
            }
          });
        });
    //停
    case TaskStatus.Stop:
      return showConfirmDialog({
        title: '提示',
        message:
          `任务终止后将无法填写，可能会对后续触发任务产生影响，确认终止？`
      })
        .then(() => {
          doTaskStop(vModel.value.id).then(res => {
            if (res.data.success) {
              ElMessage.success(res.data.msg);
              emit('refresh');
            }
          });
        });
  }
};

</script>

<template>
  <div @click="reToHandle">
    <a-flex vertical gap="12" class="card-item">
      <a-flex gap="12" justify="space-between" class="item-tip w-full">
        <div v-if="vModel.urgency">
          <van-tag :color="getUrgency.color">{{ getUrgency.text }}</van-tag>
        </div>
        <div v-else></div>
        <a-flex gap="16" align="center" class="color-#C8C9CC">
          <warning-icon @click.stop="reToDetail" />
          <el-dropdown @command="commandHandle" v-if="
                ![1, 2].includes(vModel.agree) &&
                vModel.viewStatus !== 2 &&
                [0, 1, 2].includes(tabActiveName)
              ">
            <el-icon @click.stop>
              <More />
            </el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :command="TaskStatus.Delay"
                  v-if="[0, 1].includes(tabActiveName)"
                >调
                </el-dropdown-item
                >
                <el-dropdown-item
                  :command="TaskStatus.Assignment"
                  v-if="[0, 1].includes(tabActiveName)"
                >转
                </el-dropdown-item
                >
                <el-dropdown-item
                  :command="TaskStatus.Offline"
                  v-if="[0, 1].includes(tabActiveName) && vModel.manualOffline == 1"
                >下
                </el-dropdown-item
                >
                <el-dropdown-item
                  :command="TaskStatus.Stop"
                  v-if="[0, 1, 2].includes(tabActiveName) && vModel.manualStop == 1"
                >停
                </el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </a-flex>
      </a-flex>
      <a-flex gap="8" align="center" justify="space-between" class="item-title">
        <a-flex align="center">
          <handleTaskIcon />
        </a-flex>
        <div class="flex-1">
          {{ vModel.name }}
        </div>
        <a-flex justify="center" align="center">
          <!-- 任务图标-->
          <icon-component
            :operate="vModel.operate"
            :fill="iconColor"
            v-if="iconColor"
          />
          <handleTransfer v-if="vModel.isHanded == 1" />
        </a-flex>
      </a-flex>
      <a-flex vertical gap="4" class="item-content">
        <div>
          组织来源：{{ vModel.taskSourceName }}
        </div>
        <div>
          场景名称：{{ vModel.groupName }}
        </div>
        <div v-if="vModel.operate === 2">
          转办人：{{ vModel.initiatorName }}
        </div>
        <div v-if="vModel.operate === 5">
          邀请人：{{ vModel.initiatorName }}
        </div>
        <div>
          触发时间：{{ vModel.triggerTime }}
        </div>
      </a-flex>
      <a-flex justify="end" class="px-12px">
        <el-space v-if="vModel.agree === 1">
          <el-button
            size="small"
            type="primary"
            @click.stop="agreeHandle"
          >同意
          </el-button
          >
          <el-button size="small" @click.stop="disAgreeHandle"
          >拒绝
          </el-button
          >
        </el-space>
        <el-button
          v-if="tabActiveName === 3"
          size="small"
          type="default"
          plain
          @click.stop="revokeHandle"
        >恢复
        </el-button
        >
      </a-flex>
    </a-flex>

  </div>
</template>

<style scoped lang="scss">
.card-item {
  margin: 8px 16px;
  padding: 12px 0;
  background: #ffffff;
  border-radius: 8px;

  .item-tip {
    padding: 0 12px;
  }

  .item-title {
    padding: 0 12px;
  }

  .item-content {
    background: #F7F8FA;
    margin: 0 12px;
    padding: 12px 12px;
  }
}
</style>