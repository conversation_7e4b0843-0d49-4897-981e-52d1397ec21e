<template>
  <n-card :content-style="{ padding: '10px', position: 'relative', paddingRight: '60px' }">
    <el-row :gutter="10">
      <el-col :span="16">
        <el-select v-model="data.field" placeholder="请选择字段" class="w-full">
          <el-option
            v-for="item in fieldList"
            :key="item.id"
            :label="item[propLabel]"
            :value="item.id"
          />
        </el-select>
      </el-col>
      <el-col :span="8" v-if="data.field">
        <avue-select
          class="w-full"
          :dic="symbolList"
          v-model="data.conditions"
          value-on-clear=""
          placeholder="请选择规则"
        ></avue-select>
      </el-col>
      <el-col :span="24" v-if="data.conditions">
        <el-input
          v-if="fieldType === 'select' && curRuleType == '2'"
          v-model="data.value"
        ></el-input>
        <component
          v-else
          :is="getFieldComponent(data.conditions, fieldType)"
          v-model="data.value"
          :field="field"
          format="YYYY-MM-DD HH:mm:ss"
          dateFormatType="4"
          class="!w-full"
        ></component>
      </el-col>
    </el-row>
    <slot name="customBtn">
      <el-button type="danger" link class="absolute right-10px top-10px" @click="$emit('delete')"
      >删除
      </el-button>
    </slot>
  </n-card>
</template>
<script setup lang="ts">
import { computed, watch } from 'vue';
import { getFieldComponent, getFieldSymbolDic } from '@/views/mainview/sceneTasks/detail/option/field';
import { MethodSymbolEnum } from '@/views/mainview/sceneTasks/detail/option/field.ts';

export type conditionItem = {
  field?: string;
  conditions?: MethodSymbolEnum;
  value?: string;
  prop:string;
  type: string;
};

const props = defineProps<{
  data: conditionItem;
  propLabel: string;
  fieldList: any;
  curRuleType: string;
}>();
defineEmits(['delete']);


const fieldType = computed(() => {
  if (props.propLabel === 'fieldName') {
    return field.value?.fieldType;
  } else {
    return field.value?.fixType;
  }
});

const field = computed<any>(() => {
  return props.fieldList.find((item: any) => item.id === props.data.field);
});

const symbolList = computed(() => getFieldSymbolDic(fieldType.value));

watch(
  () => props.data.field,
  (v) => {
    if (v) {
      const current = props.fieldList.find((f:any) => f.id === v)
      props.data.prop = current?.prop as string;
      props.data.type = current?.fixType as string;
    }
    delete props.data.conditions;
  }
);

watch(
  () => props.data.conditions,
  () => {
    delete props.data.value;
  }
);
</script>
<style lang="scss" scoped></style>
