import request from '../../axios';

export const page = (current: number, size: number, data: any) => {
  return request({
    url: '/specialPosition/page',
    method: 'post',
    params: {
      current,
      size
    },
    data
  });
};

export const submit = (data: any) => {
  return request({
    url: '/specialPosition/submit',
    method: 'post',
    data
  });
};

export const remove = (ids: string) => {
  return request({
    url: '/specialPosition/remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const getPositionSelect = () => {
  return request({
    url: '/specialPosition/select',
    method: 'post'
  });
};