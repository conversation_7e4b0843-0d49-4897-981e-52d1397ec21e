<template>
  <el-icon size="22" color="#606266" @click="dialogVisible = true"><FullScreen /></el-icon>

  <el-dialog
    v-model="dialogVisible"
    fullscreen
    top="40vh"
    width="70%"
    draggable
    @close="updateData"
  >
    <XtDynamic
      ref="xtDynamic" 
      v-bind="props.item"
      :item
      :source
      :itemForm
      :tableData
      :col
      :itemProp="[props.item.prop]" 
      v-model="vModel" 
      height="calc(100vh - 200px)"
      :showFull="false" 
    >
    </XtDynamic>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import XtDynamic from '../XTDynamic.vue';
import { AvueForm,AvueColumns,DynamicField} from '@/components/form-config/types/AvueTypes';

const props = withDefaults(
  defineProps<{
    item: AvueColumns & DynamicField;
    source?: number//组件的使用场景 1中间的组件配置 2 组件的默认值设置 3 预览 4 应办
    col:AvueColumns[];
    unInitData?:boolean;
    itemForm: AvueForm;
    tableData?: any[],
    updateData: () => void,//视图不更新，刷新一下
  }>(),
  {

  }
);

const vModel = defineModel<AvueForm[]>();
const xtDynamic = ref<any>(null);


const dialogVisible = ref(false)

watch(
  () => dialogVisible.value,
  (val) => {
    if (val) {
     
      xtDynamic.value!.updateData()
    }
  }
)
</script>