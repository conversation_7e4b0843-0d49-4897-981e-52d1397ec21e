<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 38</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-230.000000, -955.000000)">
            <g id="编组-38" transform="translate(230.000000, 955.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <text id="MIN函数可以获取一组数值的最小值。-·" font-family="PingFangSC-Regular, PingFang SC"
                      font-size="14" font-weight="normal" line-spacing="23">
                    <tspan x="10" y="57" fill="#3F70FF">MIN</tspan>
                    <tspan x="35.732" y="57" fill="#3A3A3A">函数可以获取一组数值的最小值。</tspan>
                    <tspan x="10" y="81" fill="#3A3A3A">·用法：</tspan>
                    <tspan x="59" y="81" fill="#3F70FF">MIN</tspan>
                    <tspan x="84.732" y="81" fill="#3A3A3A">(数字1,数字2,...)</tspan>
                    <tspan x="10" y="105" fill="#3A3A3A">·示例：</tspan>
                    <tspan x="59" y="105" fill="#3F70FF">MIN</tspan>
                    <tspan x="84.732" y="105" fill="#3A3A3A">(</tspan>
                </text>
                <rect id="矩形" fill="#EAF3FF" x="90" y="94" width="52" height="16"></rect>
                <text id="语文成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10" font-weight="normal"
                      line-spacing="16" fill="#3F70FF">
                    <tspan x="96" y="105">语文成绩</tspan>
                </text>
                <rect id="矩形" fill="#EAF3FF" x="151" y="94" width="52" height="16"></rect>
                <text id="数学成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10" font-weight="normal"
                      line-spacing="16" fill="#3F70FF">
                    <tspan x="157" y="105">数学成绩</tspan>
                </text>
                <rect id="矩形" fill="#EAF3FF" x="212" y="94" width="52" height="16"></rect>
                <text id="英语成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10" font-weight="normal"
                      line-spacing="16" fill="#3F70FF">
                    <tspan x="218" y="105">英语成绩</tspan>
                </text>
                <text id="，" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="144" y="108">，</tspan>
                </text>
                <text id="，" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="205" y="108">，</tspan>
                </text>
                <text id="）" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="270" y="107">）</tspan>
                </text>
                <text id="返回" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="279" y="107">返回</tspan>
                </text>
                <text id="三门课程中的最低分。" font-family="PingFangSC-Regular, PingFang SC" font-size="14"
                      font-weight="normal" fill="#3A3A3A">
                    <tspan x="13" y="130">三门课程中的最低分。</tspan>
                </text>
                <text id="MIN" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">MIN</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
