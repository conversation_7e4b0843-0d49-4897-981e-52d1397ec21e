<template>
  <el-form-item label="字段权限">
    <div class="flex flex-col">
      <el-checkbox v-model="data.display" label="可见" />
      <template v-if="isOperate">
        <el-checkbox :disabled="isDisabled" v-model="edit" label="可编辑" v-if="showEdit" />
        <el-checkbox v-model="data.isRadio" label="此项是单选" v-if="showRadio && hasRadio"/>
        <el-checkbox :disabled="data.isDynamic && !dynamicSet?.required" v-model="data.required" label="此项为必填项" v-if="hasRequired" />
        <!-- <el-checkbox v-model="data.showPath" label="路径可见" v-if="showPath" /> -->
      </template>
      <el-checkbox v-model="data.quality" :disabled="data.required" label="质量标识" v-if="haveQuility && taskPackageId">
        质量标识
        <el-tooltip
          class="box-item"
          effect="dark"
          content="勾选后，将纳入到任务完备率统计"
          placement="top-start"
        >
          <el-icon><QuestionFilled /></el-icon>
        </el-tooltip>
      </el-checkbox>
      <el-checkbox v-if="!data.isDynamic && formType === formTypeEnum.APPLICATION" v-model="data.print" label="可打印" />
      <slot />
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { configPermissionType } from './type';
import { DynamicAllField } from '@/components/form-config/types/AvueTypes'
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic.ts';
import { canQuilityField,canRadioField } from '@/components/form-config/types/FieldTypes';
import { PackageIdInjectKey } from '@/components/form-config/utils/injectKeys';
import { findParentById }  from '@/utils/field'
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore.ts';
import { formTypeEnum } from '@/components/form-config/const/commonDic';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { notRequiredType } from '@/components/form-config/types/FieldTypes.ts';

const props = withDefaults(
  defineProps<
    configPermissionType
  >(),
  {
    hasRequired: () => true,
    showEdit: () => true,
    showQuality: () => true,
    showPath:() => false,
    showRadio:() => true
  }
);

const taskPackageId = inject(PackageIdInjectKey);

const allColumn = inject('allColumn', {column: []})

const dynamicSet = ref<any>()

const { hides } = storeToRefs(useTaskDetailStore());

const formType = inject('formType', null)

const isDisabled = computed(() => {
  return (props.data.isDynamic && dynamicSet.value?.disabled) || 
  props.data.defaultType === DefaultTypeEnum.SIGN//选了实名签名不可编辑
});

//是否展示可编辑/必填 知识库/公式编辑/时间组件-触发时间
const isOperate = computed(() => {
  return !_.includes([DefaultTypeEnum.FORMULA, DefaultTypeEnum.TRIGGERTIME],props.data.defaultType) &&
  formType!== formTypeEnum.KNOWLEDGE
});


watch(
  () => allColumn,
  () => {
    dynamicSet.value = props.data.isDynamic? findParentById(allColumn?.column, props.data.id)?.parent : null
  },
  {
    deep: true,
    immediate:true
  }
)

//应办来源
watch(
  () => formType, 
  (value) => {
    if(value === formTypeEnum.KNOWLEDGE){
      props.data.required = false
    }
  },
  { immediate: true}
)


const edit = computed({
  get: () => {
    return !props.data.disabled;
  },
  set: v => {
    props.data.disabled = !v;
    if(props.data.fixType === FixTypeEnum.DYNAMIC){
      (props.data as DynamicAllField)?.children.forEach((item:any)=>{
        if(!(_.includes(notRequiredType, item.fixType) && item.defaultType === DefaultTypeEnum.SIGN)) item.disabled = !v
      })
    }
  },
});

// 要求：必填的时候，质量标识必须选上

watch(
  ()=>props.data.quality,
  (value)=>{
    if(props.data.fixType === FixTypeEnum.DYNAMIC){
     (props.data as DynamicAllField)?.children.forEach((item:any)=>{
      if(!_.includes(notRequiredType, item.fixType) && item.defaultType !== DefaultTypeEnum.FORMULA) item.quality = value
      })
    }
  },
  {
    immediate:true
  }
)


// 要求：当必填时，质量也得选上 7/12
watch(
  ()=>props.data.required,
  (value)=>{
    if(value){
      props.data.quality = true;
    }
    if(props.data.fixType === FixTypeEnum.DYNAMIC){
     (props.data as DynamicAllField)?.children.forEach((item:any)=>{
        if(!_.includes(notRequiredType, item.fixType) && item.defaultType !== DefaultTypeEnum.FORMULA) item.required = value
        if(value && !_.includes(notRequiredType, item.fixType) && item.defaultType !== DefaultTypeEnum.FORMULA) item.quality = true;
      })
    }
  },
  {
    immediate:true
  }
)

// 要求：当从可见变为不可见时，删除显隐里面所有 对他的显示 10/29
watch(
  () => props.data.display,
  (value) => {
    if(!value){
      const hidesData = _.cloneDeep(hides.value) as any;
      hidesData.groups?.forEach((field:any) => {
        field.display = field.display.filter((i:any) => props.data.id !== i)
      })
      hides.value = hidesData;
    }
  }
)

const haveQuility = computed(
  () => props.showQuality && _.includes(canQuilityField, props.data.fixType)
);
const hasRadio = computed(():Boolean => props.showRadio && _.includes(canRadioField, props.data.fixType))
</script>

<style scoped lang="scss"></style>
