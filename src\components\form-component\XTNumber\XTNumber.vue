<template>
  <div>{{vModel}}</div>---
  <div>{{vComputed}}</div>
  <el-input
    v-bind="$attrs"
    :disabled="disabled"
    v-model="vComputed"
    @focus="handleFocus"
    @blur="handleBlur"
    type="text"
  >
    <template #suffix v-if="numberFormat === NumberFormatTypeEnum.PERCENT">%</template>
  </el-input>
</template>

<script setup lang="ts">
import { formatValue, valueFormat } from '@/components/form-config/utils';
import { NumberField } from '@/components/form-config/types/AvueTypes';
import { NumberFormatTypeEnum } from '@/components/form-config/const/commonDic'

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<NumberField & { disabled?: boolean }>();

const vModel = defineModel<string | number>();

const vComputed = computed({
  get: () => {
    // 禁用状态直接格式化，并同步更新 vModel
    if (props.disabled) {
      const v = valueFormat(vModel.value, props);
      const formatted = formatValue(v, props);
      return formatted;
    }
    // 非禁用状态保持原有逻辑
    if (!isFocus.value) {
      return formatValue(vModel.value, props);
    } else {
      return vModel.value ?? '';
    }
  },
  set: v => {
    let newValue = v;
    if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
      // 百分比转换为实际值，不需要移除百分号
      newValue = parseFloat(v) / 100;
    }
    vModel.value = newValue;
  },
});

let isFocus = ref(false);

//获得焦点时处理
const handleFocus = () => {
  isFocus.value = true;
};

//失去焦点时处理
const handleBlur = (e: any) => {
  isFocus.value = false;
  if (props.disabled) return;
  vModel.value = valueFormat(e.target.value, props);
};
</script>

<style scoped lang="scss"></style>
