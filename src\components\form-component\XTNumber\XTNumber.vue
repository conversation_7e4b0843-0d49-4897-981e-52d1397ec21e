<template>
  <div>{{}}</div>
  <el-input
    v-bind="$attrs"
    :disabled="disabled"
    v-model="vComputed"
    @focus="handleFocus"
    @blur="handleBlur"
    type="text"
  >
    <template #suffix v-if="numberFormat === NumberFormatTypeEnum.PERCENT">%</template>
  </el-input>
</template>

<script setup lang="ts">
import { formatValue, valueFormat } from '@/components/form-config/utils';
import { NumberField } from '@/components/form-config/types/AvueTypes';
import { NumberFormatTypeEnum } from '@/components/form-config/const/commonDic'

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<NumberField & { disabled?: boolean }>();

const vModel = defineModel<string | number>();

const vComputed = computed({
  get: () => {
    // 禁用状态直接格式化，并同步更新 vModel
    if (props.disabled) {
      const v = valueFormat(vModel.value, props);
      if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
        const numValue = typeof v === 'number' ? v : parseFloat(v as string) || 0;
        const percentValue = numValue * 100;
        return formatValue(percentValue, props, false);
      } else {
        return formatValue(v, props, false);
      }
    }
    // 非禁用状态保持原有逻辑
    if (!isFocus.value) {
      // 百分比模式下显示时需要转换为百分比显示
      if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
        const numValue = typeof vModel.value === 'number' ? vModel.value : parseFloat(vModel.value as string) || 0;
        const percentValue = numValue * 100;
        return formatValue(percentValue, props, false);
      } else {
        return formatValue(vModel.value, props, false);
      }
    } else {
      // 聚焦时显示原始输入值
      if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
        const numValue = typeof vModel.value === 'number' ? vModel.value : parseFloat(vModel.value as string) || 0;
        return (numValue * 100).toString();
      } else {
        return vModel.value?.toString() ?? '';
      }
    }
  },
  set: v => {
    if (!v || v === '') {
      vModel.value = undefined;
      return;
    }

    let inputValue = parseFloat(v as string);

    // 处理无效输入
    if (isNaN(inputValue)) {
      vModel.value = undefined;
      return;
    }

    if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
      // 百分比模式下的输入限制
      const maxDecimalPlaces = props.showPrecision ? props.precision + 2 : 2;

      // 检查小数位数是否超过限制
      const decimalPlaces = (v.toString().split('.')[1] || '').length;
      if (decimalPlaces > maxDecimalPlaces) {
        // 截断到允许的小数位数
        inputValue = parseFloat(inputValue.toFixed(maxDecimalPlaces));
      }

      // 转换为实际值（除以100）
      vModel.value = inputValue / 100;
    } else {
      vModel.value = inputValue;
    }
  },
});

let isFocus = ref(false);

//获得焦点时处理
const handleFocus = () => {
  isFocus.value = true;
};

//失去焦点时处理
const handleBlur = (e: any) => {
  isFocus.value = false;
  if (props.disabled) return;

  let inputValue = e.target.value;
  if (inputValue === '') {
    vModel.value = undefined;
    return;
  }

  if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
    // 百分比模式下，输入的是百分比值，需要转换为实际值
    let numValue = parseFloat(inputValue);
    if (!isNaN(numValue)) {
      // 限制小数位数
      const maxDecimalPlaces = props.showPrecision ? props.precision + 2 : 2;
      const decimalPlaces = (inputValue.split('.')[1] || '').length;
      if (decimalPlaces > maxDecimalPlaces) {
        numValue = parseFloat(numValue.toFixed(maxDecimalPlaces));
      }

      // 转换为实际值并应用valueFormat验证
      const actualValue = numValue / 100;
      vModel.value = valueFormat(actualValue, props);
    } else {
      vModel.value = undefined;
    }
  } else {
    vModel.value = valueFormat(inputValue, props);
  }
};
</script>

<style scoped lang="scss"></style>
