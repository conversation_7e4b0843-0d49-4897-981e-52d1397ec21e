<template>
  <div>{{vModel}}</div>--
  <div>{{vComputed}}</div>
  <el-input
    v-bind="$attrs"
    :disabled="disabled"
    v-model="vComputed"
    @focus="handleFocus"
    @blur="handleBlur"
    type="text"
  >
    <template #suffix v-if="numberFormat === NumberFormatTypeEnum.PERCENT">%</template>
  </el-input>
</template>

<script setup lang="ts">
import { formatValue, valueFormat } from '@/components/form-config/utils';
import { NumberField } from '@/components/form-config/types/AvueTypes';
import { NumberFormatTypeEnum } from '@/components/form-config/const/commonDic'

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<NumberField & { disabled?: boolean }>();

const vModel = defineModel<string | number>();

const vComputed = computed({
  get: () => {
    // 禁用状态直接格式化，并同步更新 vModel
    if (props.disabled) {
      const v = valueFormat(vModel.value, props);
      if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
        const numValue = typeof v === 'number' ? v : parseFloat(v as string) || 0;
        const percentValue = numValue * 100;
        return formatValue(percentValue, props, false);
      } else {
        return formatValue(v, props, false);
      }
    }
    // 非禁用状态保持原有逻辑
    if (!isFocus.value) {
      // 百分比模式下显示时需要转换为百分比显示
      if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
        const numValue = typeof vModel.value === 'number' ? vModel.value : parseFloat(vModel.value as string) || 0;
        const percentValue = numValue * 100;
        return formatValue(percentValue, props, false);
      } else {
        return formatValue(vModel.value, props, false);
      }
    } else {
      // 聚焦时返回临时输入值
      return inputValue.value;
    }
  },
  set: v => {
    // 更新临时输入值
    inputValue.value = v?.toString() ?? '';

    // 在输入过程中进行基本验证
    if (!v || v === '') {
      vModel.value = undefined;
      return;
    }

    const inputStr = v.toString();

    // 基本格式验证：只允许数字和一个小数点
    if (!/^\d*\.?\d*$/.test(inputStr)) {
      return; // 不更新，保持之前的值
    }

    // 百分比模式下的小数位数限制
    if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
      const maxDecimalPlaces = props.showPrecision ? props.precision + 2 : 2;
      const decimalPlaces = (inputStr.split('.')[1] || '').length;
      if (decimalPlaces > maxDecimalPlaces) {
        return; // 不允许超过限制的输入
      }
    }

    // 只有当输入是完整数字时才更新vModel
    const numValue = parseFloat(inputStr);
    if (!isNaN(numValue)) {
      if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
        vModel.value = numValue / 100;
      } else {
        vModel.value = numValue;
      }
    }
  },
});

let isFocus = ref(false);
let inputValue = ref('');

//获得焦点时处理
const handleFocus = () => {
  isFocus.value = true;
  // 初始化临时输入值
  if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
    const numValue = typeof vModel.value === 'number' ? vModel.value : parseFloat(vModel.value as string) || 0;
    const percentValue = numValue * 100;
    // 处理浮点数精度问题
    const maxDecimalPlaces = props.showPrecision ? props.precision + 2 : 2;
    inputValue.value = percentValue.toFixed(maxDecimalPlaces).replace(/\.?0+$/, '');
  } else {
    inputValue.value = vModel.value?.toString() ?? '';
  }
};

//失去焦点时处理
const handleBlur = (e: any) => {
  isFocus.value = false;
  if (props.disabled) return;

  let blurInputValue = e.target.value;
  if (blurInputValue === '') {
    vModel.value = undefined;
    return;
  }

  if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
    // 百分比模式下，输入的是百分比值，需要转换为实际值
    let numValue = parseFloat(blurInputValue);
    if (!isNaN(numValue)) {
      // 限制小数位数
      const maxDecimalPlaces = props.showPrecision ? props.precision + 2 : 2;
      const decimalPlaces = (blurInputValue.split('.')[1] || '').length;
      if (decimalPlaces > maxDecimalPlaces) {
        numValue = parseFloat(numValue.toFixed(maxDecimalPlaces));
      }

      // 转换为实际值并应用valueFormat验证
      const actualValue = numValue / 100;
      vModel.value = valueFormat(actualValue, props);
    } else {
      vModel.value = undefined;
    }
  } else {
    vModel.value = valueFormat(blurInputValue, props);
  }
};
</script>

<style scoped lang="scss"></style>
