<template>
  <div class="field pl-0 overflow-hidden">
    <el-collapse v-model="fieldsTitles">
      <el-collapse-item :title="field.title" :name="field.title" v-for="field in fields">
        <template #title>
          <slot name="header" :data="field">{{ field.title }}</slot>
        </template>
        <div>
          <draggable
            class="field-group"
            tag="ul"
            :list="field.list"
            :disabled="configDisabled"
            :group="{ name: 'form', pull: 'clone', put: false }"
            ghost-class="ghost"
            :sort="false"
            item-key="label"
          >
            <template #item="{ element }: { element: FieldBaseField }">
              <li
                class="field-item"
                @click="configDisabled ? undefined : $emit('field-click', element)"
              >
                <el-tooltip :content="element.typeLabel">
                  <div class="field-item-container">
                    <component :is="Icons[element.iconPath as keyof typeof Icons]" class="icon" />
                    <el-text truncated>{{ element.copyLabel }}</el-text>
                  </div>
                </el-tooltip>
              </li>
            </template>
          </draggable>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue';
import Draggable from 'vuedraggable';
import { DesignField, FieldBaseField } from '../types/AvueTypes';
import Icons from '@/images/formDesignFieldIcon';
// Props
const props = defineProps<{
  fields: DesignField[];
  type?: number; //1：模板
}>();

// Emits
defineEmits(['field-click']);

const configDisabled = inject('disabled', false);

const fieldsTitles = props.fields.map(v => v.title);

// Icon
const { proxy } = getCurrentInstance()!;
(<any>proxy).loadScript('css', 'https://at.alicdn.com/t/font_1254447_zc9iezc230c.css');
</script>

<style lang="scss" scoped>
@import '@/components/form-config/styles/theme.scss';

.field {
  background: #fff;
  padding: 20px;
  @apply grid grid-cols-1 gap-y-30px;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #3a3a3a;
    line-height: 22px;
    margin-bottom: 20px;
  }

  .field-group {
    @apply grid grid-cols-2 gap-4 p-0 m-0;

    .field-item {
      $field_text_color: #464646;
      box-sizing: border-box;
      height: 35px;
      border: 1px solid #e6eaf0;
      cursor: move;
      background: #f9fafc;
      border-radius: 4px;
      list-style: none;
      margin: 1px;

      &-container {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        padding-left: 10px;
        padding-right: 5px;

        .el-text {
          font-size: 14px;
          color: $field_text_color;
          font-weight: 400;
        }
      }

      &:hover {
        span {
          color: var(--el-color-primary);
        }

        .icon :deep(path) {
          @apply text-#{var(--el-color-primary)};
        }

        outline: 1px solid #c1d1ff;
        background: #f0f8ff;
      }

      .icon {
        width: 18px;
        height: 18px;
        margin-right: 5px;

        :deep(path) {
          @apply fill-current text-#{$field_text_color};
        }
      }
    }
  }
}
</style>
