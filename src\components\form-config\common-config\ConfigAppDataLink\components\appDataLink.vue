<template>
    <!-- 筛选条件 -->
    <n-card class="mb-2">
        <h3>筛选条件</h3>
        <div>
          {{linkItem}}
            <el-form-item label="选择推数路径" required>
                <selectTask
                    v-model:taskId="linkItem.linkagePath"
                    v-model:fieldList="pushFieldList"
                    :taskList="taskList"
                    @click="handleChangeTask1()"
                />
            </el-form-item>
            <accordTitle
                title="添加条件"
                v-model="linkItem.logicBetween"
                @add="handleAdd"
             />
            <filterConditionItemForApp
                class="mb-3"
                v-for="(filterColumn, index) in linkItem.microAppFilter"
                :key="filterColumn.idx"
                :filterColumn="filterColumn"
                v-model:linkTaskId="linkItem.linkageTaskId"
                v-model:pushTaskId="linkItem.linkagePath"
                :conditionTaskId="taskId"

                :data="data"
                :taskList="taskList"
                v-model:pushFieldList="pushFieldList"
                v-model:linkFieldList="linkFieldList"
                @delete="handleDelete(index)"
            />
<!--          :pathType="linkItem.linkageField!.pathType"-->
        </div>

    </n-card>
    <!-- 极值规则 -->
<!--  :pathType="linkItem.linkageField!.pathType"-->
    <extremeRuleForApp
        v-model="linkItem.microAppExtreme"
        :fieldList="pushFieldList"
        >
    </extremeRuleForApp>

    <!--子表单才显示匹配-->
    <n-card class="mt-10" v-if="data.fixType === 'dynamic' && linkItem.linkageProp" >
      <h3>联动字段</h3>
      <!--           :pathType="linkItem.linkageField!.pathType"-->
      <matchFieldForApp
        v-model:field="linkItem.linkageProp"
        :linkage="false"
        :data="data"
        :fieldList="pushFieldList">
      </matchFieldForApp>
      <template v-if="data.fixType === 'dynamic' && linkItem.linkageProp" >
        <addDynamicLink
          title="添加子表单下的字段绑定"
          :data="data"
          :taskList="taskList"
          v-model:pushTaskId="linkItem.linkagePath"
          :pushDynamicField="dynamicFieldList"
          v-model:dynamicFields="linkItem.dynamicField"
        >
        </addDynamicLink>
      </template>
    </n-card>

    <el-row :gutter="10" class="m-16px px-30px" v-else>
      <el-col :span="8">
        <el-input v-model="currentFieldLabel" disabled></el-input>
      </el-col>
      <el-col :span="2"> = </el-col>
      <el-col :span="8">
        <avue-select
          class="!w-full"
          :dic="fieldList2"
          :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }"
          v-model="linkItem.linkageProp"
          placeholder="请选择字段"
        ></avue-select>
      </el-col>
    </el-row>
</template>

<script setup lang="ts">
import matchFieldForApp from './matchFieldForApp.vue'
import { FieldBaseField, appDataLinkField,AvueColumns } from '@/components/form-config/types/AvueTypes';
import selectTask from '../../components/selectTask.vue';
import filterConditionItemForApp from './filterConditionItemForApp.vue';
import accordTitle from '../../components/accordTitle/index.vue';
import extremeRuleForApp from './extremeRuleForApp.vue';
import { dataType } from '@/api/interface/configDataLink.ts';
import { ElMessage } from 'element-plus';
import { dataLinkCurrentMatchFieldApp} from '@/components/form-config/types/FieldTypes.ts';
import useFieldList from '@/views/mainview/sceneTasks/detail/hooks/useFieldList.ts';
import { pushTaskListType } from '@/api/interface/task'
import addDynamicLink from '../../components/addDynamicLink/index.vue';
import { generateId } from '@/components/form-config/utils/index.js';
import { DynamicAllField } from '@/components/form-config/types/AvueTypes';


const props = defineProps<{
    taskId: string;
    data: FieldBaseField & appDataLinkField;
    taskList:pushTaskListType[]
}>();

const currentFieldLabel = computed(()=>{
  return props.data.copyLabel;
})

const linkItem = defineModel<dataType>('linkItem', { default: ()=>({microAppExtreme:{},linkagePath:""}) });
const pushFieldList = ref<AvueColumns[]>([])
const linkFieldList = ref<AvueColumns[]>([])

const dynamicFieldList = computed(() => {
  const filteredItem = pushFieldList.value.find((item:AvueColumns) => item.prop === linkItem.value.linkageProp);
  return filteredItem ? (filteredItem as DynamicAllField).children : [];
});

//推数路径改动清除选项
const handleChangeTask1 = () => {
  console.log('ddddddd');
  console.log('ddddddd',linkItem.value);
  linkItem.value.linkageProp= '';
  linkItem.value.dynamicField= [];
  linkItem.value.microAppFilter = [{
    idx: generateId(),
    dataProp: '',
    currentFormProp:'',
    filterType: "CURRENT_FORM_FIELD",
    operator:'',
    customize: ''
  }]
  linkItem.value.microAppExtreme = {}
  linkItem.value.logicBetween = '1'
};

const handleAdd = () => {
    if(!linkItem.value.linkagePath){
        ElMessage.warning('请先选择推数路径');
        return
    }
    if(!linkItem.value.logicBetween){
        ElMessage.warning('请先选择条件的逻辑关系(且/或)');
        return
    }

    if (_.isArray(linkItem.value.microAppFilter)) {
    const haveEmpty = linkItem.value.microAppFilter?.some(
      i =>
        _.isEmpty(i.dataProp) ||
        _.isEmpty(i.operator)
    );
    if (haveEmpty) {
      ElMessage.warning('有空条目请勿添加新的条目');
      return;
    }
  }
    // if (_.isArray(linkItem.value.microAppFilter)) {
    //     const haveEmpty = linkItem.value.microAppFilter?.some(i => _.isEmpty(i.dataProp) || _.isEmpty(i.operator) || (_.isEmpty(i.linkageProp) && _.isEmpty(i.currentFormProp)));
    //     if (haveEmpty) {
    //     ElMessage.warning('有空条目请勿添加新的条目');
    //     return;
    //     }
    // }
    (linkItem.value.microAppFilter ??= []).push({dataProp:'',filterType:'CURRENT_FORM_FIELD',idx: generateId()});
}

const handleDelete = (index: number) => {
    linkItem.value.microAppFilter?.splice(index, 1);
};

//字段
const { getFiledList } = useFieldList();

getFiledList({
  id:  props.taskId || '',
  unFlatDynamic: false,
  changeToAble: true,
  unFlatDataCollect: false,
  unFlatDataSelect: false,
  unFlatKnowledgeSelect: false,
});

const fieldList2 = computed(() => {//最终选择的当前筛选出来的微应用表单的字段 eg:去除自己
  let column = pushFieldList.value as AvueColumns[] || [];
  const arr = column.map(v => {
    return {
      ...v,
      disabled: false,
    };
  });
  const sameTypeArr =  arr.filter(item => {
    if(linkItem.value.linkagePath==props.taskId){//当前微应用去除自己
      return item.fixType===props.data.fixType&&item.id!=props.data.id
    }
    return item.fixType===props.data.fixType
  })
  return sameTypeArr.filter(item => _.includes(dataLinkCurrentMatchFieldApp, item.fixType));
});

</script>
