import { ChartTypeEnum } from '../types';
import ChartImages from '../assets/charts';

export const ChartsDic = [
  {
    value: ChartTypeEnum.TEXT,
    label: ChartImages[ChartTypeEnum.TEXT],
    remark: ['指标图', '0个维度,1个指标']
  },
  {
    value: ChartTypeEnum.PIE,
    label: ChartImages[ChartTypeEnum.PIE],
    remark: ['饼图', '1个维度,1个指标']
  },
  {
    value: ChartTypeEnum.COLUMN,
    label: ChartImages[ChartTypeEnum.COLUMN],
    remark: ['柱形图', '1个维度,1个或多个指标', '2个维度,1个指标']
  },
  {
    value: ChartTypeEnum.BAR,
    label: ChartImages[ChartTypeEnum.BAR],
    remark: ['条形图', '1个维度,1个或多个指标', '2个维度,1个指标']
  },
  {
    value: ChartTypeEnum.LINE,
    label: ChartImages[ChartTypeEnum.LINE],
    remark: ['折线图', '1个维度,1个或多个指标', '2个维度,1个指标']
  },
  {
    value: ChartTypeEnum.TABLE,
    label: ChartImages[ChartTypeEnum.TABLE],
    remark: ['表格', '多个指标']
  }
];