import request from '@/axios';

// 信息审核分页
export const examinePage = (params: { current: number; size: number }) => {
  return request({
    url: '/dept_invite_verify_record/page',
    method: 'get',
    params,
  });
};

export const examineResult = (data: { agreeStatus: number; id: string }) => {
  return request({
    url: '/dept_invite_verify_record/verify/result',
    method: 'put',
    data,
  });
};
// 微应用分页
export const getUserWaitVerifyPage = (params: { current: number; size: number;actorId:string }) => {
  return request({
    url: '/micro-app-common/getUserWaitVerifyPage',
    method: 'get',
    params,
  });
};
// 微应用表单数据
export const initAppformInfo = (params: { instanceId: string }) => {
  return request({
    url: '/micro-app-form-data-model/info',
    method: 'get',
    params,
  });
};
// 微应用提交/退回/拒绝
export const appVerify = (data: {
  formDataList?:any;
  flwTaskId: string;
  instanceId: string;
  verifyStatus: 0 | 1 | 2;
  remark:string;
  signature?:string;
  microAppId:string;
}) => {
  return request({
    url: '/microApp/verify',
    method: 'put',
    data,
  });
};
// 微应用表单权限
export const getFlwTaskPermissionVO = (params: { microAppId: string;instanceId:string;flwTaskId:string }) => {
  return request({
    url: '/micro-app-common/getFlwTaskPermissionVO',
    method: 'get',
    params,
  });
};
// 微应用表单数据
export const getFlwFormInfo = (params: { instanceId:string; }) => {
  return request({
    url: '/micro-app-form-data-model/info',
    method: 'get',
    params,
  });
};
// 微应用获取用户信息
export const getUserInfo = (params: { tenantId:string;userId:string }) => {
  return request({
    url: '/userTenant/getUserInfo',
    method: 'get',
    params,
  });
};



