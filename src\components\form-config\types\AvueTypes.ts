import { DataCollectType, dataLinksType, dataType } from '@/api/interface/configDataLink.ts';
import { DefaultTypeEnum, DynamicTypeEnum } from '@/components/form-config/const/commonDic.ts';
// import { DesignFieldType } from './field1'
import { Mark } from '@/types/CodemirrorTypes.ts';
import { FixTypeEnum, SystemTypeEnum } from '@/components/form-config/types/field.ts';
export * from '../field-config';
export * from '../common-config';
export * from '../field-config';
import {
  InputField,
  DateField,
  DateRangeField,
  NumberField,
  BasicInfoField,
  CalculationTable,
  AddressField,
  DepartAdminType,
  DynamicField,
  FormulaEditType,
  SelectField,
  StatisticalType,
  TargetValueType,
  TipsField,
  UploadField,
  UserDeptField,
  UsersField,
  UserTagField,
  WorkingAdminType,
  PhotoField,
} from '../field-config';
import {
  InputAllField,
  DateAllField,
  DateRangeAllField,
  NumberAllField,
  BasicInfoAllField,
  CalculationTableAllType,
  AddressAllField,
  DepartAdminAllType,
  DividerAllField,
  DynamicAllField,
  FormulaEditAllType,
  getUserCodeAllType,
  InterspaceAllType,
  InventoryComAllType,
  PhotoAllField,
  SelectAllField,
  SignatureAllField,
  SignatureCollectAllField,
  StatisticalAllType,
  TargetValueAllType,
  TipsAllField,
  UploadAllField,
  UserDeptAllField,
  UsersAllField,
  UserTagAllField,
  WorkingAdminAllType,
  MulSelectAllField,
  MulSelectField,
  InterspaceType,
} from '../field-config';

import { DateFormatTypeEnum, InputType, SelectColumnTypeEnum } from '../const/commonDic';
import { groupSummaryField, DataSelection } from '../common-config';

export type AvueColumns =
  | InputAllField
  | DateAllField
  | DateRangeAllField
  | NumberAllField
  | BasicInfoAllField
  | CalculationTableAllType
  | AddressAllField
  | DepartAdminAllType
  | DividerAllField
  | DynamicAllField
  | FormulaEditAllType
  | getUserCodeAllType
  | InterspaceAllType
  | InventoryComAllType
  | PhotoAllField
  | SelectAllField
  | MulSelectAllField
  | SignatureAllField
  | SignatureCollectAllField
  | StatisticalAllType
  | TargetValueAllType
  | TipsAllField
  | UploadAllField
  | UserDeptAllField
  | UsersAllField
  | UserTagAllField
  | WorkingAdminAllType;

export type DesignFieldType =
  | InputField
  | DateField
  | DateRangeField
  | NumberField
  | BasicInfoField
  | CalculationTable
  | AddressField
  | DepartAdminType
  | DynamicField
  | FormulaEditType
  | SelectField
  | MulSelectField
  | StatisticalType
  | TargetValueType
  | TipsField
  | UploadField
  | PhotoAllField
  | UserDeptField
  | UsersField
  | UserTagField
  | WorkingAdminType
  | InventoryComAllType
  | InterspaceType
  | SignatureCollectAllField
  | DataSelection;

/*
====================================================================================
组件基础属性
====================================================================================
*/

export type AvueForm = Record<string, any>;

export type CommonField = {
  span: number;
  display: boolean;
  showLabel: boolean;
  defaultType: DefaultTypeEnum | DynamicTypeEnum;
  defaultValue?: any;
  visibleBool: boolean;
};

//组件基础属性
export type BaseField = {
  fixType: FixTypeEnum | SystemTypeEnum; // fixType是因为组件库的几个组件改变显示格式的时候，type会变化，导致有些地方匹配不到这些组件，现加入固定类别
  copyLabel: string;
  typeLabel: string;
  iconPath: string;
  component?: string;
};

export type MapField = {
  id: string;
  prop: string;
  isDynamic?: boolean;
  isForm?: boolean;
  isCrud?: boolean;
};

//微应用基础字段
export type microAppCommonField = {
  print: boolean;
};

//字段权限-可编辑
export type disableType = {
  disabled: boolean;
};

//字段权限-可编辑/必填
export type requireType = {
  required: boolean;
};
export type permissionType = disableType & requireType;

//字段权限-质量标识
export type qualityType = {
  quality: boolean;
};

export type basePermissionType = permissionType & qualityType;

//是否单选
export type singleType = {
  isRadio: boolean;
};

//是否可清空
export type clearableType = {
  clearable: boolean;
};

//字段库和普通组件的区别属性
export type templateField = {
  isTemplate: boolean;
  mid: string;
  templateId: string;
};

//type 文本/日期/日期范围/选择等组件用到
export type SpecificType = {
  type: DateFormatTypeEnum | InputType | SelectColumnTypeEnum;
};

//成员选择/部门/工作组/空间/物品值的类型
export type dataValueType = {
  dataType: 'array' | 'string';
};

//提示
export type TitleField = {
  labelTip?: string;
};

//备注-可导入表格的组件可添加备注
export type RemarkField = {
  remark?: string;
};

//联动/筛选等需要监视的字段
export type linkFieldsType = {
  linkFields: string[];
};

// 基础组件合集
export type FieldBaseField = CommonField & BaseField & MapField & Partial<templateField>;

export type WidgetPropType = {
  data: FieldBaseField;
};

/*
====================================================================================
表单配置
====================================================================================
*/
export type XtFieldBase = {
  title: string;
  list: (BaseField & DesignFieldType)[];
};

export type XtFieldBaseByApp = {
  title: string;
  list: (BaseField & DesignFieldType & microAppCommonField)[];
};

export type DesignField = {
  title: string;
  list: AvueColumns[];
};
/*
====================================================================================
组件公用设置 数据联动/公式编辑等
====================================================================================
*/

//默认值自定义
export type CustomDefaultType = {
  defaultValue?: any;
};

//默认值-数据联动
export type DataLinkField = {
  dataLinks?: dataLinksType;
};

//默认值-微应用数据联动
export type appDataLinkField = {
  appDataLinks?: dataType;
  linkFields?: string[];
};

//默认值-公式编辑
export type ConfigFormula = {
  template?: string;
  marks?: Mark[];
};
export type FormulaField = {
  formula?: ConfigFormula;
};

//默认值-值与值 值与数据表
export type ruleType = {
  ruleExpressId?: string;
};

//默认值-添加筛选条件
export type DataFilter = {
  query?: DataCollectType;
};

export type RequestComponentProps = {
  unInitData?: boolean;
};

//分组汇总

//默认值-自定义/数据联动/公式编辑
export type AllDefaultValueType =
  | CustomDefaultType
  | DataLinkField
  | FormulaField
  | appDataLinkField;

// 组件表单类型
export type AvueOption = {
  disabled?: boolean;
  column: XtElFormItemType[];
};

// 数据字段类型
export type DataFieldBase = {
  showFieldIds?: string[];
  showFields?: AvueColumns[];
};

// 组件渲染当前item类型
export type XtElFormItemType = AvueColumns &
  FormulaField &
  requireType &
  disableType &
  WorkingAdminType &
  ruleType &
  appDataLinkField &
  DataFilter &
  groupSummaryField;

// 类型映射
export type FieldTypeMap = {
  [FixTypeEnum.INPUT]: InputField & BaseField;
  [FixTypeEnum.DATE]: DateField & BaseField;
  [FixTypeEnum.DATERANGE]: DateRangeField & BaseField;
  [FixTypeEnum.NUMBER]: NumberField & BaseField;
  [FixTypeEnum.SELECT]: SelectField & BaseField;
  [FixTypeEnum.MUL_SELECT]: SelectField & BaseField;
  [FixTypeEnum.MUL_CASCADER]: MulSelectField & BaseField;
  [FixTypeEnum.ADDRESS]: AddressField & BaseField;
  [FixTypeEnum.DIVIDER]: DesignFieldType & BaseField;
  [FixTypeEnum.DYNAMIC]: DynamicField & BaseField;
  [FixTypeEnum.UPLOAD]: UploadField & BaseField;
  [FixTypeEnum.PHOTO]: PhotoField & BaseField;
  [FixTypeEnum.TIPS]: TipsField & BaseField;
  [FixTypeEnum.USERS]: UsersField & BaseField;
  [FixTypeEnum.USER_TAG]: UserTagField & BaseField;
  [FixTypeEnum.USER_DEPT]: UserDeptField & BaseField;
  [FixTypeEnum.BASIC_INFO]: BasicInfoField & BaseField;
  [FixTypeEnum.SIGNATURE]:basePermissionType&  BaseField & DesignFieldType;
  [FixTypeEnum.SIGNATURE_COLLECT]: DataFilter & BaseField & DesignFieldType;
  [FixTypeEnum.FORMULA_EDIT]: FormulaEditType & BaseField;
  [FixTypeEnum.INVENTORY_COM]: InterspaceType & BaseField;
  [FixTypeEnum.INTERSPACE_COM]: InterspaceType & BaseField;
  [FixTypeEnum.STATISTICAL]: StatisticalType & BaseField;
  [FixTypeEnum.TARGET_VALUE]: TargetValueType & BaseField;
  [FixTypeEnum.GET_USERCODE]: BaseField & DesignFieldType;
  [FixTypeEnum.DEPART_ADMIN]: DepartAdminType & BaseField;
  [FixTypeEnum.WORKING_ADMIN]: WorkingAdminType & BaseField;
  [FixTypeEnum.CALCULATION_TABLE]: CalculationTable & BaseField;
};
