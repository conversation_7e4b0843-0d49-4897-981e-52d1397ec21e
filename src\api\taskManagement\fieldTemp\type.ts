// 分页响应参数
export interface ResPage<T> {
    records: T[];
    size: number;
    total: number;
}

// 分页请求参数
export interface ReqPage {
    current: number;
    size: number;
}

//档案门类
export namespace FieldTemp {
    export interface ReqParams extends ReqPage {
        sceneId?: string;
    }
    export interface ReqSearch {

    }
    export interface details{
        content: string;
        type:string,
        id:string
    }
    export interface ResList {
        details: details[];
        name: string;
        sceneId: string;
        id: string;
        formId:string;
        createUser: string;
        createTime: string;
    }
    export interface subParams {
        details: any[];
        name: string;
        sceneId: string;
        id?: string
    }
}
