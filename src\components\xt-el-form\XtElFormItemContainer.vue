<template>
  <el-row :gutter="20">
    <xt-el-form-item
      v-for="item in columns"
      v-model="form[item.prop]"
      :key="item.prop"
      :item="item"
      :propPrefix="propPrefix"
      :itemForm="form"
      :itemColumns="columns"
      :source="source"
      :col="columns"
    ></xt-el-form-item>
  </el-row>
</template>

<script setup lang="ts">
import { AvueForm, XtElFormItemType } from '@/components/form-config/types/AvueTypes.ts';
import XtElFormItem from './XtElFormItem.vue';


withDefaults(
  defineProps<{
    form: AvueForm;
    columns?: XtElFormItemType[];
    propPrefix?: string[];
    source?: number//组件的使用场景 1中间的组件配置 2 组件的默认值设置 3 预览 4 应办
  }>(),
  {
    columns: () => [],
  }
);


</script>

<style scoped></style>
