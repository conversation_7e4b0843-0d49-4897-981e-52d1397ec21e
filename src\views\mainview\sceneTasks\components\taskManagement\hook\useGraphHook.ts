import {Cell, Edge, Graph, Node, NodeView, Path} from "@antv/x6";
import {Options as GraphOptions} from "@antv/x6/lib/graph/options";
import taskGraphNode from "@/views/mainview/sceneTasks/components/taskManagement/node/taskGraphNode.vue";
import {Component} from "vue";
import {register} from "@antv/x6-vue-shape";
import {VueShapeConfig} from "@antv/x6-vue-shape/lib/registry";
import {Selection} from '@antv/x6-plugin-selection'
//@ts-ignore
import Hierarchy from "@antv/hierarchy";
import taskMenuNode, {MenuDataProps, TaskMenuProps} from "@/views/mainview/sceneTasks/components/taskManagement/node/taskMenuNode.vue";
import {HierarchyResult} from "@/types/Hierarchy";
import {Button} from "@antv/x6/es/registry/tool/button";
import Properties = Node.Properties;

export interface TaskTreeProps {
    versionId: string,
    addType: string,
    metricsId:string,
    targetId?: string,
    targetValue: number,
    targetValueSync: any,
    requirements: string,
    typeSynchronization: string,
    targetName: string,
    children?: TaskTreeProps[],
    executeUser: string,
    executeRole?: string,
    hasChildrenLeft: boolean,
    hasChildrenRight: boolean,
    prevName?:string|null,
    id: string,
    notificationUser: string,
    parentId: string | number,
    //taskPackageId: string,
    x: number,
    y: number,
    foldLeft: boolean,
    foldRight: boolean,
    side: 'left' | 'right' | '',
    root: boolean,
}

// export interface TreeConfigProps{
// }
export interface TaskNodeDataProps {
    data: TaskTreeProps
    hX: number
    hY: number
    side: 'left' | 'right'
    rootX: number
    rootY: number
}

export const NodeConstant = {
    taskNode: 'taskNode',
    menuNode: 'menuNode',
    taskEdgeRight: 'taskRightEdge',
    taskEdgeLeft: 'taskLeftEdge',
    taskConnector: 'taskConnector',
    edgeTool: 'edgeTool',
}

function filterFoldTask(data: TaskTreeProps) {
    if (data.children && data.children.length > 0) {
        if (data.root) {
            const rootLeft = data.children.filter(s => {
                return s.side === "left"
            })
            const rootRight = data.children.filter(s => {
                return s.side === "right"
            })
            data.hasChildrenLeft = rootLeft.length > 0
            data.hasChildrenRight = rootRight.length > 0
            if (data.foldLeft) {
                data.children = rootRight
            }
            if (data.foldRight) {
                data.children = rootLeft
            }
            if (data.foldLeft && data.foldRight) {
                data.children = []
            }
        } else {
            data.hasChildrenLeft = false
            data.hasChildrenRight = false
            if (data.side === "left") {
                data.hasChildrenLeft = true
                if (data.foldLeft) {
                    data.children = []
                }
            }
            if (data.side === "right") {
                data.hasChildrenRight = true
                if (data.foldRight) {
                    data.children = []
                }
            }
        }
        data.children.forEach(s=>{
            filterFoldTask(s)
        })
    } else {
        data.hasChildrenLeft = false
        data.hasChildrenRight = false
    }
    return data;
}

function mindMap(task: Partial<TaskTreeProps>, side: 'left' | 'right'): HierarchyResult<TaskTreeProps> {
    return Hierarchy.mindmap<TaskTreeProps>(task, {
        direction: "H",
        getHeight() {
            return 150
        },
        getWidth() {
            return 250
        },
        getHGap() {
            return 100
        },
        getVGap() {
            return 50
        },
        getSide: () => {
            return side
        },
    })
}

class IGraph extends Graph {
    private allCells: any[] = []
    private isEmbedded = false
    private hasDisabled = false
    public selectNodeId:string|undefined=undefined
    constructor(options: GraphOptions.Manual,disabled: boolean) {
        console.log("初始化Graph",options)
        super(options);
        //注册主节点
        this.registerDomainNode(taskGraphNode)
        this.registerMenuNode(taskMenuNode)
        this.registerEdge()
        this.zoomTo(.5)
        this.hasDisabled=disabled
        if (!disabled){
            this.registerTools()
            this.setPlugin()
        }

    }
    setSelectNodeId(id:string|undefined){
        if (id){
            this.selectNodeId=id
        }
    }
    cleanSelect(){
        this.selectNodeId=undefined
    }
    onRendered(callback:(id?:string)=>void){
        this.on('render:done', () => {
            callback&&callback(this.selectNodeId)
        })
    }
    registerTools() {
        const EdgeTool = Button.define<Button.Options>({
            markup: [
                {
                    tagName: 'circle',
                    selector: 'button',
                    attrs: {
                        r: 18,
                        stroke: '#fe854f',
                        strokeWidth: 2,
                        fill: 'white',
                        cursor: 'pointer',
                    },
                },
                {
                    tagName: 'text',
                    textContent: '删除',
                    selector: 'icon',
                    attrs: {
                        fill: '#fe854f',
                        fontSize: 10,
                        textAnchor: 'middle',
                        pointerEvents: 'none',
                        y: '0.3em',
                    },
                },
            ],
        })

        Graph.registerEdgeTool(NodeConstant.edgeTool, EdgeTool, true)
    }

    registerEdge() {
        this.registerConnector()
        // 边右边
        Graph.registerEdge(
            NodeConstant.taskEdgeRight,
            {
                inherit: 'edge',
                connector: {
                    name: NodeConstant.taskConnector,
                    args: {
                        offset: 65
                    }
                },
                attrs: {
                    line: {
                        targetMarker: '',
                        stroke: '#55BC63',
                        strokeWidth: 2,
                    },
                },
                zIndex: 0,
            },
            true,
        )
        // 边坐标边
        Graph.registerEdge(
            NodeConstant.taskEdgeLeft,
            {
                inherit: 'edge',
                connector: {
                    name: NodeConstant.taskConnector,
                    args: {
                        offset: -65
                    }
                },
                attrs: {
                    line: {
                        targetMarker: '',
                        stroke: '#55BC63',
                        strokeWidth: 2,
                    },
                },
                zIndex: 0,
            },
            true,
        )
    }

    registerConnector() {
        Graph.registerConnector(
            NodeConstant.taskConnector,
            // @ts-ignore
            (sourcePoint, targetPoint, routerPoints, options: Record<string, any>) => {
                const {offset = 65} = options
                const midX = sourcePoint.x + offset
                const midY = sourcePoint.y
                const pathData = `
                 M ${sourcePoint.x} ${sourcePoint.y}
                 L ${midX} ${midY}

                 L ${midX} ${targetPoint.y}
                 L ${targetPoint.x} ${targetPoint.y}
                `
                return options.raw ? Path.parse(pathData) : pathData
            },
            true,
        )
    }

    setPlugin() {
        this.use(new Selection({
            enabled: true,
            multiple: true,
            rubberband: false,
            movable: true,
            showNodeSelectionBox: false,
        }))
        this.on("node:selected", (e) => {
            const {node}=e
            node.prop("select",true)
        },this)
        this.on("node:unselected", (e) => {
            const {node}=e
            node.prop("select",false)
        },this)
        // this.on("node:click", (e) => {
        //     const {node}=e
        //     console.log("selec")
        //     node.prop("select",true)
        // },this)
    }

    listenEmbed(callback: (sourceCell: Node<Properties> | null, targetCell: Cell) => void) {
        this.on("node:embedded", (e) => {
            console.log("embedded")
            this.isEmbedded=true
            callback(e.currentParent, e.cell)
        },this)
    }

    listenRootTaskMove(callback: (e: NodeView.TranslateEventArgs<any>) => void) {
        this.on("node:moved", e => {
            if (this.isEmbedded){
                return this.isEmbedded=false
            }
            callback&& callback(e)
        },this)
    }

    setTaskMenu(menu: TaskMenuProps[]) {
        this.removeTaskMenu()

        this.on("node:contextmenu", (e) => {
            e.e.stopPropagation();
            const {x, y, node} = e
            this.addNode({
                id: NodeConstant.menuNode,
                shape: NodeConstant.menuNode,
                x, y,
                data: {
                    menu,
                    currentNode: node
                } as MenuDataProps
            })
        },this)
        this.on("blank:click", () => {
            this.getNodes().forEach(n=>{
                n.prop("select",false)
            })
            this.removeTaskMenu()
        },this)
    }

    removeTaskMenu() {
        this.removeCell(NodeConstant.menuNode)
    }

    registerMenuNode(component: Component, option?: VueShapeConfig) {
        //全局注册菜单
        register({
            shape: NodeConstant.menuNode,
            component: component,
            width: 100,
            height: 0,
            ...option
        })
    }

    registerDomainNode(component: Component, option?: VueShapeConfig) {
        //全局注册node
        register({
            shape: NodeConstant.taskNode,
            component: component,
            width: 150,
            height: 300,
            zIndex: 11,
            ...option
        })

    }
    sortTask(tasks: TaskTreeProps[]) {

        // 对 children 数组按照 prevName 和 id 进行排序
        const result = [];
        const prevNull=tasks.filter(s=>(s.prevName===undefined||s.prevName===null))
        const prevNotNull=tasks.filter(s=>!(s.prevName===undefined||s.prevName===null))
        // 将数据转换为 Map 方便按照 id 查找
        const dataMap = new Map(prevNotNull.map(item => [item.prevName, item]));
        console.log('dataMao',dataMap);
        // 根据 prevId 进行链式排序
        let current = prevNotNull.find(item =>!item.prevName);
        // if (!current) current=prevNotNull.find(item =>item.prevName === '');
        while (current) {
            result.push(current);
            current = _.cloneDeep(dataMap.get(current.targetName));
            if (result.length>dataMap.size){
                current=undefined
            }
        }
        if (result.length!==dataMap.size){
            result.push(...dataMap.values())
        }
        result.push(...prevNull)
        result.forEach(task=>{
            if (task.children){
                task.children=this.sortTask(task.children)
            }
        })
        return result
    }
    hierarchyTask(tasks: TaskTreeProps[]) {
        this.allCells.length=0
        const mindMaps = _.cloneDeep(tasks).map(task => {
            //过滤折叠节点并添加是否可展开属性
            task=filterFoldTask(task)
            // console.log(task.children,"ggg")
            const mindTask = mindMap(task, "right")
            const taskLeft = this.sortTask((task.children || []).filter(c => c.side === 'left'))
            const taskRight = this.sortTask((task.children || []).filter(c => c.side === 'right'))
            console.log(taskLeft, taskRight, "taskLeft,taskRight")
            const leftMind = mindMap({
                ...task,
                children: taskLeft
            }, "left").children
            const rightMind = mindMap({
                ...task,
                children: taskRight
            }, "right").children
            // Object.assign(mindTask.children||[],leftMind,rightMind)
            mindTask.children = [
                //@ts-ignore
                ...leftMind,
                ...rightMind
            ]
            return mindTask
        })
        console.log('mindMaps',mindMaps);
        this.addTaskOffsetNodes(mindMaps)
        this.resetCells(this.allCells)
    }

    addTaskOffsetNodes(mindMaps: HierarchyResult<TaskTreeProps>[]) {
        mindMaps.forEach(node => {
            const {data} = node
            this.addTaskNodes(node, undefined, {x: data.x, y: data.y, rootX: node.x, rootY: node.y},)
        })
    }

    addTaskNodes(node: HierarchyResult<TaskTreeProps>, parentNode?: Cell, offSet?: { x: number, y: number,rootX:number,rootY:number }) {

        const cNode = this.addTaskNode(node, parentNode, offSet)
        const children: Node<Node.Properties>[] = []
        ;(node.children || []).forEach(node => {
            const cNd = this.addTaskNodes(node, cNode, offSet)
            children.push(cNd)
        })
        cNode.setChildren(children)
        return cNode
    }

    addTaskNode(node: HierarchyResult<TaskTreeProps>, parentNode?: Cell, offSet?: { x: number, y: number,rootX:number,rootY:number }) {
        const {x = 0, y = 0,rootX=0,rootY=0} = offSet || {}
        const {data} = node
        // data.x=node.x+x
        // data.y=node.y+y
        const cloneData = data
        delete cloneData.children
        const cNode = this.createNode({
            shape: NodeConstant.taskNode,
            id: node.id,
            x: node.x + x,
            y: node.y + y,
            width: 100,
            height: 40,
            data: {
                data: cloneData,
                hasChildrenLeft: node.hasChildrenLeft,
                hasChildrenRight: node.hasChildrenRight,
                hX: node.x,
                hY: node.y,
                rootX:rootX,
                rootY:rootY,
                side: node.side,
            } as TaskNodeDataProps,
        })
        if (parentNode) {
            // cNode.setParent(parentNode)
            // parentNode.addChild(cNode)
            const cEdge=this.createEdge({
                shape: node.side === 'left' ? NodeConstant.taskEdgeLeft : NodeConstant.taskEdgeRight,
                target: {
                    cell: cNode,
                    anchor: {
                        name: node.side === 'left' ? 'right' : 'left',
                        args: {
                            dx: 0,
                        },
                    },
                },
                source: {
                    cell: parentNode,
                    anchor: {
                        name: node.side || 'right',
                        args: {
                            dx: 0,
                        },
                    },
                },
            })
            this.allCells.push(cEdge)
        }
        cNode.prop("disabled",this.hasDisabled)
        this.allCells.push(cNode)
        return cNode
    }

    addTaskEdgeRemoveTools(callback: (edge: Edge) => void) {
        this.on("edge:mouseenter", ({cell}) => {
            cell.addTools({
                name: NodeConstant.edgeTool,
                args: {
                    distance: "-60",
                    onClick(vm: any) {
                        callback(vm.cell)
                    },
                },
            })
        },this)
        this.on("edge:mouseleave", ({cell}) =>
                cell.removeTool(NodeConstant.edgeTool)
            ,this)
    }
}

export default IGraph