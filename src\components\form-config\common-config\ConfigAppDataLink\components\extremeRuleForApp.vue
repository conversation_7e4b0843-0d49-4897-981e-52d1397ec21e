<template>
  <n-card>
    <h3><span class="text-red bold">* </span> 极值规则</h3>
    <el-row :gutter="10" class="mb-2" v-if="!isMul">
      <el-col :span="2"> 根据 </el-col>
      <el-col :span="5">
        <avue-select
          class="w-full"
          :dic="numberDic"
          v-model="vModel.extremeProp"
          :props="{ label: 'copyLabel', value: 'prop' }"
        ></avue-select>
      </el-col>
      <el-col :span="2" class="text-center"> 取 </el-col>
      <el-col :span="5">
        <avue-select
          class="w-full"
          :dic="numberType.includes(sortFieldType as any) ? fetchOption2 : fetchOption3"
          v-model="vModel.rule"
          placeholder="请选择"
        ></avue-select>
      </el-col>
    </el-row>
  </n-card>
</template>

<script setup lang="ts">
import { fetchOption2,fetchOption3 } from '@/components/form-config/utils/option';
import { extremeRuleType } from '@/api/interface/configDataLink.ts';
import { AvueColumns } from '@/components/form-config/types/AvueTypes'
import {SystemTypeEnum} from '@/components/form-config/types/field'
import { createList, dateType,numberType, systemByFilterSortType, systemByFilterSortType1} from '@/components/form-config/types/FieldTypes';
// import { SystemTypeEnum } from '@/components/form-config/types/field';

const props = defineProps<{
  data?: AvueColumns;
  fieldList: AvueColumns[];
  isMul?: boolean; //是否是多个筛选条件
  pathType?: number
}>();

const vModel = defineModel<Partial<extremeRuleType>>({ default: {} });

// const rulesDic = [
//   { label: '往前', value: 0 },
//   { label: '选择时间', value: 1 },
//   { label: '具体日期时间', value: 2 },
//   { label: '所选任务首次触发时', value: 3 },
// ];
// const dataDic = [
//   { label: '当年', value: 0 },
//   { label: '当月', value: 1 },
//   { label: '当周', value: 2 },
//   { label: '当日', value: 3 },
// ];

const systemField = computed(() => {
  const types:SystemTypeEnum[] = props.pathType === 0 ? systemByFilterSortType : systemByFilterSortType1
  return createList.filter(item => types.includes(item.fixType as SystemTypeEnum));
})

const numberDic = computed(() => {
  const types = [...numberType, ...dateType]
  const arr = props.fieldList.filter(item => _.includes(types, item.fixType));
  return [...arr, ...systemField.value]
});


const sortFieldType = computed(() => {
  return props.fieldList?.find(item => item.id === vModel.value.extremeProp)?.fixType
})
</script>

<style scoped lang="scss">
.flex-center {
  display: flex;
  align-items: center;
  // justify-content: center;
}
</style>
