<template>
  <el-drawer
    v-model="open"
    :size="width"
    :title
    append-to-body
    destroy-on-close
    @closed="onClose"
  >
    <template #footer>
      <el-button
        @click="onClose"
      >取消
      </el-button>
      <el-button
        class="ml-8px!"
        type="primary"
        @click="onSubmit"
      >提交
      </el-button>
    </template>
    <el-form
      ref="formRef"
      :class="{'h-full':fixHeight}"
      :model
      class="flex-form"
      label-position="top"
    >
      <slot name="default" />
    </el-form>
  </el-drawer>
</template>

<script
  lang="ts"
  setup
>

import { FormInstance } from 'element-plus';

const props = withDefaults(defineProps<{
  width?: number
  model?: Record<string, any>,
  fixHeight?: boolean
}>(), {
  width: () => 517,
  model: () => ({})
});

const emits = defineEmits<{
  close: [],
  submit: [form: Record<string, any>]
}>();

const open = ref<boolean>(false);
const title = ref<string>('');
const formRef = ref<FormInstance>();

const onSubmit = () => {
  formRef.value?.validate()
    .then(() => {
      emits('submit', props.model);
    });
};

const onClose = () => {
  formRef.value?.clearValidate();
  emits('close');
};

defineExpose({
  setTitle: (t: string) => {
    title.value = t;
  },
  open: () => {
    open.value = true;
  },
  close: () => {
    open.value = false;
  }
});

</script>

<style
  lang="scss"
  scoped
>
.flex-form {
  @apply flexList;

  :deep(.el-form-item) {
    margin-bottom: 0 !important;
  }
}
</style>