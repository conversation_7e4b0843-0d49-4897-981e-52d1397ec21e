 <template>
  <div class="task_node_container"
       @mouseenter="mouseEnter"
       @mouseleave="mouseLeave"
       :class="{
         'has_selected':selectCurrent,
         'has_hover':hasHover,
       }"
       ref="contBoxRef">
    <div class="task_body">
      <div class="text-20px" :class="{'white':selectCurrent,'text-#2097F6':!selectCurrent}">{{ nodeData.data.targetName }}</div>
      <div class="text-20px" :class="{'white':selectCurrent,'text-#2097F6':!selectCurrent}">{{ nodeData.data.requirements }}</div>
<!--      <div class="task_desc" v-if="hasDisabled">-->
<!--        <div class="task_desc_label">-->
<!--          <div class="tx">执行角色</div>-->
<!--        </div>-->
<!--        <div class="task_desc_value">{{ nodeData.data.executeRole }}-->
<!--        </div>-->
<!--      </div>-->
<!--      <template v-else>-->
<!--        <div class="task_desc">-->
<!--          <div class="task_desc_label">-->
<!--            <div class="tx">执行人</div>-->
<!--          </div>-->
<!--          <div class="task_desc_value">{{ nodeData.data.executeUser }}-->
<!--          </div>-->
<!--        </div>-->
<!--        <div class="task_desc">-->
<!--          <div class="task_desc_label">-->
<!--            <div class="tx">告知人</div>-->
<!--          </div>-->
<!--          <div class="task_desc_value">{{ nodeData.data.notificationUser }}-->
<!--          </div>-->
<!--        </div>-->
<!--      </template>-->
    </div>
    <div class="collect_group right" v-if="nodeData.data.hasChildrenRight">
      <div class="collect_line"></div>
      <div class="collect_btn" @click.stop="emitFold('right')">
        <RemoveFilled class="fold_icon" v-if="!nodeData.data.foldRight"/>
        <CirclePlusFilled class="fold_icon" v-else />
      </div>
    </div>
    <div class="collect_group left" v-if="nodeData.data.hasChildrenLeft">
      <div class="collect_btn" @click.stop="emitFold('left')">
        <RemoveFilled class="fold_icon" v-if="!nodeData.data.foldLeft"/>
        <CirclePlusFilled class="fold_icon" v-else/>
      </div>
      <div class="collect_line"></div>
    </div>
    <div class="menu_body" v-if="selectCurrent">
        <div @click="emitAdd(nodeData.side||'left')" v-if="nodeData.data.root||nodeData.side==='left'" class="add_bt bt_left"><CirclePlusFilled /></div>
        <div @click="emitAdd(nodeData.side||'right')" v-if="nodeData.data.root||nodeData.side==='right'" class="add_bt bt_right"><CirclePlusFilled /></div>
        <div @click="emitAdd('top')" v-if="!nodeData.data.root" class="add_bt bt_top"><CirclePlusFilled /></div>
        <div @click="emitAdd('bottom')" v-if="!nodeData.data.root" class="add_bt bt_bottom"><CirclePlusFilled /></div>
    </div>
  </div>
</template>

<script setup lang="ts">

import {VueShape} from "@antv/x6-vue-shape";

import { taskEmitter} from "@/views/mainview/sceneTasks/components/taskManagement/event/taskEvent.ts";
import {CirclePlusFilled, RemoveFilled} from "@element-plus/icons-vue";
import {TaskNodeDataProps} from "@/views/mainview/sceneTasks/components/taskManagement/hook/useGraphHook.ts";
const node = inject<any>("getNode")() as VueShape
const contBoxRef=ref<HTMLElement>()
const nodeData = ref<TaskNodeDataProps>(node.getData<TaskNodeDataProps>())
const hasHover=ref<boolean>(false)
//const hasDisabled=ref<boolean>(node.prop("disabled"))
const selectCurrent=ref<boolean>(false)
node.on("change:data", (e: VueShape) => {
  nodeData.value = e.getData<TaskNodeDataProps>()
})
node.on('change:select', (e:any) => {
  const {current}=e
  // node.prop("select",true)
  selectCurrent.value=current
})

const emitAdd=(type:any)=>{
  if (['left','right'].includes(type)){
    taskEmitter.emit("addLR",{
      parentId:nodeData.value.data.id,
      foldLeft:false,
      foldRight:false,
      side:type
    })
  }else{
    taskEmitter.emit("addBT", {node,type})
  }
}
const mouseEnter=()=>{
  // console.log("ent",e)
  hasHover.value=true
}
const mouseLeave=()=>{
  // console.log("lea",e)
  hasHover.value=false
}
const emitFold=(type:string)=>{
  console.log("emitFold",type)

  node.prop("select",false)
  const {foldLeft,foldRight}=nodeData.value.data
  const fold={
    foldLeft:foldLeft,
    foldRight:foldRight
  }
  if (type==='left'){
    fold.foldLeft=!foldLeft
  }else{
    fold.foldRight=!foldRight
  }
  taskEmitter.emit("fold",{
    ...nodeData.value.data,
    ...fold
  })
}
//动态监听节点高度变化
watchEffect(()=>{
  if (contBoxRef.value?.offsetHeight){
    const height=contBoxRef.value?.offsetHeight||120
    node.resize(250,height)
  }
})
</script>

<style scoped lang="scss">
.task_node_container {
  width: 211px;
  height: 63px;
  background: #E7F4FF;
  box-shadow: 4px 4px 0px 0px #2097F6;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #2097F6;
  text-align: center;
  font-size: 16px;

  //background: #ffffff;
  //padding: 10px;
  //width: 250px;
  //min-height: 120px;
  //border-radius: 10px;
  //border: 1px solid #C0C4CC;
  cursor: pointer;
  position: relative;
  .menu_body {
    .add_bt{
      position: absolute;
      left: 0;
      bottom: 50%;
      height: 30px;
      width: 30px;
      cursor: pointer;
      transition:color 0.45s;
      transform: translate(-50%,-50%);
      border-radius: 50%;
      background: #ffffff;
      color:orange;
      &:hover{
        color: skyblue;
      }
    }
    .bt_left{
      left: 0;
      top: 50%;
    }
    .bt_right{
      left: 100%;
      top: 50%;
    }
    .bt_top{
      top: 0;
      left: 50%;
    }
    .bt_bottom{
      bottom: 0;
      left: 50%;
      transform: translate(-50%,50%);
    }
  }
}

.has_selected{
  background-color: #3F8CFF;
  color: white;
  //border: 1px solid #25aff3!important;
}
.has_hover{
  border: 1px solid #55BC63;
}
.task_name{
  font-size: 16px;
  font-weight: bold;
  color: #3A3A3A;
  padding: 10px 0;
}
.task_body {
  height: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  font-family: PingFangSC-Regular, PingFang SC;
  .task_desc {
    display: flex;
    flex-direction: row;
    padding: 5px 0;
    .task_desc_label {
      margin-right: 10px;
      width: 70px;
      .tx{
        font-size: 14px;
        color: #9EA5B2;
      line-height: 28px;
        padding: 0 5px;
        background: rgba(216,218,220,0.22);
      }
    }

    .task_desc_value {
      flex: 1;
      font-size: 14px;
      line-height: 28px;
      color: #9EA5B2;
    }
  }
}
.collect_group.right{
  left: 100%;
  top: 50%;
}
.collect_group.left{
  left: -80px;
  top: 50%;
}
.collect_group{
  position: absolute;
  transform: translateY(-50%);
  justify-content: space-between;
  align-items: center;
  display: flex;
  .fold_icon{

  }
  .collect_line{
    height:2px;
    background: #55BC63;
    width: 50px;
  }
  .collect_btn{
    width: 30px;
    height: 30px;
    color: gray;
    background: #FFFFFF;
    border-radius: 50%;
    cursor: pointer;
  }
}
</style>