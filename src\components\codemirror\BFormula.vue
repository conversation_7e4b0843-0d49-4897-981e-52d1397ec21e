<template>
  <div class="flex-(~ col) gap-16px size-full min-h-540px text-14px">
    <div :style="{ height: `${inputHeight}px` }" class="flex-(~ col) top border-(1px solid #DDDFE7) shrink-0">
      <div class="bg-#DDDFE7 h-32px leading-32px pl-16px">{{ valText }}=</div>
      <BCodemirror ref="xtCode" v-model="v" :mark-render="markRender" :placeholder="placeholder" class="flex-1" />
    </div>
    <div :style="{ height: bottomHeight ? `${bottomHeight}px` : '', flex: bottomHeight ? '' : '1' }"
      class="grid gap-16px overflow-hidden" :class="formulaType ? 'grid-cols-4' : 'grid-cols-1'">
      <div class="bottom-child">
        <div class="leading-30px">选择字段</div>
        <div class="container">
          <a-input v-model:value="fieldSearch" :bordered="false" allowClear placeholder="搜索字段">
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
          <el-scrollbar class="list" view-class="py-8px">
            <div v-for="item in fieldFilter" v-if="fieldFilter.length" :key="item.prop" class="field-list-item"
              @click="fieldClick(item)">
              <el-text truncated :title="item.copyLabel">
                {{ item.copyLabel }}
              </el-text>

              <div class="defaultTag type-tag whitespace-nowrap shrink-0">
                {{ renderType(item.fixType) }}
              </div>
            </div>
            <div v-else>
              <el-empty :image-size="70"></el-empty>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <template v-if="formulaType">
        <div class="bottom-child">
          <div class="leading-30px">选择函数</div>
          <div class="container">
            <a-input v-model:value="formulaSearch" :bordered="false" allowClear placeholder="搜索函数">
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
            <el-scrollbar class="list">
              <el-tree :data="formulaFilter" :props="{ children: 'children', label: 'formulaName' }" default-expand-all
                @node-click="handleNodeClick">
                <template #default="{ data }">
                  <span class="w-full h-full flex items-center" @mouseenter="setInfo(data)">
                    {{ data.formulaName }}
                  </span>
                </template>
              </el-tree>
            </el-scrollbar>
          </div>
        </div>
        <div class="bottom-child col-span-2 overHidden">
          <div class="h-30px" />
          <el-image fit="cover" :src="remarkUrl" @error="() => (showContainer = true)"
            @load="() => (showContainer = false)">
            <template #error>
              <div></div>
            </template>
          </el-image>
          <div v-if="showContainer" class="container h-full mt-(-8px)"></div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getFormulaList } from '@/api/formula';
import BCodemirror from '@/components/codemirror/BCodemirror.vue';
import { BCmProp, FieldType, Mark } from '@/components/codemirror/types.ts';
import { deptColumn } from '@/views/banban/application/flowApp/option/xtFields.ts';
import { SearchOutlined } from '@ant-design/icons-vue';
import xtFields from '../../views/mainview/sceneTasks/detail/option/xtFields.ts';
import { createList, dataTableDefList } from '../form-config/types/FieldTypes.ts';
import { generateMarksFromTemplate } from './utils.ts';

const props = withDefaults(
  defineProps<
    BCmProp & {
      bottomHeight?: number;
    }
  >(),
  {
    placeholder: () => '请输入',
    fieldList: () => [],
    filterFormual: () => [],
    inputHeight: () => 200,
    templateKey: () => 'template',
    marksKey: () => 'marks',
  }
);

const vModel = defineModel<any>({ default: () => ({}) });

const v = computed({
  get: () => {
    let disposeMarks = generateMarksFromTemplate(
      vModel.value?.[props.templateKey] || '',
      vModel.value?.[props.marksKey] || []
    );
    return {
      ...vModel.value,
      [props.marksKey]: disposeMarks,
    };
  },
  set: v => {
    nextTick(() => {
      vModel.value = v;
    });
  },
});

// 渲染mark
const xtCode = ref<InstanceType<typeof BCodemirror>>();

const markRender = (mark: Mark) => {
  const replace = document.createElement('span');
  let tagClass = 'defaultTag';
  const field = props.fieldList?.find(item => item.prop === mark?.attributes?.prop);
  let innerText;
  if (field) {
    innerText = field.copyLabel;
  } else {
    tagClass = 'errorTag';
    innerText = '未知字段';
  }

  replace.className = `m-2px px-5px replaceSpan ${tagClass}`;
  replace.innerText = innerText;
  return replace;
};

// 字段
const fieldSearch = ref('');
const fieldFilter = computed(() => {
  if (_.isEmpty(fieldSearch.value)) {
    return props.fieldList;
  }
  return props.fieldList.filter(item => {
    return item.copyLabel.includes(fieldSearch.value);
  });
});

const fieldClick = (item: FieldType) => {
  xtCode.value?.insertMark(`@${item.prop}%`, {
    attributes: {
      prop: item.prop!,
      type: item.fixType,
    },
  });
  xtCode.value?.focus();
};

const renderType = (fixType?: string) => {
  return (
    xtFields
      .map(item => item.list)
      .flat()
      .concat([deptColumn, ...createList, ...dataTableDefList] as any[])
      .find(item => item.fixType === fixType)?.copyLabel ?? '未知'
  );
};

// 公式
type FormulaTree = {
  formulaName: string;
  canClick?: boolean;
  remarkUrl?: string;
  children?: FormulaTree[];
};
const formulaList = ref<FormulaTree[]>([]);

onMounted(() => {
  if (props.formulaType) {
    getFormulaList(props.formulaType).then(res => {
      formulaList.value = res.data.data ?? [];
    });
  }
});

const formulaSearch = ref('');

const filFormual = computed<FormulaTree[]>(() => {
  if (!props.filterFormual || !props.filterFormual.length) {
    return formulaList.value;
  }
  return formulaList.value
    .map(item => {
      return {
        ...item,
        children: item.children?.filter(child => props.filterFormual.includes(child.formulaName)),
      };
    })
    .filter(f => f.children && f.children.length);
});

const formulaFilter = computed(() => {
  if (_.isEmpty(formulaSearch.value)) {
    return filFormual.value;
  }
  return _.flatten(
    filFormual.value.map(
      item =>
        item.children?.filter(child =>
          _.toLower(child.formulaName).includes(_.toLower(formulaSearch.value))
        ) ?? []
    )
  );
});

// 函数介绍
const remarkUrl = ref('');

const setInfo = (data: FormulaTree) => {
  remarkUrl.value = data.remarkUrl ?? '';
};

const showContainer = ref(false);

const handleNodeClick = (data: FormulaTree) => {
  if (data.canClick) {
    xtCode.value?.insertText(`${data.formulaName}()`, true);
    xtCode.value?.focus();
  }
};
</script>

<style lang="scss">
.bottom-child {
  box-sizing: border-box;
  @apply flex-(~ col) gap-8px place-self-auto overflow-hidden;

  .container {
    border: 1px solid #dddfe7;
    box-sizing: border-box;
    border-radius: 4px;
    @apply flex-(~ 1 col) overflow-y-hidden;

    .list {
      border-top: 1px solid #dddfe7;
      @apply flex-1;
    }
  }
}

.bottom-child:first-child {
  flex-basis: calc(5 / (5 + 5 + 8) * 100%);
}

.bottom-child:nth-child(2) {
  flex-basis: calc(5 / (5 + 5 + 8) * 100%);
}

.bottom-child:last-child {
  flex-basis: calc(8 / (5 + 5 + 8) * 100%);
}

.field-list-item {
  @apply px-16px cursor-pointer flex items-center justify-between h-32px;

  &:hover {
    background-color: #eaf3ff;
    color: #3f70ff;
  }

  .type-tag {
    border-radius: 10px;
    font-size: 10px;
    width: 60px;
    @apply flex-center h-23px;
  }
}

.defaultTag {
  background-color: #eaf3ff;
  color: #3f70ff;
}

.errorTag {
  background-color: #fff1f0;
  color: #cf1322;
}
</style>
