<!-- FactorGroup.vue -->
<template>
  <el-button @click="$emit('add')" type="primary" size="small" class="mt-20px block ml-0!"
    >+ 添加{{ title }}</el-button
  >
  <div
    v-for="factor in factors"
    :key="factor.id"
    class="border-1 border-#ebebeb border-solid p-20px mt-10px"
  >
    <component
      :is="factorCom"
      :factor="(factor as any)"
      :key="factor.id"
      @delete="(id:number) => handleDelete(id)"
    />
  </div>
</template>

<script setup lang="ts">
// import FactorItem from './FactorItem.vue'
import { Permission } from '../../../types';
import MainFactor from './MainFactor.vue';
import SpaceFactor from './SpaceFactor.vue';
import TimeFactor from './TimeFactor.vue';
import ControlFactor from './ControlFactor.vue';

const props = defineProps<{
  type: keyof typeof factorEnum;
  title: string;
}>();

const factorEnum = {
  principalFactorList: MainFactor,
  spaceFactorList: SpaceFactor,
  timeFactorList: TimeFactor,
  eventFactorList: ControlFactor,
} as const;

const factorCom = computed(() => {
  return factorEnum[props.type];
});

let factors = defineModel<Permission[typeof props.type][][number]>({ default: [] });

const handleDelete = (id: number) => {
  factors.value = factors.value.filter(item => item.id !== id) as Permission[typeof props.type][][number];
};

defineEmits(['add']);
</script>
