<template>
  <div class="w-full h-full relative pb-20px pt-20px gap-20px">
    <!-- <div v-if="isEmpty" class="absolute inset-0 flex justify-center">
      <el-empty :description="emptyDescription" :image-size="175" />
    </div> -->
    <div class="flex z-0 mb-20px">
      <el-button class="px-20px!" :icon="Plus" round type="primary" @click="$emit('add')"
        >添加条件组
      </el-button>
    </div>
    <el-scrollbar
      ref="scrollBar"
      :style="{ width: `${mainWidth}px` }"
      view-class="overflow-x-hidden"
    >
      <xt-animate
        id="config-item-container"
        animate="fadeRight"
        class="flex flex-col gap-20px"
        group
        tag="div"
      >
      <el-collapse>
        <draggable :list="(data as T)" :group="{ name: 'form' }" ghost-class="ghost"
          :animation="300" item-key="prop" >
          <template #item="{ element, index }">
            <n-card class="mb-3">
              <el-collapse-item :name="index">
              <template #title>
                <div style="display: flex;justify-content: space-between;width: 95%;">
                  <h2 class="m-0">条件组{{ index + 1}}</h2>
                  <el-button type="danger" :icon="Delete" link @click.stop="$emit('delete', index)"></el-button>
                </div>
              </template>
              <slot :index="index" :item="element" name="item" :key="element.idx"/>
            </el-collapse-item>
            </n-card>
            </template>
          </draggable>
        </el-collapse>
      </xt-animate>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts" generic="T extends (conditionGroupType|dataLinksItem|DataCollectItem |FilterItemType)[]">
import useScroll from '@/hooks/useScroll.js';
import { Plus,Delete } from '@element-plus/icons-vue';
import { conditionGroupType } from './type';
import { dataLinksItem,DataCollectItem,FilterItemType } from '@/api/interface/configDataLink.ts';
import Draggable from 'vuedraggable';



withDefaults(
  defineProps<{
    data?: T;
    emptyDescription?: string;
    mainWidth?: number;
  }>(),
  {
    emptyDescription: () => '暂时还没有配置条件',
    mainWidth: () => 700,
  }
);

defineEmits<{
  add: [];
  delete: [index: number];
}>();

// const isEmpty = computed(() => _.isEmpty(props.data));

//滚动相关
const { scrollBar, scrollToBottom } = useScroll();

defineExpose({ scrollToBottom });
</script>

<style lang="scss" scoped>
.el-collapse{
  border-top: 0px;
  border-bottom: 0px;
  :deep(.el-collapse-item__wrap){
    border-bottom: 0px;
  }
  :deep(.el-collapse-item__header){
    border-bottom: 0px;
  }
}

</style>
