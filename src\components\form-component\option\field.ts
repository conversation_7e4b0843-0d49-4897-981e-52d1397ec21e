import { dateAllType, inputType } from '@/components/form-config/types/FieldTypes.ts';
import XTInput from '@/components/form-component/XTInput/XTInput.vue';
import MXTInput from '@/components/form-component/XTInput/MXTInput.vue';
import MXTUrlInput from '@/components/form-component/XTInput/components/MXTUrlInput.vue';
import { ClientComponentType, ClientTypes } from '@/components/form-component/option/types.ts';
import { InputFormatTypeEnum } from '@/components/form-config/const/commonDic';
import { XtElFormItemType } from '@/components/form-config/types/AvueTypes.ts';
import { PropType } from 'vue';

const FieldProps = {
  props: {
    item: Object as PropType<XtElFormItemType>
  }
};
// 表单组件不同场景的渲染规则
export const ClientComponentController: ClientComponentType[] = [
  {
    fixType: inputType,
    clientSymbols: [
      {
        client: [ClientTypes.PC],
        component: defineComponent(props => {
          return () => {
            return h(XTInput, {
              ...props
            });
          };
        })
      },
      {
        client: [ClientTypes.MOBILE],
        component: defineComponent(props => {
          console.log(props, 'props');
          if (props.item.showFormat === InputFormatTypeEnum.URL) {
            return () => {
              return h(MXTUrlInput, {
                ...props.item
              });
            };
          } else {
            return () => {
              return h(MXTInput, {
                ...props.item
              });
            };
          }
        }, FieldProps)
      }
    ]
  },
  {
    fixType: dateAllType,
    clientSymbols: [
      {
        client: [ClientTypes.PC, ClientTypes.MOBILE],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('avue-date'), {
              ...props.item
            }, FieldProps);
          };
        })
      }
    ]
  }
];