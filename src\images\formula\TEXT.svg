<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 46</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-230.000000, -1845.000000)">
            <g id="编组-46" transform="translate(230.000000, 1845.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-6" transform="translate(10.000000, 42.000000)" font-family="PingFangSC-Regular, PingFang SC"
                   font-size="14" font-weight="normal" line-spacing="23">
                    <text id="TEXT函数可以将数字转化成文本。-·用">
                        <tspan x="0" y="15" fill="#3F70FF">TEXT</tspan>
                        <tspan x="35.168" y="15" fill="#3A3A3A">函数可以将数字转化成文本。</tspan>
                        <tspan x="0" y="39" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="39" fill="#3F70FF">TEXT</tspan>
                        <tspan x="84.168" y="39" fill="#3A3A3A">(数字)</tspan>
                        <tspan x="0" y="63" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="63" fill="#3F70FF">TEXT</tspan>
                        <tspan x="84.168" y="63" fill="#3A3A3A">(3.1415)返回"3.1415"。</tspan>
                    </text>
                </g>
                <text id="TEXT" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">TEXT</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
