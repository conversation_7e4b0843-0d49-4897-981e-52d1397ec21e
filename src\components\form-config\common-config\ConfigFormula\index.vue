<template>
  <div class="w-full">
    <el-button class="w-full" size="large" @click="openDialog">公式编辑</el-button>
    <el-dialog
      title="公式编辑"
      append-to-body
      destroy-on-close
      v-model="visible"
      width="800px"
      :close-on-click-modal="false"
    >
      <b-formula
        v-model="formula"
        :formulaType="FormulaType.DATA"
        :fieldList="(filterList as FieldType[])"
        :valText="data.copyLabel"
      />
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="visible = false" round>取 消</el-button>
          <el-button type="primary" @click="saveFormula" round>确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { FormulaFieldType } from './type';
// import ConfigFormulaDialog from './ConfigFormulaDialog.vue';
import BFormula from '@/components/codemirror/BFormula.vue';
import { FormulaType } from '@/api/formula';
import useFieldList from '@/views/mainview/sceneTasks/detail/hooks/useFieldList.ts';
import { default as useAppFieldList }  from '@/views/banban/application/flowApp/hooks/useFieldList.ts';
import { canBeFormulaType } from '@/components/form-config/types/FieldTypes.ts';
import { PackageIdInjectKey } from '@/components/form-config/utils/injectKeys';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore.ts';
import { inject } from 'vue';
import UseApplicationDetailStore from '@/views/banban/application/flowApp/store/useApplicationDetailStore.ts';
import { formTypeEnum } from '@/components/form-config/const/commonDic.ts';
import { deptColumn } from '@/views/banban/application/flowApp/option/xtFields.ts'
import { FieldType } from '@/components/codemirror/types.ts';

const props = defineProps<{
  data: FormulaFieldType
}>();
const visible = ref(false);
const formType = inject('formType', undefined);

const formula = ref<any>();
const { taskId } = storeToRefs(useTaskDetailStore());
const { applicationId } = storeToRefs(UseApplicationDetailStore());
const selectTask = ref(taskId.value!);
const taskPackageId = inject(PackageIdInjectKey);

// 来源是微应用时，改一下fieldList来源
const { fieldList, getFiledList } = formType === formTypeEnum.APPLICATION ?
  useAppFieldList(canBeFormulaType, {
    outTask: false,
  })
  :
  useFieldList(canBeFormulaType, {
  outTask: !taskPackageId?.value,
  // initColumn: column,
  });

const filterList = computed(() => {
  const originalList = formType === formTypeEnum.APPLICATION ? [...fieldList.value,deptColumn] : fieldList.value;
  return originalList.filter(i => !_.includes(i.prop,props.data.prop))
});

getFiledList({
  id: (formType === formTypeEnum.APPLICATION ? applicationId.value :selectTask.value) as string,
  unFlatDynamic: true,
  unFlatDataCollect: true,
  unFlatDataSelect: true,
  unFlatKnowledgeSelect: true,
});

const openDialog = () => {
  //先选中自己
  visible.value = true;
  formula.value = _.cloneDeep(props.data.formula);
};


const saveFormula = () => {
  props.data.formula = _.cloneDeep(formula.value);
  ElMessage.success('公式保存成功');
  visible.value = false;
};
</script>

<style lang="scss" scoped>
::v-deep(.container) {
  max-height: 340px; /* 限制最大高度 */
}
</style>
