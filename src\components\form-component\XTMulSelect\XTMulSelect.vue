<template>
  <avue-select 
    v-if="type === SelectColumnTypeEnum.SELECT"
    all
    multiple
    v-model="vModel"
    placeholder="请选择内容"
    type="tree"
    v-bind="$attrs"
    :dic="dic">
  </avue-select>
  <div v-else-if="type === SelectColumnTypeEnum.CHECKBOX">
    <el-checkbox
      :disabled="($attrs.disabled as boolean)"
      v-model="checkAll"
      :indeterminate="isIndeterminate"
      @change="handleCheckAllChange"
    >
      全选
    </el-checkbox>
    <el-checkbox-group v-model="vModel" @change="handleCheckedCitiesChange" v-bind="$attrs">
      <el-checkbox
        :value="v.label"
        v-for="v in dic"
        :key="v.label"
        :label="v.label"
        :disabled="v.disabled"
      />
    </el-checkbox-group>

  </div>
  <XtCascader v-else v-bind="$attrs" v-model="vModel" />
</template>

<script setup lang="ts"> 
import { SelectColumnTypeEnum } from '@/components/form-config/const/commonDic';
import type { CheckboxValueType } from 'element-plus'
const props = defineProps<{
  dic: { label: string; disabled: boolean; value: string }[];
  source: number;
  type: SelectColumnTypeEnum;
}>();

const vModel = defineModel<string[]>({ default: [] });

const checkAll = ref(false)
const isIndeterminate = ref(true)
const handleCheckAllChange = (val: CheckboxValueType) => {
  vModel.value = val ? props.dic.map(item => item.label) : []
  isIndeterminate.value = false
}

const handleCheckedCitiesChange = (value: CheckboxValueType[]) => {
  const checkedCount = value.length
  checkAll.value = checkedCount === props.dic.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < props.dic.length
}
</script>
<style lang="scss" scoped></style>
