<template>
  <el-form-item>
    <el-checkbox v-model="limitDate">{{ title }}</el-checkbox>
  </el-form-item>
  <el-row :gutter="10" v-if="limitDate">
    <el-col :span="8">
      <el-select v-model="limitDateType" class="w-full" @change="handleChange">
        <el-option
          v-for="item in dateLimitTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-col>
    
    <!-- 固定值 -->
    <el-col :span="16" v-if="limitDateType === DateLimitTypeEnum.FIXED">
      <el-date-picker
        v-model="limitValue.value"
        type="date"
        placeholder="选择日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
      <!-- <XtDatePicker
        class="w-full"
        v-model="limitValue.value"
        v-bind="data"
        :disabled="false"
      >
      </XtDatePicker> -->
    </el-col>
    <!--表单值  -->
    <el-col :span="16" v-if="limitDateType === DateLimitTypeEnum.FORM">
      <el-select v-model="limitValue.value" class="w-full">
        <el-option
          v-for="item in dateDic"
          :key="item.prop"
          :label="item.copyLabel"
          :value="item.prop"
        />
      </el-select>
    </el-col>
    <!-- 动态值 -->
    <el-col :span="16" v-if="limitDateType === DateLimitTypeEnum.DYNAMICS">
      <el-select v-model="limitValue.value" class="w-full">
        <el-option
          v-for="item in dynamicsList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-col>
    <!-- 自定义 -->
     <template v-if="limitDateType === DateLimitTypeEnum.DEFAULT">
      <el-col :span="4" >
        <p class="color-#606266 font-size-14px mt-1 text-center">当前</p>
      </el-col>
      <el-col :span="6">
        <el-input-number
          :step="1"
          class="w-full"
          v-model="(limitValue.value as number)"
          controls-position="right"
          :step-strictly="true"
        />
      </el-col>
      <el-col :span="6">
        <el-select v-model="limitValue.type" class="w-full">
          <el-option
            v-for="item in dateDefaultInterList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-col>
     </template>
  </el-row>

</template>

<script setup lang="ts">
import { dateLimitTypeList,dynamicsList,dateDefaultInterList } from '../const.ts';
import { dataDefaultVal,DateAllField } from '../type.ts';
import { DateLimitTypeEnum } from '../const.ts';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { storeToRefs } from 'pinia';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts'

const { taskBase } = storeToRefs(useTaskDetailStore());

//日期类型
const dateDic = computed(() => {
  let column = (taskBase.value as any).column || [];
  return column.filter((item:AvueColumns )=> item.fixType === FixTypeEnum.DATE && item.prop !== props.data.prop);
});

const props = defineProps<{
  data: DateAllField;
  title: string;
}>();

const limitDate = defineModel<boolean>('limitDate')
const limitDateType = defineModel<string>('limitDateType')
const limitValue = defineModel<dataDefaultVal>('limitValue',{default:()=>({value:'',type:''})})

const handleChange = () =>{
  limitValue.value = { value:'',type:'' }
}

</script>