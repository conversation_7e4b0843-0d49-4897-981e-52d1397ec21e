import request from '@/axios';
// 获取部门tree
export const getDepartmentTree = tid => {
  return request({
    url: '/common/dept/tree',
    method: 'get',
    params: {
      tid,
    },
  });
};
// 成员分页
export const getDepartmentList = (current, size, params) => {
  return request({
    url: '/common/tenant/member/page',
    method: 'get',
    params: {
      current,
      size,
      ...params,
    },
  });
};
// 邀请
export const inviteuser = phone => {
  return request({
    url: '/blade-system/common/tenant/member/add',
    method: 'post',
    params: {
      phone,
    },
  });
};
// 新增部门
export const addDepartments = (deptName, parentId, tid) => {
  return request({
    url: '/common/dept/add',
    method: 'post',
    data: {
      deptName,
      parentId,
      tid,
    },
  });
};
// 部门重命名
export const editDepartmentName = (deptName, id) => {
  return request({
    url: '/common/dept/rename',
    method: 'put',
    data: {
      deptName,
      id,
    },
  });
};
// 删除部门
export const delDepartments = deptId => {
  return request({
    url: '/common/dept/delete',
    method: 'put',
    params: {
      deptId,
    },
  });
};
// 添加成员
export const newadduser = userTenant => {
  return request({
    url: '/common/tenant/member/add',
    method: 'post',
    data: {
      ...userTenant,
    },
  });
};
// 添加成员
export const getUserList = () => {
  return request({
    url: '/blade-system/role/listRole',
    method: 'post',
  });
};
// 移除成员
export const delusers = data => {
  return request({
    url: '/common/dept/remove/member',
    method: 'delete',
    data: {
      ...data,
    },
  });
};
// 成员编辑详情
export const editoruser = (tid, userId) => {
  return request({
    url: '/common/tenant/member/info',
    method: 'get',
    params: {
      tid,
      userId,
    },
  });
};
// 成员编辑
export const edituser = data => {
  return request({
    url: '/common/tenant/member/update',
    method: 'put',
    data: {
      ...data,
    },
  });
};
// 设置负责人
export const setConscientious = data => {
  return request({
    url: '/common/dept/update',
    method: 'put',
    data: {
      ...data,
    },
  });
};
// 设置管理员
export const setManager = data => {
  return request({
    url: '/common/dept/setLeaders',
    method: 'put',
    data: {
      ...data,
    },
  });
};
// 管理员负责人回显
export const getleader = deptId => {
  return request({
    url: '/common/dept/leader/feedback',
    method: 'get',
    params: {
      deptId,
    },
  });
};
// 外部联系人回显
export const getexternal = tenantId => {
  return request({
    url: '/common/tenant/getOuterContact',
    method: 'get',
    params: {
      tenantId,
    },
  });
};
// 外部联系人提交
export const setOuterContact = (obj, tenantId) => {
  return request({
    url: '/common/tenant/setOuterContact',
    method: 'post',
    data: {
      obj,
      tenantId,
    },
  });
};
// 下载模板
export const exportBlob = () => {
  return request({
    url: '/common/down-template',
    method: 'get',
    responseType: 'blob',
  });
};
// 导出用户
export const exportPlace = (deptId, tid, userIds) => {
  return request({
    url: '/common/user-export',
    method: 'post',
    data: {
      deptId,
      tid,
      userIds,
    },
    responseType: 'blob',
  });
};
// 导出数据
export const exportPlaceLength = (deptId, tid, userIds) => {
  return request({
    url: '/common/user-export-count',
    method: 'post',
    data: {
      deptId,
      tid,
      userIds,
    },
  });
};
// 部门导入模板校验
export const exportCheck = data => {
  return request({
    url: '/common/user-import-validate',
    method: 'post',
    data: data,
  });
};
// 部门不勾选上传接口
export const exportImport = data => {
  return request({
    url: '/common/user-import',
    method: 'post',
    data: data,
  });
};

// 部门导入模板核验校验
export const exportVerifyCheck = data => {
  return request({
    url: '/common/user/verify/import',
    method: 'post',
    data: data,
  });
};
// 任务包获取
export const gettask = () => {
  return request({
    url: '/scene_task_group/getVersionList',
    method: 'get',
  });
};
// 工作组获取
export const getworks = tid => {
  return request({
    url: '/workgroup/tree',
    method: 'get',
    params: {
      tid,
    },
  });
};
// 工作组成员获取
export const getworkinguser = data => {
  return request({
    url: '/workgroup/page/user',
    method: 'get',
    params: {
      ...data,
    },
  });
};
// 工作组成员获取
export const setworking = (resumptionSceneGroupIdList, resumptionUserIdList, userId) => {
  return request({
    url: '/common/resumption/set',
    method: 'post',
    data: {
      resumptionSceneGroupIdList,
      resumptionUserIdList,
      userId,
    },
  });
};
// 工作组成员获取
export const getetworking = (tid, userId) => {
  return request({
    url: '/common/tenant/member/info',
    method: 'get',
    params: {
      tid,
      userId,
    },
  });
};
// 排序
export const sort = data => {
  return request({
    url: '/common/dept/sort',
    method: 'put',
    data: data,
  });
};
// 发起核验
export const getVerify = userIds => {
  return request({
    url: '/common/user/request/verify',
    method: 'post',
    data: userIds,
  });
};
// 查询移除人应办数据
export const getdelUserData = (userIdList) => {
  return request({
    url: '/staging/queryByUser',
    method: 'post',
    data: userIdList,
  });
};
// 应办转交
export const handledTransfer = (forwardedHandledDtos) => {
  return request({
    url: '/staging/forwarded',
    method: 'post',
    data: forwardedHandledDtos,
  });
};
// 查询移除明细
export const getdetachUser = (deptId) => {
  return request({
    url: '/removal/query',
    method: 'get',
    params: {deptId},
  });
};
