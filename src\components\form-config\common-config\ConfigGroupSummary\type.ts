import { DynamicAllField } from '../../field-config';

//分组汇总

export interface groupItem {
  idx: string;
  pathField: string; //分组字段
  method: string;
  field: string; //绑定字段
}

export interface groupSummaryType {
  path: string;
  group?: groupItem[];
  summary?: groupItem[];
}
export type groupSummaryField = {
  groupSummary?: groupSummaryType;
};

export type gruopSummaryAllField = DynamicAllField & groupSummaryField;
