<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>SUM</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-570.000000, -350.000000)">
            <g id="SUM" transform="translate(570.000000, 350.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="日期天数函数可以将日期时间字段值加/减指-2" transform="translate(10.000000, 42.000000)"
                   font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="23">
                    <text id="SUM函数可以获取一组数值的总和。-·用">
                        <tspan x="0" y="15" fill="#3F70FF">SUM</tspan>
                        <tspan x="31.192" y="15" fill="#3A3A3A">函数可以获取一组数值的总和。</tspan>
                        <tspan x="0" y="39" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="39" fill="#3F70FF">SUM</tspan>
                        <tspan x="80.192" y="39" fill="#3A3A3A">(数字1,数字2,···)</tspan>
                        <tspan x="0" y="63" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="63" fill="#3F70FF">SUM</tspan>
                        <tspan x="80.192" y="63" fill="#3A3A3A">(</tspan>
                    </text>
                </g>
                <g id="编组-30" transform="translate(98.000000, 93.000000)">
                    <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="52" height="16"></rect>
                    <text id="语文成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                          font-weight="normal" line-spacing="16" fill="#3F70FF">
                        <tspan x="6" y="11">语文成绩</tspan>
                    </text>
                </g>
                <g id="编组-30" transform="translate(157.000000, 93.000000)">
                    <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="52" height="16"></rect>
                    <text id="数学成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                          font-weight="normal" line-spacing="16" fill="#3F70FF">
                        <tspan x="6" y="11">数学成绩</tspan>
                    </text>
                </g>
                <g id="编组-30" transform="translate(215.000000, 93.000000)">
                    <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="52" height="16"></rect>
                    <text id="英语成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                          font-weight="normal" line-spacing="16" fill="#3F70FF">
                        <tspan x="6" y="11">英语成绩</tspan>
                    </text>
                </g>
                <text id="，" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="151" y="106">，</tspan>
                </text>
                <text id="，备份" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="209" y="106">，</tspan>
                </text>
                <text id="）" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="270" y="106">）</tspan>
                </text>
                <text id="返回" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="282" y="105">返回</tspan>
                </text>
                <text id="三门课程的总分。" font-family="PingFangSC-Regular, PingFang SC" font-size="14"
                      font-weight="normal" fill="#3A3A3A">
                    <tspan x="17" y="132">三门课程的总分。</tspan>
                </text>
                <text font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">SUM</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
