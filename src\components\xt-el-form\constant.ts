import { InjectionKey, Ref } from 'vue';
import { AvueForm } from '@/components/form-config/types/AvueTypes.ts';

export const formInjectKey: InjectionKey<Ref<AvueForm>> = Symbol('form');

export const disabledInjectKey: InjectionKey<Ref<boolean>> = Symbol('disabled');
export const MenuDisabledInjectKey: InjectionKey<boolean> = Symbol('menuDisabled');

export const packageInjectKey: InjectionKey<Ref<string | undefined> | undefined> =
  Symbol('packageId');

export const previousShouldDoInjectKey: InjectionKey<Ref<string | undefined> | undefined> =
  Symbol('previousShouldDoId');

export const handleIdInjectKey: InjectionKey<string | undefined> = Symbol('handleId');
export const handleTypeInjectKey: InjectionKey<string | undefined> = Symbol('handleType'); //1 应办 2 即时办事

 

export const taskIdInjectKey: InjectionKey<Ref<string | undefined>> = Symbol('taskId');

export const taskSourceInjectKey:InjectionKey<Ref<string | undefined> | undefined> = Symbol('taskSource');//应办的组织来源
export const tenantIdInjectKey: InjectionKey<Ref<string | undefined> | undefined> = Symbol('tenantId');//应办的组织来源

export const formDisabledKey: InjectionKey<Ref<boolean | undefined> | undefined> = Symbol('formDisabled');//表格是否禁用

export const fieldItemSourceKey:InjectionKey<string | undefined> = Symbol('fieldItemSource');// 三连框的用处

export const previewTypeKey:InjectionKey<number | undefined> = Symbol('previewType');// 区分表单的预览，应办/微应用等

export const userRangeTypeKey:InjectionKey<string | undefined> = Symbol('userRangeType');// 成员选择配置用户范围 1.全部 2.内部 3.外部

export const isApiLinkKey:InjectionKey<Ref<boolean | null>> = Symbol('isApiLink');// 是否正在请求联动






  


