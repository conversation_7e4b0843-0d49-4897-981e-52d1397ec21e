
import { getAllArticle, getAllSpace } from "@/api/common/index.ts"
import { tenantIdInjectKey,taskSourceInjectKey } from '@/components/xt-el-form/constant.ts'
import { getDepartmentTree } from '@/api/zy/zycore';
import { getWorkGroupTree } from '@/api/InstantService/index';
import { getUserListByTid } from '@/api/orgInfo';
import { useStore } from 'vuex';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts';
import {
  userDeptType,
  usersType,
  userTagType,
  inventoryComType,
  interspaceComType
} from "@/components/form-config/types/FieldTypes.ts";
export function useDataList(columns?: AvueColumns[],) {
  const spaceList = ref<any[]>([])
  const articleList = ref<any[]>([])
  const deptTreeData = ref<any[]>([])
  const dutyTreeData = ref<any[]>([])
  const allUserList = ref<any[]>([])
  const tenantId = inject(tenantIdInjectKey, undefined); // 任务组织来源
  const taskSource = inject(taskSourceInjectKey, undefined); // 任务组织来源

  const store = useStore();
  const userMsg = store.state.user.userInfo;
  //空间组件列表
  const getSpace = async () => {
    const { data } = await getAllSpace({ tenantId: tenantId?.value || userMsg.tenant_id })
    spaceList.value = data.data
  }

  //物品组件列表
  const getArticle = async () => {
    const { data } = await getAllArticle({ tenantId: tenantId?.value || userMsg.tenant_id })
    articleList.value = data.data
  }

  //部门列表
  const initDeptTreeData = async () => {
    const res = (await getDepartmentTree(taskSource?.value || userMsg.tid)).data.data || [];
    deptTreeData.value = flattenTree(res) || []
  };

  //工作组列表
  const initDutyTreeData = async () => {
    let res = (await getWorkGroupTree({ tid: taskSource?.value || userMsg.tid })).data.data || [];
    dutyTreeData.value = flattenTree(res) || []
  };

  //用户列表
  const initAllUserList = async () => {
    allUserList.value = (await getUserListByTid(taskSource?.value || userMsg.tid)).data.data;
  };
  const filterColumn = (typeList: any) => {
    return columns?.some(item => typeList.includes(item.fixType));
  }
  const flattenTree = (treeData: any, flattenedArray: any = []) => {
    treeData.forEach((node: any) => {
      flattenedArray.push({ ...node, children: [] });
      if (node.children && node.children.length > 0) {
        flattenTree(node.children, flattenedArray);
      }
    });
    return flattenedArray;
  };

  if (columns?.length) {
    if (filterColumn(usersType)) {
      initAllUserList()
    }
    if (filterColumn(userDeptType)) {
      initDeptTreeData()
    }
    if (filterColumn(userTagType)) {
      initDutyTreeData()
    }
    if (filterColumn(inventoryComType)) {
      getArticle();
    }
    if (filterColumn(interspaceComType)) {
      getSpace();
    }
  }


  return {
    getSpace,
    getArticle,
    initDeptTreeData,
    initDutyTreeData,
    initAllUserList,
    spaceList,
    articleList,
    deptTreeData,
    dutyTreeData,
    allUserList
  }
}