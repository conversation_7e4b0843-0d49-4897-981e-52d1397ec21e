<!-- ModelItem.vue -->
<template>
  <div class="border-1 border-#ebebeb border-solid p-20px mt-10px">
    <div class="flex items-center justify-between w-full">
      <h4>型号 {{ Nzh.cn.encodeS(index + 1) }}</h4>
      <el-icon
        color="#ff7070"
        class="ml-30px cursor-pointer"
        @click="$emit('delete', model.shelvesModel.id)"
        ><DeleteFilled
      /></el-icon>
    </div>

    <el-form-item label="型号名称">
      <el-input v-model="model.shelvesModel.name" placeholder="型号名称" />
    </el-form-item>

    <el-button
      @click="solutionStore.addPermission(model.shelvesPlanDetailList)"
      type="primary"
      size="small"
      >+ 添加使用权方案</el-button
    >
    <div v-for="(permission, pInx) in model.shelvesPlanDetailList" :key="permission.id">
      <PermissionSection :permission="permission" :index="pInx" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import Nzh from 'nzh';
import { DeleteFilled } from '@element-plus/icons-vue';
import { useSolutionStore } from '../store/index';
import PermissionSection from './PermissionSection/index.vue';
import { Model } from '../types';

defineProps<{
  model: Model;
  index: number;
}>();

defineEmits(['delete'])

const solutionStore = useSolutionStore();
</script>
