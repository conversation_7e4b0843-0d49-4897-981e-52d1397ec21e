import request from '@/axios';

//微应用文件夹列表
export const getMicroAppFolderList = () => {
  return request({
    url: '/microAppFolder/list',
    method: 'get',
  });
};
//微应用文件夹树
export const getMicroAppFolderTree = (tenantId:string) => {
  return request({
    url: '/microAppFolder/tree',
    method: 'get',
    params:{
      tenantId
    }
  });
};
//微应用文件夹树根据id获取子文件夹
export const getMicroAppFolderById = (parentId:any,type:0|1|2|3) => {
  return request({
    url: '/microAppFolder/getChildren',
    method: 'get',
    params:{
      parentId,type
    },
  });
};
//微应用文件夹新增
export const addMicroAppFolder = (data:any) => {
  return request({
    url: '/microAppFolder/submit',
    method: 'post',
    data,
  });
};
//微应用文件夹树新增
export const addMicroAppFolderTree = (data:any) => {
  return request({
    url: '/microAppFolder/add',
    method: 'post',
    data,
  });
};
//微应用文件夹树编辑
export const updateMicroAppFolderTree = (data:any) => {
  return request({
    url: '/microAppFolder/update',
    method: 'put',
    data,
  });
};
//微应用文件夹重命名
export const renameMicroAppFolder = (data:{folderId:string,name:string}) => {
  return request({
    url: '/microAppFolder/rename',
    method: 'put',
    data,
  });
};
// //微应用重命名
// export const reNameMicroApp = (data:any) => {
//   return request({
//     url: '/microApp/rename',
//     method: 'put',
//     data,
//   });
// };
//微应用编辑
export const editMicroApp = (data:any) => {
  return request({
    url: '/microApp/update',
    method: 'put',
    data,
  });
};
//微应用列表
export const getMicroApp = (folderId:string) => {
  return request({
    url: `/microApp/user/list/${folderId}`,
    method: 'get',
  });
};
//获取 政府类 企业类 个人类微应用
export const getClassifyMicroApp = () => {
  return request({
    url: `/micro-app-common/getClassifyMicroApp`,
    method: 'get',
  });
};
//微应用列表管理员
export const getMicroAppAdminPage = (current:number,size:number,params:any) => {
  return request({
    url: `/microApp/manage/list/${params.folderId}`,
    method: 'post',
    params:{
      current,size,...params
    },
  });
};
//微应用新增
export const addMicroApp = (data:any) => {
  return request({
    url: '/microApp/submit',
    method: 'post',
    data
  });
};
//微应用复制
export const copyMicroApp = (microAppId:string) => {
  return request({
    url: `/microApp/copy/${microAppId}`,
    method: 'put',
  });
};

//微应用删除
export const deleteMicroApp = (ids :string) => {
  return request({
    url: '/microApp/remove',
    method: 'post',
    params: {
      ids
    }
  });
};
//微应用启用
export const enableMicroApp = (microAppId :string) => {
  return request({
    url: `/microApp/enable/${microAppId}`,
    method: 'put',
  });
};
//微应用停用
export const disableMicroApp = (microAppId :string) => {
  return request({
    url: `/microApp/disable/${microAppId}`,
    method: 'put',
  });
};
//微应用分类排序
export const sortMicroAppFolder = (folderIds :string[]) => {
  return request({
    url: `/microAppFolder/sort`,
    method: 'put',
    data:{
      folderIds
    }
  });
};
//微应用排序
export const sortMicroApp = (folderId :string,microAppIds:string[]) => {
  return request({
    url: `/microApp/sort`,
    method: 'put',
    data:{
      folderId,
      microAppIds
    }
  });
};