<script setup lang="ts">
import { range } from 'lodash';
import { CascaderOption, CascaderValue } from 'element-plus';
import _ from 'lodash';

interface DateTimeValue {
  timeClassification: 'CURRENT' | 'PAST' | 'FUTURE' | ''
  values: string
  timeUnits: 'DAY' | 'WEEK' | 'MONTH' | 'QUARTER' | 'YEAR' | ''
}

interface DateCustomValueType {
  startDateTime?: DateTimeValue
  endDateTime?: DateTimeValue
}

const getDayNumber = (start:number=1, end:number=100): CascaderOption[] => {
  return range(start, end).map(v => ({
    value: v.toString(),
    label: v.toString(),
    children: [
      { value: 'DAY', label: '天' },
      { value: 'WEEK', label: '周' },
      { value: 'MONTH', label: '月' },
      { value: 'QUARTER', label: '季' },
      { value: 'YEAR', label: '年' }
    ]
  }));
};

const options: CascaderOption[] = [
  {
    value: 'CURRENT',
    label: '当前',
    children: getDayNumber(1,2)
  },
  {
    value: 'PAST',
    label: '过去',
    children: getDayNumber()
  },
  {
    value: 'FUTURE',
    label: '未来',
    children: getDayNumber()
  }
];

const vModel = defineModel<DateCustomValueType>({ required: true });

onMounted(() => {
  if (_.isEmpty(vModel.value)) {
    vModel.value = {
      startDateTime: {
        timeClassification: '',
        values: '',
        timeUnits: ''
      },
      endDateTime: {
        timeClassification: '',
        values: '',
        timeUnits: ''
      }
    };
  }
});

const value1 = ref<string[]>([]);
const value2 = ref<string[]>([]);

watchEffect(() => {
  value1.value = [
    vModel.value?.startDateTime?.timeClassification || '',
    vModel.value?.startDateTime?.values || '',
    vModel.value?.startDateTime?.timeUnits || ''
  ];
  value2.value = [
    vModel.value?.endDateTime?.timeClassification || '',
    vModel.value?.endDateTime?.values || '',
    vModel.value?.endDateTime?.timeUnits || ''
  ];
});

const handleChange = (value: CascaderValue, type: 1 | 2) => {
  if (!vModel.value) return;

  const [timeClassification = '', values = '', timeUnits = ''] = value;

  const updateObject = {
    timeClassification: timeClassification as DateTimeValue['timeClassification'],
    values,
    timeUnits: timeUnits as DateTimeValue['timeUnits']
  };

  if (type === 1) {
    vModel.value.startDateTime = {
      ...(vModel.value.startDateTime || {}),
      ...updateObject
    };
  } else {
    vModel.value.endDateTime = {
      ...(vModel.value.endDateTime || {}),
      ...updateObject
    };
  }
};
</script>

<template>
  <div class="flex items-center gap-5px">
    <el-cascader v-model="value1" separator="-" :options="options" @change="handleChange($event,1)" />
    到
    <el-cascader v-model="value2" separator="-" :options="options" @change="handleChange($event,2)" />
  </div>
</template>

<style scoped lang="scss">

</style>