<template>
  <div>
    <config-title :data="data"></config-title>
    <el-form-item label="字段编辑">
      <div v-for="(item, key) in data.extraCol" :key="key">
        <el-checkbox v-model="item.display">
          <el-input :value="getDisplayLabel(item)" disabled></el-input>
        </el-checkbox>
      </div>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import { BasicInfoAllField } from './type';

defineProps<{
  data: BasicInfoAllField;
}>();

// 获取显示标签，优先使用 copyLabel，其次使用 label
const getDisplayLabel = (item: any) => {
  return item.copyLabel || item.label || '';
};
</script>

<style scoped lang="scss"></style>
