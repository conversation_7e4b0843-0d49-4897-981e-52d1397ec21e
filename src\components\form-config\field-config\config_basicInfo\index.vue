<template>
  <div>
    <config-title :data="data"></config-title>
    <el-form-item label="字段编辑">
      <div v-for="item in data.extraCol">
        <el-checkbox v-model="item.display">
          <el-input v-model="item.copyLabel" disabled></el-input>
        </el-checkbox>
      </div>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import { BasicInfoAllField } from './type';

defineProps<{
  data: BasicInfoAllField;
}>();
</script>

<style scoped lang="scss"></style>
