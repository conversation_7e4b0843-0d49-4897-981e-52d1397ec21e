import { DropdownItem, MarkMenuTypeEnum } from '@/components/chart-design/types/widget.ts';
import { ElDropdownItem, ElMessageBox } from 'element-plus';

const generator: DropdownItem = {
  key: MarkMenuTypeEnum.RENAME,
  label: '重命名',
  component: function(params) {
    return defineComponent(() => {
      return () => {
        return (
          <ElDropdownItem
            onClick={() => {
              ElMessageBox.prompt('请输入名称', '重命名')
                .then(({ value }) => {
                  params.item.title = value || params.item.title;
                });
            }}
          >
            {this.label}
          </ElDropdownItem>
        );
      };
    });
  }
};

export default generator;