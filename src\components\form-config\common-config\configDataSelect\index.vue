<template>
  <div>
    <config-title :data="data"></config-title>
    <config-permission :data="data" />
    <config-default-type :data="data" />
  </div>
</template>

<script setup lang="ts">
import { DataSelectAllField } from './type';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
// import Condition from './common/condition/index.vue'

defineProps<{
  data: DataSelectAllField;
}>();

</script>

<style scoped lang="scss">
.dataBox {
  :deep(.el-checkbox-group) {
    @apply flex-(~ col) ml-10px;
  }
}
</style>
