import { 
  FieldBaseField, AvueColumns
} from '@/components/form-config/types/AvueTypes';

// 微应用-数据选择
export type DataFieldBase = {
  showFieldIds?: string[];
  showFields?: AvueColumns[];
}
export type DataSelection = DataFieldBase & {
  query:DataSelectionQuery
}
export type DataSelectField = DataFieldBase & {
  taskId?: string;
};

export type DataSelectionQuery = {
  filter:any[],
  showFieldIds:string[],
  fieldRule:number,
  showField:any[],
  path:string
}
export type DataSelectionAllField = DataSelection & FieldBaseField
export type DataSelectAllField = FieldBaseField & DataSelectField;