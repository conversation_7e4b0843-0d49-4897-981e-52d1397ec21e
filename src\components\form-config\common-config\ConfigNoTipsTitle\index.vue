<template>
  <el-form-item label="标题">
    <el-input
      v-model="data.copyLabel"
      clearable
      placeholder="请输入标题"
      @change="handleChangeTitle"
    ></el-input>
    <el-checkbox v-model="data.showLabel" label="显示标题" />
  </el-form-item>
</template>

<script setup lang="ts">
import { watchEffect } from 'vue';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { storeToRefs } from 'pinia';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';
import { configNoTipsTitleType } from './type.ts';

const props = defineProps<configNoTipsTitleType>();

const { taskBase } = storeToRefs(useTaskDetailStore());

const handleChangeTitle = ( value:string ) => {
  const { column } = taskBase.value as TaskBase;
  // 判断有没有相同标题的组件
  const sameTextList = column?.filter(item => {
    if (item.fixType === props.data.fixType) {
      return item.copyLabel === value;
    }
  }) || [];
  if (sameTextList?.length >= 2) {
    // ElMessage({
    //   message: '不允许有相同标题的组件',
    //   type: 'warning',
    // });
    // props.data.copyLabel = '';
  }
};

watchEffect(() => {
  // props.data.label = props.data.showLabel ? props.data.copyLabel : '';
});
</script>

<style scoped lang="scss"></style>
