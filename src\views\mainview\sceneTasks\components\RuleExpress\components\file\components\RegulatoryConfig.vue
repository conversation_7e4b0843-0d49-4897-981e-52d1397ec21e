<template>
  <div>
    <el-form
      ref="formRef"
      :model="regulatoryData"
      label-position="left"
      :rules="rules"
      label-width="150"
      class="max-w-550px"
    >
      <template v-for="h in formLabelOption" :key="h.title">
        <h3>{{ h.title }}</h3>
        <el-form-item
          v-for="item in h.option"
          :key="item.label"
          :label="item.label"
          :required="hasRequired(item)"
        >
          <el-row :gutter="5" class="w-full" v-if="item.select.length">
            <el-col
              :span="item.select.length === 1 ? 24 : 12"
              v-for="(st, sIndex) in item.select"
              :key="sIndex"
            >
              <el-form-item :prop="st.value" :rules="(getRules(st) as any)">
                <component
                  v-if="
                    item.type !== 'input' &&
                    item.type !== 'select' &&
                    item.type !== 'date' &&
                    item.type !== 'scope'
                  "
                  :is="getComponent(st.fixType, st.component, st?.showFormat)"
                  v-model="regulatoryData[(st.value as keyof typeof regulatoryData)]"
                  :dic="item.dic"
                  :placeholder="`请选择${item.label}`"
                  :data="{ disabled: false, st }"
                  @blur="(v:any) => handleBlur(v, item)"
                />

                <div v-else-if="item.type === 'date'">
                  <a-date-picker
                    class="w-full"
                    v-if="st.value === 'effectiveTime'"
                    v-model:value="regulatoryData[(st.value as keyof typeof regulatoryData)]"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :disabled-date="disabledDateByEffecTime"
                    :getCalendarContainer="(triggerNode:any) => triggerNode.parentNode"
                  />
                  <a-date-picker
                    class="w-full"
                    v-else-if="st.value === 'failureTime'"
                    v-model:value="regulatoryData[(st.value as keyof typeof regulatoryData)]"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :disabled-date="disabledDateByFailTime"
                    :getCalendarContainer="(triggerNode:any) => triggerNode.parentNode"
                  />
                  <a-date-picker
                    class="w-full"
                    v-else-if="st.value === 'tellTime'"
                    v-model:value="regulatoryData[(st.value as keyof typeof regulatoryData)]"
                    format="YYYY-MM-DD HH:mm:ss"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :show-time="{ defaultValue: dayJs('00:00:00', 'HH:mm:ss') }"
                    :getCalendarContainer="(triggerNode:any) => triggerNode.parentNode"
                    placement="topRight"
                  />
                  <component
                    v-else
                    :is="getComponent(st.fixType, st.component, st?.showFormat)"
                    v-model="regulatoryData[(st.value as keyof typeof regulatoryData)]"
                    :dic="item.dic"
                    placeholder="请输入"
                    :disabled="item.disabled"
                    v-bind="{ ...st, value: regulatoryData[(st.value as keyof typeof regulatoryData)] }"
                    @blur="(v:any) => handleBlur(v, item)"
                  />
                </div>
                <component
                  v-else
                  :is="getComponent(st.fixType, st.component, st?.showFormat)"
                  v-model="regulatoryData[(st.value as keyof typeof regulatoryData)]"
                  :dic="item.dic"
                  placeholder="请输入"
                  :disabled="item.disabled"
                  v-bind="{ ...st, value: regulatoryData[(st.value as keyof typeof regulatoryData)] }"
                  @blur="(v:any) => handleBlur(v, item)"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div v-if="item.child?.length" class="w-full">
            <el-row v-for="(ct, cIndex) in item.child" :key="cIndex" :gutter="5">
              <el-col :span="12" v-for="(ctItem, ctInx) in ct.select" :key="ctInx">
                <el-form-item :prop="childPropValid(item)">
                  <component
                    :is="getComponent(ctItem.fixType, ctItem.component, ctItem?.showFormat)"
                    :placeholder="ctItem.fixType === 'input' ? '请输入' : `请选择${ct.label}`"
                    v-model="regulatoryData[(ctItem.value as keyof typeof regulatoryData)]"
                    v-bind="{ ...ctItem, value: regulatoryData[(ctItem.value as keyof typeof regulatoryData)] }"
                    :data="{ disabled: false }"
                    @blur="(v:any) => handleBlur(v, ct)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import type { FormRules } from 'element-plus';
import useComponents from '@/components/form-config/hooks/useComponents';
import { formLabelOption } from '../../../../../detail/option/taskFileDic';
import { regulatoryType } from '../../../../../detail/types/TaskDetailType';
import { ElMessage } from 'element-plus';
import { taskSourceInjectKey } from '@/components/xt-el-form/constant.ts';
import { InstitutionalLevelsDIC } from '@/views/banban/know/regulations/options';
import { useStore } from 'vuex';
import dayJs from 'dayjs';

const regulatoryData = defineModel<regulatoryType>({ default: {} });
const formRef = ref();

const store = useStore();
const tid = computed(() => {
  const userMsg = store.getters.userInfo;
  return userMsg.tid;
});
const userMsg = computed(() => {
  return store.getters.userInfo;
});
provide(taskSourceInjectKey, tid);

let unRequiredFieldArr = ['releaseContent', 'failureTime', 'otherNumber', 'contentSummary'];

const hasRequired = computed(() => (item: any) => {
  let valueArr = item.select.map((item: { value: string; type: string }) => item.value);
  if (valueArr.some((s: string) => unRequiredFieldArr.includes(s))) return false;
  return true;
});
const handleBlur = (v: any, item: any) => {
  // 失焦时判断代码是否和类型符合
  const { value } = v;
  const { type } = item;
  if (!value) return;
  if (value && !value.includes(':')) return;
  let endIndex = value.indexOf(':');
  let valueType = value.slice(2, endIndex);
  const codeText = (item.select.length && item.select[1].value) || '';
  if (valueType !== type) {
    regulatoryData.value[codeText as keyof typeof regulatoryData.value] = '';
    ElMessage.warning('复制代码类型和当前不符合,请重新复制');
  }
};

const disabledDateByEffecTime = (current: any) => {
  return (
    current && current > dayJs(regulatoryData.value?.failureTime).endOf('day').subtract(1, 'day')
  );
};
const disabledDateByFailTime = (current: any) => {
  return current && current < dayJs(regulatoryData.value?.effectiveTime).endOf('day');
};

const rules = reactive<FormRules<regulatoryType>>({
  level: [
    {
      required: true,
      message: '制度层级必填',
      trigger: 'change',
    },
  ],
  orgId: [
    {
      required: true,
      message: '制定单位或代码必填其一',
      trigger: 'change',
    },
  ],
  pubOrgIdField: [
    {
      required: true,
      message: '制定单位或代码必填其一',
      trigger: 'change',
    },
  ],
});

const getRules = computed(() => (st: any) => {
  if (st.value === 'tellTime') {
    const { tellTime, tellTimeField } = regulatoryData.value!;
    if (tellTime || tellTimeField) {
      return false;
    } else {
      return [
        {
          required: true,
          message: '告知时间或代码必填其一',
          trigger: 'change',
        },
      ];
    }
  } else if (st.value === 'effectiveTime') {
    const { effectiveTime, effectiveTimeField } = regulatoryData.value!;
    if (effectiveTime || effectiveTimeField) {
      return false;
    } else {
      return [
        {
          required: true,
          message: '生效时间或代码必填其一',
          trigger: 'change',
        },
      ];
    }
  } else if (st.value === 'pubTime') {
    const { pubTime, pubTimeField } = regulatoryData.value!;
    if (pubTime || pubTimeField) {
      return false;
    } else {
      return [
        {
          required: true,
          message: '发布时间或代码必填其一',
          trigger: 'change',
        },
      ];
    }
  } else if (st.value === 'systemName') {
    const { systemName, systemNameField } = regulatoryData.value!;
    if (systemName || systemNameField) {
      return false;
    } else {
      return [
        {
          required: true,
          message: '制度名称或代码必填其一',
          trigger: 'change',
        },
      ];
    }
  }
});

const childPropValid = (item: any) => {
  if (!item.child || item.disabled) return '';
  let selects = item.child.map((item: any) => item.select);
  let validField = '';
  selects.forEach((s: any) => {
    let value = regulatoryData.value[s[0].value as keyof regulatoryType];
    let code = regulatoryData.value[s[1].value as keyof regulatoryType];
    if (value || code) {
      validField = '';
    } else {
      validField = s[0].value;
    }
  });
  return validField;
};

watch(
  () => [regulatoryData.value.workingGroupId, regulatoryData.value.depId],
  ([w, d]) => {
    if (_.isEmpty(w) && _.isEmpty(d)) {
      regulatoryData.value.level = InstitutionalLevelsDIC[0].value;
    } else {
      regulatoryData.value.level = InstitutionalLevelsDIC[1].value;
    }
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  regulatoryData.value.orgId = userMsg.value.tid;
  regulatoryData.value.orgIdName = userMsg.value.real_name;
});

const { getComponent } = useComponents();

defineExpose({
  formRef,
});
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label-wrap .el-form-item__label) {
  margin: 0 !important;
  display: flex !important;
  align-items: start !important;
}
:deep(.el-form-item__content) {
  flex-direction: column;
  align-items: start;
}
</style>
