// import { selectType } from './../../form-config/types/FieldTypes';
import XtDateRange from '@/components/form-component/XTDaterange/XTDaterange.vue';
import XtDynamicFilter from '../components/XtDynamicFilter.vue';
import {
  AddressAllField,
  AvueColumns,
  NumberField,
  SelectAllField
} from '@/components/form-config/types/AvueTypes.ts';
import {
  triggerTimeType,
  addressType,
  inputType,
  interspaceComType,
  inventoryComType,
  dateType,
  numberType,
  multipleSelectType,
  mulCascaderType,
  usersType,
  userTagType,
  userDeptType,
  formulaEditType,
  targetValueType,
  statisticalType,
  adminAllType,
  submitterTheDeptType,
  submitterTheWorkType,
  submitterTimeType,
  departMentType,
  workLeadType,
  organizeAdminType,
  executorUserType,
  submitterType,
  noSubmitUserType,
  calculationTableType,
  departAdminType,
  workingAdminType,
  getUsercodeType,
  endUpdateTimeType,
  endUpdateUserType,
  updateTimeType,
  defaultDeptType,
  launchUserType,
  launchDeptType,
  launchDutyType,
  dateRangeType,
  mulSelectType, selectType,userCurrentType
} from '@/components/form-config/types/FieldTypes.ts';
import XtField from '@/components/nbxt/XtField.vue';
import { ElInput, ElDatePicker } from 'element-plus';
import { PropType } from 'vue';
import NumberRange from '@/views/mainview/sceneTasks/detail/components/common/NumberRange.vue';
import { SymbolListTypes, TaskLinkageTypes } from '@/components/conditions/types/types.ts';
import DateCustomFilter from '../components/DateCustomFilter.vue';
import DateToBefore from '../components/DateToBefore.vue';
import SelectUser from '@/views/banban/application/flowApp/approvalProcess/scWorkflow/component/selectUserIdList.vue';
import { SelectColumnTypeEnum } from '@/components/form-config/const/commonDic';

export enum MethodSymbolEnum {
  /**  等于 */
  EQ = 'eq',
  /** 不等于 */
  NEQ = 'neq',
  /** 等于任意一个 */
  EQ_ANY = 'eqAny',
  /** 不等于任意一个 */
  NEQ_ANY = 'neqAny',
  /** 包含 */
  INCLUDE = 'include',
  /** 不包含 */
  EXCLUDE = 'exclude',
  /** 为空 */
  IS_NULL = 'isNull',
  /** 不为空 */
  NOT_NULL = 'notNull',
  /** 大于 */
  GT = 'gt',
  /** 大于等于 */
  GE = 'ge',
  /** 小于 */
  LT = 'lt',
  /** 小于等于 */
  LE = 'le',
  /** 大于今天 */
  GT_TODAY = 'gtToday',
  /** 大于等于今天 */
  GE_TODAY = 'geToday',
  /** 小于今天 */
  LT_TODAY = 'ltToday',
  /** 小于等于今天 */
  LE_TODAY = 'leToday',
  /** 等于今天  */
  EQ_TODAY = 'eqToday',
  /** 选择范围 */
  BETWEEN = 'between',
  /** 包含任意一个 */
  INCLUDE_ANY = 'includeAny',
  /** 同时包含 */
  INCLUDE_ALL = 'includeAll',
  /** 属于 */
  BELONG = 'belong',
  /** 不属于 */
  NOT_BELONG = 'notBelong',
  /** 此刻往前 */
  INNOVATE = 'innovate',
  /** 某时往前 */
  MOVING_BEFORE = 'movingBefore',
  /** 某时往后 */
  MOVING_AFTER = 'movingAfter',
  /** 等于今日.日 */
  EQ_DAY = 'eqDay',
  /** 等于今日.月 */
  EQ_YEAR = 'eqYear',
  /** 等于今日.年 */
  EQ_MONTH = 'eqMonth',
  /** 包含当前用户 */
  INCLUDE_CURUSER = 'includeCurUser',
  /** 被包含于 */
  BE_INCLUDE = 'beInclude',
  /** 被包含任意一个 */
  BE_INCLUDE_ANY = 'beIncludeAny',
  /** 不包含当前用户 */
  NOT_INCLUDE_CUR_USER = 'notIncludeCurUser',
  /** 等于此刻 */
  EQ_AT = 'eqAt',
  /** 大于此刻 */
  GT_AT = 'gtAt',
  /** 小于此刻 */
  LT_AT = 'ltAt',
  /** 动态筛选 */
  DYNAMIC_FILTER = 'dynamicFilter',
  /** 包含当前用户所在部门 */
  CONTAINS_USER_DEPT  = 'containsUserDept',
  /** 不包含当前用户所在部门 */
  NOT_CONTAINS_USER_DEPT =  'notContainsUserDept',
  /** 被包含当前用户所在部门 */
  BE_INCLUDE_CUR_USER = 'beContainsUserDept',
  /** 包含任意一个当前用户所在部门 */
  CONTAINS_ANY_USER_DEPT = 'containsAnyUserDept',
  /** 被包含任意一个当前用户所在部门 */
  BE__CONTAINS_ANY_USER_DEPT = 'beContainsAnyUserDept',
  /** 包含当前用户所在工作组 */
  CONTAINS_USER_WORKGROUP  = 'containsUserWorkGroup',
  /** 不包含当前用户所在工作组 */
  NOT_CONTAINS_USER_WORKGROUP  = 'notContainsUserWorkGroup',
  /** 被包含于当前用户所在工作组 */
  BE_INCLUDE_CUR_WORKGROUP  = 'beContainsUserWorkGroup',
  /** 包含任意一个当前用户所在工作组 */
  CONTAINS_ANY_USER_WORKGROUP  = 'containsAnyUserWorkGroup',
  /** 被包含任意一个当前用户所在工作组 */
  BE_CONTAINS_ANY_USER_WORKGROUP  = 'beContainsAnyUserWorkGroup',
  /** 等于当前用户 */
  EQ_CUR_USER  = 'eqCurUser',
  /** 不等于当前用户 */
  NEQ_CUR_USER  = 'neqCurUser',
  /** 被包含于当前用户 */
  BE_CONTAINS_USER  = 'beContainsUser',
  /** 包含任意一个当前用户 */
  CONTAINS_USER  = 'containsUser',
  /** 被包含任意一个当前用户 */
  BE_CONTAINS_ANY_USER  = 'beContainsAnyUser',
  /** 自定义筛选 */
  DIY_FILTER  = 'diyFilter',
}

export const SymbolItem: {
  [P in MethodSymbolEnum]: {
    Chinese: string;
  };
} = {
  [MethodSymbolEnum.EQ]: {
    Chinese: '等于',
  },
  [MethodSymbolEnum.NEQ]: {
    Chinese: '不等于',
  },
  [MethodSymbolEnum.EQ_ANY]: {
    Chinese: '等于任意一个',
  },
  [MethodSymbolEnum.NEQ_ANY]: {
    Chinese: '不等于任意一个',
  },
  [MethodSymbolEnum.INCLUDE]: {
    Chinese: '包含',
  },
  [MethodSymbolEnum.EXCLUDE]: {
    Chinese: '不包含',
  },
  [MethodSymbolEnum.IS_NULL]: {
    Chinese: '为空',
  },
  [MethodSymbolEnum.NOT_NULL]: {
    Chinese: '不为空',
  },
  [MethodSymbolEnum.GT]: {
    Chinese: '大于',
  },
  [MethodSymbolEnum.GE]: {
    Chinese: '大于等于',
  },
  [MethodSymbolEnum.LT]: {
    Chinese: '小于',
  },
  [MethodSymbolEnum.LE]: {
    Chinese: '小于等于',
  },
  [MethodSymbolEnum.GT_TODAY]: {
    Chinese: '大于今天',
  },
  [MethodSymbolEnum.GE_TODAY]: {
    Chinese: '大于等于今天',
  },
  [MethodSymbolEnum.LT_TODAY]: {
    Chinese: '小于今天',
  },
  [MethodSymbolEnum.LE_TODAY]: {
    Chinese: '小于等于今天',
  },
  [MethodSymbolEnum.EQ_TODAY]: {
    Chinese: '等于今天',
  },
  [MethodSymbolEnum.BETWEEN]: {
    Chinese: '选择范围',
  },
  [MethodSymbolEnum.INCLUDE_ANY]: {
    Chinese: '包含任意一个',
  },
  [MethodSymbolEnum.INCLUDE_ALL]: {
    Chinese: '同时包含',
  },
  [MethodSymbolEnum.BELONG]: {
    Chinese: '属于',
  },
  [MethodSymbolEnum.NOT_BELONG]: {
    Chinese: '不属于',
  },
  [MethodSymbolEnum.INNOVATE]: {
    Chinese: '此刻往前',
  },
  [MethodSymbolEnum.MOVING_BEFORE]: {
    Chinese: '某时往前',
  },
  [MethodSymbolEnum.MOVING_AFTER]: {
    Chinese: '某时往后',
  },
  [MethodSymbolEnum.EQ_DAY]: {
    Chinese: '等于今日.日',
  },
  [MethodSymbolEnum.EQ_MONTH]: {
    Chinese: '等于今日.月',
  },
  [MethodSymbolEnum.EQ_YEAR]: {
    Chinese: '等于今日.年',
  },
  [MethodSymbolEnum.INCLUDE_CURUSER]: {
    Chinese: '包含当前用户',
  },
  [MethodSymbolEnum.BE_INCLUDE]: {
    Chinese: '被包含于',
  },
  [MethodSymbolEnum.BE_INCLUDE_ANY]: {
    Chinese: '被包含任意一个',
  },
  [MethodSymbolEnum.NOT_INCLUDE_CUR_USER]: {
    Chinese: '不包含当前用户',
  },
  [MethodSymbolEnum.EQ_AT]: {
    Chinese: '等于此刻',
  },
  [MethodSymbolEnum.GT_AT]: {
    Chinese: '大于此刻',
  },
  [MethodSymbolEnum.LT_AT]: {
    Chinese: '小于此刻',
  },
  [MethodSymbolEnum.DYNAMIC_FILTER]: {
    Chinese: '动态筛选',
  },
  [MethodSymbolEnum.CONTAINS_USER_DEPT]: {
    Chinese: '包含当前用户所在部门',
  },
  [MethodSymbolEnum.NOT_CONTAINS_USER_DEPT]: {
    Chinese: '不包含当前用户所在部门',
  },
  [MethodSymbolEnum.BE_INCLUDE_CUR_USER]: {
    Chinese: '被包含当前用户所在部门',
  },
  [MethodSymbolEnum.CONTAINS_ANY_USER_DEPT]: {
    Chinese: '包含任意一个当前用户所在部门',
  },
  [MethodSymbolEnum.BE__CONTAINS_ANY_USER_DEPT]: {
    Chinese: '被包含任意一个当前用户所在部门',
  },
  [MethodSymbolEnum.CONTAINS_USER_WORKGROUP]: {
    Chinese: '包含当前用户所在工作组',
  },
  [MethodSymbolEnum.NOT_CONTAINS_USER_WORKGROUP]: {
    Chinese: '不包含当前用户所在工作组',
  },
  [MethodSymbolEnum.BE_INCLUDE_CUR_WORKGROUP]: {
    Chinese: '被包含于当前用户所在工作组',
  },
  [MethodSymbolEnum.CONTAINS_ANY_USER_WORKGROUP]: {
    Chinese: '包含任意一个当前用户所在工作组',
  },
  [MethodSymbolEnum.BE_CONTAINS_ANY_USER_WORKGROUP]: {
    Chinese: '被包含任意一个当前用户所在工作组',
  },
  [MethodSymbolEnum.EQ_CUR_USER]: {
    Chinese: '等于当前用户',
  },
  [MethodSymbolEnum.NEQ_CUR_USER]: {
    Chinese: '不等于当前用户',
  },
  [MethodSymbolEnum.BE_CONTAINS_USER]: {
    Chinese: '被包含于当前用户',
  },
  [MethodSymbolEnum.CONTAINS_USER]: {
    Chinese: '包含任意一个当前用户',
  },
  [MethodSymbolEnum.BE_CONTAINS_ANY_USER]: {
    Chinese: '被包含任意一个当前用户',
  },
  [MethodSymbolEnum.DIY_FILTER]: {
    Chinese: '自定义筛选',
  },
};

const userResultDic = [
  {
    label: '当前用户所在工作组',
    value: 'UserCurrentWorkGroup',
  },
  {
    label: '当前用户所在部门',
    value: 'UserCurrentDepart',
  },
  {
    label: '当前用户',
    value: 'UserCurrent',
  },
];

export const CommonSymbol = [MethodSymbolEnum.IS_NULL, MethodSymbolEnum.NOT_NULL];

const FieldProps = {
  props: {
    field: Object as PropType<AvueColumns>,
  },
};

export const SymbolList: SymbolListTypes[] = [
  {
    types: inputType,
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.EQ,
          MethodSymbolEnum.NEQ,
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(ElInput, {
              clearable: true,
            });
          };
        }),
      },
    ],
  },
  {
    types: [...dateType],
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.GE,
      MethodSymbolEnum.LE,
      MethodSymbolEnum.GT,
      MethodSymbolEnum.LT,
      MethodSymbolEnum.GT_TODAY,
      MethodSymbolEnum.GE_TODAY,
      MethodSymbolEnum.LT_TODAY,
      MethodSymbolEnum.LE_TODAY,
      MethodSymbolEnum.EQ_TODAY,
      MethodSymbolEnum.EQ_AT,
      MethodSymbolEnum.GT_AT,
      MethodSymbolEnum.LT_AT,
      MethodSymbolEnum.BETWEEN,
      MethodSymbolEnum.INNOVATE,
      MethodSymbolEnum.MOVING_BEFORE,
      MethodSymbolEnum.MOVING_AFTER,
      MethodSymbolEnum.DYNAMIC_FILTER,
      MethodSymbolEnum.DIY_FILTER,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.EQ,
          MethodSymbolEnum.NEQ,
          MethodSymbolEnum.GE,
          MethodSymbolEnum.LE,
          MethodSymbolEnum.GT,
          MethodSymbolEnum.LT,
          MethodSymbolEnum.MOVING_BEFORE,
          MethodSymbolEnum.MOVING_AFTER,
        ],
        component: defineComponent(props => {
          return () => {
            return h(XtField, {
              data: props.field as any,
              noPlaceHolder: true,
            });
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.BETWEEN],
        component: defineComponent(props => {
          return () => {
            return h(XtDateRange, { ...props.field, type: 'datetimerange' });
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.INNOVATE],
        component: defineComponent(_props => {
          return () => {
            return h(DateToBefore);
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.DYNAMIC_FILTER],
        component: defineComponent(_props => {
          return () => {
            return h(XtDynamicFilter);
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.DIY_FILTER,],
        component: defineComponent(_props => {
          return () => {
            return h(DateCustomFilter);
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: numberType,
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.GT,
      MethodSymbolEnum.GE,
      MethodSymbolEnum.LT,
      MethodSymbolEnum.LE,
      MethodSymbolEnum.EQ_DAY,
      MethodSymbolEnum.EQ_MONTH,
      MethodSymbolEnum.EQ_YEAR,
      MethodSymbolEnum.BETWEEN,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.EQ,
          MethodSymbolEnum.NEQ,
          MethodSymbolEnum.GT,
          MethodSymbolEnum.GE,
          MethodSymbolEnum.LT,
          MethodSymbolEnum.LE,
        ],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent("XTNumber"), props.field as NumberField);
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.BETWEEN],
        component: defineComponent(_props => {
          return () => {
            return h(NumberRange, {
              clearable: true,
              controls: false,
            });
          };
        }),
      },
    ],
  },
  {
    types: selectType,
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.EQ_ANY,
      MethodSymbolEnum.NEQ_ANY,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [MethodSymbolEnum.EQ, MethodSymbolEnum.NEQ],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('avue-select'), {
              ...props.field,
              clearable: true,
              filterable: true,
              allowCreate: true,
              dic: (props.field as SelectAllField).dicData,
            });
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.EQ_ANY, MethodSymbolEnum.NEQ_ANY],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('avue-select'), {
              ...props.field,
              clearable: true,
              dic: (props.field as SelectAllField).dicData,
              all: true,
              multiple: true,
              filterable: true,
              allowCreate: true,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: multipleSelectType,
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      // MethodSymbolEnum.INCLUDE_ALL,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(props => {
          if (_.includes(SelectColumnTypeEnum.CASCADER, props.field.type)) {
            return () => {
              return h(resolveComponent('el-tree-select'), {
                ...props.field,
                clearable: true,
                data: (props.field as SelectAllField).dicData,
                multiple: true,
                filterable: true,
                allowCreate: true,
              });
            };
          } else {
            return () => {
              return h(resolveComponent('avue-select'), {
                ...props.field,
                clearable: true,
                dic: (props.field as SelectAllField).dicData,
                all: true,
                multiple: true,
                filterable: true,
                allowCreate: true,
              });
            };
          }
        }, FieldProps),
      },
    ],
  },
  {
    types: mulCascaderType,
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(props => {
          if ((props.field as any).dicData) {
            return () => {
              return h(XtField, {
                data: props.field as any,
                noPlaceHolder: true,
              });
            };

          }else{
            return () => {
              return h(resolveComponent('el-tree-select'), {
                ...props.field,
                clearable: true,
                data: (props.field as SelectAllField).dicData,
                multiple: true,
                filterable: true,
                allowCreate: true,
              });
            };
          }
        }, FieldProps),
      },
    ],
  },
  {
    types: addressType,
    symbols: [MethodSymbolEnum.BELONG, MethodSymbolEnum.NOT_BELONG, ...CommonSymbol],
    components: [
      {
        symbols: [MethodSymbolEnum.BELONG, MethodSymbolEnum.NOT_BELONG],
        component: defineComponent(props => {
          const addressField = props.field as AddressAllField;

          return () => {
            return h(resolveComponent('XtArea'), {
              ...props.field,
              addressPrecision:
                addressField.addressPrecision > 0
                  ? addressField.addressPrecision - 1
                  : addressField.addressPrecision ?? 3,
              props: {
                checkStrictly: true,
                multiple: true,
              },
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [
      ...usersType,
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.INCLUDE_CURUSER,
      MethodSymbolEnum.NOT_INCLUDE_CUR_USER,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUsers'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [
      ...departAdminType,
      ...workingAdminType,
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.INCLUDE_CURUSER,
      MethodSymbolEnum.NOT_INCLUDE_CUR_USER,
      MethodSymbolEnum.BE_CONTAINS_USER,
      MethodSymbolEnum.CONTAINS_USER,
      MethodSymbolEnum.BE_CONTAINS_ANY_USER,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUsers'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [
      ...executorUserType,
      ...noSubmitUserType,
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.BE_CONTAINS_USER,
      MethodSymbolEnum.CONTAINS_USER,
      MethodSymbolEnum.BE_CONTAINS_ANY_USER,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUsers'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.EXCLUDE],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('avue-select'), {
              ...props.field,
              dic: [userResultDic[2]],
              multiple: false,
              clearable: false,
              value: 'UserCurrent',
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: userTagType,
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.CONTAINS_USER_WORKGROUP,
      MethodSymbolEnum.NOT_CONTAINS_USER_WORKGROUP,
      MethodSymbolEnum.BE_INCLUDE_CUR_WORKGROUP,
      MethodSymbolEnum.CONTAINS_ANY_USER_WORKGROUP,
      MethodSymbolEnum.BE_CONTAINS_ANY_USER_WORKGROUP,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUserTag'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: userDeptType,
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.CONTAINS_USER_DEPT,
      MethodSymbolEnum.NOT_CONTAINS_USER_DEPT,
      MethodSymbolEnum.BE_INCLUDE_CUR_USER,
      MethodSymbolEnum.CONTAINS_ANY_USER_DEPT,
      MethodSymbolEnum.BE__CONTAINS_ANY_USER_DEPT,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUserDept'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [...formulaEditType, ...calculationTableType],
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.GT,
      MethodSymbolEnum.GE,
      MethodSymbolEnum.LT,
      MethodSymbolEnum.LE,
      MethodSymbolEnum.BETWEEN,
      MethodSymbolEnum.EQ_DAY,
      MethodSymbolEnum.EQ_MONTH,
      MethodSymbolEnum.EQ_YEAR,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.EQ,
          MethodSymbolEnum.NEQ,
          MethodSymbolEnum.GT,
          MethodSymbolEnum.GE,
          MethodSymbolEnum.LT,
          MethodSymbolEnum.LE,
        ],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('XTNumber'), {
              data: props.field as any,
              noPlaceHolder: true,
            });
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.BETWEEN],
        component: defineComponent(() => {
          return () => {
            return h(NumberRange, {
              clearable: true,
              controls: false,
            });
          };
        }),
      },
    ],
  },
  {
    types: [...targetValueType, ...statisticalType],
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.GT,
      MethodSymbolEnum.GE,
      MethodSymbolEnum.LT,
      MethodSymbolEnum.LE,
      MethodSymbolEnum.BETWEEN,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.EQ,
          MethodSymbolEnum.NEQ,
          MethodSymbolEnum.GT,
          MethodSymbolEnum.GE,
          MethodSymbolEnum.LT,
          MethodSymbolEnum.LE,
        ],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('XTNumber'), props.field as NumberField);
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.BETWEEN],
        component: defineComponent(() => {
          return () => {
            return h(NumberRange, {
              clearable: true,
              controls: false,
            });
          };
        }),
      },
    ],
  },
  {
    types: adminAllType,
    symbols: [MethodSymbolEnum.BE_INCLUDE, MethodSymbolEnum.EQ_ANY, ...CommonSymbol],
    components: [
      {
        symbols: [MethodSymbolEnum.BE_INCLUDE, MethodSymbolEnum.EQ_ANY],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('avue-select'), {
              ...props.field,
              dic: props.field.fixType === 'departAdmin' ? [userResultDic[1]] : [userResultDic[0]],
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [...submitterType, ...getUsercodeType, ...endUpdateUserType],
    symbols: [
      // MethodSymbolEnum.EQ,
      // MethodSymbolEnum.NEQ,
      // MethodSymbolEnum.INCLUDE_CURUSER1,
      // MethodSymbolEnum.NOT_INCLUDE_CUR_USER1,
      MethodSymbolEnum.EQ_CUR_USER,
      MethodSymbolEnum.NEQ_CUR_USER,
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          // MethodSymbolEnum.EQ,
          // MethodSymbolEnum.NEQ
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUsers'), {
              multiple: false,
              disabled: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [...submitterTimeType, ...triggerTimeType, ...endUpdateTimeType,...updateTimeType],
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.GT,
      MethodSymbolEnum.LT,
      MethodSymbolEnum.GE,
      MethodSymbolEnum.LE,
      MethodSymbolEnum.GT_TODAY,
      MethodSymbolEnum.GE_TODAY,
      MethodSymbolEnum.LT_TODAY,
      MethodSymbolEnum.LE_TODAY,
      MethodSymbolEnum.EQ_TODAY,
      MethodSymbolEnum.BETWEEN,
      MethodSymbolEnum.EQ_AT,
      MethodSymbolEnum.GT_AT,
      MethodSymbolEnum.LT_AT,
      MethodSymbolEnum.INNOVATE,
      MethodSymbolEnum.MOVING_BEFORE,
      MethodSymbolEnum.MOVING_AFTER,
      MethodSymbolEnum.DYNAMIC_FILTER,
      MethodSymbolEnum.DIY_FILTER,
      ...CommonSymbol,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.EQ,
          MethodSymbolEnum.NEQ,
          MethodSymbolEnum.GE,
          MethodSymbolEnum.LE,
          MethodSymbolEnum.GT,
          MethodSymbolEnum.LT,
          MethodSymbolEnum.MOVING_BEFORE,
          MethodSymbolEnum.MOVING_AFTER,
        ],
        component: defineComponent(() => {
          return () => {
            return h(ElDatePicker, {
              clearable: true,
              format: 'YYYY-MM-DD HH:mm:ss',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              type: 'datetime',
            });
          };
        }),
      },
      {
        symbols: [MethodSymbolEnum.BETWEEN],
        component: defineComponent(props => {
          return () => {
            return h(XtDateRange, { ...props.field, type: 'datetimerange' });
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.INNOVATE],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('DateInput'), props.field as NumberField);
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.DYNAMIC_FILTER],
        component: defineComponent((props) => {
          return () => {
            return h(XtDynamicFilter,{...props.field});
          };
        }, FieldProps),
      },
      {
        symbols: [MethodSymbolEnum.DIY_FILTER,],
        component: defineComponent(_props => {
          return () => {
            return h(DateCustomFilter);
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: submitterTheDeptType,
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.CONTAINS_USER_DEPT,
      MethodSymbolEnum.NOT_CONTAINS_USER_DEPT,
      MethodSymbolEnum.BE_INCLUDE_CUR_USER,
      MethodSymbolEnum.CONTAINS_ANY_USER_DEPT,
      MethodSymbolEnum.BE__CONTAINS_ANY_USER_DEPT,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUserDept'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [
      ...submitterTheWorkType,
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.CONTAINS_USER_WORKGROUP,
      MethodSymbolEnum.NOT_CONTAINS_USER_WORKGROUP,
      MethodSymbolEnum.BE_INCLUDE_CUR_WORKGROUP,
      MethodSymbolEnum.CONTAINS_ANY_USER_WORKGROUP,
      MethodSymbolEnum.BE_CONTAINS_ANY_USER_WORKGROUP,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUserTag'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [...departMentType, ...workLeadType, ...organizeAdminType],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.INCLUDE_CURUSER,
      MethodSymbolEnum.NOT_INCLUDE_CUR_USER,
      MethodSymbolEnum.BE_CONTAINS_USER,
      MethodSymbolEnum.CONTAINS_USER,
      MethodSymbolEnum.BE_CONTAINS_ANY_USER,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUsers'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [...inventoryComType],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('x-t-inventory-com'), {
              ...props.field,
              dic: [userResultDic[2]],
              multiple: false,
              clearable: false,
              value: 'UserCurrent',
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [...interspaceComType],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(props => {
          return () => {
            return h(resolveComponent('x-t-interspace-com'), {
              ...props.field,
              dic: [userResultDic[2]],
              multiple: false,
              clearable: false,
              value: 'UserCurrent',
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [
      ...defaultDeptType,
      ...launchDeptType,
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.CONTAINS_USER_DEPT,
      MethodSymbolEnum.NOT_CONTAINS_USER_DEPT,
      MethodSymbolEnum.BE_INCLUDE_CUR_USER,
      MethodSymbolEnum.CONTAINS_ANY_USER_DEPT,
      MethodSymbolEnum.BE__CONTAINS_ANY_USER_DEPT,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUserDept'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [
      ...launchUserType
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.BE_INCLUDE_ANY,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(SelectUser, {
              ..._props.field,
              dataType: 'array',
              multiple: true,
            } as any);
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: [
      ...launchDutyType
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.CONTAINS_USER_WORKGROUP,
      MethodSymbolEnum.NOT_CONTAINS_USER_WORKGROUP,
      MethodSymbolEnum.BE_INCLUDE_CUR_WORKGROUP,
      MethodSymbolEnum.CONTAINS_ANY_USER_WORKGROUP,
      MethodSymbolEnum.BE_CONTAINS_ANY_USER_WORKGROUP,
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('XTUserTag'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
];

//表格里的组件匹配字段
export const dynamicSymbolList: SymbolListTypes[] = [
  {
    types: [
      ...inputType,
      ...dateType,
      ...numberType,
      ...selectType,
      ...multipleSelectType,
      ...formulaEditType,
      ...calculationTableType,
      ...statisticalType,
      ...inventoryComType,
      ...interspaceComType,
      ...targetValueType,
      ...getUsercodeType,
      ...departAdminType,
      ...workingAdminType
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(ElInput, {
              clearable: true,
            });
          };
        }),
      },
    ],
  },
  {
    types: userTagType,
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.CONTAINS_USER_WORKGROUP,
      MethodSymbolEnum.NOT_CONTAINS_USER_WORKGROUP,
      MethodSymbolEnum.BE_INCLUDE_CUR_WORKGROUP,
      MethodSymbolEnum.CONTAINS_ANY_USER_WORKGROUP,
      MethodSymbolEnum.BE_CONTAINS_ANY_USER_WORKGROUP,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('select-duty-by'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: userDeptType,
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.CONTAINS_USER_DEPT,
      MethodSymbolEnum.NOT_CONTAINS_USER_DEPT,
      MethodSymbolEnum.BE_INCLUDE_CUR_USER,
      MethodSymbolEnum.CONTAINS_ANY_USER_DEPT,
      MethodSymbolEnum.BE__CONTAINS_ANY_USER_DEPT,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [MethodSymbolEnum.INCLUDE, MethodSymbolEnum.BE_INCLUDE],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('select-dept-by'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
  {
    types: usersType,
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
      MethodSymbolEnum.INCLUDE_CURUSER,
      MethodSymbolEnum.NOT_INCLUDE_CUR_USER,
      ...CommonSymbol
    ],
    components: [
      {
        symbols: [
          MethodSymbolEnum.INCLUDE,
          MethodSymbolEnum.EXCLUDE,
          MethodSymbolEnum.BE_INCLUDE,
          MethodSymbolEnum.INCLUDE_ANY,
          MethodSymbolEnum.BE_INCLUDE_ANY,
        ],
        component: defineComponent(_props => {
          return () => {
            return h(resolveComponent('select-user-by'), {
              multiple: false,
              clearable: false,
            });
          };
        }, FieldProps),
      },
    ],
  },
];


//历史任务比较默认规则匹配
export const SymbolLinkage:TaskLinkageTypes[]=[
  //文本
  {
    types:inputType,
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
    ]
  },
  //日期时间-数字-公式编辑-提交时间-计算表-触发时间-目标值-统计指标
  {
    types:[
      ...dateType,
      ...numberType,
      ...endUpdateTimeType,
      ...formulaEditType,
      ...submitterTimeType,
      ...calculationTableType,
      ...triggerTimeType,
      ...targetValueType,
      ...statisticalType,
    ],
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.LE,
      MethodSymbolEnum.GE,
      MethodSymbolEnum.LT,
      MethodSymbolEnum.GT,
    ],
    result:[
      {
        types:[...dateType,...submitterTimeType,...endUpdateTimeType],
        result: [
          ...dateType,
          ...triggerTimeType,
          ...submitterTimeType,
        ]
      },
    ]
  },
  //时间范围-地址
  {
    types:[...dateRangeType,...addressType],
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
    ]
  },
  //单项选择
  {
    types:[
      ...selectType,
  
    ],
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.EQ_ANY,
      MethodSymbolEnum.NEQ_ANY,
    ],
    result:[
      {
        types: [...selectType],
        result: [
          ...selectType,
        ]
      }
    ]
  },
  //多项选择-级联选择(多选）-成员选择-工作组选择-部门选择-物品组件-空间组件-部门管理员-工作组管理员-提交人所在部门-提交人的部门管理员
  // -提交人所在工作组-提交人的工作组管理员-提交人的组织管理员-执行人-未提交人
  {
    types:[
      ...mulSelectType,
      ...mulCascaderType,
      ...usersType,
      ...userTagType,
      ...userDeptType,
      ...inventoryComType,
      ...interspaceComType,
      ...departAdminType,
      ...workingAdminType,
      ...submitterTheDeptType,
      ...departMentType,
      ...submitterTheWorkType,
      ...workLeadType,
      ...organizeAdminType,
      ...executorUserType,
      ...noSubmitUserType,
    ],
    symbols: [
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.INCLUDE_ANY,
      MethodSymbolEnum.BE_INCLUDE_ANY,
    ],
    result:[
      {
        types: [...usersType,...executorUserType],
        result: [
          ...usersType,
          ...getUsercodeType,
          ...submitterType,
        ]
      },
    ]
  },
  //提交人-获取用户ID-最后更新人
  {
    types:[
      ...submitterType,
      ...getUsercodeType,
      ...endUpdateUserType,
    ],
    symbols: [
      MethodSymbolEnum.EQ,
      MethodSymbolEnum.NEQ,
      MethodSymbolEnum.INCLUDE,
      MethodSymbolEnum.EXCLUDE,
      MethodSymbolEnum.BE_INCLUDE,
      MethodSymbolEnum.BE_INCLUDE_ANY,
    ],
    result:[
      {
        types: [
          ...submitterType,
          ...getUsercodeType,
          ...endUpdateUserType,
        ],
        result: [
          ...usersType,
          ...getUsercodeType,
          ...submitterType,
        ]
      }
    ]
  },
]

// 校验不需要判断value框的条件
export const isResultList = [
  MethodSymbolEnum.IS_NULL,
  MethodSymbolEnum.NOT_NULL,
  MethodSymbolEnum.NEQ_ANY,
  MethodSymbolEnum.EQ_ANY,
  MethodSymbolEnum.GT_TODAY,
  MethodSymbolEnum.GE_TODAY,
  MethodSymbolEnum.LT_TODAY,
  MethodSymbolEnum.LE_TODAY,
  MethodSymbolEnum.EQ_TODAY,
  MethodSymbolEnum.INCLUDE_ANY,
  MethodSymbolEnum.INNOVATE,
  MethodSymbolEnum.EQ_DAY,
  MethodSymbolEnum.EQ_YEAR,
  MethodSymbolEnum.EQ_MONTH,
  MethodSymbolEnum.INCLUDE_CURUSER,
  MethodSymbolEnum.NOT_INCLUDE_CUR_USER,
  MethodSymbolEnum.EQ_AT,
  MethodSymbolEnum.GT_AT,
  MethodSymbolEnum.LT_AT,
  MethodSymbolEnum.CONTAINS_USER_DEPT,
  MethodSymbolEnum.NOT_CONTAINS_USER_DEPT,
  MethodSymbolEnum.BE_INCLUDE_CUR_USER,  // 注意原枚举中的命名
  MethodSymbolEnum.CONTAINS_USER_WORKGROUP,
  MethodSymbolEnum.NOT_CONTAINS_USER_WORKGROUP,
  MethodSymbolEnum.BE_INCLUDE_CUR_WORKGROUP,  // 注意原枚举中的命名
  MethodSymbolEnum.CONTAINS_ANY_USER_WORKGROUP,
  MethodSymbolEnum.BE_CONTAINS_ANY_USER_WORKGROUP,
  MethodSymbolEnum.EQ_CUR_USER,
  MethodSymbolEnum.NEQ_CUR_USER
];
