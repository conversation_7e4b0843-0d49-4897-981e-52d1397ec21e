<template>
  <el-dialog
    v-model="visible"
    title="关注服务号"
    width="400"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="wechat-bind-dialog">
      <!-- 二维码区域 -->
      <div class="qrcode-container">
        <img src='/img/wxAccount.jpg' alt="微信服务号" class="qrcode-image">
        <p class="qrcode-tip">请使用微信扫描二维码关注</p>
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <template #footer>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref} from 'vue'

// 响应式数据
const visible = ref(false)

// 打开弹窗
const open = async () => {
  visible.value = true;
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}


// 暴露方法给父组件
defineExpose({
  open,
})
</script>

<style scoped>
.wechat-bind-dialog {
  text-align: center;
}

.qrcode-container {
  padding: 20px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  display: block;
  margin: 0 auto 15px;
  border: 1px solid #eee;
}

.qrcode-tip {
  color: #666;
  margin-bottom: 10px;
}

.loading-icon {
  animation: rotating 2s linear infinite;
  margin-bottom: 10px;
  font-size: 24px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>