import XtFormDesign from '@/components/form-config/index.vue';

const fields = import.meta.glob('@/components/form-component/XT*/**/*.vue', { eager: true });
console.log(fields, 'fields');
const filteredComponents = Object.entries(fields).reduce((acc, [path, component]) => {
  // 过滤掉路径中包含 [/components/](file://d:\banban_old_web\node_modules\unplugin-vue-components) 的文件
  if (/form-component\/XT[^\/]+\/components\//.test(path)) {
    return acc; // 过滤掉这类路径
  }
  const folderName = path.match(/form-component\/(XT[^\/]+)\//)?.[1];
  // 只保留 XT 或 MXT 开头的组件名
  const fileName = path
    .split('/')
    .pop()
    .replace(/\.vue$/, '');
  if (!fileName.startsWith('XT') && !fileName.startsWith('MXT')) return acc;

  acc[fileName] = component.default;
  return acc;
}, {});
// debugger
const components = {
  XtFormDesign, // 手动加入
  ...filteredComponents,
};
console.log(components, 'components');
const createComponents = app => {
  for (const [key, component] of Object.entries(components)) {
    // 将组件添加到 Vue 应用中
    app.component(key, component);
  }
};

export default createComponents;
