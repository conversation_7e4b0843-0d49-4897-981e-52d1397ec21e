<template>
  <xt-cascader
    class="w-full"
    v-model="vModel"
    :dic="dicData"
    :props="{ label: 'label', value: 'label' }"
    clearable
    :multiple="true"
  />
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue';
defineProps<{
  dicData: { label: string; disabled: boolean; value: string }[];
}>();

const vModel = defineModel();
</script>
<style lang="scss" scoped></style>
