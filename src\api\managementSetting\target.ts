import request from '@/axios';

//新增编辑指标
export const addUpdateTarget = (data: Object) => {
  return request({
    url: '/metrics/editMetrics',
    method: 'post',
    data: data
  });
};

//指标详情
export const targetDetail = (metricsId: string) => {
  return request({
    url: '/metrics/detail',
    method: 'get',
    params: { metricsId }
  });
};

//绑定版本指标选择列表
export const getNameList = () => {
  return request({
    url: '/metrics/nameValueList',
    method: 'get'
  });
};

//绑定版本
export const bindVersion = (form: any) => {
  return request({
    url: '/metrics/bindVersion',
    method: 'get',
    params: form
  });
};

export const getMetricsDic = () => {
  return request({
    url: '/metrics/metricsDic',
    method: 'get'
  });
};

//规则列表
export const getRuleList = (versionId: string) => {
  return request({
    url: '/ruleExpress/selectFileRule',
    method: 'get',
    params: {
      versionId
    }
  });
};

//任务下的规则
export const getRuleIds = (taskId: string) => {
  return request({
    url: '/ruleExpress/listTaskApplicationRule',
    method: 'get',
    params: {
      taskId
    }
  });
};

//规则预览
export const getRuleDetail = (ruleExpressId: string) => {
  return request({
    url: '/ruleExpress/showTaskApplicationRule',
    method: 'get',
    params: {
      ruleExpressId
    }
  });
};