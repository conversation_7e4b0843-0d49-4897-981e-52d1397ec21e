<template>
  <div class="w-full">
    <slot :data="modelList">
      <el-card
        class="box-card"
        :class="{ disabled: disabled }"
        @click="disabled ? void 0 : openSelect()"
      >
        <template v-if="modelList.length > 0">
          <el-tag
            v-for="(item, index) in modelList"
            :key="item.id"
            :closable="!disabled && closable"
            @close="unCheckArticle(index)"
            style="margin: 0 5px"
            size="large"
            >{{ item.goodsName }}
          </el-tag>
        </template>
        <div v-else class="select_placeholder">{{ placeholder }}</div>
      </el-card>
    </slot>
    <selectArticle
      :modelList
      @submit="handleSubmit"
      v-model:visible="dialogVisible"
      :config="{ tid: taskSource || userMsg?.tid }"
    >
    </selectArticle>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import selectArticle from './component/selectArticle.vue';
import { selectUserByCom } from './XTInventoryCom';
import { getArticleByIds1 } from '@/api/common/index';
import { taskSourceInjectKey } from '@/components/xt-el-form/constant.ts';
import { useStore } from 'vuex';
import { isEmptyValue } from '@/utils/field';
import { useFormItem, ElMessage } from 'element-plus';
import { articleType } from '@/api/common/type';

const { formItem } = useFormItem();
const store = useStore();
const userMsg = store.state.user.userInfo;

const props = withDefaults(defineProps<selectUserByCom>(), {
  dataType: 'array',
  multiple: false,
  placeholder: '请选择',
  initData: false,
  taskPackageId: '',
  closable: true,
  disabled: false,
});

const dialogVisible = ref(false);
const vModel = defineModel<string[] | string>();
const modelList = ref<articleType.ResDataParams[]>([]);
const taskSource = inject(taskSourceInjectKey, null); //任务组织来源
//选中物品
const handleSubmit = (n: articleType.ResDataParams[]) => {
  if (props?.isRadio && n.length > 1) {
    ElMessage.warning('只能选择一个数据，请重新选择');
    return;
  }
  dialogVisible.value = false;
  modelList.value = n;
  vModel.value = props.dataType === 'array' ? n.map(t => t.id) : n.map(t => t.id).join(',');
};

const openSelect = () => {
  dialogVisible.value = true;
};

const unCheckArticle = (index: number) => {
  modelList.value.splice(index, 1);
  vModel.value =
    props.dataType === 'array'
      ? modelList.value.map(t => t.id)
      : modelList.value.map(t => t.id).join(',');
};

watch(
  () => vModel.value,
  async val => {
    formItem?.validate('change');
    if (!isEmptyValue(val)) {
      const ids = props.dataType === 'array' ? val : (val as string)?.split(',');
      if (!_.isArray(ids)) return;
      let res = await getArticleByIds1(ids as string[]);
      modelList.value = res.data.data;
    } else {
      modelList.value = [];
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

defineExpose({
  modelList: modelList,
});
</script>

<style lang="scss" scoped>
.disabled {
  background: #f6f6f6;
}

.select_placeholder {
  color: #a8abb2;
  font-size: 14px;
}

.box-card {
  cursor: pointer;
  width: 100%;

  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
</style>
