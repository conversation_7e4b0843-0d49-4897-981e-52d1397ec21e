<template>
  <div 
    class="ellipsis"
  >
    <el-tooltip
      trigger="click" 
      placement="right-end"
    > 
      <template #content>
        <div class="max-w-500px">
         {{ formatterRowToText( row, item) }}
        </div>
      </template>
      <span class="cell-content">
        <!-- 附件 -->
        <template v-if="item.fixType === FixTypeEnum.UPLOAD">
          <div 
            v-for="(v,index) in row[item.prop]" 
            :key="index" 
            class="hover:bg-gray-100 px-2 flex items-center rounded"
            @click="previewBack(v)"
          >
            <el-icon class="color-[#909399]"><Document /></el-icon>
            <span class="ml-2 color-[#409eff]">{{v?.name}}</span>
          </div>
        </template>
        <!-- 拍照 -->
        <template v-else-if="item.fixType === FixTypeEnum.PHOTO">      
          <span v-for="(url,index) in row[item.prop]" :key="url">
            <a :href="url" target="_blank" class="color-#409eff">图片{{ index+1 }}, </a>
          </span>        
        </template>
        <!-- 文本组件-链接 -->
        <template v-else-if="(item as InputAllField).showFormat === InputFormatTypeEnum.URL">      
          <a :href="row[item.prop]" target="_blank" class="color-#409eff">{{row[item.prop]}}</a>       
        </template>
        <!-- 签名 -->
        <template v-else-if="item.fixType === FixTypeEnum.SIGNATURE">      
          <el-image
            v-if="row[item.prop]"
            style="width: 100px; height: 100px"
            :src="row[item.prop]"
            fit="contain"
          />     
        </template>
        <template v-else>{{ formatterRowToText( row, item) }}</template>
      </span> 
    </el-tooltip> 
  </div>
</template>

<script lang="ts" setup>
import { AvueColumns, AvueForm,InputAllField } from '@/components/form-config/types/AvueTypes.ts';
import { InputFormatTypeEnum } from '@/components/form-config/const/commonDic.ts';

import { FixTypeEnum } from '@/components/form-config/types/field.ts';


withDefaults(
  defineProps<{
    originColumns: AvueColumns[];
    item: AvueColumns,
    row:AvueForm,
    formatterRowToText: any
  }>(),
  {

  }
);


function previewBack(file:{ name:string, url:string }){
  const officeEnd = ["docx","doc","xlsx","xls","pptx"]
  const startUrl = "https://view.officeapps.live.com/op/view.aspx?src=";
  const fileType = file.url.substring(file.url.lastIndexOf(".") + 1)

  const openUrl = officeEnd.includes(fileType) ? startUrl + file.url : file.url
  window.open(openUrl, '_blank');
  return;
}

</script>