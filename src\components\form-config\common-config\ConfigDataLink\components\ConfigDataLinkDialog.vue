<template>
    <el-button class="w-full" size="large" @click="visible = true">数据联动</el-button>
    <el-dialog
      title="数据联动"
      append-to-body
      :destroy-on-close="true" 
      v-model="visible"
      width="1000px"
      :close-on-click-modal="false"
    >
        <containerContent ref="container" :data="linkData.list" @add="addDataLinks" @delete="handleGroupDel" :main-width="900">
            <template #item="{ item, index }">
                <n-card class="mb-2">
                    <h3>联动方式</h3>
                    <!-- <avue-radio v-model="item.method" :dic="dic" @click="(val:any)=>changeMethod(val, index)"></avue-radio> -->
                    <el-radio-group v-model="item.method" @change="(val:any)=>changeMethod(val, index)">
                        <el-radio :value="0">触发联动</el-radio>
                        <el-radio :value="1">非触发联动</el-radio>
                    </el-radio-group>
                </n-card>
                <div v-if="item.method === 0" >
                    <triggerLink :taskId="taskId" :linkItem="item" :data="data" :taskList="taskList" />
                </div>
                <div v-if="item.method === 1">
                    <unTriggerLink :taskId="taskId" :linkItem="item" :data="data" :taskList="taskList" />
                </div>
                
                
                <!-- <multitaskUnTrigger :taskId="taskId" :linkItem="item" :data="data" :taskList="taskList" v-if="item.method === 2" /> -->
                <!-- 触发联动 -->
            </template>
        </containerContent>
        <template #footer>
            <div class="flex justify-end">
            <el-button type="primary" @click="handleSubmit" round>保存</el-button>
            </div>
      </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { configDataLinkType } from '../type';
import containerContent from '../../components/containerContent/index.vue';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { storeToRefs } from 'pinia';
import triggerLink from './triggerLink.vue';
import unTriggerLink from './unTriggerLink.vue';
import { ElMessage } from 'element-plus';
import { pushTaskListType } from '@/api/interface/task'
import { getPushTaskList } from '@/api/taskManagement/task'
import usePackageTreeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTreeStore.ts';
import { dataLinksType } from '@/api/interface/configDataLink'
import { generateId } from '@/components/form-config/utils';


const params = {
    logicBetween: '1',
    logic: '',
    linkageTaskId: '',
    linkageOperator: '',
    extremeRule: {},
    linkageField: { path: '', pathType: 0, linkage: false, field: '',filterColumnList:[] },
    filterCriteriaList:[{path: '', pathType: 0, linkage: false, field: '',filterColumnList:[]}],        
}

const { versionId } = storeToRefs(usePackageTreeStore());

const props = defineProps<{
    data: configDataLinkType['data'];
}>();
const visible = ref(false)
const linkData = ref<dataLinksType>( {list:[]});
const { taskId } = storeToRefs(useTaskDetailStore()) as { 
  taskId: any
};;

//相关的当前组件
const linkFields = computed(() => {
  const currentFieldProps = linkData.value.list
    .filter(item => item.columnList)
    .flatMap(item => item.columnList?.filter(column => column.prop).map(v => v.prop));
  const currentFilterProps = linkData.value.list
    .filter(item => item.linkageField?.filterColumnList)
    .flatMap(item => item.linkageField?.filterColumnList?.filter(column => column.currentFormProp).map(v => v.currentFormProp));
  return _.uniq([...currentFieldProps, ...currentFilterProps]);
});

//校验
const checkData = () => {
    if(_.isArray(linkData.value.list)){
        const haveEmpty = linkData.value.list.some(i => {
            if(i.method === 0){
                if(props.data.fixType === 'dynamic'){
                    return (
                        !i.linkageField?.field || 
                        !i.linkageField?.path || 
                        !i.dynamicFields?.length ||
                        i.dynamicFields?.some(v=> !v.currentProp || !v.pushProp) 
                    )
                }else{
                    return !i.linkageField?.field || !i.linkageField?.path
                }
            }else if(i.method === 1){
                if(props.data.fixType === 'dynamic'){
                    return (
                        !i.linkageField?.field || 
                        !i.linkageField?.path || 
                        (!i.extremeRule?.extremumOption && i.extremeRule?.extremumOption !== 0)||
                        !i.dynamicFields?.length ||
                        i.dynamicFields?.some(v=> !v.currentProp || !v.pushProp) 
                    )
                }
                else return !i.linkageField?.field || !i.linkageField?.path || (!i.extremeRule?.extremumOption && i.extremeRule?.extremumOption !== 0)
            }
            // else if(i.method === 2){
            //     return !i.filterCriteriaList?.length || i.filterCriteriaList?.some(v=> !v.path || !v.field) || !i.extremeRule?.extremumOption
            // }
        });
        if (haveEmpty) {
            ElMessage.warning('推数路径、联动字段不能为空、非触发联动极值规则(至每次任务触发时所生成数据且满足以上条件中最新/最旧)不能为空');
            return false; 
        }
    }
    return true
}

//添加条件组
const addDataLinks = () => {
    if(checkData()){
        (linkData.value.list ??= []).push({
            idx: generateId(),
            method: 0,
            ..._.cloneDeep(params)

        });
    }    
}

//删除条件组
const handleGroupDel = (index: number) => {
    linkData.value?.list.splice(index, 1);
};

const changeMethod = (method: any, index: number) => {
    switch (method) {
        case 0:
            linkData.value.list[index] = {
                idx: generateId(),
                method: 0,
                ..._.cloneDeep(params)
            }
            break;
        case 1:
            linkData.value.list[index] = {
                idx: generateId(),
                method: 1,
                ..._.cloneDeep(params)

            }
            break;
        case 2:
            linkData.value.list[index] = {
                idx: generateId(),
                method: 2,
                ..._.cloneDeep(params)

            }
            break;
        default:
            console.log()
    }

}

const handleSubmit = () => {
    if(!checkData()) return
    props.data.dataLinks = _.cloneDeep(linkData.value)
    props.data.linkFields = _.cloneDeep(Array.from(new Set(linkFields.value as string[])));
    visible.value = false;
}

//任务
const taskList = ref<pushTaskListType[]>([])
const getList = () => {
  getPushTaskList({ version: versionId.value as any || '', type: '0' }).then((res) => {
    taskList.value = res.data.data
  })
}
getList()

watch(
    () => visible.value,
    (val) => {
        if(val) {
           linkData.value = _.cloneDeep(props.data.dataLinks) || {list:[]}
        }
    },
    { immediate: true }
)




</script>

<style scoped lang="scss"></style>