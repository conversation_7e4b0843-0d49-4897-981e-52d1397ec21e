<template>
  <el-form-item label="主体因素">
    <div class="flex items-center gap-8px w-full">
      <el-tree-select
        v-model="factor.industryTypeId"
        :props="{ label: 'industryName', value: 'industryName' }"
        :data="selectTreedata"
        class="70%"
        :render-after-expand="false"
        check-strictly
      />
      <el-checkbox v-model="factor.ban">禁用</el-checkbox>
      <HintPop content="如果勾选,所填写的主体因素限制使用" />
      <el-icon color="#ff7070" class="ml-30px cursor-pointer" @click="$emit('delete', factor.id)"
        ><DeleteFilled
      /></el-icon>
    </div>
  </el-form-item>
</template>
<script setup lang="ts">
import { mainFactor } from '../../../types';
import { gettype } from '@/api/zy/zymsg';
import HintPop from './HintPop.vue';
import { DeleteFilled } from '@element-plus/icons-vue';

defineProps<{
  factor: mainFactor;
}>();

const selectTreedata = ref([]);

gettype().then(res => {
  selectTreedata.value = res.data.data;
});

defineEmits(['delete'])
</script>
<style lang="scss" scoped></style>
