<template>
  <el-cascader
    class="w-full"
    :popper-class="{'m_cascader':isMobile()}"
    v-model="vModel"
    v-bind="$attrs"
    :options="options"
    :collapse-tags="$attrs.disabled"
    :collapse-tags-tooltip="$attrs.disabled"
    :props="computedProps"
  ></el-cascader>
</template>

<script setup>
import { useVModel } from '@vueuse/core';
import { isMobile } from '@/utils/client';

const props = defineProps(['modelValue', 'dicData', 'dic', 'multiple', 'props']);

const emits = defineEmits(['update:modelValue']);

const vModel = useVModel(props, 'modelValue', emits);

const options = computed(() => props.dicData || props.dic);

const computedProps = computed(() => {
  return {
    ...props.props,
    multiple: props.multiple,
  };
});
</script>

<style lang="scss">
.m_cascader{
  width: 90%;
  overflow: scroll;
}
</style>
