<?xml version="1.0" encoding="UTF-8"?>
<svg width="323px" height="260px" viewBox="0 0 323 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 4备份</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-396.000000, -47.000000)">
            <rect fill="#FFFFFF" x="0" y="0" width="1610" height="3471"></rect>
            <g id="编组-4备份" transform="translate(396.000000, 47.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <g id="编组-2" transform="translate(10.000000, 10.000000)">
                    <text id="·从左侧面板选择字段和函数，或输入函数" font-family="PingFangSC-Regular, PingFang SC"
                          font-size="14" font-weight="normal" line-spacing="23">
                        <tspan x="0" y="15" fill="#3A3A3A">·从左侧面板选择字段和函数，或输入函数</tspan>
                        <tspan x="0" y="39" fill="#3A3A3A">·公式编辑举例：</tspan>
                        <tspan x="0" y="63" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="63" fill="#3F70FF">IF</tspan>
                        <tspan x="60.396" y="63" fill="#3A3A3A">(</tspan>
                    </text>
                    <text id="文成绩&gt;60时返回及格，否则返回不及格。" font-family="PingFangSC-Regular, PingFang SC"
                          font-size="14" font-weight="normal" fill="#3A3A3A">
                        <tspan x="7" y="89">文成绩&gt;60时返回及格，否则返回不及格。</tspan>
                    </text>
                    <g id="编组-30" transform="translate(66.000000, 49.000000)">
                        <rect id="矩形" fill="#EAF3FF" x="0" y="3" width="52" height="16"></rect>
                        <text id="语文成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                              font-weight="normal" line-spacing="16" fill="#3F70FF">
                            <tspan x="6" y="13">语文成绩</tspan>
                        </text>
                        <text id="&gt;60,&quot;及格&quot;,&quot;不及格&quot;)，当语"
                              font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                              fill="#3A3A3A">
                            <tspan x="55" y="15">&gt;60,"及格","不及格")，当语</tspan>
                        </text>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
