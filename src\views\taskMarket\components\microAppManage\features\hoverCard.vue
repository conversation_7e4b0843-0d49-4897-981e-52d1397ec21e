<script setup lang="ts">
import More from '~/public/img/microApp/more.svg';
import AppCard from '@/views/taskMarket/components/microAppManage/appCard.vue';
import User from '~/public/img/microApp/user.svg';
defineProps<{
  item:any
}>()

const emits = defineEmits(['toAppDetail','reToDataManage','handleToMyApp','handleToMyApprove','handleToRelated']);
</script>

<template>
  <el-popover
    placement="top-end"
    :show-arrow="false"
    popper-class="custom-popover"
    :popper-style="{minWidth:'68px', width:'68px',padding:'8px',paddingBottom:'4px', border:'none'}"
  >
    <template #reference>
      <app-card class="flex" @click="emits('toAppDetail')">
        <el-image class="h-40px w-40px ml-3" :src="item?.icon">
          <template #error>
            <el-image class="h-40px w-40px" src="/img/microApp/microApp.svg"></el-image>
          </template>
        </el-image>
        <el-text line-clamp="2" class="mx-3 flex-1">{{ item.appName }}</el-text>
      </app-card>
    </template>
    <template #default>
      <el-tooltip
        class="self-start"
        v-if="item.createBy"
        effect="dark"
        :content="`创建人:${item.createBy}`"
        placement="top-start"
      >
        <el-icon class="mr-2 icon-hover" size="20"><User /></el-icon>
      </el-tooltip>
      <el-dropdown  trigger="hover" class="self-start menu_icon" :teleported="false">
        <el-icon @click.stop size="20">
          <el-icon class="icon-hover"><More /></el-icon>
        </el-icon>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="emits('reToDataManage')" v-if="item.canManage">数据管理</el-dropdown-item>
            <el-dropdown-item @click="emits('handleToMyApp')" >我发起的</el-dropdown-item>
            <el-dropdown-item @click="emits('handleToMyApprove')" >我审批的</el-dropdown-item>
            <el-dropdown-item @click="emits('handleToRelated')" >与我相关</el-dropdown-item>

          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>

  </el-popover>
</template>

<style scoped lang="scss">

</style>