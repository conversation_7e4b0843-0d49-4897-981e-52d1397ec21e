<template>
  <el-col
    :span="isMobile()?24:item.span"
    v-if="item.visibleBool || props.source === fieldSource.SETDEFAULT"
    :class="[item.isCrud ? 'mb-0px' : 'mb-15px', !item.display ? '!hidden' : '']"
  >
    <el-form-item :prop="itemProp" :rules="isRules && item.display?rules:[]">
      <template #label v-if="showLabel">
        <item-label />
        <el-tooltip effect="dark" :content="(item as TitleField).labelTip" v-if="(item as TitleField).labelTip"
          placement="right">
          <el-icon class="ml-5px">
            <QuestionFilled />
          </el-icon>
        </el-tooltip>
        <a-spin v-if="loading"/>
      </template>
      <!--表格不回显组件，其他情况会回显组件-->
      <slot name="field">
        <!-- 表格不可见子组件也不可见-->
        <component
          v-if="item.display && (!item.isDynamic || (item.isDynamic && parentField?.display))"
          ref="childComponentRef"
          class="flex-1 w-full"
          :class="{'overflow-auto': needsScroll(item.fixType)}"
          :is="getComponent(item.fixType as FixTypeEnum)"
          v-bind="item"
          :key="item.prop"
          v-model="vModel"
          :placeholder="getPlaceholder(item)"
          :disabled="itemDisabled"
          :itemProp="itemProp"
          :itemForm="itemForm"
          :item="item"
          :dic="(item as SelectAllField).dicData"
          :taskPackageId="taskPackageId"
          :source="source"
          :col="itemColumns"
          :tableData="tableData"
          :dataList="dataList"
          >
          <!-- dataList传给工作组管理员和部门管理员 tableData传给数据选择-->
        </component>
        <template v-else>
          <!-- 表格组件不可见的时候还是渲染获取子组件的数据，其他保持不变 -->
          <component
            v-show="item.display && item.fixType === FixTypeEnum.DYNAMIC"
            ref="childComponentRef"
            class="flex-1 w-full"
            :is="getComponent(item.fixType as FixTypeEnum)"
            v-bind="item"
            :key="item.prop"
            v-model="vModel"
            :placeholder="getPlaceholder(item)"
            :disabled="itemDisabled"
            :itemProp="itemProp"
            :itemForm="itemForm"
            :item="item"
            :dic="(item as SelectAllField).dicData"
            :taskPackageId="taskPackageId"
            :source="source"
            :col="itemColumns"
            :tableData="tableData"
            :dataList="dataList"
            >
            <!-- dataList传给工作组管理员和部门管理员 tableData传给数据选择-->
          </component>
        </template>

      </slot>
    </el-form-item>
  </el-col>

</template>

<script setup lang="ts">
import { visibleAndHiddenRuleHttp } from '@/api/taskManagement/componentFetch';
import { getRules } from '@/utils/formUtils';
import { deepClone } from '@/utils/util';
import { computedInject } from '@vueuse/core';
import { WatchStopHandle } from 'vue';
import { DefaultTypeEnum, DynamicDataTypeEnum, HandleTypeEnum } from '../form-config/const/commonDic.ts';
import useComponents from '../form-config/hooks/useComponents.ts';
import {
  AvueColumns,
  AvueForm,
  CustomDefaultType,
  SelectAllField,
  TitleField,
  ConfigFormula,
  groupSummaryField,XtElFormItemType,DynamicField
} from '@/components/form-config/types/AvueTypes.ts';
import {
  createType,
  dynamicType,
  allSelectItemType,
  getUsercodeType,
  userAdminType,
  dividerType
} from '../form-config/types/FieldTypes.ts';
import { transRule } from '@/components/form-config/utils/validators';
import {
  disabledInjectKey,
  formInjectKey,
  packageInjectKey,
  handleIdInjectKey,
  taskIdInjectKey,
  handleTypeInjectKey,
  formDisabledKey,
  previewTypeKey
} from './constant';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore.ts';
import { filterFormInject, filterFormInjectTemp,mergeArrays, fieldDefault } from '@/utils/field';
import { doLinkageApp, doLinkageRule, doDataFilter } from '@/api/banban/field'
import { formulaCalculate } from '@/api/banban/field';
import { fieldSource, DynamicTypeEnum,previewEnum } from '@/components/form-config/const/commonDic';
import { handleSelectField, isEmptyVmodel, isEmptyValue, processTableData,fixNumber } from '@/utils/field';
import { isMobile } from '@/utils/client';
import { delAllDynamic } from '@/api/banban';
import { useRequestStore } from '@/store/requestLoad.js';
import { useStore } from 'vuex'
import {useRequest} from './fieldRequest.ts'
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import XtDynamic from '@/components/form-component/XTDynamic/XTDynamic.vue';
import useHiddenStore from '@/views/mainview/sceneTasks/detail/store/useHiddenStore.ts';
import { findParentById }  from '@/utils/field'
import { HidesRuleSetting, MutualRuleSetting } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType';
import { useMutual, type MutualType } from '@/components/xt-el-form/hooks/useMutual.ts';

const store = useStore()
const userMsg = store.state.user.userInfo
const requestStore = useRequestStore();
const { taskId:taskId1 } = storeToRefs(useTaskDetailStore());
const route = useRoute();
const formInject = inject('allformInjectKey', null) || inject(formInjectKey,undefined)!;
const taskPackageId = inject(packageInjectKey, undefined);
const handleId = inject(handleIdInjectKey, undefined);//应办id
const taskId = inject(taskIdInjectKey, undefined);//任务Id

const handleType = inject(handleTypeInjectKey, undefined);
const previewType = inject(previewTypeKey, undefined);
const childComponentRef = ref<InstanceType<typeof XtDynamic> | null>(null);
const formDisabled = inject(formDisabledKey, undefined);//是否禁用整个表格
const col = inject('col', undefined) //表格编辑获取不到外层col
const { hidden } = storeToRefs(useHiddenStore())

const props = withDefaults(
  defineProps<{
    item: XtElFormItemType;
    //子表单前缀
    propPrefix?: string[];
    //上级表单数据
    itemForm: AvueForm;
    //上级表单col
    itemColumns: AvueColumns[];
    //是否显示label
    showLabel?: boolean;
    //是否验证表单
    isRules?:boolean;
    source?: number//组件的使用场景 1中间的组件配置 2 组件的默认值设置 3 预览 4 应办
    col: AvueColumns[];
  }>(),
  {
    propPrefix: () => [],
    showLabel: () => true,
    isRules: () => true,
  }
);

defineOptions({
  inheritAttrs: false,
});

const vModel = defineModel();
const itemForm = computed(() => props.itemForm);
const {
  TargetValueRequest,
  dataList,
  getDeptAdminList,
  getGroupAdminList,
  getUserInfo,
  getTriggerTime,
  getuserByDefaultType
} = useRequest(props, vModel,itemForm)
const { hides, mutualExclusions } = storeToRefs(useTaskDetailStore());//组件内部的请求
const { isEqual, formatDicData, reverseSetting } = useMutual();
//label组件渲染
const ItemLabel = () =>
  h(
     'span',
    {
      style: {
        fontSize: _.includes(dynamicType, props.item.fixType) ? '16.4px' : '14px',
        color:'#333',
        fontWeight:_.includes(dynamicType, props.item.fixType) ? 'bold' : 'normal',
      }
    },
    `${props.item.copyLabel}：`
  );


//子表单prop
const itemProp = computed(() => [...props.propPrefix, props.item.prop]);

const rules = computed(() => transRule(props.item));

const ruleFormula = ref<ConfigFormula>({})

const loading = ref(false);

const tableData = computed(() =>{
  return props.itemForm[`${props.item.id}-tableData`]
})

const needsScroll = computed(() => (type:string) => {
  return type === 'photo'
})

const showLabel = computed(() => {
  return props.item.copyLabel && 
  props.showLabel && 
  props.item.showLabel && 
  props.item.display && 
  !_.includes(dividerType, props.item.fixType)
})

// 组件相关
const { getComponent, getPlaceholder } = useComponents();

//显隐不可见/查看应办历史数据不请求联动等接口
const notRequest = computed(() => {
  return !props.item.visibleBool || formDisabled?.value
})

//整体联动，不请求单独的联动/公式编辑等接口
const allLinkRequest = computed(() => { 
  return handleType === HandleTypeEnum.HANDLED || handleType === HandleTypeEnum.INSTANTSERVICE || previewType === previewEnum.TASK
}) 

const parentField = computed(() => {
  return findParentById(col || props.col, props.item.id)?.parent
})


const itemDisabled = computedInject(disabledInjectKey, v => {
  if (!v) return false;
  return v!.value || props.item.disabled || _.includes(createType, props.item.fixType);
}, null);

// 自定义默认值
onMounted(async() => {
  vModel.value = isEmptyValue(vModel.value) ? _.cloneDeep(fieldDefault[props.item.fixType as keyof typeof fieldDefault]) : vModel.value
  initDefaultValue()

  //数据联动
  handleSubmitLink({})

  //添加筛选条件
  handleSubmitFilter({})

   //公式编辑
  submitFormula(formInject?.value)

});

const initDefaultValue = () =>{
  if(notRequest.value) return
  if (
    props.item.defaultType === DefaultTypeEnum.CUSTOM &&  !_.includes(dynamicType, props.item.fixType) &&
    !isEmptyValue((<CustomDefaultType>props.item).defaultValue) &&
    isEmptyValue(vModel.value) &&
    props.item.visibleBool === true
  ) {
    vModel.value = deepClone((<CustomDefaultType>props.item).defaultValue ?? null);
  }
  // 当不可见时,需要组件内部请求的值
  if(props.item.visibleBool){
    fetchComponentData()
  }

  if(
    _.includes(dynamicType, props.item.fixType) &&
    props.item.defaultType === DefaultTypeEnum.CUSTOM &&
    !_.isEmpty((<CustomDefaultType>props.item).defaultValue)
  ){
    if(isEmptyVmodel(vModel.value)){
      vModel.value = deepClone((<CustomDefaultType>props.item).defaultValue ?? null);
    }
  }

  if(_.includes(getUsercodeType, props.item.fixType)){
      vModel.value = userMsg.user_id
  }

}

function fetchComponentData(){
  if(notRequest.value) return
  const requestConfig = {
    [FixTypeEnum.TARGET_VALUE] : TargetValueRequest,
    [FixTypeEnum.DEPART_ADMIN]: getDeptAdminList,
    [FixTypeEnum.WORKING_ADMIN]: getGroupAdminList,
    [FixTypeEnum.SIGNATURE]: getUserInfo,
    [FixTypeEnum.DATE]: getTriggerTime,
    [FixTypeEnum.USERS]: getuserByDefaultType
  }

  const fixType = props.item.fixType as keyof typeof requestConfig;
  const requestFn = requestConfig[fixType];

  if (requestFn) {
    if (_.includes(userAdminType, props.item.fixType)) {
      requestFn(props.itemForm[props.item.userComponentType as string]);
    } else {
      // @ts-ignore
      requestFn();
    }
  }

}


//数据联动
const handleSubmitLink = (formData= {}) => {
  const item = props.item
  if(props.source !== fieldSource.HANDLED || props.item.ruleExpressId) return

  //微应用数据联动
  if(item.appDataLinks?.microAppFilter?.length){
    submitDatalinksApp(formData);
  }

}

//添加筛选条件
const handleSubmitFilter = (formData = {}) => {
  const item = props.item
  if(props.source !== fieldSource.HANDLED) return
  if (item.query?.list?.length && !_.includes(dynamicType, item.fixType)) {
    submitQuery(formData)
  }

}


const filterVmodel = (data:string) => {
  if (isEmptyValue(data)) return _.cloneDeep(fieldDefault[props.item.fixType as keyof typeof fieldDefault]);
  const parsedData = fixNumber(data, props.item); //数字类型要处理格式
  return _.includes(allSelectItemType, props.item.fixType)? handleSelectField(parsedData, props.item) : parsedData;//选择组件要单独处理
}


//提交微应用的数据联动
const submitDatalinksApp = async(formData:Record<string, any>) => {
  const isExamine:number = Number(route.query?.isExamine || 0) as number;
  if(notRequest.value||isExamine==1) return
  loading.value = true
  requestStore.incrementRequest();
  //即使办事表格不处理
  if(_.includes(dynamicType, props.item.fixType) && handleType === HandleTypeEnum.HANDLED){
    const dynamicId = props.itemForm[`${props.item.prop}-id`]
    if(_.isArray(vModel.value) && vModel.value?.length > 0 && props.source !== fieldSource.PREVIEW){
      if(!dynamicId) return
      await delAllDynamic({ id:dynamicId })
    }
  }
  vModel.value = []
  doLinkageApp({
    handledId: handleId,
    //taskId: taskId?.value,
    microAppId: route.query.appId as string,
    componentId: props.item.id,
    //copyLabel: props.item.copyLabel,
    formData
  }).then((res) => {
    requestStore.incrementCompleted();
    const  data  = res
    vModel.value =  _.includes(dynamicType, props.item.fixType) ? mergeArrays(vModel.value, data.data?.data, props.item) : filterVmodel(data.data?.data)

    //数据联动会影响公式编辑，请求结束后再刷一下表格
    if (
      _.includes(dynamicType, props.item.fixType) &&
      childComponentRef.value && 'updateData' in childComponentRef.value
    ) {
      childComponentRef.value.updateData();
    }

  }).finally(() => {

    loading.value = false
  })

}

//提交公式编辑
const submitFormula = (formData:Record<string, any>) => {
  if(notRequest.value) return
  if(allLinkRequest.value) return//应办/场景不请求公式编辑接口，跟整体联动合并
  if(
    props.item.defaultType === DefaultTypeEnum.FORMULA &&
    props.item?.formula?.template  &&
    !props.item?.ruleExpressId
  ){
    loading.value = true
    requestStore.incrementRequest();
    formData = _.isNil(formData) ? {} : formData
    let rowId = ''
    if(props.item.isDynamic){
      rowId = props.itemForm.id
    }
    const params = {
      formula: props.item.formula?.template,
      type:0,
      formData,
      rowId,
      formId:props.item.id,
      copyLabel: props.item.copyLabel
    }
    formulaCalculate(params).then(res => {
      requestStore.incrementCompleted();
      const { data } = res
      vModel.value = fixNumber(data.data, props.item)
    }).finally(() => {
      loading.value = false


    })

  }

}

//提交值与值 值与数据表
const submitRule = (type:number, formData:Record<string, any>) => {
  if(notRequest.value) return
  loading.value = true
  requestStore.incrementRequest();
  formData = _.isNil(formData) ? {} : formData
  doLinkageRule({ ruleId: props.item.ruleExpressId!, taskId: taskId?.value as string,type, formData}).then(res => {
    requestStore.incrementCompleted();
    const { data } = res
    vModel.value = data.data

  }).finally(() => {
    loading.value = false

  })
}

//提交添加筛选条件
const submitQuery = async(formData:Record<string, any>) => {
  if(notRequest.value) return
  if(allLinkRequest.value) return
  loading.value = true
  requestStore.incrementRequest();
  if(_.includes(dynamicType, props.item.fixType) && handleType === HandleTypeEnum.HANDLED){
    const dynamicId = props.itemForm[`${props.item.prop}-id`]
    if(_.isArray(vModel.value) && vModel.value?.length > 0 && props.source !== fieldSource.PREVIEW){
      if(!dynamicId) return
      await delAllDynamic({ id:dynamicId })
    }
    vModel.value = []
  }
  doDataFilter({handledId: handleId, taskId: taskId?.value,componentId:props.item.id,formData}).then(async(res) => {
    requestStore.incrementCompleted();
    const { data } = res
    vModel.value =  _.includes(dynamicType, props.item.fixType) ? mergeArrays(vModel.value, data.data?.data, props.item) : filterVmodel(data.data?.data)

  }).finally(() => {
    loading.value = false

  })
}

let itemWatches: WatchStopHandle[] = [];

function getPropByFieldId(fieldId:string){
  return props.col.find(i => fieldId === i.id)?.prop
}


function hiddenItemFn(rules:any,col:any,displayList:string[]){
  rules.forEach((item:any) => {
    const other = col.filter((i:any) => item.display?.includes(i.id)).filter((it:any) => !displayList?.includes(it.id));
    if (!_.isEmpty(other)) {
      other.forEach((it:any) => {
        const hid = hidden!.value.get(it);
        if(!_.isEqual(hid,[item.hiddenId])){
          return
        }
        if(_.includes(hid,item.hiddenId)){
          hidden.value.set(it,hid.filter((h:any) => h !== item.hiddenId));
        }
        // 递归一下
        const filter = getRules(hides.value as HidesRuleSetting, it.id);
        hiddenItemFn(filter,props.col,displayList);
        it.visibleBool = false;
      })
    }
  });
}

function getVisionHidden(rules:any){
  rules.forEach((item:any) => {
    const params = {
      filter:item.depList.map((res:any)=> (
          {
            operator: res.conditions,
            prop: res.prop || getPropByFieldId(res.fieldId),
            value: res.result,
            id: res.fieldId
          }
        )
      ),
      logic:item.logic,
      formData: formInject.value,
      copyLabel:props.item.copyLabel
    }
    loading.value = true
    requestStore.incrementRequest();
    visibleAndHiddenRuleHttp(params).then(res => {
      requestStore.incrementCompleted();
      const other = props.col.filter(i => item.display?.includes(i.id));
      if (!_.isNil(other)) {
        other.forEach(it => {
          const hid = hidden.value.get(it);
          if(res?.data?.data){
            if(hid?.length){
              const isMy = _.includes(hid,item.hiddenId);
              if(isMy) return;
              hidden.value.set(it,[...hid,item.hiddenId])
            }else{
              hidden.value.set(it,[item.hiddenId])
            }
          }else{
            if(hid?.length){
              // 当有成功的字段，且当前判断为false的字段不在其中，不修改为false
              const isMy = _.includes(hid,item.hiddenId);
              if(!isMy) return it.visibleBool = true;
              // 当前判断为false的字段在其中，把他去除，接着如果还剩有其他的成功字段，直接return不改判 继续为显示
              const trueDisplay = hid.filter((h:any) => h !== item.hiddenId);
              hidden.value.set(it,trueDisplay);
              if(trueDisplay.length) return;
            }
          }

          // 如果是false，就把他的值和被他所影响的display都置为false
          if(!res?.data?.data){
            // formInject.value[it.prop] = undefined;
            const filter = getRules(hides.value as HidesRuleSetting, it.id);
            hiddenItemFn(filter,props.col,[]);
          }
          it.visibleBool = !!res?.data?.data;

        })
      }
    }).finally(() => {
      loading.value = false

  })
  })

}

const getVisionHiddenDebounce = _.debounce(getVisionHidden,1000)


onMounted(() => {
  if(!allLinkRequest.value){
    const rules = getRules(hides.value as HidesRuleSetting, props.item.id);
    if (!_.isEmpty(rules)) {
      // 先涉及到的隐藏

      rules.forEach(item => {
        const other = props.col.filter(i => (item.display as string[])?.includes(i.id));
        if (!_.isEmpty(other)) {
          other.forEach(it => {
            it.visibleBool = false;
          })
        }
      });

      itemWatches.push(
        watch(
          [vModel, () => props.item.visibleBool,formInject],
          // @ts-ignore
          ([v, d, f],[oldV,oldD,oldF]) => {
            // 若自己不显示则不显示其他
            if (!d) {
              return;
            }
            if(!_.isEqual(f?.[props.item.prop], oldF?.[props.item.prop]) || (d !== oldD) ){
              getVisionHiddenDebounce(rules)
            }
          },
          {
            immediate: true,
          }
        )
      )
    };
  }
  
  // 处理显隐的时候，默认值的回显
  itemWatches.push(
    watch(
      () => props.item.visibleBool,
      async(displayBool) => {
        if(displayBool){
          handleSubmitFilter(formInject.value)
          handleSubmitLink(formInject.value)
          submitFormula(formInject.value)
          initDefaultValue()
          fetchComponentData()
        }
        else{
          if(_.includes(dynamicType, props.item.fixType)){
            const dynamicId = props.itemForm[`${props.item.prop}-id`]
            if(_.isArray(vModel.value) && vModel.value?.length > 0 && props.source !== fieldSource.PREVIEW && handleType === HandleTypeEnum.HANDLED){
              if(!dynamicId) return
              await delAllDynamic({ id:dynamicId })
            }
            vModel.value = []
            return
          }
          vModel.value = _.cloneDeep(fieldDefault[props.item.fixType as keyof typeof fieldDefault])
        }
      }
    )
  )
})

function mutualFn(rules:MutualType[],newValue:any){
  rules.forEach(item => {
    const { result, mutualResult, mutualFieldId,id } = item;
    const eqRes = isEqual(newValue, result, props.item.fixType);
    const targetField = props.col.find(i => i.id === mutualFieldId);
    const { type , fixType, dicData } = targetField as SelectAllField;
    formatDicData(dicData,mutualResult,type,fixType,eqRes,id!);
  })
}

// 互斥规则逻辑
onMounted(() => {
    const rules:MutualType[] = [];
    (mutualExclusions.value as MutualRuleSetting)?.groups?.forEach((i:MutualType) => {
      if(props.item.id === i.fieldId){
        rules.push(i);
      }else if(props.item.id === i.mutualFieldId){
        rules.push(reverseSetting(i))
      }
    })
    if(rules.length){
      itemWatches.push(
        watch(vModel,(newValue) => {
          mutualFn(rules,newValue)
        })
      )
    }
})


//值与值
onMounted(() => {
  if (props.item.defaultType === DefaultTypeEnum.FORMULA && props.item.mid && props.item.ruleExpressId && !_.isNil(ruleFormula.value)) {
    const debouncedSubmitRule = _.debounce(submitRule, 300);
    itemWatches.push(
      watch(
        formInject,
        async(value, oldValue) => {
          const fields = ruleFormula.value?.marks?.map(v => v.attributes?.prop)
          const newObj = filterFormInjectTemp(fields, props.col, value)
          const oldObj = filterFormInjectTemp(fields, props.col, oldValue)
          if (!_.isEqual(newObj, oldObj)) {
            debouncedSubmitRule(0, newObj);
          }
        },
        {
          deep: true,
          immediate: true,
        }
      )
    );
  }
});

const groupSummaryPath = computed(() => {
  const groupSummary = (props.item as AvueColumns & groupSummaryField)?.groupSummary;
  if (!groupSummary || !groupSummary.path) return undefined;
  return formInject.value[groupSummary.path] || undefined;
});
onMounted(() => {
  const fP = props.item;
  //公式编辑
  if(
    fP.defaultType === DefaultTypeEnum.FORMULA &&
    fP?.formula?.template  &&
    !fP?.ruleExpressId
  ){
    const debouncedSubmitRule = _.debounce(submitFormula, 300);
    itemWatches.push(
      watch(
        formInject,
        (value, oldValue) => {
          const fields = fP.formula?.marks?.map(v => v.attributes?.prop)
          const newObj = filterFormInject(value, fields)
          const oldObj = filterFormInject(oldValue, fields)
          if (!_.isEqual(newObj, oldObj)) {
            debouncedSubmitRule(value)
          }
        },
        {
          deep: true,
          immediate: true,
        }
      )
    );
  }

  //分组汇总
  if(
    fP.defaultType === DynamicTypeEnum.GROUPSUMMARY &&
    fP.groupSummary?.path &&
    (fP.groupSummary?.group?.length || fP.groupSummary?.summary?.length)
  ){

    const debouncedProcessTableData = _.debounce((value: any, groupSummary: any) => {
      vModel.value = processTableData(value, groupSummary);
    }, 300);

    itemWatches.push(
      watch(
        groupSummaryPath,
        (value, oldValue) => {
          if (!_.isEqual(value, oldValue)) {
            debouncedProcessTableData(value, fP.groupSummary);
          }
        },
        {
          deep: true,
          immediate: true,
        }
      )
    );
  }
});


// 微应用数据联动
onMounted(() => {
  const fP = props.item
  const debouncedSubmitRule = _.debounce(handleSubmitLink, 300);
  if(props.source !== fieldSource.HANDLED) return
  if(fP.appDataLinks?.microAppFilter?.length){
    itemWatches.push(
      watch(
        formInject,
        (value, oldValue) => {
          const newObj = filterFormInject(value, fP.linkFields);
          const oldObj = filterFormInject(oldValue, fP.linkFields);
          if (!_.isEqual(newObj, oldObj)) {
            debouncedSubmitRule(value)
          }
        },
        {
          deep: true,
          immediate: true,
        }
      )
    );
  }
});


//添加筛选条件
onMounted(() => {
  if (props.item.query?.list?.length && props.source === fieldSource.HANDLED) {
    const debouncedSubmitRule = _.debounce(handleSubmitFilter, 300);
    itemWatches.push(
      watch(
        formInject,
        (value, oldValue) => {
          const newObj = filterFormInject(value, props.item.linkFields)
          const oldObj = filterFormInject(oldValue, props.item.linkFields)
          if (!_.isEqual(newObj, oldObj)) {
            debouncedSubmitRule(value)
          }
        },
        {
          deep: true,
          immediate: true,
        }
      )
    );
  }
});

// 清除watch
onUnmounted(() => {
  itemWatches.forEach(item => {
    item();
  });
});
</script>
<style lang="scss">
.el-upload-list__item-file-name{
  color:var(--el-color-primary);
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}
</style>
