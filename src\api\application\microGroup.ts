import request from '@/axios';

// 微应用添加分类
export const addMicroCategorize = (data:any) => {
  return request({
    url: '/microCategorize/add',
    method: 'post',
    data
  });
};

// 微应用标签列表
export const getMicroCategorizeList = (tenantId:string) => {
  return request({
    url: '/microCategorize/list',
    params:{tenantId}
  })
}

// 微应用标签绑定
export const bindMicroCategorize = (data:any) => {
  return request({
    url: '/microCategorize/bind',
    method: 'post',
    data
  })
}

// 微应用标签分页
export const getMicroCategorizePage = (params:any) => {
  return request({
    url: '/microCategorize/page',
    params:{
      ...params,
      size:9999,
      current:1,
    }
  })
}

// 微应用标签批量移除成员
export const removeMicroCategorizeMember = (data:{ids:string[], categorizeId:string}) => {
  return request({
    url: '/microCategorize/categorizeRemove',
    method: 'post',
    data
  })
}

// 微应用标签删除
export const removeMicroCategorize = (id:string) => {
  return request({
    url: '/microCategorize/categorizeDelete',
    method: 'post',
    params:{id}
  })
}

export const getMicroCategorizeBindList = (id:string) => {
  return request({
    url: '/microCategorize/getBindListById',
    params:{id}
  })
}

export const getMicroCategorizeBindListByIds = (data:string[]) => {
  return request({
    url: '/common/getUsersByCategorezeIds',
    method:'post',
    data
  })
}

export const reNameCategorize = (data:any) => {
  return request({
    url: '/microCategorize/rename',
    method:'post',
    data
  })
}



