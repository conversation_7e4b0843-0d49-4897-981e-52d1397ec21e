<template>
  <div class="select_dept">
    <slot :data="emitDuty">
      <card-tag
        @click="disabled ? void 0 : openDialog()"
        :disabled
        :closable="!disabled && closable"
        :placeholder 
        v-model="emitDuty"
        :default-prop="{id: 'id', label: 'groupName'}"
      />
    </slot>
    <el-dialog
      v-model="visible"
      title="工作组(角色)列表"
      :destroy-on-close="true"
      width="50%"
    >
      <div class="select_wrapper">
        <div class="select_top">
          <card-tag
            :disabled
            closable
            :placeholder 
            v-model="modelList"
            :default-prop="{id: 'id', label: 'groupName'}"
          />
        </div>
        <div class="select_bottom">
          <select-tree
            :tree-data="treeData"
            :strictly="true"
            :defaultProps="{id: 'id', label: 'groupName'}"
            v-model="modelList"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button type="primary" @click="visible = false">
            取消
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { ElTree, ElMessage } from 'element-plus';
import useDutyTreeStore, { DutyProps } from '@/piniaStore/banban/useDutyTreeStore.ts';
import { taskSourceInjectKey, taskIdInjectKey } from '@/components/xt-el-form/constant.ts';
import { getTaskBindWorkGroupIdsByTaskId } from '@/api/taskManagement/taskPackage';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore.ts';
import { useFormItem } from 'element-plus'
import { isEmptyValue } from '@/utils/field';
import cardTag from '../component/cardTag.vue';
import selectTree from '../component/selectTree.vue';
import { flattenTreeData } from '@/utils/field';
import { useRequest} from '../hooks/useHooks'
import {selectUserByDuty} from './XTUserTag'
import { handleIdInjectKey } from '@/components/xt-el-form/constant.ts'

const { formItem } = useFormItem()

const props = withDefaults(defineProps<selectUserByDuty>(), {
  dataType: 'array',
  multiple: false,
  placeholder: '请选择',
  taskPackageId: '',
  closable: false,
  openLoad: true,
  disabled: false,
  needAll: true, // 是否展示组织下全部工作组
  isRadio: false,
});
const { taskId: taskId1 } = storeToRefs(useTaskDetailStore());
const isTypeArray = computed(() => {
  return props.dataType === 'array';
});
const vModel = defineModel<string[] | string>({ required: false});
const dutyTreeRef = ref<InstanceType<typeof ElTree>>();
const { dutyTreeData } = storeToRefs(useDutyTreeStore());
const { findDutyByIds, initDutyTreeData } = useDutyTreeStore();
const emitDuty = ref<DutyProps[]>([]);
const taskSource = inject(taskSourceInjectKey, null); //任务组织来源
const taskId = inject(taskIdInjectKey, null); //任务Id
const handleId = inject(handleIdInjectKey, undefined);//应办id

const { getLimitDeptOrGroup, limitIds } = useRequest(handleId as string,props.item)
getLimitDeptOrGroup()
// 任务绑定的工作组
const taskGroupList = ref([]);
const modelList = ref<DutyProps[]>([]); 
const visible = ref(false)

//工作组的限制选择
const limitIdsArr = computed(() => {
  return (
    (Array.isArray(props.limitIds) && props.limitIds.length ? props.limitIds : null) ||
    (Array.isArray(limitIds.value) && limitIds.value.length ? limitIds.value : null) ||
    []
  );
});

// 解包 dutyTreeData 并确保它是数组
const treeData = computed(() => {
  const data = dutyTreeData.value;
  const flattenedData = limitIdsArr.value?.length ? flattenTreeData(data, limitIdsArr.value):data
  return Array.isArray(flattenedData) ? flattenedData : [];
});

const filterIds = computed(() => {
  return props.workIds || taskGroupList.value;
});
//获取任务绑定的工作组
const getTaskGroupList = async () => {
  try {
    if (!props.needAll) {
      const { data } = await getTaskBindWorkGroupIdsByTaskId({
        taskId: (taskId?.value || taskId1.value || props.taskId) as string,
      });
      taskGroupList.value = data.data;
    }
    await initDutyTreeData(taskSource?.value, filterIds.value, props.needAll,props.workParentId);
    
  } catch (error) {
    console.error('获取任务工作组失败:', error);
  }
};

//确认选中
const handleSubmit = () => {         
  if(props?.isRadio && modelList.value.length > 1) {
    ElMessage.warning('只能选择一个数据，请重新选择');
    return;
  }
  vModel.value = isTypeArray.value? modelList.value.map(s => s.id): modelList.value.map(s => s.id).join(',')
  visible.value = false
}

const openDialog = () => {
  visible.value = true
  modelList.value = _.cloneDeep(emitDuty.value) 
}
const clearValidate = () => {
  emitDuty.value.length = 0;
  dutyTreeRef.value?.setCheckedKeys([], false);
};
watch(
  vModel,
  async n => {
    formItem?.validate('change')
    if (!isEmptyValue(n)) {
      await getTaskGroupList();
    }
    const value = props.dataType === 'array' ? n : (n as string)?.split(',').map(id => id.trim());
    if(!_.isArray(value)) {
      emitDuty.value = []
      return
    }
    emitDuty.value = findDutyByIds(value).filter(t => t !== null);
  },
  { immediate: true}
);

onMounted(async () => {
  if (taskId?.value || taskId1.value || props.needAll ) {
    await getTaskGroupList();
  }
  if(props.workParentId){
    await getTaskGroupList()
  }
});

export interface selectUserIns {
  clearValidate: () => void;
  modelList: any;
}
defineExpose<selectUserIns>({
  clearValidate,
  modelList: emitDuty,
});
</script>

<style scoped lang="scss">
.select_dept {
  width: 100%;
}
.select_wrapper {
  display: flex;
  flex-direction: column;
  .select_bottom {
    height: 250px;
    display: flex;
    & > div {
      flex: 1;
    }
  }
}
.tree_scrollbar {
  padding: 10px;
}
.user_scrollbar {
  padding: 10px;
  width: 100%;
  border-left: 1px solid #eee;
}
.box-card {
  cursor: pointer;
  width: 100%;
  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
.select_dept_placeholder {
  color: #a8abb2;
  font-size: 14px;
}
.disabled {
  background: #f6f6f6;
}
</style>
