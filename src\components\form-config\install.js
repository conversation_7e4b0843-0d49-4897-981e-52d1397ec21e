
const Configcomponents = import.meta.glob('@/components/form-config/field-config/**/index.vue', { eager: true });
const Config = {};
Object.keys(Configcomponents).forEach(key => {
  if (/field-config\/[^\/]+\/components\//.test(key)) {
    return; // 跳过这些组件
  }
  const folderName = key.split('/').slice(-2, -1)[0]
  // 获取组件默认导出
  const component = Configcomponents[key].default;
  Reflect.set(Config, folderName, component);
});

export default Config;