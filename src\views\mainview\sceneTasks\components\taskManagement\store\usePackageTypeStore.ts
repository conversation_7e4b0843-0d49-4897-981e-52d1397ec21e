import { defineStore } from 'pinia';
import { getMarketTypeList } from '@/api/taskMarket';

interface packageType {
  name: string;
  id: number;
}

const usePackageTypeStore = defineStore('usePackageTypeStore',()=>{
  const packageTypeList = ref<packageType[]>([]);//用来选择
  const packageTypeTabsList = ref<packageType[]>([]);//用来显示tabs
  const getTypeList = () => {
    getMarketTypeList().then((res) => {
      packageTypeList.value = res.data.data as packageType[];
      packageTypeTabsList.value = _.cloneDeep(packageTypeList.value);
      packageTypeTabsList.value.unshift({id:0,name:'全部'});
    })
  }
  return{
    packageTypeList,
    packageTypeTabsList,
    getTypeList
  }
})
export default usePackageTypeStore;
