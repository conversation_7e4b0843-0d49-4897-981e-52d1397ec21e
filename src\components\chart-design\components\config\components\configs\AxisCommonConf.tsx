import useChartTypeStyleAndStore from '../../../../hooks/useChartTypeStyleAndStore.ts';
import { AxisType, EChartsAxisStyle } from '../../../../types';
import { Ref } from 'vue';


const axisRender = (chartStyle: Ref<EChartsAxisStyle>, axis: AxisType) => {
  return () => (
    <div>
      <el-form-item label="名称">
        <el-input
          v-model={chartStyle.value[`${axis}Label`]}
        />
      </el-form-item>
      <el-form-item label="文字角度">
        <el-input-number
          v-model={chartStyle.value[`${axis}LabelRotate`]}
          min={-90}
          max={90}
          precision={0}
        />
      </el-form-item>
      <el-form-item label="文字大小">
        <el-input-number
          v-model={chartStyle.value[`${axis}LabelSize`]}
          min={12}
          precision={0}
        />
      </el-form-item>
      <el-form-item label="文字颜色">
        <el-color-picker
          v-model={chartStyle.value[`${axis}LabelColor`]}
        />
      </el-form-item>
      <el-form-item label="轴线颜色">
        <el-color-picker
          v-model={chartStyle.value[`${axis}LineColor`]}
        />
      </el-form-item>
    </div>
  );
};

export default [
  {
    header: '坐标X轴',
    render: defineComponent(() => {
      const { chartStyle } = useChartTypeStyleAndStore<EChartsAxisStyle>();
      return axisRender(chartStyle, 'xAxis');
    })
  },
  {
    header: '坐标Y轴',
    render: defineComponent(() => {
      const { chartStyle } = useChartTypeStyleAndStore<EChartsAxisStyle>();
      return axisRender(chartStyle, 'yAxis');
    })
  }
];