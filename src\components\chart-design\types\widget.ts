export enum WidgetGroupEnum {
  DIMENSIONS = 'dimensionsList',
  METRICS = 'metricsList',
  AUXILIARY_METRICS = 'auxiliaryList',
}

export type DragCondition = (id: string, oldList: Mark[]) => {
  condition: boolean,
  message?: string
}[]

export enum MarkMenuTypeEnum {
  RENAME = 'rename',
  COLLECT = 'collect',
  SORT = 'sort'
}

export type GeneratorParams = {
  item: Mark
  refType: WidgetGroupEnum
}

export type DropdownItem = {
  key: MarkMenuTypeEnum
  label: string
  component: (params: GeneratorParams) => any
}

export type TopTagType = TagsBaseProps & {
  dropdownItems: DropdownItem[]
  refType: WidgetGroupEnum
}

export enum SummaryEnum {
  COUNT = 'count',
  DISTINCT = 'distinct',
  SUM = 'sum',
  AVG = 'avg',
  MAX = 'max',
  MIN = 'min',
  MEDIAN = 'median'
}

export enum SortEnum {
  NONE = 'none',
  ASC = 'asc',
  DESC = 'desc'
}

export enum MarkTypeEnum {
  NORMAL = 'normal',
  NUMBER = 'number',
  FORMULA = 'formula'
}

export type Mark = {
  id: string
  title: string
  type: MarkTypeEnum
  summary?: SummaryEnum
  sort?: SortEnum
}

export type TagsBaseProps = {
  dragGroup?: string
  label: string
  dragCondition?: DragCondition
  transClone?: (item: Mark) => Mark
}