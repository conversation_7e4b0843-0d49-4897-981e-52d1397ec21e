<template>
  <config-title :data="data" />
  <config-permission :data="data" />
  <Suspense>
    <AsyncDic :data="data" />
    <template #fallback>
      <el-form-item label="选项内容">
        <el-skeleton />
      </el-form-item>
    </template>
  </Suspense>
  <select-rule :data="data"></select-rule>
  <config-default-type :data="data" v-if="formType!== formTypeEnum.KNOWLEDGE"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import SelectRule from '../config_select/components/SelectRule.vue';
import AsyncDic from '../config_select/components/SelectDic.vue'
import { formTypeEnum } from '@/components/form-config/const/commonDic';
import { SelectAllField } from '../config_select/type';

const formType = inject('formType', null)
defineProps<{
  data: SelectAllField
}>();



</script>
