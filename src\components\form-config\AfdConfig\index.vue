<template>
  <div class="config h-full">
    <widget-config :data="widgetSelect" :column="column"></widget-config>
  </div>
</template>

<script setup>
// WidgetConfig
import WidgetConfig from './widget.vue';

const props = defineProps({
  widgetSelect: {
    type: Object,
    default: () => {
      return {};
    },
  },
  column: {
    type: Object,
    default: () => {
      return {};
    },
  }
});
</script>
<style lang="scss" scoped>
.config {
  background: #fff;

  :deep(.el-collapse-item__content) {
    padding-bottom: 0;
  }

  :deep(.el-tabs) {
    width: 100%;
    height: 100%;

    .el-tabs__header {
      margin: 0;
      height: 45px;
      background: #ffffff;
      z-index: 1000;
      position: relative;
      display: block;
      top: 0;
    }

    .el-tabs__content {
      height: calc(100% - 45px);
      overflow-y: auto;
      margin-top: 0;
    }
  }

  :deep(ul) {
    margin: 0;
    padding: 0;
    width: 100%;

    li {
      display: flex;
      align-items: center;
      width: 100%;

      .ghost {
        list-style: none;
        font-size: 0;
        height: 35px;
      }
    }
  }
}
</style>