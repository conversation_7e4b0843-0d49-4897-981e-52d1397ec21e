import useChartTypeStyleAndStore from '../../../../hooks/useChartTypeStyleAndStore.ts';
import { LineStyle } from '../../../../types';
import AxisCommonConf from './AxisCommonConf.tsx';
import EChartsCommonConf from './EChartsCommonConf.tsx';


const minMaxToMark = (min: number, max: number) =>
  _.range(min, max + 1).reduce((prev: Record<number, number>, cur: number) => {
    prev[cur] = cur;
    return prev;
  }, {});


export default [
  {
    header: '折线设置',
    render: defineComponent(() => {
      const { chartStyle } = useChartTypeStyleAndStore<LineStyle>();
      return () => (
        <div>
          <el-form-item class="no-title">
            <el-checkbox v-model={chartStyle.value.showArea}>面积堆积</el-checkbox>
          </el-form-item>
          <el-form-item label="线条宽度">
            <el-slider
              v-model={chartStyle.value.lineWidth}
              min={1}
              max={10}
              marks={minMaxToMark(1, 10)}
            />
          </el-form-item>
          <el-form-item class="no-title">
            <el-checkbox v-model={chartStyle.value.showSymbol}>圆点</el-checkbox>
          </el-form-item>
          <el-form-item label="点的大小">
            <el-slider
              v-model={chartStyle.value.symbolSize}
              min={1}
              max={10}
              marks={minMaxToMark(1, 10)}
            />
          </el-form-item>
        </div>
      );
    })
  },
  ...AxisCommonConf,
  ...EChartsCommonConf
];