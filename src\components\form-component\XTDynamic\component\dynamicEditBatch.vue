<template>
  <div class="flex items-center">
    <n-button
      text
      :focusable="false"
      :disabled="disabled"
      type="primary"
      @click="dialogVisible = true"
      >批量编辑</n-button
    >

    <el-dialog
      title="批量编辑"
      v-model="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      class="editBatch"
      width="50%"
      :close-on-click-modal="false"
    >
      <div class="flex items-center">
        <span>选择批量编辑的字段</span>
        <el-select v-model="current" class="!w-200px mx-10px" @change="handleChangeSelect">
          <el-option
            v-for="item in columnsTypeList"
            :key="item.id"
            :label="item.copyLabel"
            :value="item.id"
          ></el-option>
        </el-select>
        <span class="mr-10px">修改为</span>
        <component
          v-if="current"
          class="!max-w-300px"
          :is="getComponent(selectItem?.fixType)"
          v-bind="selectItem"
          :dic="selectItem?.dicData"
          :multiple="false"
          :placeholder="getPlaceholder(selectItem!)"
          :unInitData="true"
          v-model="result"
        
        >
        </component>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubEdit">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import useComponents from '@/components/form-config/hooks/useComponents.ts';
import { AvueForm, AvueColumns,InputField,SelectField } from '@/components/form-config/types/AvueTypes.ts';
import { fieldDefault } from '@/utils/field';

const props = defineProps(['itemColumns', 'disabled']);

const vModel = defineModel<AvueForm[]>();

const dialogVisible = ref(false);
// 组件相关
const { getComponent, getPlaceholder } = useComponents();

const columnsTypeList = computed<any>(() => {
  if (!props.itemColumns.length) return [];
  let types = [];
  types = props.itemColumns.map((item:any) => item.map((v:any) => v)).flat();
  return _.uniqBy(types, 'copyLabel');
});

const selectItem = ref<AvueColumns & InputField & SelectField>();
const current = ref('');
const result = ref();

const handleChangeSelect = (val:any) => {
  let findItem = columnsTypeList.value.find((item:any) => item.id === val);
  result.value = fieldDefault[findItem.fixType as keyof typeof fieldDefault];
  selectItem.value = _.cloneDeep(findItem);
};
const handleSubEdit = () => {
  props.itemColumns.forEach((item:any, i:number) => {
    let filArr = item.filter((el:any) => el.copyLabel === selectItem?.value!.copyLabel);
    if (filArr.length) {
      filArr.forEach((m:any) => {
        const { prop } = m;
        vModel.value![i][prop] = result.value;
      });
    }
  });
  dialogVisible.value = false;
};

const handleClose = () => {
  dialogVisible.value = false;
};


</script>

<style lang="scss">
.editBatch {
  .el-dialog__body {
    padding-top: 0;
  }
}
</style>
