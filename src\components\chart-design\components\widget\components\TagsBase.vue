<template>
  <div
    ref="tagDrag"
    class="bg-white min-h-46px p-8px pl-105px flex-(~ wrap) gap-8px
     box-border border-(1px dashed #ADADAE) rounded-1 relative items-center
     shadow-[0_0_12px_0_#F5F5F5]"
    @dragleave="handleDragleave"
    @dragover="handleDragover"
    @drop="handleDragleave"
  >
    <div
      class="tags-cant-drag flex-center
      absolute inset-y-0 left-16px font-500 text-16px"
    >{{ label }}
    </div>
    <mark-tag
      v-for="(item,index) in list"
      :key="item.id"
      :item
      :list
      v-bind="props"
      @delete="handleRemove(index)"
    />
  </div>
</template>

<script
  lang="ts"
  setup
>
import { Mark, TopTagType } from '../../../types/widget.ts';
import MarkTag from './MarkTag.vue';
import { DraggableEvent, useDraggable, UseDraggableOptions } from 'vue-draggable-plus';
import { ElMessage } from 'element-plus';
import { MARK_DEFAULT_GROUP_NAME } from '../../../const/widget.ts';

const props = withDefaults(defineProps<TopTagType>(), {
  dragGroup: () => MARK_DEFAULT_GROUP_NAME,
  transClone: (item: Mark) => item
});

const list = defineModel<Mark[]>({ default: () => [] });

const tagDrag = ref();

const handleDragover = () => {
  tagDrag.value?.classList.add('chart-tag-drag-in');
};

const handleDragleave = () => {
  tagDrag.value?.classList.remove('chart-tag-drag-in');
};

const handleAdd = (item: Mark) => {
  list.value = [...list.value, item];
};

const handleRemove = (index: number) => {
  list.value = list.value.filter((_, i) => i !== index);
};


useDraggable(tagDrag, {
  animation: 150,
  group: {
    name: props.dragGroup,
    pull: false
  },
  ghostClass: 'chart-ghost-tag',
  filter: '.tags-cant-drag',
  onAdd: (e: DraggableEvent<Mark>) => {
    const clonedData = props.transClone(e.clonedData);
    if (checkAdd(clonedData.id)) {
      handleAdd(clonedData);
    }
  },
  onUpdate: (e: DraggableEvent<Mark>) => {
    if (e.oldIndex === undefined || e.newIndex === undefined) {
      return;
    }
    // 从原位置移除元素
    const element = list.value.splice(e.oldIndex - 1, 1)[0];
    // 将元素插入到新位置
    list.value.splice(e.newIndex - 1, 0, element);
  }
} as UseDraggableOptions<Mark>);

const checkAdd = (id: string) => {
  // 外部判断项
  for (const item of (props.dragCondition?.(id, list.value) ?? [])) {
    if (item.condition) {
      ElMessage.warning(item.message);
      return false;
    }
  }
  // 判断重复
  if (list.value.some(item => item.id === id)) {
    ElMessage.warning('请勿重复添加');
    return false;
  }
  return true;
};
</script>

<style
  lang="scss"
>
.chart-tag-drag-in {
  background-color: #ECF3FF;
  border: 1px dashed #024FE5;
}
</style>