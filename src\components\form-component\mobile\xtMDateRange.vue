<script setup>
import dayjs from 'dayjs';
import { DateRangeTypeEnum } from '@/components/form-component/mobile/types/BaseMobileFieldType';
import { DateLimitTypeEnum } from '@/components/form-config/const/commonDic';
import { formInjectKey } from '@/components/xt-el-form/constant';

defineOptions({
  name: 'xt-m-daterange'
});
const vModel = defineModel({ default: () => [] });

const { disabled, type, format, dateFormatType } = toRefs(useAttrs());

const attrs=useAttrs()

const show=ref(false)

const columnTabs = computed(() => {
      return ['开始时间', '结束时间'];
});

const formInject = inject('allformInjectKey', null) || inject(formInjectKey,undefined);

const onConfirmDateTime = (dateArr) => {
  const startOption = dateArr[0]?.selectedValues || [];
  const endOption = dateArr[1]?.selectedValues || [];
  vModel.value=[dayjs(startOption).format('YYYY-MM-DD HH:mm:ss'),dayjs(endOption).format('YYYY-MM-DD HH:mm:ss')]

  show.value = false;
};

const showText = computed({
  get() {
    if (_.isEmpty(vModel.value?.[0])&&_.isEmpty(vModel.value?.[1])) return ''
    const startDayJs=dayjs(vModel.value[0]);
    const endDayJs=dayjs(vModel.value[1]);
    if (type.value===DateRangeTypeEnum.YEAR){
      return startDayJs.format('YYYY')+' 至 '+endDayJs.format('YYYY');
    }else if (type.value===DateRangeTypeEnum.MONTH){
      return startDayJs.format('YYYY-MM')+' 至 '+endDayJs.format('YYYY-MM');
    }else if (type.value===DateRangeTypeEnum.DATE){
      return startDayJs.format('YYYY-MM-DD')+' 至 '+endDayJs.format('YYYY-MM-DD');
    }
    return ""

  },
  set(v) {
    vModel.value = v;
  }
});

// 使用 ref 来存储 formInject.value 的值
const formInjectValues = ref({});
// 监视 formInject.value 的变化，并保存依赖字段的值到 formInjectValues
watchEffect(() => {
  if (attrs.limitStartDateType === DateLimitTypeEnum.FORM && formInject?.value) {
    const startDateKey = attrs.limitStartDateVal?.value || '';
    formInjectValues.value[startDateKey] = formInject.value?.[startDateKey] ?? null;
  }

  if (attrs.limitEndDateType === DateLimitTypeEnum.FORM && formInject?.value) {
    const endDateKey = attrs.limitEndDateVal?.value || '';
    formInjectValues.value[endDateKey] = formInject.value?.[endDateKey] ?? null;
  }
});
// 动态日期计算示例
const calculateDynamicDate = (value) => {
  // 将 value 转换为数字
  const num = parseInt(value, 10);

  // 根据 num 的值来决定添加或减去的单位（这里默认为 'day'）
  if (!isNaN(num)) {
    return dayjs().add(num, 'day').startOf('day');
  }

  return null;
};
// 解析日期限制值（支持所有类型）
const parseLimitDate = (limitStartDateType, defaultValue) => {
  const {value, type} = defaultValue
  switch (limitStartDateType) {
    case DateLimitTypeEnum.FIXED:
      return dayjs(value).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    case DateLimitTypeEnum.DYNAMICS:
      return calculateDynamicDate(value)?.format('YYYY-MM-DD HH:mm:ss');
    case DateLimitTypeEnum.DEFAULT:
      return dayjs().add(parseInt(value), type).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    case DateLimitTypeEnum.FORM:
      // 这里需要从 formInject 中获取相关日期组件的值
      return formInjectValues.value[value];
    // return rawValue ? dayjs(rawValue).startOf('day').format('YYYY-MM-DD HH:mm:ss') : null;
    default:
      return null;
  }
};

// 动态时间范围计算
const dateRange = computed(() => {
  const start = attrs.limitStartDate && attrs.limitStartDate ? parseLimitDate(attrs.limitStartDateType, attrs.limitStartDateVal) : null
  const end = attrs.limitEndDate && attrs.limitEndDate ? parseLimitDate(attrs.limitEndDateType, attrs.limitEndDateVal) : null
  return { start, end }
})
const startDate = ref(vModel.value[0]);
const endDate = ref(vModel.value[1]);

const onConfirm=(v)=>{
  const startDay=dayjs(v[0]);
  const endDay=dayjs(v[1]);
  vModel.value=[startDay.format('YYYY-MM-DD HH:mm:ss'),endDay.format('YYYY-MM-DD HH:mm:ss')]
  show.value = false;
}

</script>

<template>
  <van-field v-bind="$attrs"
             v-model="showText"
             @click="show=true"
             readonly label="" />
  <van-popup v-model:show="show"
             position="bottom"
             :style="{ height: '50%' }">
      <template v-if="type===DateRangeTypeEnum.YEAR">

        <van-picker-group
          :title="$attrs.label"
          :tabs="columnTabs"
          next-step-text="下一步"
          @confirm="onConfirmDateTime"
        >
        <van-date-picker
          v-model="startDate"
          :columns-type="['year']"
        />
        <van-date-picker
          v-model="endDate"
          :columns-type="['year']"
        />
        </van-picker-group>
      </template>
      <template v-if="type===DateRangeTypeEnum.MONTH">
        <van-picker-group
          :title="$attrs.label"
          :tabs="columnTabs"
          next-step-text="下一步"
          @confirm="onConfirmDateTime"
        >
        <van-date-picker
          v-model="startDate"
          :columns-type="['year','month']"
        />
        <van-date-picker
          v-model="endDate"
          :columns-type="['year','month']"
        />
        </van-picker-group>
      </template>
      <template v-if="type===DateRangeTypeEnum.DATE">
        <van-calendar :poppable="false"
                      switch-mode="year-month"
                      :show-confirm="false"
                      type="range"
                      @confirm="onConfirm" />
      </template>

  </van-popup>
</template>

<style scoped>
.m_data_picker {
  width: 100%;

  :where(.css-dev-only-do-not-override-1lemaw2).ant-picker-dropdown .ant-picker-panel-container {
    overflow: scroll;
  }

  .ant-picker-panel-container {
    overflow: scroll;
  }
}
</style>