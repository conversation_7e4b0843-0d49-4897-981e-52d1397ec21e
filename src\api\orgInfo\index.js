import request from '@/axios';

export const getOrgInfo = tenantId => {
  //获取组织信息
  return request({
    url: '/blade-system/tenant/tenantInfo',
    method: 'get',
    params: {
      tenantId,
    },
  });
};

export const updateInfo = form => {
  //更新组织信息
  return request({
    url: '/blade-system/tenant/submit',
    method: 'post',
    data: {
      ...form,
    },
  });
};

export const getUserList = (current, size, data) => {
  return request({
    url: '/common/tenant/user/list',
    method: 'post',
    params: {
      current,
      size,
    },
    data: {
      ...data,
    },
  });
};
export const getUserListByTid = tid => {
  //获取当前组织下所有成员
  return request({
    url: '/common/tenant/user/list',
    method: 'get',
    params:{tid}
  });
};

export const getDeptList = deptName => {
  //获取部门列表
  return request({
    url: '/blade-system/dept/tree',
    method: 'get',
    params: {
      deptName,
    },
  });
};
//获取部门负责人
export const getDeptPrincipal = id => {
  return request({
    url: '/blade-system/dept/detail',
    method: 'get',
    params: {
      id,
    },
  });
};
//新建部门
export const addDept = data => {
  return request({
    url: '/blade-system/dept/submit',
    method: 'post',
    data: {
      ...data,
    },
  });
};
//更换父节点
export const changeDeptParent = data => {
  return request({
    url: '/blade-system/dept/changeParent',
    method: 'post',
    data: {
      ...data,
    },
  });
};

export const addPerson = data => {
  //新建岗位
  return request({
    url: '/blade-system/userTenant/submit',
    method: 'post',
    data: {
      ...data,
    },
  });
};

export const updatePerson = data => {
  //编辑成员信息
  return request({
    url: '/blade-system/userTenant/updateUser',
    method: 'post',
    data: {
      ...data,
    },
  });
};

export const removeDeptId = ids => {
  //部门删除
  return request({
    url: '/blade-system/dept/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};
export const resetNameDept = (id, deptName) => {
  //部门重命名
  return request({
    url: '/blade-system/dept/rename/' + id,
    method: 'post',
    params: {
      deptName,
    },
  });
};
//删除成员
export const delPerson = ids => {
  return request({
    url: '/blade-system/userTenant/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};
//获取管理员
export const getAdmin = tenantId => {
  return request({
    url: '/blade-system/userTenant/getAdmin',
    method: 'get',
    params: {
      tenantId,
    },
  });
};
//设置管理员
export const setAdmin = userId => {
  return request({
    url: '/blade-system/userTenant/setting',
    method: 'post',
    params: {
      userId,
    },
  });
};
//获得某一部门下的成员
export const selectByDeptId = deptId => {
  return request({
    url: '/blade-system/userTenant/selectByDeptId',
    method: 'get',
    params: {
      deptId,
    },
  });
};

export const getAllUser = tenantId => {
  return request({
    url: '/blade-system/userTenant/getUserByOrgId/' + tenantId,
    method: 'get',
  });
};
//excel验证
export const checkExcel = uuid => {
  return request({
    url: '/blade-system/userTenant/checkTemplate',
    method: 'get',
    params: { uuid },
  });
};
//导入成员
export const importUser = uuid => {
  return request({
    url: '/blade-system/userTenant/importUser',
    method: 'get',
    params: { uuid },
  });
};
//获取权限列表tree
export const permissionTree = () => {
  return request({
    url: '/blade-system/userMenu/grant-tree',
    method: 'get',
  });
};
//权限设置
export const setPermission = data => {
  return request({
    url: '/blade-system/userMenu/grant',
    method: 'post',
    data: {
      ...data,
    },
  });
};
//获取成员权限
export const getMenu = userId => {
  return request({
    url: '/blade-system/userMenu/getMenu/' + userId,
    method: 'get',
  });
};
//导出
export const exportFileS = ids => {
  return request({
    url: '/blade-system/userTenant/export',
    method: 'post',
    params: {
      ids,
    },
    responseType: 'blob',
  });
};

//获取任务包列表
export const getTaskPackage = params => {
  return request({
    url: '/nbxt-service-task/taskPackage/list',
    method: 'post',
    params,
  });
};
//获取成员所在工作组
export const getUserDuty = userId => {
  return request({
    url: '/nbxt-service-task/dutyManage/workingGroupList/' + userId,
    method: 'get',
  });
};
export const setUserDuty = (userId, workingGroupIds) => {
  return request({
    url: '/nbxt-service-task/dutyManage/userWithWorkingGroup/update',
    method: 'post',
    data: {
      userId,
      workingGroupIds,
    },
  });
};
