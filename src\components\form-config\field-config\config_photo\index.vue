<template>
  <config-title :data="data" />
  <config-permission :data="data" />
  <config-data-link :data="data" isFormItem v-if="!data.isDynamic"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import ConfigDataLink from '@/components/form-config/common-config/ConfigDataLink/index.vue';
import { PhotoAllField } from './type';

const props = defineProps<{
  data: PhotoAllField;
}>();

watch(
  () => props.data.isDynamic, 
  (val) => {
    if(val){
      props.data.defaultValue = []
    }else{
      props.data.defaultType = 1
    }
  },
  { immediate: true }
)

</script>

<style scoped lang="scss"></style>
