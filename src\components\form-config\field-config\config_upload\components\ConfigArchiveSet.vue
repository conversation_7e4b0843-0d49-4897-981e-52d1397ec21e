<template>
  <el-button class="w-full" size="large" @click="visible = true">存证配置</el-button>
  <el-dialog
    v-model="visible"
    title="存证配置"
    width="1000"
    :destroy-on-close="true"
  >
    <el-form :model="form" label-width="150px" ref="fileForm" :rules="fileRules"  label-position="left" class="ml-5">
      <el-form-item label="存档" prop="isEa">
        <xt-radio v-model="form.isEa" :dic="isWhetherOption"></xt-radio>
      </el-form-item>
      <el-form-item label="存证" prop="isEe">
        <xt-radio v-model="form.isEe" :dic="isWhetherOption"></xt-radio>
      </el-form-item>
      <el-form-item label="存证+公证" prop="notarization" v-if="form.isEe">
        <xt-radio v-model="form.notarization" :dic="isWhetherOption"></xt-radio>
      </el-form-item>
       <el-form-item label="存至电子台账" prop="toLedger">
        <xt-radio :dic="isWhetherOption" v-model="form.toLedger" />
      </el-form-item>
      <el-form-item
        label="电子台账目录"
        :prop="form.toLedger ? 'dirIds' : ''"
        v-if="form.toLedger"
      >
        <el-tree-select
          v-model="form.dirIds"
          node-key="id"
          show-checkbox
          default-expand-all
          multiple
          :props="{ label: 'ledgerDirName' }"
          check-strictly
          :data="ledgerTree"
          class="!w-300px"
        />
      </el-form-item>
      <el-form-item label="存文件管理" prop="isMenu">
        <xt-radio :dic="isWhetherOption" v-model="form.isMenu" />
      </el-form-item>
      <el-form-item
        label="文件管理目录"
        :prop="form.isMenu ? 'menuIds' : ''"
        v-if="form.isMenu"
      >
        <el-tree-select
          v-model="form.menuIds"
          node-key="id"
          show-checkbox
          check-strictly
          :props="{
            label: 'menuName',
            children: 'child',
          }"
          multiple
          default-expand-all
          :data="fileManageTree"
          class="!w-300px"
        />
      </el-form-item>
      <el-form-item label="存至规章制度" prop="toRegulations">
        <xt-radio :dic="isWhetherOption" v-model="form.toRegulations" />
      </el-form-item>
      <RegulatoryConfig
        v-if="form.toRegulations"
        v-model="form.regulatoryData"
      />
      <el-form-item
        label="档案门类代码"
        v-if="form.isEa || form.isEe"
        prop="archivalId"
      >
        <el-cascader
          class="!w-300px"
          popper-class="!w-600px"
          v-model="form.archivalId"
          :options="catalogOrgList"
          :props="{ label: 'content', value: 'id', emitPath: false }"
          clearable
        >
          <template #default="{ data }">
            <el-tooltip effect="dark" placement="top-start" popper-class="!w-300px break-all">
              <template #content>
                <span v-if="data.name">({{ data.name }})</span>{{ data.content }}
              </template>
              <div>
                <span v-if="data.name">({{ data.name }})</span>{{ data.content }}
              </div>
            </el-tooltip>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item
        label="机构代码"
        prop="orgId"
        v-if="form.isEa || form.isEe"

      >
        <xt-select
          class="!w-300px"
          :dic="archivesList"
          :props="{ label: 'name', value: 'id' }"
          v-model="form.orgId"
        />
      </el-form-item>
      <el-form-item label="标签" >
        <el-space alignment="center">
          <div class="min-w-300px min-h-30px border-1 border-dashed border-gray rounded-2px">
            <SelectLabelBy v-model="form.labelIds" data-type="string" />
          </div>
        </el-space>
      </el-form-item>

      <el-form-item label="密级" >
        <avue-select v-model="form.level" :dic="fileClassification"/>
      </el-form-item>
      <el-form-item label="保密时间" v-if="form.level !== 1">
        <el-row class="w-full" :gutter="20">
          <el-col :span="5">档案生成起</el-col>
          <el-col :span="6">
            <el-input v-model="form.secrecyYear"/>
          </el-col>
          <el-col :span="2">
            <span>年</span>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="保密范围" v-if="form.level !== 1">
        <div class="flex flex-col gap-5px w-400px">
          <SelectDeptBy
            data-type="string"
            v-model="form.secrecyDept"
            :openLoad="true"
            multiple
            placeholder="请选择部门"
            :is-radio="false"
          />
          <SelectDutyBy
           data-type="string"
            v-model="form.secrecyGroup"
            :openLoad="true"
            multiple
            placeholder="请选择工作组"
            :is-radio="false"
          />
          <selectUserGroup
            data-type="string"
            v-model:model-list="selectUserList"
            v-model:visible="selectVisible"
            :dataCallBack="dataCallBack"
            :is-radio="false"
            :is-show-dept="false"
            :is-show-work="false"
          ></selectUserGroup>
        </div>
      </el-form-item>
      <LookLimits
        v-if="form.isEa"
        :isSelectField="true"
        :fetch-field="true"
        :isRequire="false"
        :field-list="columns"
        v-model:look-type="form.termType"
        v-model:date-start="form.termRange.start"
        v-model:date-end="form.termRange.end"
        v-model:day="form.termRange.day"
        v-model:hour="form.termRange.hour"
        v-model:minutes="form.termRange.min"
        v-model:day-code="form.termRange.dayCode"
        v-model:hour-code="form.termRange.hourCode"
        v-model:min-code="form.termRange.minCode"
        v-model:day-type="form.termRange.dayType"
        v-model:date-range-code="form.termRange.dateRangeCode"
        v-model:depts="form.viewUser.depts"
        v-model:works="form.viewUser.works"
        v-model:users="form.viewUser.users"
        v-model:dept-code="form.viewUser.deptCode"
        v-model:duty-code="form.viewUser.workCode"
        v-model:user-code="form.viewUser.userCode"
      />
      <!-- <el-form-item label="责任人员">
        <el-input disabled v-model="form.responsibleUser"></el-input>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup lang="ts">
import useUserStore from '@/piniaStore/banban/useUserStore';
import { isWhetherOption,fileClassification } from '../const.ts';
import { getCataloglList, getAgencyList } from '@/api/catalog/index';
import { Catalog } from '@/api/interface';
import { fileSetItem, UploadAllField } from '../type.ts';
import SelectDeptBy from '@/components/form-component/XTUserDept/XTUserDept.vue';
import SelectLabelBy from '@/components/select/selectLabelBy.vue';
import selectUserGroup from '@/components/select/selectUserGroup.vue';
import SelectDutyBy from '@/components/form-component/XTUserTag/XTUserTag.vue'; 
import { taskSourceInjectKey } from '@/components/xt-el-form/constant.ts';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import { renameField } from '@/utils/util';
import { useArchiveSet } from '../hooks.ts'
import RegulatoryConfig from './RegulatoryConfig.vue';
import LookLimits from '@/views/banban/know/component/LookLimits.vue';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { getFileRules } from '../const.ts';
import { objForm } from '../const.ts'

const { taskBase } = storeToRefs(useTaskDetailStore());

const columns = computed(() =>{
  return (taskBase.value as any)?.column || []
})

const fileForm = ref();
const store = useStore();
const tid = computed(() => {
  const userMsg = store.getters.userInfo;
  return userMsg.tid;
});
const { ledgerTree,fileManageTree,initLedgerTree,initFileManageTree} = useArchiveSet()


const props = defineProps<{
    data: UploadAllField;
}>();
provide(taskSourceInjectKey, tid);

const { initAllUserList, findUsersByIds } = useUserStore();

const visible = ref(false);
const selectVisible = ref(false);
const selectUserList = ref<{ id: string; nickName: string }[]>([]);
const form = ref<fileSetItem>({...objForm});
const catalogOrgList = ref<any[]>([]); // 档案门类
const getCatalog = async () => {
  const { data } = await getCataloglList({ current: 1, size: 9999 } as Catalog.ReqParams);
  catalogOrgList.value = renameField(data.data.records, 'details', 'children');
};

const archivesList = ref<any[]>([]); // 机构代码
const getAgency = async () => {
  const { data } = await getAgencyList({ current: 1, size: 9999 } as Catalog.ReqParams);
  archivesList.value = data.data.records;
};

const dataCallBack = (data: any) => {
  form.value.secrecyUser = data.map((item:any) => item.id).join();
  return data;
};

const handleSubmit = () => {
  if (
    !['isEe', 'isEa', 'toLedger', 'isMenu', 'toRegulations'].some(
      (key) => (form.value as Record<string, any>)[key]
    )
  ) {
    ElMessage.warning('存证/存档/电子档案/文件管理/规章制度不可都为否');
    return;
  }
  // if(form.value.level !== 1 && (!form.value.secrecyUser || !form.value.secrecyDept || !form.value.secrecyGroup)) {
  //   ElMessage.warning('请选择保密范围');
  //   return
  // }
   fileForm.value.validate((valid:boolean) => {
    if(valid){
       props.data.fileSet = {...form.value};
      visible.value = false;
    }

   });
};

watchEffect(() => {
  if(!form.value.toRegulations){
    form.value.regulatoryData = {}
  }
  if(!form.value.isEe){
    form.value.notarization = 0
  }
  if(form.value.level === 1){
    form.value.secrecyDept = ''
    form.value.secrecyGroup = ''
    form.value.secrecyUser = ''
    selectUserList.value = []
  }
})


watch(
  () => visible.value,
  async (val) => {
    if (val) {
      form.value = _.merge(_.cloneDeep(objForm), props.data.fileSet);
      await getCatalog();
      await getAgency();
      await initAllUserList();
      initLedgerTree()//请求台账目录
      initFileManageTree()//请求文件目录
      selectUserList.value = findUsersByIds(form.value.secrecyUser);
    }
  }
);
const fileRules = getFileRules(form);//获取校验规则
</script>

<style lang="scss" scoped>
.box-card {
  cursor: pointer;
  width: 100%;
  color: #a8abb2;
  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
</style>