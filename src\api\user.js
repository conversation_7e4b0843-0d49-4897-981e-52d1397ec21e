import request from '@/axios';

import website from '@/config/website';

export const loginByUsername = (tenantId, deptId, roleId, username, password, type, key, code) =>
  request({
    url: '/blade-auth/oauth/token',
    method: 'post',
    headers: {
      'Tenant-Id': tenantId,
      'Dept-Id': website.switchMode ? deptId : '',
      'Role-Id': website.switchMode ? roleId : '',
      'Captcha-Key': key,
      'Captcha-Code': code,
    },
    params: {
      tenantId,
      username,
      password,
      grant_type: website.captchaMode ? 'captcha' : 'password',
      scope: 'all',
      type,
    },
  });
//获取验证码
export const loginVerification = (phone) =>
  request({
    url: `/blade-auth/oauth/sendMsg`,
    // url: `/blade-auth/oauth/sendMsg/${phone}`,
    // url: `/blade-auth//oauth/sendCode/${phone}`,
    method: 'post',
    params: {
      phone
    }
  })
// export const loginByPhone = (username, code, grant_type, tenantId = '000000') =>
//   request({
//     url: '/blade-auth/oauth/tokenByMes',
//     method: 'post',
//     headers: {
//       // 'Tenant-Id': '000000',
//     },
//     params: {
//       tenantId,
//       username,
//       code,
//       grant_type
//     }
//   })
export const loginByPhone = (username, password, grant_type, tenantId = '000000') =>
  request({
    url: '/blade-auth/oauth/tokenByMes',
    method: 'post',
    headers: {
      // 'Tenant-Id': '000000',
    },
    params: {
      tenantId,
      username,
      password,
      grant_type
    }
  })
export const loginBySocial = (tenantId, source, code, state) =>
  request({
    url: '/blade-auth/oauth/token',
    method: 'post',
    headers: {
      'Tenant-Id': tenantId,
    },
    params: {
      tenantId,
      source,
      code,
      state,
      grant_type: 'social',
      scope: 'all',
    },
  });

export const loginBySso = (state, code) =>
  request({
    url: '/blade-auth/oauth/token',
    method: 'post',
    headers: {
      'Tenant-Id': state,
    },
    params: {
      tenantId: state,
      code,
      grant_type: 'authorization_code',
      scope: 'all',
      redirect_uri: website.redirectUri,
    },
  });

export const refreshToken = (refresh_token, tenantId, deptId, roleId) =>
  request({
    url: '/blade-auth/oauth/token',
    method: 'post',
    headers: {
      'Tenant-Id': tenantId,
      'Dept-Id': website.switchMode ? deptId : '',
      'Role-Id': website.switchMode ? roleId : '',
    },
    params: {
      tenantId,
      refresh_token,
      grant_type: 'refresh_token',
      scope: 'all',
    },
  });

export const registerGuest = (form, oauthId) =>
  request({
    url: '/blade-system/user/register-guest',
    method: 'post',
    params: {
      tenantId: form.tenantId,
      name: form.name,
      account: form.account,
      password: form.password,
      oauthId,
    },
  });

export const getButtons = () =>
  request({
    url: '/businessMenu/buttons',
    method: 'get',
  });

export const getCaptcha = () =>
  request({
    url: '/blade-auth/oauth/captcha',
    method: 'get',
    authorization: false,
  });

export const logout = () =>
  request({
    url: '/blade-auth/oauth/logout',
    method: 'get',
    authorization: false,
  });

export const getUserInfo = () =>
  request({
    url: '/blade-auth/oauth/user-info',
    method: 'get',
  });

export const sendLogs = list =>
  request({
    url: '/blade-auth/oauth/logout',
    method: 'post',
    data: list,
  });

export const clearCache = () =>
  request({
    url: '/blade-auth/oauth/clear-cache',
    method: 'get',
    authorization: false,
  });
export const getmyList = (userId) =>
  request({
    url: '/userTenant/mylist',
    method: 'post',
    params: {
      userId
    }
  });
export const cutorgs = (tenantId, tid, username) =>
  request({
    url: '/blade-auth/oauth/tokenByTenant',
    method: 'post',
    params: {
      tenantId,
      tid,
      username,
      grant_type: 'smscode',
    }
  });
// 外部人员info
export const getExternal = (id) =>
  request({
    url: '/workgroup/invite/outsider/info',
    method: 'get',
    params: {
      id,
    }
  });
export const agreeExternal = (id) =>
  request({
    url: '/workgroup/invite/outsider/agree',
    method: 'get',
    params: {
      id,
    }
  });


//部门邀请信息
export const getDeptInviteInfo = (id) =>
  request({
    url: '/dept_invite_common_record/info',
    method: 'get',
    params: {
      id,
    }
  });
//部门邀请确认
export const agreeDeptInviteInfo = (id) =>
  request({
    url: '/dept_invite_common_record/agree',
    method: 'put',
    params: {
      id,
    }
  });
//部门邀请身份验证确认
export const agreeDeptVerifyInviteInfo = (data) =>
  request({
    url: '/dept_invite_common_record/verify/submit',
    method: 'put',
    data: {
      ...data,
    }
  });
//修改密码
export const passwordChange = (data) =>
  request({
    url: '/blade-auth/oauth/updatePassword',
    method: 'post',
    params: {
      ...data
    }
  });
//匹配验证码
export const codeMatch = (phone, code) =>
  request({
    url: '/blade-auth/oauth/verify',
    method: 'post',
    params: {
      phone,
      code,
    }
  });
//用户注册
export const registerUser = (data) =>
  request({
    url: '/blade-auth/oauth/register',
    method: 'post',
    params: {
      ...data
    }
  });
//查询用户是否存在
export const getIsUser = (data) =>
  request({
    url: '/blade-auth/checkUserExists',
    method: 'get',
    params: {
      ...data
    },
    meta:{
      isShowError: false
    }
  });

// 根据用户查询绑定微信的url
export const getWechatQrCode = (userId) =>
  request({
    url: '/wechat/wechat-auth-url',
    method: 'get',
    params: {
      userId
    }
  });

// 获取当前账户是否绑定微信
export const getWechatBindStatus = (userId) =>
  request({
    url: '/wechat/wechat-bind-status',
    method: 'get',
    params: {
      userId
    }
  });

// 解绑微信
export const unBindWechat = (userId) =>
  request({
    url: '/wechat/unbind-wechat',
    method: 'post',
    params: {
      userId
    }
  });
