type HooksOptions = {
  initPage?: number;
  initSize?: number;
};

type Page = {
  total: number;
  currentPage: number;
  pageSize: number;
};

const useAvuePage = <T>(
  request: (page: Page) => Promise<{ d: T[]; total: number }>,
  options: HooksOptions = {}
) => {
  const data = shallowRef<T[]>([]);

  const { initPage = 1, initSize = 10 } = options;

  const page = ref<Page>({
    total: 1,
    currentPage: initPage,
    pageSize: initSize,
  });

  const initData = () => {
    request(page.value).then(({ d, total }) => {
      data.value = d;
      page.value.total = total;
    });
  };

  return {
    data,
    page,
    initData,
  };
};

export default useAvuePage;
