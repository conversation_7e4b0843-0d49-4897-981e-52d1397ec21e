import request from '@/axios';

// 类型对应角色
export const getRoleByType = (type: string) => {
  return request({
    url: '/executor_role/getByType',
    method: 'post',
    params: {type},
  });
};

// 执行人角色类型
export const getByType = () => {
  return request({
    url: '/executor_role/getTypes',
    method: 'post',
  });
};

// export const getUserListByTid = (tid: string) => {
//   return request({
//     url: '/common/aenntt/user/list',
//     method: 'get',
//     params: tid,
//   });
// };
