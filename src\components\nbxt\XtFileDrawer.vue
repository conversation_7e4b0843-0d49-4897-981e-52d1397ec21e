<template>
  <n-drawer
    v-model:show="active"
    :auto-focus="false"
    class="upload-drawer"
    height="95%"
    placement="bottom"
    :on-after-leave="handleClose"
  >
    <n-drawer-content
      :body-content-style="{ background: '#F2F5F6', padding: match ? '10px' : '40px' }"
      :header-style="{ 'justify-content': 'center', height: '70px' }"
      :title="fileTitle"
      closable
    >
    <el-switch
      class="pl-80px"
      v-model="switchFileType"
      size="large"
      active-text="PDF预览"
      inactive-text="WORD预览"
      @change="changeFileType()"
    />
      <div class="flex h-full" :class="[match ? 'gap-5px' : 'gap-20px']">
        <n-button v-bind="btnBind" @click.stop="changeFile(false)">
          <el-icon :size="iconSize">
            <ArrowLeft />
          </el-icon>
        </n-button>
        <n-carousel
          ref="fileCarousel"
          class="bg-white"
          :class="{ 'pb-60px': fileList.length > 1 }"
          @update:current-index="onCurrentChange"
          draggable
        >

          <div
            v-for="(item,index) in fileList"
            :key="`${item?.pdfFileUrl}-${switchFileType}-${index}`"
            class="h-full flex flex-col items-center gap-20px pt-20px"
          >
            <div class="flex-1 w-full">
              <PreviewFile
                height="100%" 
                :isEdit="switchFileType ? 0 : item.isEditFile"
                :fileType="switchFileType && item?.pdfFileUrl? 'Pdf':''"
                :file="{ 
                  fileName: switchFileType? item?.pdfName : item.fileName, 
                  fileUrl:switchFileType ? item?.pdfFileUrl: item.fileUrl,
                  fileId: switchFileType ? '' : item.fileId, 
                  size: switchFileType? item?.pdfFileSize : item.size,
                  downLoadUrl:switchFileType ? item?.pdfFileUrl: item.fileUrl,
                  }"
                />
              <!-- <iframe :src="`https://view.officeapps.live.com/op/view.aspx?src=${item.fileUrl}`" class="h-full w-full"></iframe> -->
            </div>
          </div>
          <template #dots="{ total, currentIndex, to }">
            <div class="flex gap-20px items-center justify-center mt-10px">
              <n-button
                v-for="index of total"
                :key="index"
                :focusable="false"
                :type="currentIndex === index - 1 ? 'primary' : 'default'"
                circle
                secondary
                @click="to(index - 1)"
              >
                {{ index }}
              </n-button>
            </div>
          </template>
        </n-carousel>
        <n-button v-bind="btnBind" @click.stop="changeFile(true)">
          <el-icon :size="iconSize">
            <ArrowRight />
          </el-icon>
        </n-button>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { FileTypeEnum } from '@/hooks/useHandleDetail';
import { matchMobile } from '@/hooks/useMobileMatch';
import PreviewFile from '@/views/mainview/sceneTasks/components/RuleExpress/components/file/PreviewFile.vue'
import { changeFilePreviewType,saveFileByRule } from '@/api/banban/index'

const active = defineModel<boolean>('active', { required: true });
 interface FileType {
  fileName: string;
  fileUrl: string;
  fileId: string;
  size: number;
  pdfName: string;
  pdfFileUrl: string;
  pdfFileSize:number;
  isEditFile?:number;
}
const props = withDefaults(defineProps<{
  // fileList: FileType[];
  fileTitle: string;
  handleId:string;
  submitStatus:number;
  fileType?: FileTypeEnum;
  back?: boolean//关闭后是否退回页面
}>(),
  {
    back: true
  }
);

const match = matchMobile();
const iconSize = computed(() => (match ? 30 : 60));
const switchFileType = ref(false)
const currentIndex = ref(0);
const showFile = ref(true)

const fileList = defineModel<FileType[]>('fileList', { required: true });
// const wordUrl = computed(() =>
//   match ? 'https://view.xdocin.com/view' : 'https://view.officeapps.live.com/op/view.aspx'
// );

const btnBind = computed(() => ({
  focusable: false,
  circle: true,
  class: 'h-auto w-auto self-center',
  quaternary: true,
}));

const fileCarousel = ref();

const onCurrentChange = (index: number) => {
  console.log('当前索引:', index);
  currentIndex.value = index
  switchFileType.value = false
};
const changeFile = (next: boolean) => {
  if (next) {
    fileCarousel.value.next();
  } else {
    fileCarousel.value.prev();
  }
};

const router = useRouter();

const handleClose = async () => {
  // 手动保存
  await saveFileByRule({handledId:props.handleId,submitType:props.fileType === FileTypeEnum.START ? 'BEFORE' : 'AFTER'})
  if (props.fileType === FileTypeEnum.END && props.back) {
    router.back();
  }
};

const changeFileType = async() => {
  showFile.value = false
  const item = fileList.value[currentIndex.value]
  if(switchFileType.value && !item.pdfFileUrl){
    const { data } = await changeFilePreviewType({ name:item.fileName,size:item.size,url:item.fileUrl })
    if(!data?.data) return
    const updatedItem = {
      ...item,
      pdfName: data.data.name,
      pdfFileUrl: data.data.url,
      pdfFileSize: data.data.size,
    };
    nextTick(() => { 
      fileList.value.splice(currentIndex.value, 1, updatedItem);
      showFile.value = true
    })
  }else{
    showFile.value = true
  }
}
</script>

<style scoped> 
.el-switch {
  z-index: 10; /* 确保 z-index 足够高 */
  pointer-events: auto; /* 确保可以点击 */
}

</style>