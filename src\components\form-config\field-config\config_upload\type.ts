import {
  AllDefaultValueType,
  FieldBaseField,
  TitleField,
  basePermissionType,
} from '@/components/form-config/types/AvueTypes';
import { regulatoryType } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType';

//附件-存证配置
export interface fileSetItem {
  isEa: number; //存档
  isEe: number; //存证
  notarization: number; //存证+公证
  archivalId: string; //档案门类代码
  orgId: string; //机构代码
  labelIds: string; // 标签
  level: number; // 档案密级
  secrecyYear: string; // 保密时间
  secrecyDept: string; // 部门
  secrecyGroup: string; // 工作组
  secrecyUser: string; // 成员
  responsibleUser: string; //责任人员
  toLedger: number; //存入电子台账
  dirIds: string[]; //电子台账目录
  isMenu: number; //存文件目录
  menuIds: string[];
  toRegulations: number; //存规章制度
  regulatoryData: Partial<regulatoryType>; //规章制度对象
  viewUser: {
    //可查看人
    depts: string; //部门
    deptCode: string; //部门组件prop
    works: string; //工作组
    workCode: string; //工作组组件prop
    users: string; //成员
    userCode: string; //成员组件prop
  };
  termType: string; //可查看期限 '0':自定义 '1':依文件生成时间 '2': 取字段作为生成时间
  termRange: {
    start: string; //自定义的时间范围-开始时间
    end: string; //自定义的时间范围-结束时间
    dateRangeCode: string; //自定义时间范围prop
    day: number | null; //依文件生成时间-天
    hour: number | null; //依文件生成时间-小时
    min: number | null; //依文件生成时间-分钟
    dayType: number; //1工作日/0天
    dayCode: string; //取字段作为生成时间-天，数字组件prop
    hourCode: string; //取字段作为生成时间-小时，数字组件prop
    minCode: string; //取字段作为生成时间-分钟，数字组件prop
  };
}

export interface fileSetType {
  fileSet: fileSetItem | null;
}

export type conditionType = { prop: string; value?: any; operator?: any; idx: string };

//附件-条件配置
export interface archiveConditionType {
  archiveCondition: conditionType[] | null;
}

/* 附件 */
export type UploadField = basePermissionType & {
  showFileList: boolean;
  fileSize?: number;
  fileSizeMB?: number;
  fileNumber?: number;
  archive: boolean;
  dataType: string;
  props: any;
  propsHttp: any;
  action: string;
  limitFileType: boolean;
  limitFileTypeList: string[];
  accept?: string[];
} & fileSetType &
  archiveConditionType;

export type UploadAllField = FieldBaseField &
  UploadField &
  TitleField &
  AllDefaultValueType &
  fileSetType &
  archiveConditionType;
