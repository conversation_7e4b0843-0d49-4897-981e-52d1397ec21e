<template>
  <div class="flex gap-20px">
    <div v-for="item in model" :key="item" class="relative">
      <el-image
        style="width: 80px; height: 80px"
        :src="item"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="[item]"
        :initial-index="4"
        fit="cover"
      />
      <div v-if="!disabled" class="van-uploader__preview-delete van-uploader__preview-delete--shadow"
        @click.stop="removePhoto(item)">
        <i class="van-badge__wrapper van-icon van-icon-cross van-uploader__preview-delete-icon">
        </i>
      </div>
    </div>
    <van-uploader v-if="!disabled" class="xtPhoto" capture="camera" :upload-icon="uploadIcon" :after-read="afterRead" />
  </div>
</template>

<script setup lang="ts">
import { putFile } from '@/api/resource/oss.js';
import { useFormItem } from 'element-plus'
 
const { formItem } = useFormItem()
const uploadIcon = new URL('../assets/PhotoComponentIcon.png', import.meta.url).href;

defineProps<{
  disabled: boolean;
}>();

const model = defineModel<string[]>();

const afterRead = (file: any) => {
  // 此时可以自行将文件上传至服务器
  const formData = new FormData();
  formData.append('file', file.file);
  putFile(formData).then((res: any) => {
    const link = res.data.data?.link;
    (model.value ??= []).push(link);
  });
};

const removePhoto = (item: string) => {
  // model去除item
  model.value = model.value?.filter((i: string) => i !== item) || [];
};

watch(
  () => model.value,
  () => {
    formItem?.validate('blur')
  },
  {
    immediate:true,
    deep: true
  }
)

</script>

<style scoped lang="scss">
.xtPhoto {
  :deep(.van-icon__image) {
    width: 100%;
    height: 100%;
  }
}
</style>
