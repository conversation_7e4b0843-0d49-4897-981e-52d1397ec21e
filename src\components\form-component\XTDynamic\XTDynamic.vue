<template>
  <div class="flex-(~ col 1) w-full" :class="{ 'fullscreen-mode': isFullScreen }">
    <div class="my-5px flex justify-between" :class="{ 'fullscreen-content': isFullScreen }">
      <!-- 分组汇总不进行任何操作 -->
      <div class="flex" v-if="item.defaultType !== DynamicTypeEnum.GROUPSUMMARY">
        <template v-if="[fieldSource.HANDLED].includes(source as unknown as fieldSource) && handleType !== HandleTypeEnum.Examine">
          <!-- 批量编辑 -->
          <dynamic-edit-batch
            v-if="!isDataSelect && !item.disabled"
            class="mr-2" 
            :disabled="disabledInject" 
            :itemColumns="itemColumns"
            v-model="vModel">
          </dynamic-edit-batch>

          <!-- 批量导入 -->
          <dynamicImport
            v-if="item.defaultType === DefaultTypeEnum.CUSTOM && !item.disabled"
            :itemColumns="itemColumns" 
            :item="item" 
            @change="importComplete">
          </dynamicImport>
          <!-- 数据选择 -->
          <XtDataSelect 
            v-if="isDataSelect"
            :show-fields="item.children" 
            :show-field-ids="showFieldIds"
            v-model="vModel"
            :tableData="tableData"
            :item
            @change="updateData"
            >
          </XtDataSelect>
        </template>
        <!-- 批量删除 -->
        <el-button
          type="danger" 
          v-if="item.batchDelete && handleType !== HandleTypeEnum.Examine"
          text 
          @click="batchDelete"
          >批量删除
        </el-button>
        <el-button
          type="danger"
          v-if="handleType === HandleTypeEnum.Examine && item.batchDelete && !item.disabled"
          text
          @click="batchDelete"
        >批量删除
        </el-button>
      </div>
      <span v-else></span>
      <div class="flex items-center">
        <!-- 全屏按钮 -->
        <el-button
          icon="FullScreen"
          text
          @click="toggleFullScreen"
          v-if="showFull && dynamicType"
          class="mr-2"
        >
          {{ isFullScreen ? '退出全屏' : '全屏' }}
        </el-button>
        <!-- 搜索 -->
        <dynamicSearch :originColumns="itemChildren" :col :search-data="searchData" @change="onSearch"></dynamicSearch>
        
      </div>
      
    </div>
    <p 
      class="m-0" 
      style="color: #f44;font-size: 12px;" 
      v-if="(vModel && vModel.length < Number(rowsMin)) || (vModel && vModel.length > Number(rowsMax))"
    >
      <el-icon color="#f44" size="12"><WarningFilled /></el-icon>
      表格行数({{rowsMin}}行 - {{rowsMax}}行)不符合任务配置要求
    </p>
    <el-card class="pt-5 pb-5" :class="{ 'fullscreen-card': isFullScreen }">
      <component
        :key="item.dynamicType"
        v-if="dynamicType"
        v-model="viewData"
        v-model:originData="vModel"
        :is="dynamicType"
        :columns="itemColumns"
        :originColumns="itemChildren"
        :disabled="disabledInject"
        v-bind="$attrs"
        ref="childComponentRef"
        :source="source"
        :height="isFullScreen ? 'calc(100vh - 200px)' : height"
        :col="col"
        :itemDisabled="item.disabled"
      >
      </component>
    </el-card>

    <div v-if="!menuDisabled">
      <el-button 
        :icon="Plus" 
        :disabled="(vModel || []).length >= Number(rowsMax)"
        link 
        type="primary" 
        @click="addColumn" 
        v-if="!disabledInject && haveColumn && !item.disabled">添加
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue';
import dynamicEditBatch from './component/dynamicEditBatch.vue';
import dynamicImport from './component/dynamicImport.vue';
import { watchImmediate } from '@vueuse/core';
import { AvueColumns, AvueForm, DataSelectField, DynamicField, XtElFormItemType} from '@/components/form-config/types/AvueTypes';
import { RowsTypeEnum} from '@/components/form-config/const/commonDic';
import { generateId } from '@/components/form-config/utils';
import { disabledInjectKey, MenuDisabledInjectKey } from '@/components/xt-el-form/constant';
import { fieldSource, DynamicDataTypeEnum, DefaultTypeEnum, DynamicTypeEnum } from '@/components/form-config/const/commonDic';
import { isEmptyVmodel,isEmptyValue } from '@/utils/field';
import { formInjectKey, handleTypeInjectKey } from '@/components/xt-el-form/constant.ts'
import XtDataSelect from '@/components/nbxt/formDesignExtra/XtDataSelect.vue'
import { HandleTypeEnum } from '@/components/form-config/const/commonDic';
import {  fieldDefault } from '@/utils/field';
import dynamicSearch from './component/dynamicSearch.vue';
import { applyFilters } from './utils';
import { isApiLinkKey } from '@/components/xt-el-form/constant.ts';

const formInject = inject(formInjectKey)!;
const handleType = inject(handleTypeInjectKey, HandleTypeEnum.HANDLED);
const isApiLink = inject(isApiLinkKey, null);//是否正在联动

defineOptions({
  inheritAttrs: false,
});

const props = withDefaults(
  defineProps< DataSelectField &{
    item: AvueColumns & DynamicField;
    source?: number//组件的使用场景 1中间的组件配置 2 组件的默认值设置 3 预览 4 应办
    col:AvueColumns[];
    unInitData?:boolean;
    itemForm: AvueForm;
    tableData?: any[];
    height?: string;
    showFull?: boolean
  }>(),
  {
    showFull: true
  }
);

const vModel = defineModel<AvueForm[]>();

// 全屏状态
const isFullScreen = ref(false);

// 切换全屏模式
const toggleFullScreen = () => {
  isFullScreen.value = !isFullScreen.value;
};

const rowsMin = ref<number | string>(0)
const rowsMax = ref<number | string>(99999)

const XtDynamicForm = defineAsyncComponent(() => import('./component/XtDynamicForm.vue'));
const XtDynamicTable = defineAsyncComponent(() => import('./component/XtDynamicTable.vue'));

const childComponentRef = ref<InstanceType<typeof XtDynamicForm> | typeof XtDynamicTable | null>(null);

const componetMap = {
  'form':XtDynamicForm,
  'crud':XtDynamicTable
} as const

const dynamicType = computed(() => componetMap[props.item.dynamicType as keyof typeof componetMap]);
const itemChildren = computed(() => {
  return props.item.children
});

const showFieldIds = computed(() => {
  return itemChildren.value.map((item: AvueColumns) => item.id);
});

//数据选择/知识标准-单选/多选
const isDataSelect = computed(() => {
  return _.includes([DynamicDataTypeEnum.SINGLE, DynamicDataTypeEnum.MULTIPLE],props.item.dynamicMulType) && 
  _.includes([DynamicTypeEnum.DATASELECT, DynamicTypeEnum.KNOWLEDGE,DynamicTypeEnum.ORGKNOWLEDGE], props.item.defaultType)
});

const getCloneColumns = () => {
  return _.cloneDeep(itemChildren.value || []);
};

const itemColumns = ref<XtElFormItemType[][]>();

watchImmediate(
  () => [itemChildren.value, vModel.value],
  () => {
    itemColumns.value = (vModel.value || []).map(getCloneColumns) as XtElFormItemType[][];
    // console.log(vModel.value, 'vmodel');
  },
  {
    deep: true,
  }
);
// 联动的时候清空搜索条件
watch(
  () =>[ isApiLink?.value, vModel.value],
  (val) => {
    if(isApiLink?.value){
      searchData.value = {}
      viewData.value = vModel.value || []
    }

  }
)

//新增的时候都给一个空值
const childrenObj = computed(() => {
  return itemChildren.value
    .filter((v:AvueColumns) => v.display)
    .reduce((acc:AvueForm, item:AvueColumns) => {
       acc[item.prop] = _.cloneDeep(fieldDefault[item.fixType as keyof typeof fieldDefault]);
      return acc;
    }, {} as Record<string, null>);
});

const addColumn = () => {
  const id = generateId();
  const newItem = {
    id,
    ...childrenObj.value
  };

  // 添加到 vModel
  (vModel.value ??= []).push(newItem);

  // 如果正在搜索，也同步添加到 viewData（共享同一个引用）
  if (isSearching.value) {
    (viewData.value ??= []).push(newItem);
  }
};

const searchData = ref<AvueForm>({}); // 存储搜索条件
const viewData = ref<AvueForm[]>([]);
const isSearching = ref(false); // 是否正在搜索状态

const onSearch = (search: AvueForm) => {
  searchData.value = search;
  const hasSearchConditions = search && Object.values(search).some(value => !isEmptyValue(value));
  isSearching.value = hasSearchConditions;

  if (hasSearchConditions) {
    // 有搜索条件时，显示过滤后的数据
    viewData.value = applyFilters(vModel.value || [], search);
  } else {
    // 无搜索条件时，显示全部数据
    viewData.value = [...(vModel.value || [])];
  }
};

//
watch(
  () => [isSearching.value, vModel.value], 
  (newVal) => { 
    if(!isSearching.value){
     viewData.value = vModel.value || []
    }
  }, 
  { immediate: true }
)

watch(
  formInject, 
  (newVal) => {
  if(props.item.rowsType === RowsTypeEnum.TRENDS){
      rowsMin.value = newVal[props.item.rowsNumbeFieldMin!] || 0
      rowsMax.value = newVal[props.item.rowsNumbeFieldMax!] || 99999
    }
  },
  { immediate: true, deep: true }
);


onMounted(() => {
  watchImmediate(
    vModel,
    v => {
      if (_.isEmpty(v)) {
        addColumn();
      }
      
    },
    { deep: true }
  );

  if(props.item.rowsType === RowsTypeEnum.CUSTOM){
    rowsMin.value = props.item?.rowsMin || 0
    rowsMax.value = props.item?.rowsMax || 99999
  }
});

const disabledInject = inject(disabledInjectKey)!;
const menuDisabled = inject(MenuDisabledInjectKey, false)!;

const haveColumn = computed(() => itemChildren.value?.length);



const updateData = () => {
  if (childComponentRef.value && 'updateTable' in childComponentRef.value) {
    childComponentRef.value.updateTable();
  }
}

const batchDelete = () => {
  childComponentRef.value?.batchDeleteData(handleType === HandleTypeEnum.Examine ? true : false)
}

//导入完成
const importComplete = (list?: AvueForm[]) => {
  if (isEmptyVmodel(vModel.value)) {
    vModel.value = list?.slice() || []; // 安全地为 vModel.value 赋值一个新数组或空数组
  } else if (list) {
    vModel.value?.push(...list); // 使用扩展运算符将 list 的元素逐个推入 vModel.value
  }
  updateData();
}


defineExpose({
  updateData
});

</script>

<style scoped>
/* 全屏模式样式 */
.fullscreen-mode {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: white;
  /* padding: 20px; */
  overflow: auto;
}

.fullscreen-content {
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
}

.fullscreen-card {
  height: calc(100vh - 160px) !important;
}

.fullscreen-card :deep(.el-card__body) {
  height: 100%;
  /* display: flex;
  flex-direction: column; */
}

/* 全屏模式下的表格优化 */
.fullscreen-mode :deep(.vxe-table) {
  height: 100% !important;
}

/* 全屏模式下的表单优化 */
.fullscreen-mode :deep(.el-scrollbar) {
  height: 100% !important;
}
</style>
