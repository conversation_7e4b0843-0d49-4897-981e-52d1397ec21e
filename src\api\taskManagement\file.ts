import request from '@/axios';
import { FileConfig } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType';

/**
 * 文件提交
 * @param file
 * @returns
 */
export const fileSubmit = (file: { fileName: string; filePath: string }) => {
  return request({
    url: '/file/submit',
    method: 'post',
    data: {
      ...file,
    },
  });
};
/**
 * 文件配置详情
 * @param data 文件配置信息
 * @returns
 */
export const fileDetail = (data: Partial<FileConfig>) => {
  return request({
    url: '/file_configuration/detail',
    method: 'get',
    params: {
      data,
    },
  });
};
/**
 * 文件配置提交
 * @param data 文件配置信息
 * @returns
 */
export const fileConfigSubmit = (data: FileConfig) => {
  return request({
    url: '/file_configuration/submit',
    method: 'post',
    data
  });
};
