<template>
  <n-card :content-style="{ padding: '10px', position: 'relative', paddingRight: '60px' }">
    <el-row :gutter="10">
      <!-- 推送任务 -->
      <el-col :span="8">
        <el-input :model-value="pushTaskLabel" disabled></el-input>
      </el-col>
      <el-col :span="8">
        <avue-select
          class="w-full"
          :dic="filterPushFieldList"
          :props="{ label: fieldLabel, value: fieldKey, type: 'prop' }"
          v-model="dataProp"
          placeholder="请选择字段"
        ></avue-select>
      </el-col>
      <!-- 规则 -->
      <el-col :span="8">
        <avue-select
          class="w-full"
          :dic="symbolList"
          v-model="symbol"
          placeholder="请选择规则"
        ></avue-select>
      </el-col>
      <el-col :span="8">
        <avue-select
          class="w-full"
          :dic="getLinkageResult"
          :props="{ label: fieldLabel, value: fieldKey, type: 'prop' }"
          v-model="linkageProp"
          placeholder="请选择字段"
        ></avue-select>
      </el-col>
    </el-row>
    <slot name="customBtn" v-if="delBtn">
      <el-col :span="2">
        <el-button type="danger" link class="absolute right-10px top-10px" @click="$emit('delete')"
        >删除
        </el-button>
      </el-col>
    </slot>
  </n-card>
</template>

<script setup lang="ts">
import { Field } from '@/types/OriginTypes.ts';
import { computed, watch } from 'vue';
import { getLinkageSymbolConfig, getLinkageSymbolResult, transChineseSymbol } from './utils/fieldUtils.ts';
import {
  MethodSymbolEnum
} from '@/components/conditions/option/field.ts';

import { TaskLinkageTypes } from '@/components/conditions/types/types.ts';
import { XtFieldType } from '@/components/form-config/types/FieldTypes.ts';

const props = withDefaults(defineProps<{
  //使用的字段key
  fieldKey?: string;
  //使用的字段Label
  fieldLabel?: string;
  //是否显示删除按钮
  delBtn:boolean
  //是否默认加入判空规则
  needEmpty?: boolean;
  // 当前任务名称
  pushTaskLabel:string|undefined
  //当前任务字段列表
  pushFieldList:Field[] | any[]
  //联动任务字段列表
  linkFieldList:Field[] | any[]
  //自定义的规则配置,与默认配置项不同需要配置，参考/src/components/conditions/option/field.ts------export const SymbolLinkage
  filterSymbolField?: TaskLinkageTypes[]
    /**
   * 过滤规则的方法，回调函数包含三个参数必须返回对应的symbol
   * @param result 默认的规则列表
   * @param type 字段类型
   * @param isDynamic 是否子表单类字段
   */
  filterSymbolMethod?: (result: MethodSymbolEnum[], type: XtFieldType, isDynamic: boolean) => MethodSymbolEnum[];

}>(), {
  fieldKey: () => 'prop',
  fieldLabel: () => 'copyLabel',
  delBtn: () => true,
  filterSymbolField: () => [],
  filterSymbolMethod: (result: MethodSymbolEnum[], _type: XtFieldType, _isDynamic: boolean) => result,
});


const emits = defineEmits(['delete']);

const dataProp = defineModel<string>('dataProp');

const linkageProp = defineModel<string>('linkageProp');

const symbol = defineModel<MethodSymbolEnum>('symbol');

//当前选择的字段
const field = computed(() => filterPushFieldList.value.find(
  item => item[props.fieldKey] === dataProp.value
));

//当前选择字段是否子表单字段
const isDynamic = computed(() => {
  return _.includes(field.value?.prop, 'dynamic');
});

const fieldType = computed(() => {
  return field.value?.fixType || field.value?.type || '';
});

const filterPushFieldList = computed(() => {
  return props.pushFieldList;
});

const filterLinkFieldList=computed(()=>{
  return props.linkFieldList
})

//当前字段规则集字典[]
const symbolList = computed(() => {
  const linkageConfig = getLinkageSymbolConfig(fieldType.value);
  const customConfig = props.filterSymbolField.find(item => _.includes(item.types, fieldType.value));
  const filterSymbolList = customConfig?.symbols || linkageConfig?.symbols;
  return transChineseSymbol(filterSymbolList);
});

const getLinkageResult = computed(() => {
  const result = getLinkageSymbolResult(fieldType.value);
  const customConfig = props.filterSymbolField.find(item => _.includes(item.types, fieldType.value))?.result;
  let customResult=customConfig?.find(item=>_.includes(item.types,fieldType.value))?.result;
  return filterLinkFieldList.value.filter(item => _.includes(customResult || result, item?.fixType || ''));
});

onMounted(() => {
  watch(
    dataProp,
    () => {
      symbol.value = undefined;
    }
  );

  watch(
    symbol,
    () => {
      linkageProp.value = undefined;
    }
  );
});
</script>

<style scoped lang="scss"></style>
