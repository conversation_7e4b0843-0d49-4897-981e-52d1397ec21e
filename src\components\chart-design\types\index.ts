import { Mark } from './widget.ts';

export enum ChartTypeEnum {
  TEXT = 'text',
  PIE = 'pie',
  COLUMN = 'column',
  BAR = 'bar',
  LINE = 'line',
  TABLE = 'table'
}

export type Target = {
  targetName?: string
  targetValue?: number
}

export type PreviewDataType = {
  chartData?: any[],
  targetList?: Target[]
  series?: any[]
}

export type DesignProps = Omit<GlobalProps, 'chartData' | 'previewData'>

export type GlobalProps = {
  previewData: PreviewDataType
} & Partial<ChartBaseProps>

export type ChartBaseProps<T extends CommonStyle = ChartStyle> = {
  chartType: ChartTypeEnum,
  // 维度
  dimensions: Mark[],
  // 指标
  metrics: Mark[],
  // 辅助指标
  auxiliary: Mark[],
  // 图表样式
  chartStyle: T
}

export type ChartProps<T extends CommonStyle = ChartStyle> =
  ChartBaseProps<T> & Required<PreviewDataType>

export type ChartStyle = TextStyle | PieStyle | ColumnStyle | BarStyle | LineStyle | TableStyle

// 整体样式
export type CommonStyle = {
  // 背景
  backgroundColor?: string,
  // 标题名称
  title?: string,
  // 标题颜色
  titleColor?: string,
  // 标题位置
  titlePosition?: TitlePositionEnum,
}

export enum TitlePositionEnum {
  LEFT = 'start',
  CENTER = 'center',
  RIGHT = 'end'
}

// 指标图样式
export type TextStyle = CommonStyle & {
  textSize?: TextSizeEnum,
  textColor?: string,
}

export enum TextSizeEnum {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large'
}

// ECharts
export type EChartsCommonStyle = CommonStyle & {
  // 数值显示
  showValue?: boolean
  valueSize?: number
  valueColor?: string
  // 图例
  showLegend?: boolean
  legendPosition?: LegendPositionEnum
}

export type AxisType = 'xAxis' | 'yAxis'

export type EChartsAxisStyle = EChartsCommonStyle & {
  xAxisLabel?: string
  xAxisLabelRotate?: number
  xAxisLabelSize?: number
  xAxisLabelColor?: string
  xAxisLineColor?: string

  yAxisLabel?: string
  yAxisLabelRotate?: number
  yAxisLabelSize?: number
  yAxisLabelColor?: string
  yAxisLineColor?: string
}

export enum LegendPositionEnum {
  BOTTOM = 'bottom',
  TOP = 'top',
  LEFT = 'left',
  RIGHT = 'right'
}

// 饼图样式
export type PieStyle = EChartsCommonStyle & {
  // 图表配色
  pieColor?: any[]
}

// 柱形图样式
export type ColumnStyle = EChartsAxisStyle & {}
export type BarStyle = EChartsAxisStyle & {}

// 折线图
export type LineStyle = EChartsAxisStyle & {
  showArea?: boolean
  lineWidth?: number
  showSymbol?: boolean
  symbolSize?: number
}

// 表格
export enum TableAlignEnum {
  LEFT = 'left',
  RIGHT = 'right',
  CENTER = 'center',
}

export type TableStyle = CommonStyle & {
  tableHeaderColor?: string;
  tableContentColor?: string;
  tableAlign?: TableAlignEnum;
}