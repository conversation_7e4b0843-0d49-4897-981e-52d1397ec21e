<template>
  <!-- 自定义/选规则 -->
  <el-select
    v-show="rulesShow"
    v-model="rulesType"
    class="mb-10px"
    @change="changeRuleType"
  >
    <el-option
      v-for="item in ruleDic"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
  <!--规则列表-值与值 -->
  <div v-show="formulaShow" class="flex w-full">
    <avue-select
      v-model="data.ruleExpressId"
      :dic="formulaRuleList"
      :props="{ label: 'ruleName', value: 'ruleExpressId' }"
      class="mb-10px"
      :clearable="true"
    />
    <el-button type="primary" text @click="visible1 = true" v-if="data.ruleExpressId">预览</el-button>
    <formulaPreview
      v-model:visible="visible1"
      v-model:formula="formulaPre"
      :fieldList="temFieldList"
      :data="data"
    />
  </div>
  <!--规则列表-值与数据表 -->
  <div v-show="dataLinkShow" class="flex w-full">
    <avue-select
      v-model="data.ruleExpressId"
      :dic="dataLinkRuleList"
      :props="{ label: 'ruleName', value: 'ruleExpressId' }"
      class="mb-10px w-full"
      :clearable="true"
    />
    <el-button type="primary" text @click="handleDataLinksPre" v-if="data.ruleExpressId">预览</el-button>
    <datalinkPreview
      v-model:visible="visible"
      v-model:data="(dataLinkPreview as dataLinksRulePreviewType)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic';
import { getFormulaRules, getDatalinksRules, getDatalinksRulesPreview } from '@/api/taskManagement/common';
import { formulaRuleType, dataLinksRuleType, dataLinksRulePreviewType } from '@/api/interface/formula';
import datalinkPreview from './components/datalinkPreview.vue';
import formulaPreview from './components/formulaPreview.vue';
import { getFieldTempDetails } from '@/api/taskManagement/fieldTemp/index';
import { RuleSelectorProps } from './type';

const props = defineProps<RuleSelectorProps>();
const rulesType = defineModel<number>('rulesType');
const visible = ref(false);
const visible1 = ref(false);

const ruleDic = [
  { label: '自定义', value: 1 },
  { label: '选规则', value: 2 },
];

const rulesShow = computed(() => {
  return props.data.fixType !== 'dynamic' && 
  (props.data.ruleExpressId || (props.data.defaultType === DefaultTypeEnum.FORMULA && props.data.templateId) || 
    props.data.defaultType === DefaultTypeEnum.LINK
  )
});

const formulaShow = computed(() => {
  return props.data.defaultType === DefaultTypeEnum.FORMULA && props.data.templateId && rulesType.value === 2;
});

const dataLinkShow = computed(() => {
  return props.data.defaultType === DefaultTypeEnum.LINK && rulesType.value === 2;
});

const formulaRuleList = ref<formulaRuleType[]>([]);
const formulaPre = ref();
const temFieldList = ref();
const dataLinkRuleList = ref<dataLinksRuleType>();
const dataLinkPreview = ref<dataLinksRulePreviewType>();

const changeRuleType = () => {
  props.data.ruleExpressId = ''
  props.data.linkFields = []
  props.data.dataLinks = { list: []}
  props.data.query = {list:[]}
  props.data.formula = {}
};

const getFormulaRulesList = async () => {
  const { data } = await getFormulaRules({
    fileId: props.data.mid as string,
    taskId: props.taskId,
    templateId: props.data.templateId,
  });
  formulaRuleList.value = data.data.ruleExpressId ? [data.data] : [];
  formulaPre.value = data.data.formula ? JSON.parse(data.data.formula) : {};
  const res = await getFieldTempDetails({ formid: props.data.templateId });
  temFieldList.value = processData(res.data.data);
};

const getDatalinksRulesList = async () => {
  const { data } = await getDatalinksRules({
    fileType: props.data.fixType as string,
    taskId: props.taskId,
  });
  dataLinkRuleList.value = data.data;
};

const processData = (data: any) => {
  return data.map((item: any) => {
    const newItem = { ...JSON.parse(item.content), prop: item.id };
    if (item.children) {
      newItem.children = processData(item.children);
    }
    return newItem;
  });
};

const handleDataLinksPre = async () => {
  getFormulaRulesPre();
  visible.value = true;
};

const getFormulaRulesPre = async () => {
  const { data } = await getDatalinksRulesPreview({ ruleExpressId: props.data.ruleExpressId });
  dataLinkPreview.value = data.data;
};

watch(
  () => props.data.ruleExpressId,
  (val) => {
    if (val) {
      rulesType.value = 2;
      props.data.dataLinks = { list: []}
      props.data.query = {list:[]}
      props.data.formula = {}
    }
  },
  { immediate: true }
);

watch(
  () => formulaShow.value,
  (val) => {
    if (val) {
      getFormulaRulesList();
    }
  },
  { immediate: true }
);

watch(
  () => dataLinkShow.value,
  (val) => {
    if (val) {
      getDatalinksRulesList();
    }
  }
);
</script>

<style scoped lang="scss"></style>