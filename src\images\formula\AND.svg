<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 45</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-230.000000, -2770.000000)">
            <g id="编组-45" transform="translate(230.000000, 2770.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-5" transform="translate(10.000000, 42.000000)" font-family="PingFangSC-Regular, PingFang SC"
                   font-size="14" font-weight="normal" line-spacing="23">
                    <text id="如果所有参数都为真，AND函数返回布尔值">
                        <tspan x="0" y="15" fill="#3A3A3A">如果所有参数都为真，</tspan>
                        <tspan x="140" y="15" fill="#3F70FF">AND</tspan>
                        <tspan x="169.148" y="15" fill="#3A3A3A">函数返回布尔值</tspan>
                        <tspan x="0" y="38" fill="#3A3A3A">true，否则返回布尔值 false。</tspan>
                        <tspan x="0" y="62" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="62" fill="#3F70FF">AND</tspan>
                        <tspan x="78.148" y="62" fill="#3A3A3A">(逻辑表达式1,逻辑表达式2,...)</tspan>
                        <tspan x="0" y="86" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="86" fill="#3F70FF">AND</tspan>
                        <tspan x="78.148" y="86" fill="#3A3A3A">(</tspan>
                    </text>
                </g>
                <g id="编组-30" transform="translate(93.000000, 117.000000)">
                    <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="52" height="16"></rect>
                    <text id="语文成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                          font-weight="normal" line-spacing="16" fill="#3F70FF">
                        <tspan x="6" y="11">语文成绩</tspan>
                    </text>
                </g>
                <g id="编组-30" transform="translate(178.000000, 117.000000)">
                    <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="52" height="16"></rect>
                    <text id="数学成绩" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                          font-weight="normal" line-spacing="16" fill="#3F70FF">
                        <tspan x="6" y="11">数学成绩</tspan>
                    </text>
                </g>
                <text id="&gt;60" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="146" y="130">&gt;60</tspan>
                </text>
                <text id="&gt;60)如果两门" font-family="PingFangSC-Regular, PingFang SC" font-size="14"
                      font-weight="normal" fill="#3A3A3A">
                    <tspan x="231" y="130">&gt;60)如果两门</tspan>
                </text>
                <text id="门课程都&gt;60，返回true，否则返回f" font-family="PingFangSC-Regular, PingFang SC"
                      font-size="14" font-weight="normal" fill="#3A3A3A">
                    <tspan x="18" y="153">门课程都&gt;60，返回true，否则返回false。</tspan>
                </text>
                <text id="," font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="173" y="130">,</tspan>
                </text>
                <text id="AND" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">AND</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
