// 分页请求参数
export interface ReqPage {
  current: number;
  size: number;
}

export namespace yearBook {
  export interface ReqParams {
    type: number;
    deptIds?: string[] | string;
  }
  // export interface ResList {
  //     type: number;
  // }
  export interface subType {
    deptId?: string;
    year?: string;
    title?: string;
    text?: string;
    type: number;
    id?: string;
  }
}

export namespace labelType {
  export interface ReqParams {
    label: string;
    id?: string;
  }
}
export namespace archives {
  export interface archivesParams {
    labels?: string;
    keyWord?: string;
    code?: string;
    tenantid: string;
  }
  export interface lookRecordParams {
    operationType: number;
    type: number;
    name: string;
    user: string;
    start?: string;
    end?: string;
    operationTime:string[]
    operationUser:string;
  }
}
export namespace files {
  export interface addFileParams {
    name: string;
    id: string;
  }
  export interface pageParams {
    current: number;
    size: number;
    menuid: string;
    start: string;
    end: string;
    search: string;
    tenantid:string
  }
}
export interface pageParamsElectric {
  current: number;
  size: number;
}
