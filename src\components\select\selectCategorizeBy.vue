<template>
  <div class="select_dept">
    <slot :data="emitDept">
      <el-card
        class="box-card"
        :class="{ disabled: disabled }"
        @click="disabled ? void 0 : openSelect()"
      >
        <template v-if="emitDept.length > 0">
          <el-tag
            v-for="item in emitDept"
            :key="item.id"
            :closable="!disabled && closable"
            @close="unCheckDept(item, true)"
            style="margin: 0 5px"
            size="large"
          >{{ item.categorizeName }}</el-tag
          >
        </template>
        <div v-else class="select_dept_placeholder">{{ placeholder }}</div>
      </el-card>
    </slot>
    <dialog-form ref="userFormRef" :title="title" data-type="Array">
      <div class="select_wrapper">
        <div class="select_top">
          <el-card class="box-card">
            <template v-if="checkDept.length > 0">
              <el-tag
                v-for="item in checkDept"
                closable
                @close="unCheckDept(item)"
                style="margin: 5px"
                size="large"
              >{{ item.categorizeName }}</el-tag
              >
            </template>

            <div v-else class="select_dept_placeholder">{{ placeholder }}</div>
          </el-card>
        </div>
        <div class="select_bottom">
          <el-scrollbar class="tree_scrollbar">
            <el-input v-model="filterText" clearable placeholder="搜索分类" class="mb-2 w-240px"/>
            <el-tree
              :data="deptTreeData"
              ref="deptTreeRefByDept"
              node-key="id"
              :expand-on-click-node="false"
              :show-checkbox="multiple"
              :check-strictly="multiple"
              @check-change="checkChange"
              :current-node-key="currentSelectDept"
              @current-change="handleCurrentChange"
              highlight-current
              :props="{ label: 'categorizeName' }"
              :filter-node-method="filterNode"
            >
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
    </dialog-form>
  </div>
</template>

<script setup lang="ts">
import DialogForm, {
  DialogFormIns,
} from '@/views/mainview/sceneTasks/components/taskManagement/component/dialogForm.vue';
import { getMicroCategorizeList } from '@/api/application/microGroup.ts'
import { ElMessage, ElTree } from 'element-plus';
import { taskSourceInjectKey } from '@/components/xt-el-form/constant.ts';
import { useFormItem } from 'element-plus'
import { reserve } from '@/piniaStore/banban/util.ts';

const { formItem } = useFormItem()
//配置项 单选多选之类的
interface configType {
  isRadio: boolean;
}
interface selectUserByProps {
  dataType?: 'array' | 'string';
  multiple?: boolean;
  placeholder?: string;
  closable?: boolean;
  disabled?: boolean;
  openLoad?: boolean;
  title?: string;
  config?: configType;
  isRadio?: boolean;
}
const props = withDefaults(defineProps<selectUserByProps>(), {
  dataType: 'array',
  multiple: false,
  placeholder: '请选择分类',
  closable: false,
  openLoad: false,
  disabled: false,
  title: '分类列表',
});
interface CategorizeProps{
  id: string;
  categorizeName: string; // 分类名
}
const isTypeArray = computed(() => {
  return props.dataType === 'array';
});
const store = useStore();
const userMsg = store.state.user.userInfo;

const vModel = defineModel<string[] | string>({ required: false, default: [] });
const deptTreeRefByDept = ref<InstanceType<typeof ElTree>>();
const userFormRef = ref<DialogFormIns>();
const currentSelectDept = ref<string>('');
const checkDept = ref<CategorizeProps[]>([]);
const taskSource = inject(taskSourceInjectKey, null); //任务组织来源
const filterText = ref('')//搜索部门
// const { deptTreeData } = storeToRefs(useDeptTreeStore());
// const { findDeptByIds, initDeptTreeData } = useDeptTreeStore();
//分类列表
const deptTreeData = ref<CategorizeProps[]>([]);
const emitDept = ref<CategorizeProps[]>([]);
const emit = defineEmits<{
  (e: 'change', value: CategorizeProps[]): void;
}>();

// initDeptTreeData(taskSource?.value);

const findDeptByIds = (ids: string | string[]) => {
  if (!_.isArray(ids)) {
    ids = _.split(ids, ',');
  }
  return ids.map(id => reserve<CategorizeProps>(deptTreeData.value, id)).filter(s => s);
};

const categorizeList = defineModel<any[]>('categorizeList', { default: () => [] });

onMounted( async () => {
  await initDeptTreeData(taskSource?.value);
  nextTick(async ()=>{
    emitDept.value = _.cloneDeep(checkDept.value=findDeptByIds(categorizeList.value.map(s=>s.id)).filter(t => t !== null) as CategorizeProps[])
  })
});
watch(()=>[emitDept.value],()=>{
  categorizeList.value = emitDept.value.map((item)=>{
    return {
      id:item.id,
      nickName:item.categorizeName,
    }
  }) as any[]
})

async function initDeptTreeData(tid?: string){
  deptTreeData.value = (await getMicroCategorizeList(tid || userMsg.tenant_id)).data.data || [];
  console.log(33);
};

const openSelect = async (callback?: (value: string | string[]) => void) => {
  props.openLoad && (await initDeptTreeData(taskSource?.value));
  deptTreeRefByDept.value?.setCheckedKeys([], false);
  setTimeout(() => {
    emitDept.value.forEach(s => deptTreeRefByDept.value?.setChecked(s, true, false));
  });
  console.log(deptTreeData,'deptTreeData');
  userFormRef.value?.open(() => {
    const resValue = isTypeArray.value
      ? checkDept.value.map(t => t.id)
      : checkDept.value.map(t => t.id).join(',');
    if (props?.isRadio) {
      if (checkDept.value.length > 1) {
        ElMessage.warning('只能选择一个数据，请重新选择');
        return;
      }
    }
    userFormRef.value?.close(() => {
      emitDept.value = _.cloneDeep(checkDept.value);
      vModel.value = resValue;
      emit('change', emitDept.value);
      _.isFunction(callback) && callback(resValue);
    });
  });
};
const handleCurrentChange = (data: CategorizeProps) => {
  if (!props.multiple) {
    checkDept.value.length = 0;
    checkChange(data, true);
  }
};
// const nodeClickHandle = (data, { checked }) => {
//   deptTreeRefByDept.value?.setChecked(data, !checked, false);
// };

//搜索部门
watch(filterText, (val) => {
  deptTreeRefByDept.value!.filter(val)
})

const filterNode = (value: string, data: CategorizeProps) => {
  if (!value) return true
  return data.categorizeName.includes(value)
}
const checkChange = (s: CategorizeProps, e: boolean) => {
  const idx = checkDept.value.findIndex(t => s.id === t.id);
  if (e) {
    if (idx === -1) {
      checkDept.value.push(s);
    }
  } else {
    checkDept.value.splice(idx, 1);
  }
};
const clearValidate = () => {
  currentSelectDept.value = '';
  checkDept.value.length = 0;
  emitDept.value.length = 0;
  deptTreeRefByDept.value?.setCheckedKeys([], false);
};
// watch(
//   vModel,
//   n => {
//     formItem?.validate('change')
//     if (_.isEmpty(n)) {
//       clearValidate();
//     }
//     @ts-ignore
//     emitDept.value = findDeptByIds(n).filter(t => t !== null);
//   },
//   { immediate: true }
// );
const unCheckDept = (s: CategorizeProps, c: boolean = false) => {
  if (props.multiple) {
    deptTreeRefByDept.value?.setChecked(s, false, false);
  } else {
    checkDept.value.length = 0;
  }
  if (c) {
    emitDept.value = emitDept.value.filter(t => t.id !== s.id);
    vModel.value = isTypeArray.value
      ? emitDept.value.map(t => t.value)
      : emitDept.value.map(t => t.value).join(',');
    emit('change', emitDept.value);
  }
};
export interface selectUserIns {
  clearValidate: () => void;
  modelList: any;
  openSelect: (callback: (value: string | string[]) => void) => void;
}
defineExpose<selectUserIns>({
  clearValidate,
  openSelect,
  modelList: emitDept,
});
</script>

<style scoped lang="scss">
.select_dept {
  width: 100%;
}
.select_wrapper {
  display: flex;
  flex-direction: column;
  .select_bottom {
    height: 250px;
    display: flex;
    & > div {
      flex: 1;
    }
  }
}
.tree_scrollbar {
  padding: 10px;
}
.user_scrollbar {
  padding: 10px;
  width: 100%;
  border-left: 1px solid #eee;
}
.box-card {
  cursor: pointer;
  width: 100%;
  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
.select_dept_placeholder {
  color: #a8abb2;
  font-size: 14px;
}
.disabled {
  background: #f6f6f6;
}
</style>
