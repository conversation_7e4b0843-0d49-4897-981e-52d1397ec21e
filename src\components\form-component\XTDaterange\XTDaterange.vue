<template>
  <div class="flex-1 w-full">
    <el-time-picker
      style="width: 100% !important; box-sizing: border-box"
      v-if="$attrs.type === 'timerange'"
      append-to-body
      v-bind="filteredAttrs"
      v-model="formattedModel"
      is-range
    />
    <el-date-picker
      style="width: 100% !important; box-sizing: border-box"
      v-else
      v-bind="filteredAttrs"
      append-to-body
      v-model="formattedModel"
    />
  </div>
</template>

<script setup lang="ts">
import { computedInject } from '@vueuse/core';
import { formContextKey } from 'element-plus';
import { disableType,DateAllField } from '@/components/form-config/types/AvueTypes'
import { DateFormatTypeEnum } from '@/components/form-config/const/commonDic'

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<{
  disabled?: boolean;
}>();

const disabled = computedInject<disableType>(formContextKey, source => {
  return (source?.disabled ?? false) || props.disabled;
});

const model = defineModel<any>({ default: () => [] });

const formattedModel = computed({
  get() {
    return forwardedAttrs.value.dateFormatType === DateFormatTypeEnum.MINUTESTIME
      ? parseModel(model.value)
      : model.value;
  },
  set(value) {
    model.value =
      forwardedAttrs.value.dateFormatType === DateFormatTypeEnum.MINUTESTIME
        ? formatModel(value)
        : value;
  },
});

const forwardedAttrs = computed(() => {
  const attrs = { ...useAttrs(), disabled: disabled.value } as DateAllField;
  attrs.valueFormat = getValueFormat(attrs.dateFormatType as DateFormatTypeEnum);
  return attrs;
});

// 过滤无效或未定义的属性
const filteredAttrs = computed(() => {
  const { type, ...rest } = forwardedAttrs.value;
  return {
    ...rest,
    type: type as any
  };
});

// 解析 model
function parseModel(value: any[]): any[] {
  if (Array.isArray(value)) {
    return value.map(v => parseValue(v));
  }
  return [];
}

// 格式化 model
function formatModel(value: any[]): any[] {
  if (Array.isArray(value)) {
    return value.map(v => formatValue(v));
  }
  return [];
}

// 解析单个值
function parseValue(value: string): string {
  if (typeof value === 'string') {
    const parts = value.split(' ');
    const timeParts = parts[1].split(':');
    return `${timeParts[0]}:${timeParts[1]}:${timeParts[2]}`;
  }
  return '';
}

// 格式化单个值 时分的格式，区分一下
function formatValue(value: string): string {
  const timeParts = value.split(':');
  return `1900-01-01 ${timeParts[0]}:${timeParts[1]}:00`;
}

// 获取 valueFormat
function getValueFormat(dateFormatType: DateFormatTypeEnum): string {
  switch (dateFormatType) {
    case DateFormatTypeEnum.YEAR:
      return 'YYYY-01-01 00:00:00';
    case DateFormatTypeEnum.MINUTES:
      return 'YYYY-MM-DD HH:mm:00';
    case DateFormatTypeEnum.MINUTESTIME:
      return 'HH:mm';
    default:
      return 'YYYY-MM-DD HH:mm:ss';
  }
}
</script>

<style lang="scss" scoped>
.component-datepicker-enhanced {
  width: 100%;
}
.el-range-editor.el-input__wrapper {
  width: 100%;
}
</style>
