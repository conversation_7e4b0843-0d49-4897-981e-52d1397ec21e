<template>
  <div>
    <selectComponent
      title="选择成员"
      :tab-list="tabList"
      v-model:modelList="modelList"
      v-model:visible="visible"
      :submitApi="submitApi"
      :getTableList="getTableList"
      :dataCallBack="dataCallBack"
      :config="config"
    >
    </selectComponent>
  </div>
</template>

<script setup lang="ts">
import {
  getDeptByOrgTree,
  getUserByDept,
  getGroupByOrgTree,
  getUserByGroup,
  getTenantBindCustomerGroup,
  getCustomerServiceRelUserList
} from '@/api/common/index';
import selectComponent from './selectComponent.vue';
import { useStore } from 'vuex';
import { EnterUserNoPage } from "@/api/zy/enterprise";
import { getTreeForEx } from '@/api/taskManagement/taskDetail';

//配置项 单选多选之类的
interface configType {
  isRadio?: boolean;
  tid?: string;
  // isFilterWork?:boolean; //是否过滤工作组
  workIds?:string[] //分组绑定的工作组集合
}

const store = useStore();
const userMsg = store.state.user.userInfo;


const modelList = defineModel<any[]>('modelList', { default: () => [] });
const visible = defineModel<boolean>('visible', { default: false });


interface DrawerProps {
  submitApi?: (params: any) => Promise<any>; //提交的接口
  getTableList?: () => void; //保存后请求表格数据的接口
  dataCallBack?: (data: any) => any; //处理提交的数据
  config?: configType;
}
const props = defineProps<DrawerProps>();

const tabList = [
  {
    name: 'first',
    label: '部门',
    api: getDeptByOrgTree,
    apiParams: { tid: props.config?.tid ?? userMsg.tid },
    leftProp: { children: 'children', label: 'deptName', value: 'id', leftSearchId: 'deptId' },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: getUserByDept,
  },
  {
    name: 'second',
    label: '工作组',
    api: getGroupByOrgTree,
    apiParams: { tid: props.config?.tid ?? userMsg.tid },
    leftProp: {
      children: 'children',
      label: 'groupName',
      value: 'id',
      leftSearchId: 'workgroupId',
    },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: getUserByGroup,
  },
  {
    name: 'customer',
    label: '客服组',
    api: getTenantBindCustomerGroup,
    apiParams: { tenantId:  userMsg.tenant_id },
    leftProp: {
      children: 'children',
      label: 'groupName',
      value: 'id',
      leftSearchId: 'customGroupId',
    },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: getCustomerServiceRelUserList,
    dataCallBack: (data: any) => {//客服组都是外部人员
      return data.map((item: any) => {
        return {
          ...item,
          innerType: 1
        };
      });
    },
  },
  {
    name: 'enterprise',
    label: '企业协作',
    api: getTreeForEx,
    apiParams: '',
    leftProp: {
      children: 'children',
      label: 'className',
      value: 'id',
      leftSearchId: 'classId',
    },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: EnterUserNoPage,
    dataCallBack: (data: any) => {
      return data.map((item: any) => {
        return {
          ...item,
          nickName: item.name,
          id: item.userId,
          innerType: 1
        };
      });
    },
  },
];
</script>
