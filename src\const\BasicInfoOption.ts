import { AvueColumns } from '../components/form-config/types/AvueTypes.ts';
import { SystemTypeEnum } from '@/components/form-config/types/field.ts';
import { ExtraInfo,basicInfoColumn } from '@/components/form-component/XTBasicInfo/XTBasicInfo.ts'

// 值与报表默认字段
export const defFieldsBySheet = [
  {
    label: '生成时间',
    prop: 'triggerTime',
    type: SystemTypeEnum.TRIGGER_TIME,
  },
  {
    label: '表格标识',
    prop: 'form',
    type: 'form',
  },
];

export const defReplaceFields = [
  {
    label: '公章代码',
    prop: 'companySeal',
    type: 'companySeal',
  },
  // {
  //   label: '法人章代码',
  //   prop: 'legalSeal',
  //   type: 'legalSeal',
  // },
];
export const uploadExtraFields = [
  {
    label: '提交时间（年月日时分秒）',
    prop: 'submitterTime',
    type: SystemTypeEnum.SUBMITTER_TIME,
  },
  {
    label: '触发时间（年月日时分秒）',
    prop: 'triggerTime',
    type: SystemTypeEnum.TRIGGER_TIME,
  },
  {
    label: '提交时间（年月日）',
    prop: 'submitterDate',
    type: SystemTypeEnum.SUBMITTER_DATE,
  },
  {
    label: '触发时间（年月日）',
    prop: 'triggerDate',
    type: SystemTypeEnum.TRIGGER_DATE,
  },
  // {
  //   label: '注册地',
  //   prop: 'registerAddress',
  //   type: 'input',
  // },
  // {
  //   label: '主要经营地址',
  //   prop: 'manageAddress',
  //   type: 'input',
  // },
  // {
  //   label: '法定代表人',
  //   prop: 'legaler',
  //   type: 'input',
  // },
];

// object类型变为数组类型
export const objectToArray = (column: ExtraInfo): AvueColumns[] => {
  return _.toPairs(column).map(([key, value]) => {
    return {
      prop: key,
      ...value,
      copyLabel: value.label,
      span: 12,
    };
  });
};

// 初始化表单设计器字段
export const getSelectInit = () => {
  const result = _.cloneDeep(basicInfoColumn);
  console.log(result,'result',basicInfoColumn);
  
  const transObj = _.toPairs(result).map(([key, value]) => {
    return [
      key,
      {
        copyLabel: value.label,
        display: false,
        visibleBool: true,
      },
    ];
  });
  console.log(_.fromPairs(transObj),'_.fromPairs(transObj)');
  
  return _.fromPairs(transObj);
};
