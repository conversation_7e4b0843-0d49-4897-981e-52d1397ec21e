<template>
  <a-row :gutter="[8,8]">
    <a-col :span="16">
      <el-select-v2
        v-model="fieldId"
        :options="fieldList"
        :props="{
          value:'fieldProp',
          label:'fieldLabel',
        }"
        filterable
        placeholder="请选择字段"
      />
    </a-col>
    <a-col
      v-if="fieldId"
      :span="8"
    >
      <el-select-v2
        v-model="symbol"
        :options="symbolList"
        class="w-full"
        placeholder="请选择规则"
      />
    </a-col>
    <a-col
      v-if="symbol"
      :span="24"
    >
      <component
        :is="getFieldComponent(symbol, field?.fieldType)"
        v-model="result"
        :field="field"
        class="!w-full"
      ></component>
    </a-col>
  </a-row>
</template>

<script
  lang="ts"
  setup
>
import {
  getFieldComponent,
  getFieldSymbolDic,
  MethodSymbolEnum
} from '@/views/mainview/sceneTasks/detail/option/field.ts';
import { Field } from '@/types/OriginTypes.ts';

const props = defineProps<{
  fieldList: Field[];
}>();

const fieldId = defineModel<string>('fieldId');
const symbol = defineModel<MethodSymbolEnum>('symbol');
const result = defineModel<any>('result');

const field = computed(() => props.fieldList.find(
  item => item.fieldProp === fieldId.value
));

const symbolList = computed(() => getFieldSymbolDic(field.value?.fieldType));

onMounted(() => {
  watch(
    fieldId,
    () => {
      symbol.value = undefined;
    }
  );

  watch(
    symbol,
    () => {
      result.value = undefined;
    }
  );
});
</script>

<style
  lang="scss"
  scoped
></style>
