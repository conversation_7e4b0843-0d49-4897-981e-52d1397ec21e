<template>
    <config-title :data="data" />
    <config-permission :data="data" />
    <config-span :data="data" />
  </template>
  
  <script setup lang="ts">
  import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
  import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
  import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
  import { getUserCodeAllType} from './type';

  
  defineProps<{
    data: getUserCodeAllType;
  }>();
  </script>