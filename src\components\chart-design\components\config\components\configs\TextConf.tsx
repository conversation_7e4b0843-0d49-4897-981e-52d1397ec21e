import useChartTypeStyleAndStore from '../../../../hooks/useChartTypeStyleAndStore.ts';
import { TextStyle } from '../../../../types';


export default [
  {
    header: '指标图样式',
    render: defineComponent(() => {
      const { chartStyle } = useChartTypeStyleAndStore<TextStyle>();

      // const textSize = computed({
      //   get: () => chartStyle.value.textSize ?? TextSizeEnum.LARGE,
      //   set: (val) => {
      //     chartStyle.value.textSize = val;
      //   }
      // });

      return () => (
        <div>
          {/*<a-form-item label="尺寸">*/}
          {/*  <ImageSelect*/}
          {/*    v-model={textSize.value}*/}
          {/*    dic={TextSizeDic}*/}
          {/*  />*/}
          {/*</a-form-item>*/}
          <el-form-item label="数值颜色">
            <el-color-picker
              v-model={chartStyle.value.textColor}
            />
          </el-form-item>
        </div>
      );
    })
  }
];