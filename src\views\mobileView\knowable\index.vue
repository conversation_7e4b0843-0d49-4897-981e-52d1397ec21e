<template>
  <!--  <a-flex class="h-full" vertical justify="center" align="center">-->
  <!--    <img src="/img/mobile/know_def.png" class="w-100% mb-20px" alt="">-->
  <!--    暂未开放-->
  <!--  </a-flex>-->
  <m-scroll noScroll tabbar-h="150">
    <template #top>
      <div>
        <van-floating-bubble
          axis="xy"
          class="refresh-btn"
          v-model:offset="offsetRefresh"
          @click="refreshHandle"
          magnetic="x"
        >
          <a-flex align="center" justify="center" gap="5" >
            <el-icon><RefreshRight /></el-icon>
            <div>刷新</div>
          </a-flex>
        </van-floating-bubble>
        <van-tabs v-model:active="active" @change="changeTabs">
          <van-tab v-for="item in tabsTreePath" :title="item.name"></van-tab>
        </van-tabs>
      </div>
    </template>
    <template #body>
      <router-view :key="$route.fullPath"></router-view>
    </template>
  </m-scroll>
</template>

<script setup>
import MScroll from '@/views/mobileView/components/mScroll.vue';
import { ref } from 'vue';

const active = ref(0);

const tabsTreePath = [
  {
    name: '履职看板',
    path: '/m/knowable/duty',
  },
  {
    name: '任务周知',
    path:"/m/knowable/notice",
  },
  {
    name: '消息通知',
    path:"/m/knowable/message",
  },
  {
    name: '信息查询',
    path:"/m/knowable/info",
  }
];


const checkbox = ref([]);

const router=useRouter()

const route=useRoute()

const data = ref([]);

const offsetRefresh=ref({
  x:274,
  y:400,
})
//刷新
const refreshHandle=()=>{
  window.location.reload()
}

onBeforeMount(()=>{
  active.value=tabsTreePath.findIndex(s=>s.path===route.path)
})

watch(()=>route.path,(v)=>{
  active.value=tabsTreePath.findIndex(s=>s.path===v)
})

const changeTabs=(w)=>{
  const current=tabsTreePath.at(w)
  router.push({
    path:current.path
  })
}

</script>
<style>
.refresh-btn.van-floating-bubble{
  border-radius: 4px;
  background: #E5EDFC;
  color: #024FE5;
  box-shadow: 0px 2px 12px 0px rgba(100,101,102,0.12);
  height: 35px;
  font-size: 14px;
  width: 77px;
}
</style>
<style scoped>

</style>