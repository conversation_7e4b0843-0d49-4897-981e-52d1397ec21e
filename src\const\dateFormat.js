import dayjs from 'dayjs';

export const fullFormat = 'YYYY-MM-DD HH:mm:ss';
export const dayFormat = 'YYYY-MM-DD';

export const yearFormat = 'YYYY';

export const monthFormat = 'YYYY-MM';

export const formatDate = (value, format) => {
  if (typeof value === 'string') {
    const result = dayjs(value).format(format);
    return result === 'Invalid Date' ? '' : result;
  }
  return _.join(
    value?.map(item => {
      const result = dayjs(item).format(format);
      return result === 'Invalid Date' ? '' : result;
    }) || [],
    ' 至 '
  );
};

export const formatFullDate = date => {
  return dayjs(date).format(fullFormat);
};

export const formatToDate = format => dayjs(format).toDate();
