<template>
  <el-button class="w-full" size="large" @click="visible = true">分组汇总</el-button>

  <el-dialog
    title="分组汇总"
    append-to-body
    :destroy-on-close="true" 
    v-model="visible"
    width="1000px"
    :close-on-click-modal="false"
  >
    <el-scrollbar height="600px">
      <p class="color-#f44">注意:系统会按照添加的分组字段顺序逐级分组</p>
      <div class="w-520px ml-auto mr-auto">
        <el-form-item label="选择数据来源" label-position="left" label-width="120">
            <el-select v-model="linkData.path" placeholder="请选择数据来源" class="w-400px" @change="handleChange" clearable>
              <el-option
                v-for="item in dynamicFields"
                :key="item.prop"
                :label="item.copyLabel"
                :value="item.prop"
                :disabled="item.prop?.includes(data?.prop)"
              />
            </el-select>
        </el-form-item>
      </div>
      <addGroup
        v-model:group="linkData.group"
        title="分组"
        :pathFieldList="(pathFieldList as DynamicAllField[])"
        :currentFieldList="data.children"
        :typeList="groupType"
      >
      </addGroup>
      <addGroup
        v-model:group="linkData.summary"
        title="汇总"
        :pathFieldList="(pathFieldList as DynamicAllField[])"
        :currentFieldList="summaryCurrentField"
        :typeList="summaryType"
      >
      </addGroup>
      
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { groupSummaryType, gruopSummaryAllField } from './type'
import { AvueColumns,DynamicAllField } from '@/components/form-config/types/AvueTypes';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { storeToRefs } from 'pinia';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import addGroup from './components/addGroup.vue';
import { numberType } from '@/components/form-config/types/FieldTypes.ts';
import { ElMessage } from 'element-plus'
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';

const { taskBase } = storeToRefs(useTaskDetailStore());
const baseData = {
  path: '',
  group: [],
  summary: []
}
const linkData = ref<groupSummaryType>({...baseData});
const dynamicFields = (taskBase.value as TaskBase).column?.filter((v:AvueColumns) => v.fixType === FixTypeEnum.DYNAMIC) as DynamicAllField[];

//数据源的子组件
const pathFieldList = computed(() => {
  return dynamicFields.find(v => v.prop === linkData.value.path)?.children || []
})

//汇总绑定字段
const summaryCurrentField = computed(() => {
  return (props.data as DynamicAllField).children.filter(v => _.includes(numberType, v.fixType)) || []
})


const props = defineProps<{
    data: gruopSummaryAllField
}>();



const groupType = [
  {
    label: '值相同为一组',
    value: '1'
  }
]
const summaryType = [
  {
    label: '求和',
    value: '1'
  },
  {
    label: '平均值',
    value: '2'
  },
  {
    label: '最大值',
    value: '3'
  },
  {
    label: '最小值',
    value: '4'
  },
  {
    label: '计数',
    value: '5'
  }
]

const visible = ref(false)
const handleChange = () => {
  linkData.value.group = []
  linkData.value.summary = []
}

const handleSubmit = () => {
  const haveEmpty = !linkData.value.group?.length || linkData.value.group?.some(i => {
    return !i.pathField || !i.field || !i.method
  })
  const haveEmpty1 = !linkData.value.summary?.length || linkData.value.summary?.some(i => {
    return !i.pathField || !i.field || !i.method
  })
  if(haveEmpty || haveEmpty1 || !linkData.value.path){
    ElMessage.warning('数据来源、分组、汇总不能为空，请填写完整')
    return
  }
  props.data.groupSummary = _.cloneDeep(linkData.value)
  visible.value = false
}

watch(
    () => visible.value,
    (val) => {
        if(val) {
           linkData.value = _.cloneDeep(props.data.groupSummary || {...baseData})
        }
    },
    { immediate: true }
)

</script>