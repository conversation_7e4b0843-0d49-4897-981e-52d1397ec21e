<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 44</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-910.000000, -955.000000)">
            <g id="编组-44" transform="translate(910.000000, 955.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-3" transform="translate(10.000000, 42.000000)" font-family="PingFangSC-Regular, PingFang SC"
                   font-size="14" font-weight="normal" line-spacing="23">
                    <text id="RAND函数可返回大于等于0且小于1的均">
                        <tspan x="0" y="15" fill="#3F70FF">RAND</tspan>
                        <tspan x="38.612" y="15" fill="#3A3A3A">函数可返回大于等于0且小于1的均匀分布</tspan>
                        <tspan x="0" y="38" fill="#3A3A3A">随机实数。</tspan>
                        <tspan x="0" y="62" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="62" fill="#3F70FF">RAND</tspan>
                        <tspan x="87.612" y="62" fill="#3A3A3A">()</tspan>
                        <tspan x="0" y="86" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="86" fill="#3F70FF">RAND</tspan>
                        <tspan x="87.612" y="86" fill="#3A3A3A">()返回0.689429476</tspan>
                    </text>
                </g>
                <text id="RAND" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">RAND</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
