import request from '@/axios';
import { Base64 } from 'js-base64';

const secretKey = btoa('NM1ooTauUJLSUFAQYaFb6Rep2cZ2uG')
export const getList = (current, size, params) => {
  return request({
    url: '/blade-resource/oss/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = id => {
  return request({
    url: '/blade-resource/oss/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

export const remove = ids => {
  return request({
    url: '/blade-resource/oss/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const add = row => {
  return request({
    url: '/blade-resource/oss/submit',
    method: 'post',
    data: row,
  });
};

export const update = row => {
  return request({
    url: '/blade-resource/oss/submit',
    method: 'post',
    data: row,
  });
};

export const enable = id => {
  return request({
    url: '/blade-resource/oss/enable',
    method: 'post',
    params: {
      id,
    },
  });
};
export const putFile = file => {
  return request({
    url: '/blade-resource/oss/endpoint/put-file',
    method: 'post',
    data: file,
  });
};
export const putNoAuthFile = file => {
  return request({
    url: '/blade-resource/oss/endpoint/put-file',
    method: 'post',
    headers:{
      "Authorization":"",
      "Blade-Auth":"",
    },
    data: file,
  });
};
//获取动态码
export const getDynamicCode = () => {
  return request({
    url: '/blade-resource/oss/endpoint/get-dynamic-code',
    method: 'get',
    params:{
      secretKey
    }
  });
};
//动态码上传文件
export const putFileByCode = (dynamicCode,file) => {
  const formData = new FormData();
  if (!file) return Promise.reject('上传失败')
  formData.append(
    'file',
    new File([Base64.toUint8Array(file.replace('data:image/png;base64,', ''))], 'test.png')
  );
  formData.append(
    'dynamicCode',
    dynamicCode
  );
  formData.append(
    'secretKey',
    secretKey
  );
  return request({
    url: '/blade-resource/oss/endpoint/put-file-plus',
    method: 'post',
    data:formData
  });
};

