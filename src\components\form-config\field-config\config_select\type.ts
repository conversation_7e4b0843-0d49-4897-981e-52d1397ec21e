import {
  AllDefaultValueType,
  FieldBaseField,
  TitleField,
  basePermissionType,
  RemarkField,
  SpecificType
} from '@/components/form-config/types/AvueTypes';
import { SelectTypeEnum } from './const';

export type TransToXtFieldType<T extends readonly string[]> = T[number];

/* 选择 */
export type SelectDic = {
  label: string;
  key: string;
  children?: SelectDic[];
  disabled?: boolean;
};

export type SelectField = basePermissionType & {
  // type: SelectColumnTypeEnum;
  filterable?: boolean;
  multiple?: boolean;
  props: any;
  dicData: SelectDic[];
  selectType: SelectTypeEnum;
  expandTrigger?: 'click' | 'hover';
  dicRules?: SelectRules[];
};

export type SelectRules = {
  dicValue: (string | string[])[];
  displayField: string[];
};

export type SelectAllField = FieldBaseField &
  SelectField &
  TitleField &
  AllDefaultValueType &
  
  RemarkField &
  SpecificType;
