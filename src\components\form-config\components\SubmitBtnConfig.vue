<template>
  <div class="submit_btn_config px-4 py-[10px]">
    <div class="submit_btn_config__title">按钮名称</div>
    <div>
      <el-input placeholder="请输入按钮名称" v-model="modelValue"  clearable></el-input>
    </div>
  </div>
</template>

<script setup lang="ts">
const modelValue = defineModel<string>({
  default: () => '提交',
});
</script>

<style scoped lang="scss">
.submit_btn_config {
  height: 113px;
  background: #ffffff;

  display: flex;
  flex-direction: column;
  justify-content: center;

  &__title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 23px;
    margin-bottom: 10px;
  }
}
</style>
