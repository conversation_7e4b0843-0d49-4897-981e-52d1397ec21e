import { onClickOutside } from '@vueuse/core';

function contextHandle(e, data) {
  e.preventDefault();
  const { x, y } = e;
  const sDom = document.getElementById(data.id);
  //设置menu位置
  sDom.style.position = 'absolute';
  sDom.style.display = 'block';
  sDom.style.left = x + 'px';
  sDom.style.top = y + 'px';
  //触发打开菜单event事件
  if (data.event) data.event(data.value);
  //点击菜单外部关闭菜单
  onClickOutside(sDom, event => {
    removeContextMenu(sDom);
  });
}

function removeContextMenu(dm) {
  dm.style.display = 'none';
}

/**
 * 右键菜单指令
 * 使用方法：绑定v-nbcontextmenu={id:菜单节点id，event：节点被点击，value:event附带数据}
 * 使用前请务必将菜单项目使用teleport包裹
 * @param app
 */

export const vnbcontextmenu = {
  // 指令的定义
  mounted(el, bind, vNode) {
    removeContextMenu(document.getElementById(bind.value.id));
    if (bind.value.showMenu === false) {
      return;
    }
    el.addEventListener('contextmenu', e => contextHandle(e, bind.value));
  },
};


