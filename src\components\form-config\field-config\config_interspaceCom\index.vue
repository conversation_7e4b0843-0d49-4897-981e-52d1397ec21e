<template>
  <div>
    <config-title :data="data"></config-title>
    <config-permission :data="data" />
    <config-default-type :data="data" />
    <config-span :data="data"></config-span>
  </div>
</template>

<script setup lang="ts">
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import { InterspaceAllType } from './type';

defineProps<{
  data: InterspaceAllType;
}>();
</script>

<style scoped lang="scss"></style>
