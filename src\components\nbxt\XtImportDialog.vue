<template>
  <div>
    <slot></slot>

    <el-dialog
      title="批量导入"
      v-model="vModel"
      append-to-body
      class="selectImportDialog"
      width="1200px"
      :close-on-click-modal="false"
      @open="initDialog"
      destroy-on-close
    >
      <div class="flex flex-col items-center">
        <n-steps
          :current="current"
          :status="topStatus === 2 ? 'error' : 'process'"
          class="ml-300px mt-20px mb-38px"
        >
          <n-step title="上传Excel文件" />
          <n-step title="校验数据" />
          <n-step title="完成" />
        </n-steps>
        <slot name="tips"></slot>
        <div class="selectImportDialog__container flex flex-col">
          <div class="flex h-50px items-center">
            <div class="color-#7E8791 text-14px">请上传Excel文件</div>
            <el-button link @click="$emit('tempDown')" type="primary">模板下载</el-button>
          </div>
          <div class="flex-1">
            <el-upload
              v-show="!fileStatus.name"
              :http-request="handleRequest"
              class="h-full importUpload"
              drag
              :action="action"
              accept=".xlsx,.xls"
              :headers="headers"
              :show-file-list="false"
              :on-change="v => $emit('change', v)"
              :before-upload="beforeUpload"
              :on-success="handleSuccess"
              :on-error="handleError"
            >
              <div class="h-full bg-white flex flex-col justify-center items-center">
                <upload-icon class="mb-20px"></upload-icon>
                <n-text style="font-size: 14px">
                  <span class="color-#0052D9">点击上传</span>
                  <span class="mx-10px">/</span>
                  <span class="grayText">拖拽到此区域</span>
                </n-text>
              </div>
            </el-upload>
            <div v-if="fileStatus.name" class="haveFile">
              <div class="haveFile_top">
                <el-progress
                  v-if="topStatus === 0 || isIng"
                  type="circle"
                  :percentage="percent"
                  :width="100"
                />
                <template v-else-if="topStatus === 1">
                  <SuccessIcon v-if="!isChecked" />
                  <CheckSuccess v-else />
                  <div class="topText" :class="{ 'mt-20px': isChecked || isCheckFail }">
                    {{ fileStatus.status.topText }}
                  </div>
                  <div class="topDescription" v-if="isChecked">
                    {{ `校验完成，预计更新${fileStatus.status.num || 0}条数据` }}
                    <slot name="error"></slot>
                  </div>
                </template>
                <template v-else-if="topStatus === 2">
                  <FailIcon />
                  <div class="topText">{{ fileStatus.status.topText }}</div>
                  <div class="topDescription" v-if="isCheckFail">
                    {{ `校验失败，${fileStatus.status.reason}` }}
                  </div>
                </template>
              </div>
              <div class="haveFile_bottom" v-if="!isChecked">
                <div class="flex items-center">
                  <ExcelIcon />
                  <span class="text-14px color-#364A64 ml-10px">{{ fileStatus.name }}</span>
                </div>
                <div v-if="topStatus !== 2">
                  {{ fileStatus.status.text }}
                </div>
                <template v-else>
                  <el-button link type="primary" @click="initDialog"
                    >{{ fileStatus.status.text }}
                  </el-button>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="mt-30px pb-45px flex justify-center">
        <el-button type="primary" @click="toCheck" v-if="fileStatus.status.type === 'success'"
          >下一步
        </el-button>
        <el-button type="primary" @click="$emit('submit')" v-else-if="isChecked" :loading="loading"> 确定</el-button>
        <el-button color="#E7E7E7" @click="$emit('cancel')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { TransitionPresets, useTransition, useVModel } from '@vueuse/core';
import UploadIcon from '@/images/uploadIcon.svg';
import ExcelIcon from '@/images/excelIcon.svg';
import SuccessIcon from '@/images/success.svg';
import FailIcon from '@/images/fail.svg';
import CheckSuccess from '@/images/checkSuccess.svg';
import useAuthHeader from '@/hooks/useAuthHeader';
import { computed } from 'vue';
import { ajaxUpload } from 'element-plus/es/components/upload/src/ajax';

const props = defineProps({
  // modelValue: {},
  action: {
    type: String,
    default: '/api/blade-resource/oss/endpoint/put-file',
  },
  uploadServer: {
    type: Boolean,
    default: true,
  },
  onSuccess: {
    type: Function,
    default: () => {},
  },
  onError: {
    type: Function,
    default: () => {},
  },
  check: {
    //需要一个返回promise的函数,返回值带有数字用于显示
    type: Function,
    default: () => Promise.resolve(0),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(['cancel', 'tempDown', 'change', 'submit']);

const vModel = defineModel()

const current = ref(1);

const { headers } = useAuthHeader();

const status = {
  ing: {
    type: 'ing',
    text: '上传中...',
  },
  success: {
    type: 'success',
    text: '上传完成',
    topText: '上传成功',
  },
  fail: {
    type: 'fail',
    text: '重新上传',
    topText: '上传失败',
  },
  checking: {
    type: 'checking',
    text: '校验中...',
  },
  checkSuccess: {
    type: 'check-success',
    topText: '校验通过',
  },
  checkFail: {
    type: 'check-fail',
    text: '重新上传',
    topText: '校验失败',
  },
};

const fileStatus = ref({
  name: '',
  status: status.ing, //ing上传中 success上传成功 fail上传失败
});

const topStatus = computed(() => {
  const type = fileStatus.value.status.type;
  if (_.endsWith(type, 'ing')) {
    return 0;
  } else if (_.endsWith(type, 'success')) {
    return 1;
  } else {
    return 2;
  }
});

const isChecked = computed(() => fileStatus.value.status.type === 'check-success');
const isCheckFail = computed(() => fileStatus.value.status.type === 'check-fail');

const initDialog = () => {
  baseNumber.value = 0;
  baseNumber2.value = 0;
  current.value = 1;
  fileStatus.value = {
    name: '',
    status: status.ing,
  };
};

//处理进度条动画
const baseNumber = ref(0);
const isIng = ref(true);
const percentage = useTransition(baseNumber, {
  duration: 1000,
  transition: TransitionPresets.easeOutExpo,
  onStarted: () => {
    isIng.value = true;
  },
  onFinished: () => {
    isIng.value = false;
  },
});

const baseNumber2 = ref(0);
const percentage2 = useTransition(baseNumber2, {
  duration: 1000,
  transition: TransitionPresets.easeOutExpo,
  onStarted: () => {
    isIng.value = true;
  },
  onFinished: () => {
    isIng.value = false;
  },
});

const percent = computed(() => {
  return fileStatus.value.status.type === 'ing'
    ? Math.floor(percentage.value)
    : Math.floor(percentage2.value);
});

//自定义上传
const handleRequest = options => {
  if (props.uploadServer) {
    return ajaxUpload(options);
  } else {
    return Promise.resolve();
  }
};

//上传前更改状态
const beforeUpload = rawFile => {
  fileStatus.value.name = rawFile.name;
  fileStatus.value.status = status.ing;
  baseNumber.value = 99;
  return true;
};

//上传成功
const handleSuccess = (response, uploadFile, uploadFiles) => {
  afterTrans(() => (fileStatus.value.status = status.success));
  props.onSuccess(response, uploadFile, uploadFiles);
};

//上传失败
const handleError = (error, uploadFile, uploadFiles) => {
  afterTrans(() => (fileStatus.value.status = status.fail));
  props.onError(error, uploadFile, uploadFiles);
};

//进行校验
const toCheck = () => {
  baseNumber2.value = 99;
  current.value++;
  fileStatus.value.status = status.checking;
  props
    .check()
    .then(num => {
      current.value++;
      afterTrans(() => {
        fileStatus.value.status = status.checkSuccess;
        fileStatus.value.status.num = _.parseInt(num);
      });
    })
    .catch(reason => {
      console.log(reason);
      afterTrans(() => {
        fileStatus.value.status = status.checkFail;
        fileStatus.value.status.reason = _.toString(reason);
      });
    });
};

const afterTrans = callback => {
  const interval = setInterval(() => {
    if (!isIng.value) {
      callback();
      clearInterval(interval);
    }
  });
};
</script>

<style lang="scss">
.selectImportDialog {
  * {
    box-sizing: border-box;
  }

  .el-dialog__header {
    padding: 0 20px;
    border-bottom: 1px solid #e9e9e9;
    height: 74px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .el-dialog__body {
    padding: 0 30px;
  }

  &__container {
    width: 550px;
    height: 336px;
    background: #f8fbfe;
    border-radius: 4px;
    border: 1px solid #f4f4f4;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .grayText {
      color: rgb(0, 0, 0, 0.6);
    }
  }

  .importUpload {
    .el-upload,
    .el-upload-dragger {
      padding: 0;
      height: 100%;
    }
  }

  .haveFile {
    display: flex;
    flex-direction: column;
    height: 100%;

    &_top {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #edf2fa;

      .topText {
        margin-top: 30px;
        font-size: 18px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 32px;
      }

      .topDescription {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.43);
        line-height: 22px;
      }
    }

    &_bottom {
      background: white;
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 20px;
      padding-right: 30px;
    }
  }
}
</style>