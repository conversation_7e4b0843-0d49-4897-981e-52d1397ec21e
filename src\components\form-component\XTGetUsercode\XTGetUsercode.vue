<template>
  <div class="user w-full p-2">
    <el-tag>{{ userName }}</el-tag>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex';
import { getUserInfo } from '@/api/banban/Examine';
import { tenantIdInjectKey } from '@/components/xt-el-form/constant.ts';

const tenantId = inject(tenantIdInjectKey);

const store = useStore();
const userMsg = store.state.user.userInfo;

const model = defineModel<string>({ default: () => '' });

const userName = ref('');

onMounted(async () => {
  if (!model.value) {
    model.value = userMsg.user_id;
    userName.value = userMsg?.detail.nick_name || userMsg.user_name;
    return
  }
  const res = await getUserInfo({ tenantId: tenantId?.value as string || userMsg?.tenant_id, userId: model.value });
  userName.value = res.data.data.nickName;
});
</script>

<style scoped lang="scss">
.user {
  border: 1px solid #eee;
  color: #666;
}
</style>
