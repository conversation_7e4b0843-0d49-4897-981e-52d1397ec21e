<template>
  <el-scrollbar class="tree_scrollbar">
    <el-input v-model="filterText" clearable placeholder="搜索" class="mb-2 w-240px"/>
    <el-tree
      :data="treeData"
      ref="treeRef"
      :node-key="defaultProps.id"
      :expand-on-click-node="false"
      :show-checkbox="true"
      :check-strictly="strictly"
      @check="handleCheckChange"
      highlight-current
      :props="defaultProps"
      :filter-node-method="filterNode"
      :render-after-expand="false"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node flex items-center">
          <span>{{data[defaultProps.label ?? 'deptName']}}</span>
          <el-button v-if="data.children?.length" type="primary" link class="ml-2" @click="chooseChildren(data)">
            包含子集
          </el-button>
        </div>
      </template>
    </el-tree>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { ElTree } from 'element-plus';

interface propType{
  id: string,
  label:string
}
const props = withDefaults(defineProps<{
  defaultProps: propType,
  treeData: any[],
  strictly?: boolean,
}>(), {
  strictly: false
  
});
const filterText = ref('')//搜索
const modelList = defineModel<any[]>({default: () => []})
const treeRef = ref<InstanceType<typeof ElTree>>();
const emit = defineEmits(['change']);

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data[props.defaultProps.label].includes(value)
}

//搜索工作组
watch(filterText, (val) => {
  treeRef.value!.filter(val)
})


const handleCheckChange = (
  _: any,
  { checkedNodes }: { checkedNodes: any[] },
) => {
  console.log(checkedNodes,'checkedNodes');
  modelList.value= checkedNodes
  
};

//复选框的勾选状态只能用setCheckedKeys更新
watch(
  modelList, 
  (val) => {
    if(!val) return
    nextTick(()=>{
      treeRef.value?.setCheckedKeys(val.map(v=>v.id), true)
    })
  },
  { deep:true, immediate:true}
)

function chooseChildren(data:any){
  function flattenTreeNodes(nodes: any[], result: any[] = []): any[] {
    for (const node of nodes) {
      // 将当前节点加入结果数组
      if(!node.disabled) result.push(node);
      // 如果有子节点，递归处理子节点
      if (node.children && node.children.length > 0) {
        flattenTreeNodes(node.children, result);
      }
    }
    return result;
  }
  const allNodes = flattenTreeNodes([data]);
  treeRef.value!.setCheckedNodes(allNodes);
  const idsMap = new Map<string, boolean>();
  modelList.value.forEach(i => idsMap.set(i.id, true))
  modelList.value.push(...allNodes.filter(i => !idsMap.has(i.id)));
}


</script>