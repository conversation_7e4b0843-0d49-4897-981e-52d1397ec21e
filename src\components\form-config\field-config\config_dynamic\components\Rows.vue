<template>
    <el-form-item label="行数范围" class="mb-20">
        <avue-select v-model="data.rowsType" :dic="rowsTypeDic" class="mb-10px" :clearable="true"/>
        <div class="flex" v-if="data.rowsType === RowsTypeEnum.CUSTOM">
            <el-input-number 
                v-model="(data.rowsMin as number)" 
                :min="1" 
                :max="100" 
                placeholder="最小值" 
                @change="handleChange"/> 
            <span class="ml-2 mr-2">~</span>
            <el-input-number 
                v-model="(data.rowsMax as number)" 
                :min="1" 
                :max="100" 
                placeholder="最大值" 
                @change="handleChange"/>
        </div>
        <div class="flex" v-if="data.rowsType === RowsTypeEnum.TRENDS">
            <el-select v-model="data.rowsNumbeFieldMin" class="!w-150px" clearable>
                <el-option 
                    v-for="item in numberDic" 
                    :key="item.prop" 
                    :label="item.copyLabel"
                    :value="item.prop" />
            </el-select> 
            <span class="ml-1 mr-1">~</span>
            <el-select v-model="data.rowsNumbeFieldMax" class="!w-150px" clearable>
                <el-option 
                    v-for="item in numberDic" 
                    :key="item.prop" 
                    :label="item.copyLabel" 
                    :value="item.prop" 
                    />
            </el-select>
        </div>
    </el-form-item>
</template>

<script setup lang="ts">
import { RowsTypeEnum, rowsTypeDic } from '@/components/form-config/const/commonDic';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';
import { DynamicAllField } from '../type';

const { taskBase } = storeToRefs(useTaskDetailStore());

const props = defineProps<{
  data: DynamicAllField;
}>();

//数字类型
const numberDic = computed(() => {
  let column = (taskBase.value as TaskBase).column || [];
  return column.filter(item => item.fixType === 'number');
});

const handleChange = () => {
  if (!Number.isInteger( props.data.rowsMin)) {
    props.data.rowsMin = Math.round(props.data.rowsMin as number);
  }
  if (!Number.isInteger( props.data.rowsMax)) {
    props.data.rowsMax = Math.round(props.data.rowsMax as number);
  }
  if(Number(props.data.rowsMax) < Number(props.data.rowsMin)){
    props.data.rowsMax = ''
    ElMessage.warning('最大值不能小于最小值');
  }
};

</script>

<style scoped lang="scss"></style>