// 数组取出里面的Json串拼成新的数组
export const handleJsonByArray = (list: any) => {
  return list.map((item: any) => {
    return {
      ...item.content,
      dataLinks: item.dataLinks,
      query: item.query,
      formula: item.formula,
      fieldType: item.fieldType,
      contentType: item.contentType,
      children: item.children?.map((child: any) => ({
        ...child.content,
        fieldType: child.content.fixType,
        contentType: child.content.type,
      })),
    };
  });
};