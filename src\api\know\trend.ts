import request from '../../axios';


export const getComplete = (data: any) => {
    return request({
        url: '/trendAnalysis/doneCountChart',
        method: 'post',
        data
    });
};
export const getTarget= (data: any) => {
    return request({
        url: '/trendAnalysis/metricsChart',
        method: 'post',
        data
    });
};
export const getLastTarget= (data: any) => {
    return request({
        url: '/trendAnalysis/sameYearMetricsChart',
        method: 'post',
        data
    });
};
// 
export const getDatas= (data: any) => {
    return request({
        url: '/staging/getScreenData',
        method: 'post',
        params:{
            ...data
        }
    });
};
export const getScreenDataList = (data: any) => {
    return request({
        url: '/staging/getScreenDataList',
        method: 'post',
        params:{
            ...data
        }
    });
};
