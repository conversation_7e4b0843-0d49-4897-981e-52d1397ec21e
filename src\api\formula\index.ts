import request from '@/axios';

export enum FormulaType {
  // 文本数字里面的公式编辑、规则速递值与值、数据表自定义
  DATA = 'DATA',
  // 触发里面依数值发起、逐一触发编辑条件
  TRIGGER = 'TRIGGER',
  // 触发里面依统计发起、公式编辑组件
  CAL = 'CAL',
  // 计算表选择框
  CAL_SELECT='CAL_SELECT'
}

export const getFormulaList = (type: FormulaType) => {
  return request({
    url: '/formula/list',
    method: 'get',
    params: { type },
  });
};
export const getFormulaDic = (type: FormulaType) => {
  return request({
    url: '/formula/dic',
    method: 'get',
    params: { type },
  });
};
