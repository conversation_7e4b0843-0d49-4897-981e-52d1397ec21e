<template>
  <Codemirror
    ref="mirror"
    v-model:value="v"
    :options="cmOptions"
    class="formula-mirror"
    @change="handleChange"
    @focus="cm?.refresh()"
    @ready="handleReady"
  />
</template>

<script
  lang="ts"
  setup
>
import Codemirror from 'codemirror-editor-vue3';
import 'codemirror/addon/display/placeholder.js';
import 'codemirror/addon/search/searchcursor.js';
import 'codemirror/addon/display/autorefresh';
import { Mark, ValueType } from './types.ts';
import { Editor, EditorConfiguration, Position } from 'codemirror';

const props = withDefaults(
  defineProps<{
    markRender: (mark: Mark) => HTMLElement;
    templateKey?: string;
    marksKey?: string;
    options?: EditorConfiguration;
  }>(),
  {
    templateKey: () => 'template',
    marksKey: () => 'marks',
    options: () => {
      return {};
    }
  }
);

type vModelType = ValueType<typeof props.templateKey, typeof props.marksKey>;

const vModel = defineModel<vModelType>({ default: () => ({}) });

const emits = defineEmits(['change']);
const handleChange = (instance: string) => {
  emits('change', instance);
};

const v = computed({
  get: () => _.unescape(vModel.value?.[props.templateKey] || ''),
  set: v => {
    nextTick(() => {
      vModel.value = {
        [props.templateKey]: v,
        [props.marksKey]: getAllMarks()!
      } as vModelType;
    });
  }
});
//获得所有mark
const getAllMarks = (): Mark[] | undefined => {
  return cm.value
    ?.getDoc()
    ?.getAllMarks()
    .filter(item => !item.className?.startsWith('CodeMirror'))
    .map(item => {
      return {
        ...item.find(),
        attributes: item.attributes
      } as Mark;
    });
};

const cmOptions: EditorConfiguration = {
  lineNumbers: false,
  autoRefresh: true,
  // line: true,
  ...props.options
};

const cm = shallowRef<Editor>();

const handleReady = (ready: Editor) => {
  cm.value = ready;

  nextTick(() => {
    //初始化mark
    const valueElement = vModel.value?.[props.marksKey];
    if (_.isArray(valueElement)) {
      valueElement?.forEach(replaceMark);
    }
  });
};

//获得当前光标位置
const getCursor: () => Position | undefined = () => {
  return cm.value?.getCursor();
};

//往光标处插入文本
const insertText = (text?: string, bracket = false): Pick<Mark, 'from' | 'to'> | undefined => {
  const startPos = getCursor();
  if (text && startPos) {
    cm.value?.getDoc().replaceRange(text, startPos);
    nextTick(() => {
      if (bracket) {
        setCursor({
          ch: startPos.ch + text.length - 1,
          line: startPos.line
        });
      }
    });
    return {
      from: {
        ch: startPos.ch,
        line: startPos.line
      },
      to: {
        ch: startPos.ch + text.length,
        line: startPos.line
      }
    };
  }
};

//设置光标
const setCursor = (pos: Position) => {
  cm.value?.getDoc().setCursor(pos);
};
const clearValue = () => {
  cm.value?.setValue('');
};

//替换为mark
const replaceMark = (mark: Mark) => {
  const { from, to, attributes } = mark;

  cm.value?.getDoc().markText(from, to, {
    atomic: true,
    replacedWith: props.markRender(mark),
    attributes,
    handleMouseEvents: true
  });
};

const insertMark = (text: string, mark: Pick<Mark, 'attributes'>) => {
  const pos = insertText(text)!;
  replaceMark({
    ...pos,
    ...mark
  });
};

//聚焦
const focus = () => {
  cm.value?.focus();
};

defineExpose({
  focus,
  insertText,
  replaceMark,
  insertMark,
  clearValue
});
</script>

<style lang="scss">
.formula-mirror {
  .CodeMirror {
    box-sizing: border-box;
    border-radius: 0;
    box-shadow: none;
    transition: var(--el-transition-box-shadow);
    transform: translate3d(0, 0, 0);
    padding: 1px 11px 1px 0;

    font-family: inherit;
    font-size: var(--el-font-size-base);
    color: var(--el-text-color-regular);
    --code-line-height: calc(var(--el-component-size) - 2px);
    line-height: var(--code-line-height);
  }

  .CodeMirror-focused {
    box-shadow: none;
  }

  .CodeMirror-placeholder {
    color: var(--el-text-color-placeholder) !important;
    line-height: 22px !important;
  }

  &:hover {
    cursor: text;
  }

  .CodeMirror-activeline-background {
    background-color: white;
  }

  .CodeMirror-gutters {
    display: none;
  }

  .CodeMirror-line {
    line-height: 22px !important;
    height: 22px;
  }
}
</style>
