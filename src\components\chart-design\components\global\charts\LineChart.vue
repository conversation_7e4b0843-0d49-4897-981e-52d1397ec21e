<template>
  <div ref="g2Container" />
</template>

<script
  lang="ts"
  setup
>
import { ChartProps, LineStyle } from '../../../types';
import useG2Option from '../../../hooks/useG2Option.ts';
import { labelInit } from '../../../const/g2Charts/label.ts';

const props = defineProps<ChartProps<LineStyle>>();

const { g2Container } = useG2Option(props, (chart) => {
  const xField = props.dimensions[0];

  props.metrics.forEach(item => {
    const mark = chart
      .line()
      .encode('x', xField.id)
      .encode('y', item.id)
      .scale('y', {
        domainMin: 0,
        nice: true
      });

    let color;

    if (props.dimensions.length === 1) {
      color = () => item.title;
      mark
        .tooltip((d) => ({
          value: d[item.id],
          name: item.title
        }));
    } else if (props.dimensions.length === 2) {
      const d2Id = props.dimensions[1].id;
      color = d2Id;
      mark
        .transform({ type: 'dodgeX' })
        .tooltip((d) => ({
          value: d[item.id],
          name: d[d2Id]
        }));
    }
    mark
      .encode('color', color)
      .encode('size', (props.chartStyle.lineWidth ?? 1));

    labelInit(mark, props.chartType, props.chartStyle, item.id);

    if (props.chartStyle.showSymbol) {
      chart.point()
        .encode('x', xField.id)
        .encode('y', item.id)
        .encode('shape', 'point')
        .encode('color', color)
        .encode('size', (props.chartStyle.symbolSize ?? 1) + 2)
        .legend('size', false)
        .tooltip(false);
    }

    if (props.chartStyle.showArea) {
      chart
        .area()
        .encode('x', xField.id)
        .encode('y', item.id)
        .encode('color', color)
        .style('fillOpacity', 0.2);
    }
  });
});
</script>

<style
  lang="scss"
  scoped
>

</style>