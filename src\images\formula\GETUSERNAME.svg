<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 45</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-230.000000, -3090.000000)">
            <g id="编组-45" transform="translate(230.000000, 3090.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-5" transform="translate(10.000000, 42.000000)" font-family="PingFangSC-Regular, PingFang SC"
                   font-size="14" font-weight="normal" line-spacing="23">
                    <text id="。" fill="#3A3A3A">
                        <tspan x="288" y="16">。</tspan>
                    </text>
                    <text id="GETUSERNAME函数可以获取当前用">
                        <tspan x="0" y="15" fill="#3F70FF">GETUSERNAME</tspan>
                        <tspan x="105.812" y="15" fill="#3A3A3A">函数可以获取当前用户的昵称</tspan>
                        <tspan x="0" y="39" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="39" fill="#3F70FF">GETUSERNAME</tspan>
                        <tspan x="154.812" y="39" fill="#3A3A3A">()</tspan>
                        <tspan x="0" y="63" fill="#3A3A3A">·示例：略</tspan>
                    </text>
                </g>
                <text id="GETUSERNAME" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">GETUSERNAME</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
