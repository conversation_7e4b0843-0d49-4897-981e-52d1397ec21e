import request from '@/axios';
// 获取物品tree
export const getGoodsTree = (tid) => {
    return request({
        url: '/warehouse/tree',
        method: 'get',
        params: {
            tid
        }
    });
};
// 新建物品仓库
export const newGoodsWarehouse = (data) => {
    return request({
        url: '/warehouse/submit',
        method: 'post',
        data: {
            ...data
        }
    });
};
// 物品分页
export const getGoodsCrud = (current, size, params) => {
    return request({
        url: '/goods/page',
        method: 'get',
        params: {
            current,
            size,
            ...params,
        },
    });
};
// 添加物品
export const addGoods = (data) => {
    return request({
        url: '/goods/add',
        method: 'post',
        data: {
            ...data
        },
    });
};
// 移除物品
export const delGoods = (data) => {
    return request({
        url: '/goods/remove',
        method: 'DELETE',
        data: {
            ...data
        },
    });
};
// 修改物品
export const modifyGoods = (data) => {
    return request({
        url: '/goods/update',
        method: 'post',
        data: {
            ...data
        },
    });
};
// 下载模板
export const exportBlob = () => {
    return request({
        url: '/goods/down-template',
        method: 'get',
        responseType: 'blob',
    });
};
// 导入物品列表
export const importDevice = (file) => {
    return request({
        url: '/goods/import',
        method: 'post',
    });
};
// 导出物品列表
export const exportPlace = (goodsIds, tid, warehouseId) => {
    return request({
        url: '/goods/export',
        method: 'post',
        data: {
            goodsIds,
            tid,
            warehouseId
        },
        responseType: 'blob',
    });
};
// 导出数据
export const exportPlaceLength = (goodsIds, tid, warehouseId) => {
    return request({
        url: '/goods/export-count',
        method: 'post',
        data: {
            goodsIds,
            tid,
            warehouseId
        },
    });
};
// 重命名
export const modifyGoodsWarehouse = (id, warehouseName) => {
    return request({
        url: '/warehouse/rename',
        method: 'put',
        data: {
            id,
            warehouseName,
        },
    });
};
// 删除物品仓库
export const deleteGoodsWarehouse = (ids) => {
    return request({
        url: '/warehouse/remove',
        method: 'DELETE',
        params: {
            ids
        },
    });
};
// 物品导入模板校验
export const exportCheck = (data) => {
    return request({
        url: '/goods/import-validate',
        method: 'post',
        data: data
    });
};
// 排序
export const sort = (data) => {
    return request({
        url: '/warehouse/sort',
        method: 'put',
        data: data
    });
};