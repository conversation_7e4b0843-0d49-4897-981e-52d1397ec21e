<script setup lang="ts">

defineOptions({
  inheritAttrs: false,
  name:"m-xt-input"
})

const modelValue = defineModel<string>({ default: () => [] });

const props = withDefaults(defineProps<{
  disabled: boolean;
  placeholder: string;
}>(), {
  disabled: false,
  placeholder: '请输入文本',
});

</script>

<template>
  <div>
    <van-field v-model="modelValue" v-bind="props" />
  </div>
</template>

<style scoped lang="scss">

</style>