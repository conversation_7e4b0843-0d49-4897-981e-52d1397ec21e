<template>
  <config-no-title :data="data" />
  <config-permission :data="data" :show-edit="false" :has-required="false" />

</template>

<script setup lang="ts">
import { DividerAllField } from './type';
import ConfigNoTitle from '@/components/form-config/common-config/ConfigNoTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';


defineProps<{
  data: DividerAllField;
}>();
</script>

<style scoped lang="scss"></style>