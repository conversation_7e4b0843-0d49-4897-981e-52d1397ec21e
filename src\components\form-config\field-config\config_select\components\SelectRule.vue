<template>
  <div>
    <!-- <el-form-item label="规则速递">
      <el-button class="w-full" size="large" @click="openRuleDialog">点击配置</el-button>
    </el-form-item> -->

    <!-- 选项配置-->
    <el-dialog
      title="添加规则"
      append-to-body
      destroy-on-close
      v-model="ruleVisible"
      class="ruleDialog"
      width="590px"
      :close-on-click-modal="false"
    >
      <select-rule-form ref="ruleForm" :data="data" :broColumn="broColumn!" />
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="ruleVisible = false" round>取 消</el-button>
          <el-button type="primary" @click="saveRules" round>确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { noSelectRuleType } from '@/components/form-config/types/FieldTypes';
import { OptionInjectKey } from '@/components/form-config/utils/injectKeys';
import { computedInject } from '@vueuse/core';
import SelectRuleForm from './SelectRuleForm.vue';
import { AvueColumns } from '@/components/form-config/types/AvueTypes';
import { SelectAllField } from '@/components/form-config/types/AvueTypes';
const props = defineProps<{
  data: SelectAllField
}>();
const broColumn = computedInject(OptionInjectKey, source => {
  return getFiledList(source?.()?.column);
});

const ruleVisible = ref(false);


const ruleForm = ref();

const saveRules = () => {
  props.data.dicRules = ruleForm.value.ruleTableData;
  ruleVisible.value = false;
};

const getFiledList = (column: AvueColumns[] = []) => {
  let result = _.cloneDeep(column).filter(item => {
    item.disabled = false;
    return !_.includes(noSelectRuleType, item.type);
  });
  //是不是在第一层
  if (!props.data.isDynamic) {
    return result.filter(item => item.prop !== props.data?.prop);
  }
  return result
    .filter(item => item.type === 'dynamic')
    .map(item => {
      const it = item as any;
      const findItem = (it.children)?.column?.find((i:AvueColumns) => i.prop === props.data?.prop);
      if (_.isEmpty(findItem)) {
        return [];
      }

      return it?.children?.column?.filter((i:AvueColumns) => i.prop !== props.data?.prop) || [];
    })
    .flat();
};
</script>
