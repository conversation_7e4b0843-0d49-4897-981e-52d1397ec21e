<script setup lang="ts">

defineOptions({
  name: 'xt-m-checkbox'
});

const prop = withDefaults(defineProps<{
  placeholder: string;
  dic: any[];
  dicData: any[];
  disabled: boolean;
}>(), {
  placeholder: () => '请选择',
  dic: () => []
});

const modelValue = defineModel({default:()=>[]});

const dicts = computed(() => (!_.isEmpty(prop.dic) ? prop.dic : prop.dicData));
</script>

<template>
  <el-checkbox-group v-model="modelValue" v-bind="prop">
    <el-checkbox v-for="item in dicts"
                 :key="item.label"
                 :label="item.label">{{ item.label }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<style scoped lang="scss">

</style>