<template>
  <div class="w-full">
    <vxe-table
      class="mytable-style"
      :key="tableKey"
      border
      ref="tableRef"
      :data="vModel"
      :max-height="tableHeight"
      :sort-config="sortConfig"
      :column-config="{resizable: true}"
      :scroll-y="{enabled: isScroll ? true: true, gt: 0}"
      :scroll-x="{enabled: isScroll ? true:false, gt: 0}"
      @checkbox-all="selectAllChangeEvent"
      @checkbox-change="selectChangeEvent"
      :cell-class-name="cellClassName"
      :header-cell-class-name="(cellClassName as any)"
    >
      <vxe-column type="checkbox" width="60" align="center"></vxe-column>
      <vxe-column title="序号" width="50" align="center" >
        <template #default="{rowIndex}">
          {{rowIndex + 1}}
        </template>
      </vxe-column>
      <vxe-column
        :key="item.prop"
        :field="item.prop"
        :width="item.display? 300 : 1"
        align="center" 
        :sortable="item.display"
        v-for="(item) in originColumns"
        :resizable="item.display"
        :display="item.display"
      >
        <template #header>
          <div :class="item.required ? 'requireHeader' : ''" class="flex items-center justify-center" v-if="item.display">
            {{ item.copyLabel }}
            <el-tooltip
              effect="dark"
              :content="(item as TitleField).labelTip"
              v-if="(item as TitleField).labelTip"
              placement="right"
            >
              <el-icon class="ml-5px">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <template #default="{row,rowIndex}">
          <div>
            <xt-el-form-item
              v-model="row[item.prop]"
              :item="item"
              :propPrefix="[...itemProp, String(rowIndex)]"
              :item-form="row"
              :item-columns="columns[rowIndex]"
              :col="col"
              :show-label="false"
              :source="source"
              >
              <!-- 部门管理员/工作组管理员/实名签名渲染组件，其他不渲染 -->
              <template 
                #field 
                v-if="!showFieldType.includes(item.fixType as any) ||
                (item.fixType === FixTypeEnum.SIGNATURE && 
                item.defaultType === DefaultTypeEnum.SIGN)"
              >
                <dynamicField
                  :item
                  :row
                  :originColumns
                  :formatterRowToText
                >
                </dynamicField>
              </template>
            </xt-el-form-item>
          </div>
        </template>
      </vxe-column>
      <vxe-column  fixed="right" label="操作" width="80" v-if="$attrs.defaultType !== DynamicTypeEnum.GROUPSUMMARY">
        <template #default="{ row, rowIndex }">
          <el-button type="primary" text @click="handleEdit(row, rowIndex)">编辑</el-button>
        </template>
      </vxe-column>
    </vxe-table>

    <el-dialog
      v-model="visible"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      title="编辑数据"
      width="50%"
    >
      <el-scrollbar :height="isMobile()?'auto':'600px'" v-if="editOption.column?.length">
        <xt-el-form
          v-model="editData"
          :option="editOption">
        </xt-el-form>

      </el-scrollbar>

      <template #footer>
        <el-button type="primary" @click="updateData">确定</el-button>
        <el-button @click="visible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { VxeTable, VxeColumn } from 'vxe-table'
import { AvueColumns,XtElFormItemType, AvueForm, TitleField } from '@/components/form-config/types/AvueTypes.ts';
import XtElFormItem from '@/components/xt-el-form/XtElFormItem.vue';
import { handleTable } from '@/components/xt-el-form/hooks.ts'
import XtElForm from '@/components/xt-el-form/XtElForm.vue';
import { useTableOperations } from '@/components/xt-el-crud/tools/useFormat'
import { AvueOption } from '@/components/form-config/types/AvueTypes';
import { formInjectKey  } from '@/components/xt-el-form/constant.ts'
import { VxeTableInstance, VxeTableEvents } from 'vxe-table'
import type { VxeTablePropTypes } from 'vxe-table'
import { fieldSource } from '@/components/form-config/const/commonDic';
import dynamicField from './dynamicField.vue';
import {
  departAdminType,
  workingAdminType,
  getUsercodeType
} from '@/components/form-config/types/FieldTypes';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic';
import { useDataList } from '@/components/xt-el-crud/tools/useData.ts'
import { DynamicTypeEnum } from '@/components/form-config/const/commonDic'
import 'vxe-table/lib/style.css';
import { isMobile } from '@/utils/client';
import { handleTypeInjectKey } from '@/components/xt-el-form/constant.ts'
import { HandleTypeEnum } from '@/components/form-config/const/commonDic';
import { isEmptyValue} from '@/utils/field';


const showFieldType = [...departAdminType, ...workingAdminType,...getUsercodeType] //工作组管理员/部门管理员/用户id渲染组件本身
const noOptimizeType = [...departAdminType, ...workingAdminType]//工作组管理员/部门管理员不优化表格

defineOptions({
  inheritAttrs: false,
});

const props = withDefaults(
  defineProps<{
    item?: AvueColumns;
    columns?: AvueColumns[][];
    originColumns: XtElFormItemType[];
    itemProp?: string[];
    fixedColumn?: boolean;
    fixedNumber?: number;
    disabled: boolean;
    source?: number//组件的使用场景 1中间的组件配置 2 组件的默认值设置 3 预览 4 应办
    col:AvueColumns[];
    height?: string
  }>(),
  {
    columns: () => [],
    itemProp: () => [],
  }
);


const { spaceList,articleList,deptTreeData,dutyTreeData,allUserList } = useDataList(props.originColumns)
const { formatterRowToText } = useTableOperations({spaceList,articleList,deptTreeData,dutyTreeData,allUserList})
const handleType = inject(handleTypeInjectKey, HandleTypeEnum.HANDLED);

const vModel = defineModel<AvueForm[] | null[]>({
  default: () => [],
});
const originData = defineModel<AvueForm[] | null[]>('originData',{
  default: () => [],
});

const formInject = inject(formInjectKey)!;
const tableKey = ref(1)


//表格是否有工作组管理员/部门管理员的组件
const isAdminType = computed(() => {
  return props.originColumns.some(v => _.includes(noOptimizeType, v.fixType))
})

const isScroll = computed(() => {
  return (_.includes([HandleTypeEnum.HANDLED,HandleTypeEnum.INSTANTSERVICE],handleType ) && !isAdminType.value) || 
  (handleType === HandleTypeEnum.Examine && !isFormula.value && !isAdminType.value) ||
  props.source === fieldSource.SETDEFAULT
})

provide('allformInjectKey', formInject);//表格里面传整个form
provide('col', props.col)

const { batchDeleteData, handleSelectionChange } = handleTable(vModel,originData, '')

//表格是否有公式编辑的组件
const isFormula = computed(() => {
  return props.originColumns.some(v => v?.formula?.template)
})

const tableRef = ref<VxeTableInstance<AvueColumns>>()
const selectChangeEvent: VxeTableEvents.CheckboxChange<AvueColumns> = () => {
  const $table = tableRef.value
  if ($table) {
    const records = $table.getCheckboxRecords()
    handleSelectionChange(records)
  }
}

const selectAllChangeEvent: VxeTableEvents.CheckboxAll<AvueColumns> = () => {
  const $table = tableRef.value
  if ($table) {
    const records = $table.getCheckboxRecords()
    handleSelectionChange(records)
  }
}


//表格组件不可见时不显示表头，宽度设置为1且不可拖动，去掉右边框
const cellClassName: VxeTablePropTypes.CellClassName<AvueColumns> = ({ column }) => {
  const display = props.originColumns.find(v => v.prop === column.field)?.display
  if (column.field && !display) {
    return 'fixBorder'
  }
  return null
}

const tableHeight = computed(() => {
  return props.height ? 600 : 400;
});


const visible = ref(false)
const editData = ref<AvueColumns>()
const editOption = ref<AvueOption>({ column: [] });
const editIndex = ref(0);
//编辑表格
const handleEdit = (rowData:AvueColumns, index:number)=> {
  visible.value = true
  editIndex.value = index
  editData.value = {...rowData}
  editOption.value = {
    column: _.cloneDeep(props.originColumns || []),
  };
}
//更新表格
const updateTable = () => {
  tableKey.value += 1
}
//提交编辑
const updateData = () => {
  if (!editData.value?.id) return;

  const editedData = _.cloneDeep(editData.value) as AvueForm;

  // 批量更新，减少响应式触发次数
  nextTick(() => {
    // 更新显示数据（可能是搜索结果）
    const viewIndex = vModel.value.findIndex(
      item => item?.id === editedData.id
    );
    if (viewIndex !== -1) {
      vModel.value[viewIndex] = editedData;
    }

    // 更新源数据
    const originIndex = originData.value.findIndex(
      item => item?.id === editedData.id
    );
    if (originIndex !== -1) {
      originData.value[originIndex] = editedData;
    }
  });

  visible.value = false;
  updateTable();
}

// watch(editData, (newVal) => {
//   if (newVal && editIndex.value !== undefined) {
//     vModel.value[editIndex.value] = _.cloneDeep(newVal);
//   }
// }, { deep: true });


// 自定义排序方法
const sortConfig = ref<VxeTablePropTypes.SortConfig<any>>({
  trigger: 'cell', // 触发排序的方式
  multiple: true, // 允许多列排序
  chronological: true,
  sortMethod: ({ data, sortList }) => {
    // 创建一个排序函数数组
    const sortFunctions: ((a: AvueForm, b: AvueForm) => number)[] = [];

    // 根据 sortList 生成排序函数
    sortList.forEach(sortItem => {
      const { field, order } = sortItem;
      const col = { type: field.split(':#')[0], prop: field };
      sortFunctions.push((a: AvueForm, b: AvueForm) => {
        const aVal = formatterRowToText(a, col);
        const bVal = formatterRowToText(b, col);
        // 处理空值：null / undefined / 空字符串
        const isEmptyA = isEmptyValue(aVal);
        const isEmptyB = isEmptyValue(bVal);

        if (isEmptyA && !isEmptyB) return 1; // A 是空值，B 不是 → A 排在后面
        if (!isEmptyA && isEmptyB) return -1; // B 是空值，A 不是 → B 排在后面
        if (isEmptyA && isEmptyB) return 0; // 都为空 → 保持原顺序

        // if (aVal === bVal) return 0;
        if(_.isString(aVal) && _.isString(bVal)) return order === 'asc' ? aVal.localeCompare(bVal, 'zh-CN') : bVal.localeCompare(aVal, 'zh-CN')
        else return order === 'asc' ? (aVal > bVal ? 1 : -1) : (aVal > bVal ? -1 : 1)
      });
    });

    // 依次应用排序函数
    return data.sort((a, b) => {
      for (const sortFunction of sortFunctions) {
        const result = sortFunction(a, b);
        if (result !== 0) return result;
      }
      return 0;
    });
  }
});



defineExpose({
  batchDeleteData,
  updateTable
});
</script>

<style scoped lang="scss">
:deep(.mytable-style .fixBorder) {
  background-image: linear-gradient(var(--vxe-ui-table-border-color), var(--vxe-ui-table-border-color));
  background-repeat: no-repeat;
  background-size: 100% var(--vxe-ui-table-border-width);
  background-position: 0 100%;
}

.requireHeader::before {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}
:deep(.vxe-header--column .vxe-cell){
  display: flex;
  align-items: center;
  justify-content: center;

}

</style>
