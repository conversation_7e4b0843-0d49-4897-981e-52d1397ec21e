// 支付方式枚举
enum modePayEnum {
  PAYLATER = 'payLater', // 先付后用
  LATERPAY = 'laterPay', // 先用后付
  FORTHWITH = ' forthwith', // 即时支付
}

export const modePayList = [
  {
    label: '先付后用',
    value: modePayEnum.PAYLATER,
  },
  {
    label: '先用后付',
    value: modePayEnum.LATERPAY,
  },
  {
    label: '即时支付',
    value: modePayEnum.FORTHWITH,
  },
];

// 使用权方案因素列表
export const permissionFactorList = [
  {
    option: '按使用人数进行管理',
    fields: [
      {
        field: '使用人数',
        units: [
          { label: '人/月', value: 1 },
          { label: '人', value: 0 },
        ],
      },
    ],
    value:0
  },
  {
    option: '按使用次数进行管理',
    fields: [
      {
        field: '使用次数',
        units: [
          { label: '次/月', value: 1 },
          { label: '次', value: 0 },
        ],
      },
    ],
    value:1
  },
  {
    option: '按短信使用量进行管理',
    fields: [
      {
        field: '短信使用量',
        units: [
          { label: '条/月', value: 1 },
          { label: '条', value: 0 },
        ],
      },
    ],
    value:2
  },
  {
    option: '按数据存储量决定管理',
    fields: [
      {
        field: '数据存储量',
        units: [
          { label: 'G/月', value: 1 },
          { label: 'G', value: 0 },
        ],
      },
    ],
    value:3
  },
  {
    option: '按存证功能使用量开展管理',
    fields: [
      {
        field: '存证使用量',
        units: [
          { label: '次/月', value: 1 },
          { label: '次', value: 0 },
        ],
      },
    ],
    value:4
  },
  {
    option: '按公证功能使用量开展管理',
    fields: [
      {
        field: '公证使用量',
        units: [
          { label: '次/月', value: 1 },
          { label: '次', value: 0 },
        ],
      },
    ],
    value:5
  },
  {
    option: '按存档次数开展管理',
    fields: [
      {
        field: '存档次数',
        units: [
          { label: '次/月', value: 1 },
          { label: '次', value: 0 },
        ],
      },
    ],
    value:6
  },
  {
    option: '按存台账次数开展管理',
    fields: [
      {
        field: '存台账次数',
        units: [
          { label: '次/月', value: 1 },
          { label: '次', value: 0 },
        ],
      },
    ],
    value:9
  },
  {
    option: '按存文件次数开展管理',
    fields: [
      {
        field: '文件次数',
        units: [
          { label: '次/月', value: 1 },
          { label: '次', value: 0 },
        ],
      },
    ],
    value:10
  },
];

// 价格方案因素列表
export const priceFactorList = [
  {
    option: '按使用人数计费',
    fields: [
      {
        field: '人数分段',
        units: [{ label: '元/人', value: 'yuanPerPerson' }],
      },
    ],
    payMode: modePayEnum.LATERPAY,
    value:0
  },
  {
    option: '按使用次数计费',
    fields: [
      {
        field: '次数分段',
        units: [{ label: '元/次', value: 'yuanPerTime' }],
      },
    ],
    payMode: modePayEnum.LATERPAY,
    value:1
  },
  {
    option: '按短信使用量计费',
    fields: [
      {
        field: '短信使用量分段',
        units: [{ label: '元/条', value: 'yuanPerMessage' }],
      },
    ],
    payMode: modePayEnum.FORTHWITH,
    value:2
  },
  {
    option: '按数据存储量计费',
    fields: [
      {
        field: '数据存储量分段',
        units: [{ label: '元/G', value: 'yuanPerGB' }],
      },
    ],
    payMode: modePayEnum.LATERPAY,
    value:3
  },
  {
    option: '按存证功能使用量计费',
    fields: [
      {
        field: '存证使用量分段',
        units: [{ label: '元/文件/次', value: 'yuanPerFilePerTime' }],
      },
    ],
    payMode: modePayEnum.FORTHWITH,
    value:4
  },
  {
    option: '按公证功能使用量计费',
    fields: [
      {
        field: '公证使用量分段',
        units: [{ label: '元/文件/次', value: 'yuanPerFilePerTime' }],
      },
    ],
    payMode: modePayEnum.LATERPAY,
    value:5
  },
  {
    option: '按公证项目类型计费',
    // fields: [
    //   {
    //     field: '公证使用量分段',
    //     units: [{ label: '元/文件/次', value: 'yuanPerFilePerTime' }],
    //   },
    //   {
    //     field: '公证项目类型计费',
    //     units: [{ label: '元/文件/次', value: 'yuanPerFilePerTime' }],
    //   },
    // ],
    payMode: modePayEnum.LATERPAY,
    value:7
  },
  {
    option: '按存档次数计费',
    fields: [
      {
        field: '存档次数分段',
        units: [{ label: '元/文件/次', value: 'yuanPerFilePerTime' }],
      },
    ],
    payMode: modePayEnum.LATERPAY,
    value:8
  },
  {
    option: '按存台账次数收费',
    fields: [
      {
        field: '台账次数分段',
        units: [{ label: '元/文件/次', value: 'yuanPerFilePerTime' }],
      },
    ],
    payMode: modePayEnum.LATERPAY,
    value:9
  },
  {
    option: '按存文件次数收费',
    fields: [
      {
        field: '文件次数分段',
        units: [{ label: '元/文件/次', value: 'yuanPerFilePerTime' }],
      },
    ],
    payMode: modePayEnum.LATERPAY,
    value:10
  },
];

export const scopeBaseList = [
  {
    label: '行政区',
    value: 1,
  },
];

// 时间因素下拉列表
export const timeBaseList = [
  {
    label: '下载之日起',
    value: 1,
  },
];

// 时间单位列表
export const unitList = [
  {
    label: '天',
    value: 1,
  },
  {
    label: '周',
    value: 2,
  },
  {
    label: '月',
    value: 3,
  },
  {
    label: '年',
    value: 4,
  },
];
