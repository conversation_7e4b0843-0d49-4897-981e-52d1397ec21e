<template>
  <n-card>
    <h3>极值规则</h3>
    <el-row :gutter="10" class="mb-2" v-if="!isMul">
      <el-col :span="2"> 根据 </el-col>
      <el-col :span="5">
        <avue-select
          class="w-full"
          :dic="numberDic"
          v-model="vModel.extremumType"
          :props="{ label: 'copyLabel', value: 'id' }"
        ></avue-select>
      </el-col>
      <el-col :span="2"> 取 </el-col>
      <el-col :span="5">
        <avue-select
          class="w-full"
          :dic="numberType.includes(sortFieldType as any) ? fetchOption2 : fetchOption1"
          v-model="vModel.extremumItem"
          placeholder="请选择"
        ></avue-select>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="mb-2">
      <el-col :span="7"> 根据当前任务每次触发时 </el-col>
      <el-col :span="5">
        <el-select v-model="vModel.extremumDirection" clearable>
          <el-option
            v-for="item in rulesDic"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-col>
      <!-- 往前 -->
      <template v-if="vModel.extremumDirection === 0">
        <el-col :span="3">
          <div class="flex-center"><el-input v-model="vModel.extremumSelectDay"></el-input> 天</div>
        </el-col>
        <el-col :span="3">
          <div class="flex-center">
            <el-input v-model="vModel.extremumSelectHour"></el-input> 时
          </div>
        </el-col>
        <el-col :span="3">
          <div class="flex-center"><el-input v-model="vModel.extremumSelectMin"></el-input> 分</div>
        </el-col>
      </template>
      <!-- 选择时间 -->
      <template v-if="vModel.extremumDirection === 1">
        <el-col :span="9">
          <avue-select
            class="w-full"
            :dic="dataDic"
            v-model="vModel.extremumSelectTime"
          ></avue-select>
        </el-col>
      </template>
      <!-- 具体日期时间 -->
      <template v-if="vModel.extremumDirection === 2">
        <el-col :span="9">
          <el-date-picker
            v-model="vModel.extremumSelectDate"
            :editable="false"
            value-format="YYYY-MM-DD hh:mm:ss"
            format="YYYY-MM-DD hh:mm:ss"
            type="datetime"
          />
        </el-col>
      </template>
      <!-- 其他具体日期时间 -->
      <template v-else> </template>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="10"> 
        <el-form-item label="至每次任务触发时所生成数据且满足以上条件中" required>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <avue-select
          class="w-full"
          clearable
          value-on-clear=""
          :dic="fetchOption1"
          v-model="vModel.extremumOption"
        ></avue-select>
      </el-col>
    </el-row>
  </n-card>
</template>

<script setup lang="ts">
import { fetchOption1, fetchOption2 } from '@/components/form-config/utils/option';
import { FieldBaseField,AvueColumns} from '@/components/form-config/types/AvueTypes';
import { SystemTypeEnum } from '@/components/form-config/types/field';
import { extremeRuleType } from '@/api/interface/configDataLink.ts';
import { createList, dateType,numberType, systemByFilterSortType, systemByFilterSortType1} from '@/components/form-config/types/FieldTypes';


const props = defineProps<{
  data?: FieldBaseField;
  fieldList?: AvueColumns[];
  isMul?: boolean; //是否是多个筛选条件
  pathType?: number
}>();

const vModel = defineModel<Partial<extremeRuleType>>({ default: {} });

const rulesDic = [
  { label: '往前', value: 0 },
  { label: '选择时间', value: 1 },
  { label: '具体日期时间', value: 2 },
  { label: '所选任务首次触发时', value: 3 },
];
const dataDic = [
  { label: '当年', value: 0 },
  { label: '当月', value: 1 },
  { label: '当周', value: 2 },
  { label: '当日', value: 3 },
];

const systemField = computed(() => {
  const types:SystemTypeEnum[] = props.pathType === 0 ? systemByFilterSortType : systemByFilterSortType1
  return createList.filter(item => types.includes(item.fixType as SystemTypeEnum));
})

const numberDic = computed(() => {
  const types = [...numberType, ...dateType]
  const arr = props.fieldList?.filter(item => _.includes(types, item.fixType)) || [];
  return [...arr, ...systemField.value]
});


const sortFieldType = computed(() => {
  return props.fieldList?.find(item => item.id === vModel.value.extremumType)?.fixType
})
</script>

<style scoped lang="scss">
.flex-center {
  display: flex;
  align-items: center;
  // justify-content: center;
}
</style>
