<template>
  <div ref="g2Container" />
</template>

<script
  lang="ts"
  setup
>
import { ChartProps, ColumnStyle } from '../../../types';
import useG2Option from '../../../hooks/useG2Option.ts';
import { labelInit } from '../../../const/g2Charts/label.ts';

const props = defineProps<ChartProps<ColumnStyle>>();

const { g2Container } = useG2Option(props, (chart) => {
  const xField = props.dimensions[0];

  props.metrics.forEach(item => {
    const mark = chart
      .interval()
      .encode('x', xField.id)
      .encode('y', item.id);

    if (props.dimensions.length === 1) {
      mark
        .encode('color', () => item.title)
        .encode('series', () => item.id)
        .tooltip((d) => ({
          value: d[item.id],
          name: item.title
        }));
    } else if (props.dimensions.length === 2) {
      const d2Id = props.dimensions[1].id;
      mark
        .encode('color', d2Id)
        .transform({ type: 'dodgeX' })
        .tooltip((d) => ({
          value: d[item.id],
          name: d[d2Id]
        }));
    }

    labelInit(mark, props.chartType, props.chartStyle, item.id);
  });
});
</script>

<style
  lang="scss"
  scoped
>

</style>