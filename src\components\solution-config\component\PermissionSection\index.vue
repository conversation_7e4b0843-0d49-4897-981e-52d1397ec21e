<!-- PermissionSection.vue -->
<template>
  <h4>方案 {{ Nzh.cn.encodeS(index + 1) }}</h4>
  <div class="border-1 border-#ebebeb border-solid px-20px mt-10px ">
    <FactorGroup
      type="principalFactorList"
      title="主体因素"
      v-model="permission.principalFactorList"
      @add="solutionStore.addFactor(permission, 'principalFactorList')"
    />

    <FactorGroup
      type="spaceFactorList"
      title="空间因素"
      v-model="permission.spaceFactorList"
      @add="solutionStore.addFactor(permission, 'spaceFactorList')"
    />

    <FactorGroup
      type="timeFactorList"
      title="时间因素"
      v-model="permission.timeFactorList"
      @add="solutionStore.addFactor(permission, 'timeFactorList')"
    />

    <FactorGroup
      type="eventFactorList"
      title="事件因素"
      v-model="permission.eventFactorList"
      @add="solutionStore.addFactor(permission, 'eventFactorList')"
    />
    <!-- 其他因素组... -->
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import Nzh from 'nzh';
import FactorGroup from './component/FactorGroup.vue';
import { Permission } from '../../types';
import { useSolutionStore } from '../../store/index';

defineProps<{
  permission: Permission;
  index: number;
}>();

const solutionStore = useSolutionStore();
</script>
