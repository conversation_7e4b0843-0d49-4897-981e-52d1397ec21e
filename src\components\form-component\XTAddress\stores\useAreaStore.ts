import { defineStore } from 'pinia';
import { ref } from 'vue';

interface Area {
  code:string
  name:string
  children?:Area[]
}

const useAreaStore = defineStore('area', () => {
  const area = ref<Area[] | null>(null);

  const getArea = async () => {
    if (_.isEmpty(area.value)) {
      area.value = (await import('../const/pcas-code.json')).default;
    }
    return area.value;
  };

  return {
    getArea,
  };
});

export default useAreaStore;
