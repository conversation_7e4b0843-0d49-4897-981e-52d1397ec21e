import request from '@/axios';

export const getTree = () => {
  return request({
    url: '/dataTable/tree',
    method: 'post',
    data: {}
  });
};

export const submit = (form: any) => {
  return request({
    url: '/dataTable/submit',
    method: 'post',
    data: form
  });
};

export const remove = (id: string) => {
  return request({
    url: '/dataTable/remove',
    method: 'post',
    params: {
      ids: id
    }
  });
};

export const getByIds = (ids: string[]) => {
  return request({
    url: '/dataTable/getByIds',
    method: 'post',
    data: {
      originList: ids
    }
  });
};

export const getDfsTree = (dataTable: any) => {
  return request({
    url: '/dataTable/dfsTree',
    method: 'post',
    data: dataTable
  });
};

export const getByVersionId = (versionId: string) => {
  return request({
    url: '/dataTable/getByVersionId',
    method: 'post',
    params: {
      versionId
    }
  });
};

export const submitField = (form: any) => {
  return request({
    url: '/dataTableField/submit',
    method: 'post',
    data: form
  });
};

export const getFieldList = (dataTableId: string) => {
  return request({
    url: '/dataTableField/list',
    method: 'post',
    data: {
      dataTableId
    }
  });
};

export const removeField = (id: string) => {
  return request({
    url: '/dataTableField/remove',
    method: 'post',
    params: {
      ids: id
    }
  });
};

export const getByDataTableId = (dataTableId: string) => {
  return request({
    url: '/dataTableField/getByDataTableId',
    method: 'post',
    params: {
      dataTableId
    }
  });
};


