<template>
  <a-collapse
    v-model:active-key="activeKey"
    ghost
  >
    <a-collapse-panel
      key="1"
      header="整体样式"
    >
      <el-form-item label="背景">
        <el-color-picker
          v-model="chartStyle.backgroundColor"
        />
      </el-form-item>
      <el-form-item label="标题名称">
        <el-input
          v-model="chartStyle.title"
          clearable
          placeholder="请输入 标题"
        />
      </el-form-item>
      <el-form-item label="标题颜色">
        <el-color-picker v-model="chartStyle.titleColor" />
      </el-form-item>
      <el-form-item label="标题位置">
        <avue-radio
          v-model="chartStyle.titlePosition"
          :dic="TitlePositionDic"
        />
      </el-form-item>
    </a-collapse-panel>
  </a-collapse>
</template>

<script
  lang="ts"
  setup
>
import { storeToRefs } from 'pinia';
import useChartDesignStore from '../../../store/useChartDesignStore.ts';
import { TitlePositionDic } from '../../../const';

const { chartStyle } = storeToRefs(useChartDesignStore());

const activeKey = ref(['1']);
</script>

<style
  lang="scss"
  scoped
>

</style>