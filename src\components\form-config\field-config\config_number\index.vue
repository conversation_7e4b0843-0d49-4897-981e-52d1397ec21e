<template>
  <config-title :data="data" />
  <config-permission :data="data" />
  <el-form-item label="格式">
    <avue-select v-model="data.numberFormat" :clearable="false" :dic="numberFormat" />
  </el-form-item>
  <el-form-item>
    <el-checkbox v-model="data.showPrecision">保留小数位数</el-checkbox>
  </el-form-item>
  <el-form-item v-if="data.showPrecision">
    <el-input-number
      v-model="data.precision"
      :max="10"
      :min="0"
      controls-position="right"
      placeholder="精度"
      step-strictly
      style="width: 100px; margin-left: 20px"
    ></el-input-number>
  </el-form-item>
  <el-form-item>
    <el-checkbox v-model="data.showPoints">显示千分符</el-checkbox>
  </el-form-item>
  <el-form-item>
    <div class="previewNumber">
      <span>{{ previewNumber }}</span>
    </div>
  </el-form-item>
  <el-form-item label="数字范围">
    <div class="flex items-center justify-around">
      <el-input v-model="data.min" placeholder="最小范围" />
      <span class="mx-10px">~</span>
      <el-input v-model="data.max" placeholder="最大范围" />
    </div>
  </el-form-item>
  <config-default-type :data="data" v-if="formType!== formTypeEnum.KNOWLEDGE"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import { numberFormat } from './const';
import { formatValue } from './utils';
import { ResetConfigInjectKey } from '@/components/form-config/utils/injectKeys';
import { NumberAllField } from './type';
import { formTypeEnum } from '@/components/form-config/const/commonDic';

const formType = inject('formType',null)
const props = defineProps<{
  data: NumberAllField;
}>();

const initNumber = 888888;

const previewNumber = computed(() => formatValue(initNumber, props.data, true));

const resetConfig = inject(ResetConfigInjectKey);

watch(
  () => props.data.numberFormat,
  () => {
    resetConfig?.();
  }
);
</script>

<style lang="scss" scoped>
.previewNumber {
  height: 40px;
  background: #f4f5f5;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  span {
    font-size: 15px;
    font-weight: 600;
    color: #3a3a3a;
    line-height: 21px;
  }
}
</style>
