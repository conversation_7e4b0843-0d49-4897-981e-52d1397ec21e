<template>
    <div class="w-full">
      <van-field v-model="vComputed"  v-bind="props" @focus="handleFocus"
                 @blur="handleBlur" label="" type="text" >
        <template #right-icon v-if="numberFormat === 'percent'">%</template>
      </van-field>
    </div>
  
  </template>
  
  <script setup lang="ts">
  import { NumberField } from '@/components/form-config/types/AvueTypes';
  import { formatValue, valueFormat } from '@/components/form-config/utils';
  
  defineOptions({
    name:'m-xt-number-picker',
    inheritAttrs: false,
  });
  
  const props = withDefaults(defineProps<NumberField|{
    placeholder:string
    disabled:boolean
  }>(), {
    placeholder: '请输入',
  })
  
  const vModel = defineModel<string | number>();
  
  const vComputed = computed({
    get: () => {
      // 禁用状态直接格式化，并同步更新 vModel
      if (props.disabled) {
        const v = valueFormat(vModel.value,props);
        const formatted = formatValue(v, props);
        return formatted;
      }
      // 非禁用状态保持原有逻辑
      if (!isFocus.value) {
        return formatValue(vModel.value, props);
      } else {
        return vModel.value ?? '';
      }
    },
    set: v => {
      vModel.value = v;
    },
  });
  
  let isFocus = ref(false);
  
  //获得焦点时处理
  const handleFocus = () => {
    isFocus.value = true;
  };
  
  //失去焦点时处理
  const handleBlur = (e: any) => {
    isFocus.value = false;
    if (props.disabled) return;
    console.log(11111);
    
    vModel.value = valueFormat(e.target.value, props);
  };
  
  
  // const debouncedValueFormat = _.debounce((val:string|number) => {
  //   if (val) {
  //     vModel.value = valueFormat(val, props);
  //   }
  // }, 300); 
  
  // // 公式编辑-数据联动等其他途径赋值 
  // watch(
  //   () => vModel.value,
  //   (val) => {
  //     debouncedValueFormat(val)
  //   },
  //   { immediate: true } 
  // )
  
  </script>
  
  <style scoped lang="scss"></style>
  