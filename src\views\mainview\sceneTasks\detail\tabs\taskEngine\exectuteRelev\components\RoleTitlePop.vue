<template>
  <el-popover placement="top" :width="500" popper-style="padding: 10px;">
    <template #reference>
      <span class="text-center"
        ><el-icon :size="15" color="#808080" v-if="isShowIcon"><QuestionFilled /></el-icon
      ></span>
    </template>
    <template #default>
      <h3 class="my-10px">角色命名示例：</h3>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item v-for="item in roleList" class="mb-10px mr-5px">
          {{ item.name }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </template>
  </el-popover>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';
import { getByType, getRoleByType } from '@/api/taskManagement/role.ts';

type roleType = {
  id: string;
  name: string;
};

withDefaults(defineProps<{ isShowIcon?: boolean }>(), {
  isShowIcon: true,
});

const roleLibrary = ref([]);
const type = ref('');
const roleList = ref<roleType[]>([]);

const initRoleType = async () => {
  try {
    const { data } = await getByType();
    if (!data.data.length) return;
    if (!type.value) {
      type.value = data.data[0];
    }
    roleLibrary.value = data.data;

    if (type.value) {
      await handleRoleType(type.value);
    }
  } catch (error) {
    // 在这里处理可能出现的错误
    console.error('Error initializing role type:', error);
  }
};

const handleRoleType = async (type: string) => {
  try {
    const { data } = await getRoleByType(type);
    roleList.value = [...data.data];
  } catch (error) {
    console.error('Error handling role type:', error);
  }
};

onMounted(() => initRoleType());
</script>
<style lang="scss" scoped></style>
