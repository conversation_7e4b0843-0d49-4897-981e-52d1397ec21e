<template>
  <el-scrollbar class=" bg-white config" view-class="pt-55px pb-16px">
    <!--图表类型-->
    <image-select v-model="chartType" :dic="ChartsDic" class="px-16px" />
    <!--具体配置-->
    <el-form :label-width="68" label-position="left">
      <common-conf class="lastNoMargin" />
      <a-collapse v-model:active-key="activeKey" collapsible="icon" ghost>
        <a-collapse-panel v-for="(item, index) in Conf" :key="index" :header="item.header" class="pb-0">
          <component :is="item.render" class="w-full box-border lastNoMargin" />
        </a-collapse-panel>
      </a-collapse>
    </el-form>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { ChartsDic } from '../../const/config.ts';
import useChartDesignStore from '../../store/useChartDesignStore.ts';
import ImageSelect from '../ImageSelect.vue';
import CommonConf from './components/CommonConf.vue';
import Configs from './components/configs';

const { chartType } = storeToRefs(useChartDesignStore());

const Conf = computed(() => Configs[chartType.value + '_conf']);

const activeKey = ref<number[]>([]);

watch(Conf, (v) => {
  const r = [];
  if (!!v?.length) {
    for (let i = 0; i < v.length; i++) {
      r.push(i);
    }
  }
  activeKey.value = r;
}, {
  immediate: true
});
</script>

<style lang="scss" scoped>
.lastNoMargin {
  :deep(.el-form-item:last-child) {
    margin-bottom: 0;
  }
}

.config {
  :deep(.ant-collapse-header) {
    padding: 16px;

    .ant-collapse-expand-icon {
      padding-inline-end: 10px;
    }
  }

  :deep(.ant-collapse-content-box) {
    padding: 0;
    padding-block: 0 !important;
    @apply px-38px;
  }

  :deep(.el-form-item) {
    margin-bottom: 8px;

    &.no-title {
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>