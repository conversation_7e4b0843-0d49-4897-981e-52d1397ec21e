<template>
  <div>
    <config-title :data="data"></config-title>
    <config-data-collect :data="data" />
  </div>
</template>

<script setup lang="ts">
import { SignatureCollectAllField } from './type';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigDataCollect from '@/components/form-config/common-config/ConfigDataCollect/index.vue';


defineProps<{
  data: SignatureCollectAllField;
}>();



</script>

<style scoped lang="scss">
.dataBox {
  :deep(.el-checkbox-group) {
    @apply flex-(~ col) ml-10px;
  }
}
</style>
