import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getTargetTree } from '@/api/taskManagement/taskPackage.ts';
import { addAndUpdateTask, removeTargetNode } from '@/api/taskManagement/task.ts';
import { TaskTreeProps } from '@/views/mainview/sceneTasks/components/taskManagement/hook/useGraphHook.ts';

interface FindNodeProps {
  node?: TaskTreeProps;
  parentNode?: TaskTreeProps;
}

const useTaskGraphStore = defineStore('taskGraph', () => {
  const currentPackageId = ref<string>('');
  const taskTreeData = ref<TaskTreeProps[]>([]);
  // 初始化任务树数据
  const initTaskTreeData = async (taskPackageId: string) => {
    console.log('initTaskTreeData', taskPackageId);
    currentPackageId.value = taskPackageId;
    let data = (await getTargetTree()).data.data;
    console.log('data',data);
    taskTreeData.value = (data&&JSON.stringify(data)!='{}')?data:[];
    //taskTreeData.value = [];
    console.log('dffff',taskTreeData.value);
  };
  function delChildrenNode(node: TaskTreeProps | undefined, parentNode?: TaskTreeProps) {
    if (!node) return;
    if (parentNode) {
      parentNode.children = (parentNode.children ??= []).filter(item => item.id !== node.id);
    } else {
      taskTreeData.value = taskTreeData.value.filter(item => item.id !== node.id);
    }
  }
  const findNodeById = (
    id: string | number,
    data: TaskTreeProps[] = taskTreeData.value,
    parentData?: TaskTreeProps
  ): FindNodeProps => {
    let res: FindNodeProps = {};

    for (let i = 0; i < data.length; i++) {
      if (data[i].id === id) {
        res.parentNode = parentData;
        res.node = data[i];
        return res;
      }

      // Move the recursive call outside the loop
      if (data[i].children) {
        const childResult = findNodeById(id, data[i].children, data[i]);
        // Check if a match is found in the child subtree
        if (childResult.node) {
          return childResult;
        }
      }
    }

    return res;
  };
  const addTaskS = async (task: Partial<TaskTreeProps>, parentId?: string | number) => {
    console.log('task',task);
    return await addAndUpdateTask({
      //taskPackageId: currentPackageId.value,
      metricsId: task.metricsId,
      targetId: task.targetId,
      targetName: task.targetName,
      targetValue: task.targetValue,
      requirements: task.requirements,
      x: 0,
      y: 0,
      side: task.side || '',
      foldLeft: Boolean(task.foldLeft),
      foldRight: Boolean(task.foldRight),
      root: !Boolean(parentId),
      prevName: task.prevName,
      parentId,
    }).then((res: any) => {
      if (!parentId) {
        taskTreeData.value.push(res.data.data);
        return res;
      }
      const { node } = findNodeById(parentId);
      if (node) {
        (node.children ??= []).push(res.data.data);
      }
      return res;
    });
  };
  const updateTaskParentS = async (
    task: TaskTreeProps,
    newTask: Partial<TaskTreeProps>,
    parentId?: string | number
  ) => {
    const resTask = {
      ...task,
      ...newTask,
    };
    return addAndUpdateTask({
      ...resTask,
      parentId,
    }).then(res => {
      const { node, parentNode } = findNodeById(task.id);
      res.data.data.children = node?.children;
      const newNode = Object.assign(node || {}, res.data.data);

      if (newNode) {
        delChildrenNode(node, parentNode);
        if (!parentId) {
          newNode && taskTreeData.value.push(newNode);
        } else {
          const { node: pNode } = findNodeById(parentId);
          newNode && pNode && (pNode.children ??= []).push(newNode);
        }
      }
    });
  };
  const updateTaskS = async (
    task: TaskTreeProps,
    newTask: Partial<TaskTreeProps> = {},
    location: boolean = false
  ) => {
    const resTask = {
      ...task,
      ...newTask,
    };
    const { node } = findNodeById(newTask.id);
    if (location) {
      if (node) {
        Object.assign(node, task);
      }
      return;
    }
    resTask.children = [];
    return addAndUpdateTask({
      ...resTask,
    }).then(res => {
      if (node) {
        res.data.data.children = node.children;
        Object.assign(node, res.data.data);
      }
      return res;
    });
  };
  const deleteTaskS = async (task: TaskTreeProps, isAll: boolean) => {
    return removeTargetNode(task.id, isAll).then(() => {
      const { parentNode, node } = findNodeById(task.id);
      if (!isAll) {
        taskTreeData.value.push(
          ...(node?.children || []).map(it => {
            it.parentId = 0;
            return it;
          })
        );
      }
      return delChildrenNode(node, parentNode);
    });
  };
  return {
    initTaskTreeData,
    currentPackageId,
    taskTreeData,
    addTaskS,
    updateTaskParentS,
    updateTaskS,
    deleteTaskS,
    findNodeById,
  };
});
export default useTaskGraphStore;
