import { ChartProps, EChartsCommonStyle, LegendPositionEnum } from '../types';
import { use } from 'echarts/core';
import { LegendPositionDic } from '../const';
import { DatasetComponent, LegendComponent, TooltipComponent, TransformComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { AxisInit, AxisInitParams, AxisMain, SeriesLabelTypes, TooltipTypes } from '../const/echarts.ts';
import { SortEnum } from '../types/widget.ts';

const useEchartsOption = <T extends EChartsCommonStyle>(
  props: ChartProps<T>,
  initSeries: (seriesLabel: any) => any[],
  echartsComp: any[] = []
) => {
  use([
    TooltipComponent,
    CanvasRenderer,
    LegendComponent,
    DatasetComponent,
    TransformComponent,
    ...echartsComp
  ]);

  const option = computed(() => {
    const r: any = {};
    if (props.chartStyle.showLegend) {
      r['legend'] = {
        ...LegendPositionDic
          .find(item => item.value === (props.chartStyle.legendPosition ?? LegendPositionEnum.BOTTOM))!
          .config
        // formatter: (name: any) => {
        //   const data = props.chartData.find(item => item[labelKey] == name)[props.metrics[0].id];
        //   const sum = _.sum(props.chartData.map(item => item[valueKey]));
        //   return `${name} ${_.round(data / sum, 2) * 100}%`;
        // }
      };
    }
    r['tooltip'] = TooltipTypes[props.chartType];
    let datasetIndex = '0';
    r['dataset'] = [{
      source: props.chartData
    }];
    // 找排序
    const find = [...props.dimensions, ...props.metrics]
      .find(item => item.sort === SortEnum.ASC || item.sort === SortEnum.DESC);
    if (find) {
      datasetIndex = '1';
      r['dataset'].push({
        transform: {
          fromDatasetIndex: '0',
          type: 'sort',
          config: {
            dimension: find.id,
            order: find.sort
          },
          print: true
        }
      });
    }

    //  系列配置
    r['series'] = [];
    const other = initSeries(
      SeriesLabelTypes[props.chartType](props.chartStyle)
    );
    other.forEach(item => item['datasetIndex'] = datasetIndex);
    r['series'].push(...other);
    let maxTargetValue: number | undefined = undefined;
    // 计算target
    if (props.targetList?.length) {
      // 计算target最大值
      maxTargetValue = _.maxBy(props.targetList, 'targetValue').targetValue;

      if (r['series'].length) {
        const axisMain = AxisMain[props.chartType] ?? 'xAxis';
        // 辅助指标
        r['series'][0]['markLine'] = {
          silent: true,
          symbol: 'none',
          lineStyle: {
            color: '#1677ff'
          },
          data: props.targetList.map(it => ({
            [axisMain]: it.targetValue,
            label: {
              position: 'insideEndTop',
              formatter: () => `${it.targetName}: ${it.targetValue ?? ''}`
            }
          }))
        };
      }
    }
    // 初始化坐标轴
    const axiosParams: AxisInitParams = {
      chartStyle: props.chartStyle,
      maxTargetValue
    };
    const axis = AxisInit[props.chartType];
    r['xAxis'] = axis?.xAxis(axiosParams);
    r['yAxis'] = axis?.yAxis(axiosParams);

    return r;
  });

  return {
    option
  };
};

export default useEchartsOption;