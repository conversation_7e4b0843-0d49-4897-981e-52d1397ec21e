import request from '@/axios';
import { ExecutorBatchType } from '@/types/taskPackageTypes';

export const getTaskBindWorkGroupIdsByTaskId = (params: { taskId: string }) => {
  return request({
    url: '/scene_task_group/getTaskBindWorkGroupIdsByTaskId',
    method: 'get',
    params,
  });
};
/**
 * 获取场景集树
 * @param params 接口参数
 */
export const getTaskPackage = (params?: any) => {
  return request({
    url: '/scene_task_group/tree',
    method: 'get',
    params,
  });
};
/**
 * 添加场景集
 * @param params 接口参数
 */
export const taskPackageAdd = (params: { parentId?: string; tid: string; groupName: string }) => {
  return request({
    url: '/scene_task_group/add',
    method: 'post',
    data: params,
  });
};
/**
 * 修改场景集
 * @param params 接口参数
 */
export const taskPackageUpdate = (params: { id: string; groupName: string }) => {
  return request({
    url: '/scene_task_group/update',
    method: 'post',
    data: params,
  });
};

/**
 * 根据场景集id查询当前场景树
 * @param params 接口参数
 */
export const getTreeBySceneGroupId = (params: { sceneId: string }) => {
  return request({
    url: '/scene_task_group/sceneTaskGroup/treeBySceneGroupId',
    method: 'get',
    params,
  });
};

// 场景集移除
export const taskPackageRemove = (params: { tid: string; id: string }) => {
  return request({
    url: '/scene_task_group/remove',
    method: 'delete',
    params,
  });
};
// 场景集修改
export const getTaskPackageUpdate = (params: { groupName: string; id: string }) => {
  return request({
    url: '/scene_task_group/update',
    method: 'delete',
    params,
  });
};
// 场景集修改
export const bindWorkGroup = (data: { relWorkGroupIds: string; sceneGroupId: string }) => {
  return request({
    url: '/scene_task_group/bindWorkGroup',
    method: 'post',
    data,
  });
};

/**
 * 场景集任务分页
 * @param params 接口参数
 */
export const getTaskPackagePage = (params: {
  taskName?: string;
  current: number;
  sceneTaskGroupId: string;
  size: number;
  tenantId: string;
  versionId:string;
  addExecutorFlag:null | number
}) => {
  return request({
    url: '/scene_task_group/task/page',
    method: 'get',
    params,
  });
};
/**
 * 根据场景集返回下面所有任务
 * @param params 接口参数
 */
export const getAllTaskListByPackageId = (params: { id: string }) => {
  return request({
    url: '/scene_task_group/getTaskListBySceneId',
    method: 'get',
    params,
  });
};

/**
 * 场景集添加任务
 * @param params 接口参数
 */
export const getTaskPackagePageAdd = (params: {
  groupIds: string;
  taskName: string;
  tid: string;
}) => {
  return request({
    url: '/scene_task_group/task/add',
    method: 'post',
    data: params,
  });
};
/**
 * 场景集编辑任务
 * @param params 接口参数
 */
export const getTaskPackagePageUpdate = (params: {
  groupIds: string;
  taskName: string;
  taskId?: string;
  tid: string;
}) => {
  return request({
    url: '/scene_task_group/task/update',
    method: 'put',
    data: params,
  });
};

/**
 * 根据版本查所有任务
 * @param params 接口参数
 */
export const getTaskListByVersionId = (params: { id: string }) => {
  return request({
    url: '/scene_task_group/getTaskListByVersionId',
    method: 'get',
    params,
  });
};

/**
 * 立即触发
 */
export const instantTriggerTask = (data: { id: string }) => {
  return request({
    url: '/scene_task_group/instantTrigger',
    method: 'post',
    data,
  });
};

/**
 * 任务页面提示
 */
export const getTaskPageTip = (params: { groupId: string }) => {
  return request({
    url: '/scene_task_group/getTaskPageTip',
    method: 'get',
    params,
  });
};
//自定义执行人下拉框
export const getSelfExecutorSelectBox = (params: {
  groupId: string;
  taskName?: string;
  executorType: number;
}) => {
  return request({
    url: '/scene_task_group/selfExecutorSelectBox',
    method: 'get',
    params,
  });
};
//批量设置执行人
export const setExecutorBatch = (data: ExecutorBatchType) => {
  return request({
    url: '/scene_task_group/setExecutorBatch',
    method: 'put',
    data,
  });
};
export const taskDetailSort = (data: any) => {
  return request({
    url: `/scene_task_group/sort`,
    method: 'post',
    data,
  });
};

/**
 * 任务超市列表
 * @param current
 * @param size
 * @param params 接口参数
 */
export interface TaskPackageMarketProps {
  delist: string;
  downloadNum: string;
  id: string;
  listingTime: string;
  taskPackageName: string;
  tenantName: string;
}
export const taskPackageMarket = (
  current: number,
  size: number,
  params: Partial<TaskPackageMarketProps> = {}
) => {
  return request({
    url: '/nbxt-service-task/taskPackage/listing/page',
    method: 'post',
    params: {
      current,
      size,
    },
    data: {
      ...params,
    },
  });
};
/**
 * 添加任务包
 * @param data
 */
export const addTaskPackage = (data: object) => {
  return request({
    url: '/nbxt-service-task/taskPackage/submit',
    method: 'post',
    data: {
      ...data,
    },
  });
};
/**
 * 上架任务包 / 上传到微应用
 * @param id
 * @param info
 */
export const listingTaskPackage = (info: {
  detail?: string;
  name: string;
  id: string;
  tasks: string;
}) => {
  return request({
    url: '/task_market/listing',
    method: 'post',
    data: { ...info },
  });
};
/**
 * 上架任务包 / 微应用自动下载
 * @param id
 * @param info
 */
export const automaticDownloadTaskPackage = (versionId: string) => {
  return request({
    url: '/task_market/automaticDownload',
    method: 'post',
    params: { versionId },
  });
};

/**
 * 下架任务包
 * @param id
 */
export const outTaskPackage = (id: string) => {
  return request({
    url: '/nbxt-service-task/taskPackage/delist',
    method: 'post',
    params: {
      id,
    },
  });
};
/**
 * 任务超市意见列表
 * @param id
 */
export const marketTaskInfo = (id: string) => {
  return request({
    url: '/nbxt-service-task/taskPackage/listing/info/list',
    method: 'post',
    params: {
      id,
    },
  });
};
/**
 * 任务超市意见列表
 * @param id
 */
export const downloadTaskMarket = (id: string) => {
  return request({
    url: '/nbxt-service-task/taskPackage/download',
    method: 'post',
    params: {
      id,
    },
  });
};
/**
 * 删除任务包
 * @param ids
 */
export const delTaskPackage = (ids: string) => {
  return request({
    url: '/nbxt-service-task/taskPackage/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};
/**
 * 获取工作组
 *
 */
export const getDutyManageList = (taskPackageId: string) => {
  return request({
    url: '/nbxt-service-task/dutyManage/list',
    method: 'post',
    data: {
      taskPackageId,
    },
  });
};
/**
 * 获取工作组下拉
 *
 */
export const getDutySelectList = (taskPackageId: string) => {
  return request({
    // url: "/nbxt-service-task/dutyManage/workingGroupTreeList",
    url: '/nbxt-service-task/dutyManage/list',
    method: 'post',
    data: {
      taskPackageId,
    },
  });
};

/**
 * 获取工作组
 *
 */
export const delDuty = (ids: string) => {
  return request({
    url: '/nbxt-service-task/dutyManage/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};
/**
 * 新增工作组
 */
export const addDutyManage = (params: object, taskPackageId?: string) => {
  return request({
    url: '/nbxt-service-task/dutyManage/submit',
    method: 'post',
    data: {
      ...params,
      taskPackageId,
    },
  });
};

/**
 * 获取任务
 * @param taskPackageId 任务包id
 */
export const getTask = (taskPackageId: string) => {
  return request({
    url: '/nbxt-service-task/task/tree',
    method: 'post',
    params: {
      taskPackageId,
    },
  });
};

export const getTargetTree = () => {
  console.log('getTree');
  return request({
    url: '/metrics/showKanban',
    method: 'get',
  });
};
//获取组织的版本通过tenantId
export const getOrgByTenantId = (tenantId: string) => {
  return request({
    url: '/scene_task_group/getVersionListByTenantId',
    method: 'get',
    params: { tenantId },
  });
};
//获取组织的版本和应办数量通过tenantId
export const getOrgByTenantIdWidthCount = (tenantId: string) => {
  return request({
    url: '/scene_task_group/getVersionListWithHandledCount',
    method: 'get',
    params: { tenantId },
  });
};
//获取微应用树形结构
export const getApplicationTree = (tid: string) => {
  return request({
    url: '/scene_task_group/micro/application/tree',
    method: 'get',
    params: { tid },
  });
};
//分组树包含任务
export const getSceneTaskGroup = (id: string) => {
  return request({
    url: '/scene_task_group',
    method: 'get',
    params: { id },
  });
};

