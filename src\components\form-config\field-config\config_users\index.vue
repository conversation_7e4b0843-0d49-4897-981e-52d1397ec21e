<template>
  <div>
    <config-title :data="data"></config-title>
    <config-permission :data="data">
      <el-checkbox v-model="data.isShowDept" label="展示部门"/>
      <el-checkbox v-model="data.isShowWork" label="展示工作组"/>
    </config-permission>
    <el-form-item label="选择范围" v-if="isShowRange">
      <el-select v-model="data.userRange" placeholder="请选择成员的控制范围">
      <el-option
        v-for="item in userRangeDic"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    </el-form-item>
    <config-default-type :data="data"/>
    <config-span :data="data"></config-span>
  </div>
</template>

<script setup lang="ts">
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import { UsersAllField } from './type';
import { userRangeDic, UserRangeEnum } from './const';
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic';

const props = defineProps<{
  data: UsersAllField;
}>();

const isShowRange = computed(() => {
  return !_.includes([DefaultTypeEnum.ALLUSER, DefaultTypeEnum.EXTERNALUSER, DefaultTypeEnum.INTERNALUSER],props.data.defaultType)
});

watchEffect(() => {
  if(!props.data.userRange){
    props.data.userRange = UserRangeEnum.ALL
  }
  if(!isShowRange.value){
    props.data.userRange = UserRangeEnum.ALL
  }
});
</script>

<style scoped lang="scss"></style>
