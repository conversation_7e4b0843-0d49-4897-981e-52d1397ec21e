<template>
  <!--  123<el-cascader-->
  <!--    class="w-full"-->
  <!--    popper-class="m_cascader"-->
  <!--    v-model="vModel"-->
  <!--    v-bind="$attrs"-->
  <!--    :options="options"-->
  <!--    :collapse-tags="$attrs.disabled"-->
  <!--    :collapse-tags-tooltip="$attrs.disabled"-->
  <!--    @focus="focusHandle"-->
  <!--    :props="computedProps"-->
  <!--  ></el-cascader>-->
  <van-field
    v-model="vModel"
    is-link
    readonly
    v-bind="props"
    label=""
    @click="open"
  />
  <van-popup v-model:show="show" round position="bottom">
    <van-cascader
      :field-names="{
        text: props.props.label, value: props.props.value
      }"
      title="请选择所在地区"
      :options="options"
      @close="show = false"
      @finish="onFinish"
    >
      <template #option="{option,selected}">
        {{option[props.props.label]}}
      </template>
    </van-cascader>
  </van-popup>
</template>

<script setup>
import { useVModel } from '@vueuse/core';

defineOptions({
  name: 'xt-m-cascader'
});

const props = defineProps(['placeholder','modelValue', 'dicData', 'dic', 'multiple', 'props']);

const emits = defineEmits(['update:modelValue']);

const vModel = useVModel(props, 'modelValue', emits);

const options = computed(() => props.dicData || props.dic);

const show=ref(false)

const open=()=>{
  show.value = true;
}

const onFinish=(a)=>{
  console.log(a,vModel.value);
}

const computedProps = computed(() => {
  return {
    ...props.props,
    multiple: props.multiple
  };
});

const focusHandle = (e) => {
  e.target.blur();
};
</script>

<style lang="scss">
.m_cascader {
  width: 90%;
  overflow: scroll;
}
</style>
