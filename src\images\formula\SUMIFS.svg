<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 44</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-910.000000, -1240.000000)">
            <g id="编组-44" transform="translate(910.000000, 1240.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <text id="SUMIFS函数可以使同时满足多个条件的" font-family="PingFangSC-Regular, PingFang SC"
                      font-size="14" font-weight="normal" line-spacing="23">
                    <tspan x="10" y="57" fill="#3F70FF">SUMIFS</tspan>
                    <tspan x="61.436" y="57" fill="#3A3A3A">函数可以使同时满足多个条件的数字相</tspan>
                    <tspan x="10" y="80" fill="#3A3A3A">加并返回和。</tspan>
                    <tspan x="10" y="104" fill="#3A3A3A">·用法：</tspan>
                    <tspan x="59" y="104" fill="#3F70FF">SUMIFS</tspan>
                    <tspan x="110.436" y="104" fill="#3A3A3A">(数组_求和区域, 数组_判断区域</tspan>
                    <tspan x="10" y="127" fill="#3A3A3A">1, 逻辑表达式1, 数组_判断区域2, 逻辑表达式</tspan>
                    <tspan x="10" y="150" fill="#3A3A3A">2, ...)</tspan>
                    <tspan x="10" y="174" fill="#3A3A3A">·示例：</tspan>
                    <tspan x="59" y="174" fill="#3F70FF">SUMIFS</tspan>
                    <tspan x="110.436" y="174" fill="#3A3A3A">([1,1,1,1,1], [A,B,C,D,A], "A",</tspan>
                    <tspan x="10" y="197" fill="#3A3A3A">[a,b,c,d,e], "a")，可得到既满足A种类又满足a种</tspan>
                    <tspan x="10" y="220" fill="#3A3A3A">类对应的数字求和结果为1。</tspan>
                </text>
                <text id="SUMIFS" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">SUMIFS</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
