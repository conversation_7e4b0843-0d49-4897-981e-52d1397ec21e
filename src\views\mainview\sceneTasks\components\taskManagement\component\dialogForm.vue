<template>
  <component
    :is="`el-${dialogType}`"
    :title="title"
    v-model="visible"
    @closed="closed()"
    :with-header="withHeader"
    append-to-body
    @opened="opened()"
    :width="width"
    v-bind="$attrs"
  >
    <!-- <template #title="scoped"><slot name="title"></slot></template> -->
    <template #header="">
      <slot name="header"></slot>
    </template>
    <div class="dialog_form_body">
      <el-scrollbar class="scroll_body">
        <el-form
          :model="vModel"
          v-bind="formBind()"
          class="form_body"
          :label-position="labelPosition"
          ref="dFormRef"
          @submit.prevent="submit()"
          :rules="rules"
        >
          <slot></slot>
        </el-form>
      </el-scrollbar>
      <div class="btn_bottom">
        <slot name="foot">
          <el-button type="primary" v-if="submitBtn" @click="submit()">{{ submitText }}</el-button>
          <el-button @click="close()">取消</el-button>
        </slot>
      </div>
    </div>
  </component>
</template>

<script setup lang="ts">
import { ref, useAttrs } from 'vue';
import { FormInstance, FormRules } from 'element-plus';

const dFormRef = ref<FormInstance>();
const visible = ref<boolean>(false);
type Props = {
  title?: string;
  submitText?: string;
  rules?: FormRules;
  dialogType?: 'dialog' | 'drawer';
  labelPosition?: 'left' | 'top' | 'right';
  direction?: 'rtl' | 'ltr' | 'ttb' | 'btt';
  width?: number | string;
  withHeader?: boolean;
  submitBtn?: boolean;
  dataType?: 'Array' | 'Object';
};
const emit = defineEmits(['closed']);
const props = withDefaults(defineProps<Props>(), {
  title: '提示',
  submitText: '确定',
  dialogType: 'dialog',
  labelPosition: 'left',
  width: '50%',
  // direction: 'rtl',
  withHeader: true,
  submitBtn: true,
  dataType: 'Object',
});
const attrs = useAttrs();

function formBind() {
  return attrs;
}
const vModel = defineModel<any>({ default: {} });
let callbackFunc: ((form: any) => void) | undefined;
const open = (callback?: (form: any) => void) => {
  dFormRef.value?.clearValidate();
  visible.value = true;
  callbackFunc = callback;
};
const opened = (callback?: (e?: any) => void) => {
  _.isFunction(callback) && callback();
};
const closed = () => {
  dFormRef.value?.resetFields();
  vModel.value = props.dataType === 'Array' ? [] : {};
  emit('closed');
};
const close = (callback?: (e?: any) => void) => {
  visible.value = false;
  _.isFunction(callback) && callback();
};

const validate = (callback?: (e: boolean) => void) => {
  return dFormRef.value?.validate(callback);
};
const submit = () => {
  validate((valid: boolean) => {
    valid && callbackFunc?.(vModel.value);
  });
};
export interface DialogFormIns {
  open: <T>(callback?: (form: T) => void) => void;
  close: (callback?: (e?: any) => void) => void;
  opened: (callback?: (e?: any) => void) => void;
  validate: (callback?: (valid?: boolean) => void) => void;
  submitForm: () => void;
}

defineExpose<DialogFormIns>({
  open,
  close,
  opened,
  validate,
  submitForm: submit,
});
</script>

<style scoped lang="scss">
.btn_bottom {
  display: flex;
  justify-content: end;
}

.dialog_form_body {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .scroll_body {
    padding: 10px 0;
  }
}
</style>
