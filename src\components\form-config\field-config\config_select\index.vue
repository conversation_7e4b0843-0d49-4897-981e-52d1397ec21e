<template>
  <config-title :data="data" />
  <config-permission :data="data" />
  <el-form-item label="类型">
    <avue-select v-model="selectType" :dic="selectTypeDicFilter" :clearable="false" />
  </el-form-item>
  <template v-if="data.defaultType === DefaultTypeEnum.CUSTOM || data.defaultType === DefaultTypeEnum.LINK">
    <Suspense>
      <AsyncDic :data="data" />
      <template #fallback>
        <el-form-item label="选项内容">
          <el-skeleton />
        </el-form-item>
      </template>
    </Suspense>
  </template>
  <select-rule :data="data"></select-rule>
  <config-default-type :data="data" v-if="formType!== formTypeEnum.KNOWLEDGE"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import { selectTypeDic, mulSelectTypeDic } from '@/components/form-config/field-config/const';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import SelectRule from './components/SelectRule.vue';
import AsyncDic from './components/SelectDic.vue'
import {  SelectAllField } from './type';
import { CustomDefaultType } from '@/components/form-config/types/AvueTypes'
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic.ts';
import { mulSelectType } from '@/components/form-config/types/FieldTypes';
// const AsyncDic = defineAsyncComponent(() => import('@/components/form-config/common-config/select/SelectDic.vue'));
import { formTypeEnum } from '@/components/form-config/const/commonDic';

const formType = inject('formType', null)
const props = defineProps<{
  data: SelectAllField
}>();
interface colType{
  type: string;
  multiple?: boolean;
  component?: string;
}
const selectTypeDicFilter = computed(() => {
  return _.includes(mulSelectType,props.data.fixType) ? mulSelectTypeDic: selectTypeDic;
});

const selectType = computed({
  get: () => {
    return props.data.selectType;
  },
  set: v => {
    props.data.selectType = v;
    const col =  selectTypeDicFilter.value.find((item:any) => item.value === v)!.column
    props.data.type = col.type;
    props.data.multiple = col.multiple;
    props.data.component = (col as colType).component;

    if (!(col as colType).component) {
      delete props.data.component;
    }
    if (_.isNil(col.multiple)) {
      delete props.data.multiple;
    }
  },
});

watch(
  () => props.data.selectType,
  () => {
    (props.data as CustomDefaultType).defaultValue = undefined;
  }
);

watchEffect(() => {
  if (props.data.isCrud) {
    // selectType.value = SelectTypeEnum.SINGLE_SELECT;
  }
});
</script>
