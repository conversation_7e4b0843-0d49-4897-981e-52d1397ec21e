import useChartTypeStyleAndStore from '../../../../hooks/useChartTypeStyleAndStore.ts';
import { EChartsCommonStyle } from '../../../../types';
import { LegendPositionDic } from '../../../../const';

export default [
  {
    header: '数值显示',
    render: defineComponent(() => {
      const { chartStyle } = useChartTypeStyleAndStore<EChartsCommonStyle>();

      return () => (
        <div>
          <el-form-item class="no-title">
            <el-checkbox v-model={chartStyle.value.showValue}>显示数值</el-checkbox>
          </el-form-item>
          <el-form-item label="字体大小">
            <el-input-number
              v-model={chartStyle.value.valueSize}
              min={12}
              precision={0}
            />
          </el-form-item>
          <el-form-item label="字体颜色">
            <el-color-picker
              v-model={chartStyle.value.valueColor}
            />
          </el-form-item>
        </div>
      );
    })
  },
  {
    header: '图例',
    render: defineComponent(() => {
      const { chartStyle } = useChartTypeStyleAndStore<EChartsCommonStyle>();

      return () => (
        <div>
          <el-form-item class="no-title">
            <el-checkbox v-model={chartStyle.value.showLegend}>显示图例</el-checkbox>
          </el-form-item>
          <el-form-item class="no-title">
            <el-select-v2
              placeholder="请选择 图例位置"
              v-model={chartStyle.value.legendPosition}
              options={LegendPositionDic}
            >
            </el-select-v2>
          </el-form-item>
        </div>
      );
    })
  }
];