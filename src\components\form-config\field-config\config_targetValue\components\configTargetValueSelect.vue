<template>
  <el-form-item label="指标体系">
    <avue-select v-model="data.targetId" :dic="targetDic" button :props="{ label: 'targetName', value: 'id' }" @change="change"></avue-select>
  </el-form-item>
</template>
<script setup lang="ts">
import { getNameValueList } from '@/api/taskManagement/componentFetch';
import { TargetValueAllType } from '../type';

const props = defineProps<{
  data: TargetValueAllType;
}>();

type Target = {
  id:string;
  targetName:string;
  targetValue:string;
}

const targetDic = ref<Target[]>([]);

async function initDic (){
  const { data:res } = await getNameValueList();
  if(res && res.code === 200){
    targetDic.value = res.data || []
  }
}
const change = (item:any) => {
  props.data.defaultValue = item.item.targetValue

}

onMounted(()=>{
  initDic()
})
</script>
<style lang="scss" scoped></style>
