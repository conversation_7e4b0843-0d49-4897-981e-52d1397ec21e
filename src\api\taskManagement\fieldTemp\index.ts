import request from '@/axios';
import { FieldTemp } from './type';

//字段模板列表
export const getFieldTempList = (params: FieldTemp.ReqParams, data: any) => {
  return request({
    url: '/template_form/pageByScene',
    method: 'post',
    data,
    params,
  });
};
//字段模板列表不分页
export const getFieldTempListNoPage = (params: { sceneId: string },data: any) => {
  return request({
    url: '/template_form/listByScene',
    method: 'post',
    data,
    params,
  });
};

//字段模板删除
export const removeTemp = (params: { ids: string }) => {
  return request({
    url: '/template_form/remove',
    method: 'post',
    params,
  });
};

//字段模板详情
export const getFieldTempDetails = (params: { formid?: string }) => {
  return request({
    url: '/template_form_detail/detailById',
    method: 'post',
    params,
  });
};

//保存
export const submitFieldTemp = (data: FieldTemp.subParams) => {
  return request({
    url: '/template_form/submit',
    method: 'post',
    data,
  });
};
