<script setup lang="ts">
import { FolderTreeTypes } from '@/views/document/types/docTypes.ts';
import { addFolderTree, delFolderTree, getFolderTreeList, sortFolderTree, updateFolderTree } from '@/api/doc';
import { ElMessageBox, ElMessage } from 'element-plus';
import useAuthHeader from '@/hooks/useAuthHeader.ts';
import DialogForm, { DialogFormIns } from '@/views/mainview/sceneTasks/components/taskManagement/component/dialogForm.vue';
import { toArray } from 'tree-lodash';
import { Refresh } from '@element-plus/icons-vue';
const dialogFormRef = ref<DialogFormIns>();

const rowForm = ref<FolderTreeTypes>({});

const inputRef = ref();

const fileList = ref<{url:string,name:string}[]>([]);

const treeData = ref<FolderTreeTypes[]>([]);

const accept=".md,.txt"

const onLoad = () => {
  getFolderTreeList().then(res => {
    treeData.value = res.data.data;
  });
};

const rowEdit = (row: FolderTreeTypes) => {
  rowForm.value = _.cloneDeep(row);
  if (row.parentId===0) rowForm.value.parentId=''
  if (row.fileUrl){
    fileList.value.push({ url: row.fileUrl,name:row.fileUrl})
  }
  dialogFormRef.value?.open<FolderTreeTypes>(form => {
    if (!form.parentId){
      form.parentId=0
    }
    updateFolderTree(form).then(res => {
      if (res.data.success) {
        ElMessage.success(res.data.msg);
        onLoad();
        dialogFormRef.value?.close();
      }
    });
  });
};

const rowAdd = () => {
  dialogFormRef.value?.open(form => {
    addFolderTree(form).then(res => {
      if (res.data.success) {
        ElMessage.success(res.data.msg);
        onLoad();
        dialogFormRef.value?.close();
      }
    });
  });
};
const { headers } = useAuthHeader();

const ossFile = ref<string>('');

const uploadSuccess = (res: any) => {//直接上传文件返回
  rowForm.value.fileUrl = res.data.link;
};
const uploadOssFile = (res: any) => {//直接上传文件返回
  ossFile.value = res.data.link;
};

const rowDel = (row: FolderTreeTypes) => {
  ElMessageBox.confirm('确定删除该目录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    delFolderTree(row.id).then(res => {
      if (res.data.success) {
        ElMessage.success(res.data.msg);
        onLoad();
      }
    })
  })
}

const closeDialogForm=()=>{
  fileList.value=[]
}
const filterDic = (id: any) => {
  const copyTreeData = _.cloneDeep(treeData.value);
  toArray(copyTreeData).forEach(item=>{
    if (item.id===id){
      item.disabled=true
    }
  })
  return copyTreeData
};

const updateFileUrl=(res:any,form:FolderTreeTypes)=>{
  if (!res) return
  form.fileUrl=res.data.link;
  updateFolderTree(form).then(res => {
    if (res.data.success) {
      ElMessage.success(res.data.msg);
      onLoad();
    }
  });
}
const copyText=async ()=>{
  inputRef.value.select()
  if (ossFile.value){
    await navigator.clipboard.writeText(ossFile.value)
    ElMessage.success('已复制到剪切板')
  }
}
const beforeRemove=(uploadFile: UploadFile, uploadFiles: UploadFiles)=>{
  rowForm.value.fileUrl=""
}
onLoad()

const nodeDrop=()=>{
  sortFolderTree(treeData.value).then(res=>{
    if (res.data.success){
      ElMessage.success(res.data.msg)
      onLoad()
    }
  })
}
</script>

<template>
  <el-card class="mx-10px h-full">
    <div class="flex justify-center">
      <div class="flex gap-10px items-center w-500px">
<!--        <el-tooltip-->
<!--          class="box-item"-->
<!--          effect="dark"-->
<!--          content="若文件中包含图片等附件，请先上传至OSS，再进行上传"-->
<!--          placement="top-start"-->
<!--        >-->
<!--          <el-icon color="#FFBD21"><WarningFilled /></el-icon>-->
<!--        </el-tooltip>-->
        <el-input @click="copyText" ref="inputRef" v-model="ossFile" readonly clearable placeholder="文件地址">
        </el-input>
        <el-upload
          :headers="headers"
          :accept="['image/png', 'image/jpeg', 'image/webp', 'image/gif']"
          :show-file-list="false"
          action="/api/blade-resource/oss/endpoint/put-file"
          :on-success="uploadOssFile"
        >
          <el-button type="primary">上传附件</el-button>
        </el-upload>
        <el-button @click="ossFile=''">清除</el-button>
      </div>
    </div>
    <div class="flex justify-between items-center">
      <el-button type="primary" @click="rowAdd">添加目录</el-button>
      <el-icon class="mr-20px p-5px cursor-pointer"
               color="#606266"
               @click="onLoad"
               style="border-radius: 50%;border: 1px solid #dcdfe6"><Refresh /></el-icon>
    </div>
    <el-table class="mt-10px">
      <el-table-column align="left" label="目录名称" />
      <el-table-column align="left" label="文件" />
      <el-table-column align="right" label="操作" />
    </el-table>
    <el-tree
      :data="treeData"
      draggable
      :indent="10"
      style="border-bottom: 1px solid #ebeef5;"
      class="h-600px overflow-scroll"
      @node-drop="nodeDrop"
      :expand-on-click-node="false"
      node-key="id"
    >
      <template #default="{ node, data }">
        <el-row class="w-full items-center">
          <el-col :span="8" class="mb-0">{{data.aidedName}}</el-col>
          <el-col :style="{'margin-left': (node.level-1) * -7 +'px'}" :span="8" class="mb-0 self-center text-left">
            <el-link v-if="data.fileUrl" :href="data.fileUrl"  class="w-150px">{{data.fileUrl}}</el-link>
            <el-upload
              v-else
              :headers="headers"
              :accept="accept"
              :show-file-list="false"
              action="/api/blade-resource/oss/endpoint/put-file"
              :on-success="(e)=>updateFileUrl(e,data)"
            >
              <el-button size="small" type="primary">上传附件</el-button>
            </el-upload>
          </el-col>
          <el-col :span="8" :style="{'margin-left': (node.level-1) * 7 +'px'}" class="mb-0 text-right">
            <el-button type="primary" link @click="rowEdit(data)">编辑</el-button>
            <el-button type="primary" link @click="rowDel(data)">删除</el-button>
          </el-col>
        </el-row>
      </template>
    </el-tree>
    <dialog-form dialog-type="drawer" @closed="closeDialogForm" ref="dialogFormRef" v-model="rowForm">
      <el-form-item label="目录名称" prop="aidedName">
        <el-input placeholder="请输入目录名称" v-model="rowForm.aidedName"></el-input>
      </el-form-item>
      <el-form-item label="所属目录" prop="parentId">
        <avue-input-tree default-expand-all
                         v-model="rowForm.parentId"
                         :props="{
                             label:'aidedName',
                             value:'id',
                           }"
                         placeholder="请选择内容"
                         :dic="filterDic(rowForm.id)"></avue-input-tree>
      </el-form-item>
      <el-form-item label="上传文件" prop="fileUrl">
        <el-upload
          class="w-full"
          :headers="headers"
          :accept="accept"
          v-model:file-list="fileList"
          action="/api/blade-resource/oss/endpoint/put-file"
          multiple
          :before-remove="beforeRemove"
          :on-success="uploadSuccess"
          :limit="1"
        >
          <el-button type="primary">上传附件</el-button>
        </el-upload>
      </el-form-item>
      <template #footer>
        <el-button type="primary">提交</el-button>
        <el-button>取消</el-button>
      </template>
    </dialog-form>
  </el-card>

</template>

<style scoped lang="scss">
:deep(.el-table__body-wrapper) {
  .el-scrollbar__wrap{
    display: none;
  }
}
:deep(.el-tree-node__content){
  padding: 10px 0;
  box-sizing: content-box;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  max-width: 600px;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>