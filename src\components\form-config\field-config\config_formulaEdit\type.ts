 import {
   FieldBaseField,
   TitleField,
   FormulaField
 } from '@/components/form-config/types/AvueTypes';
import { fieldListType } from '@/api/interface/configDataLink.ts';

/* 公式编辑 */

//公式编辑-过滤条件
export type formulaFilterType = {
  formulaFilter: {
    taskId: string;
    columnList: fieldListType[]
  }
}
export type FormulaEditType = FormulaField & formulaFilterType;
export type FormulaEditAllType = FieldBaseField  & TitleField & FormulaEditType