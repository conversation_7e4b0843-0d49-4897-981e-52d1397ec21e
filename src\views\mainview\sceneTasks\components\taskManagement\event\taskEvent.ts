import mitt from "mitt";
import {TaskTreeProps} from "@/views/mainview/sceneTasks/components/taskManagement/hook/useGraphHook.ts";
import {Cell, Node} from "@antv/x6";
import Properties = Node.Properties;

type TaskEvent = {
    addLR: Partial<TaskTreeProps>
    addBT: { node: Cell, type: 'top' | 'bottom' }
    fold: TaskTreeProps
    taskSelected: Node<Properties>
}
const taskEmitter = mitt<TaskEvent>()

const offTaskEmit = () => {
    taskEmitter.all.clear()
}
export {
    taskEmitter,
    offTaskEmit
}