<template>
  <XtImportDialog 
    v-model="visible" 
    @cancel="handleCancel" 
    @tempDown="downLoadTemplate" 
    :on-success="handleSuccess"
    :upload-server="false" 
    :check="handleCheck" 
    @submit="handleSubmit"
    :loading="loading" 
  >
    <div class="w-full h-full flex items-center" @click="handleImport()">
      <slot>
        <n-button text type="primary" :focusable="false">批量导入</n-button>
      </slot>
    </div>
    <template #error>
      <span class="color-#f44">导入失败条数{{ errNum }}</span>
    </template>
  </XtImportDialog>
  
</template>

<script setup>
import XtImportDialog from '@/components/nbxt/XtImportDialog.vue';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { BanbanTableImportType } from '@/components/form-config/types/FieldTypes.ts';
import { exportTemp, getUserId } from '@/api/banban'
import { generateId } from '@/components/form-config/utils';
import _ from 'lodash';

const props = defineProps(['item', 'disabled', 'tableId','fieldName']);
const emits = defineEmits(['change']);

const visible = ref(false);

// 表头
const tableFromInfo = computed(() => {
  return props.item.children.filter(v => BanbanTableImportType.includes(v.fixType) && !(v.defaultType) && v.display)
});

const tableFromInfo1 = computed(() => {
  return props.item.children.filter(v => BanbanTableImportType.includes(v.fixType) && !(v.defaultType) && v.display).map(item => {
    return {
      id: item.id,
      title: item.copyLabel,
      type: item.fixType,
      content: { ... item}
    }
  })
});

const handleImport = () => {
  visible.value = true;
};

// 取消
const handleCancel = () => {
  visible.value = false;
};

// 下载模板
const downLoadTemplate = () => {
  if (!tableFromInfo1.value.length) {
    return
  }
  exportTemp(tableFromInfo1.value).then(res => {
    saveAs(res.data, props.fieldName || `应办表格-${props.item.copyLabel}`)
  })
};

let fileRaw = null;
const handleSuccess = (response, uploadFile, uploadFiles) => {
  fileRaw = uploadFile.raw;
};

const importDic = ref([]);
const errNum = ref(0)
const defaultReason = '校验失败';

// 进行校验
const handleCheck = () => {
  return new Promise(async (resolve, reject) => {
    try {
      // 获取模板
      const response = await exportTemp(tableFromInfo1.value);
      if (response.data instanceof Blob) {
        const tempV = await response.data.arrayBuffer();
        const tempExcel = XLSX.read(tempV);

        // 获得上传的excel
        const newV = await readFile(fileRaw);
        const newExcel = XLSX.read(newV);
        const result = checkXlsx(tempExcel, newExcel);
        if (!_.isString(result)) {
          // 这里增加成员选择处理
          const hasUsersField = result.result.some(row => 
            Object.keys(row).some(key => key.startsWith('users:'))
          );
          if (hasUsersField) {
            try {
              const processedResult = await processUserPhones(result);
              importDic.value = processedResult;
            } catch (error) {
              reject(result);
              return;
            }
          } else {
            importDic.value = result.result;
          }
          
          errNum.value = result.failedRows;
          resolve(result.len);
        } else {
          reject(result);
        }
      } else {
        reject('Unexpected response type');
      }
    } catch (e) {
      console.log(e);
      reject(defaultReason);
    }
  });
};


function colId(n) {
  let s = '';
  while (n > 0) {
    s = String.fromCharCode(65 + (n - 1) % 26) + s;
    n = Math.floor((n - 1) / 26);
  }
  return s;
}


const tempCol = computed(() => {
  const length = tableFromInfo.value.length;
  return Array.from({ length }, (_, i) => colId(i + 1) + '1');
});

// 手机号验证
const PHONE_REGEX = /^1[3-9]\d{9}$/;

// 处理用户手机号查询用户ID
const processUserPhones = async (checkResult) => {
  const { result: rows } = checkResult;
  const usersFields = [];
  const invalidPhoneRows = new Set();
  
  const excelDataStartRow = 2; 

  rows.forEach((row, rowIndex) => {
    Object.entries(row).forEach(([key, value]) => {
      if (key.startsWith('users:') && typeof value === 'string') {
        const phones = value.split(';').map(phone => phone.trim()).filter(Boolean);
        const invalidPhones = phones.filter(phone => !PHONE_REGEX.test(phone));
        
        if (invalidPhones.length) {
          invalidPhoneRows.add(rowIndex);
          row[key] = [];
        } else if (phones.length > 0) {
          const excelRow = rowIndex + excelDataStartRow;
          usersFields.push({ key, phones, rowIndex, excelRow });
        }
      } else if (key.startsWith('users:')) {
        invalidPhoneRows.add(rowIndex);
        row[key] = [];
      }
    });
  });

  if (usersFields.length === 0) return rows;

  const batchSize = 100;
  const processedRows = JSON.parse(JSON.stringify(rows));

  for (let i = 0; i < usersFields.length; i += batchSize) {
    const batch = usersFields.slice(i, i + batchSize);
    
    const requestData = batch.map(item => ({
      phone: item.phones,
      row: item.excelRow,
      key: item.key
    }));

    try {
      const response = await getUserId(requestData);
      const responseData = Array.isArray(response.data.data) ? response.data.data : [];

      const compositeKeyToRequestItem = new Map();
      batch.forEach(item => {
        const compositeKey = `${item.excelRow}-${item.key}`;
        compositeKeyToRequestItem.set(compositeKey, item);
      });

      responseData.forEach((item) => {
        const { row: excelRow, userId, key } = item;
        if (typeof excelRow !== 'number' || !Array.isArray(userId) || !key) return;

        const compositeKey = `${excelRow}-${key}`;

        const requestItem = compositeKeyToRequestItem.get(compositeKey);
        if (!requestItem) {
          return;
        }

        const { rowIndex } = requestItem;
        
        processedRows[rowIndex][key] = userId;
      });
    } catch (error) {
      batch.forEach(({ rowIndex }) => invalidPhoneRows.add(rowIndex));
    }
  }

  // invalidPhoneRows.forEach(rowIndex => {
  //   Object.keys(processedRows[rowIndex]).forEach(key => {
  //     if (key.startsWith('users:')) {
  //       processedRows[rowIndex][key] = [];
  //     }
  //   });
  // });

  return processedRows;
};

// 模板验证
const checkXlsx = (tempExcel, newExcel) => {
  if (!tempExcel.SheetNames.length || !newExcel.SheetNames.length) {
    return defaultReason;
  }
  const tempSheet = tempExcel.Sheets[tempExcel.SheetNames[0]];
  const newSheet = newExcel.Sheets[newExcel.SheetNames[0]];

  // 对比模板部分
  if (
    !tempCol.value.every(item => {
      return tempSheet[item]?.v === newSheet[item]?.v;
    })
  ) {
    return '模板校验不通过';
  }

  // 检查数据部分
  let sheetToJson = XLSX.utils.sheet_to_json(newSheet, {
    header: 1,
  });

  // 过滤掉表头和空行
  sheetToJson = sheetToJson.filter((item, index) => {
    if (index === 0) return false; // 过滤表头
    return Object.values(item).some(value => value !== undefined && value !== null && value !== '');
  });

  if (_.isEmpty(sheetToJson)) {
    return '请确定有符合条件的数据';
  }

  // 验证10000行总行数
  if (sheetToJson.length > 10000) {
    return '总行数超过10000行';
  }
  
  const padDateTime = (dateTimeStr) => {
    const [datePart, timePart] = dateTimeStr.split(' ');
    const [year, month, day] = datePart.split('-').map(part => part.padStart(2, '0'));
    const [hour, minute, second] = (timePart || '00:00:00').split(':').map(part => part.padStart(2, '0'));

    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')} ${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${second.padStart(2, '0')}`;
  };
  
  // 定义验证和转换函数
  const validateAndTransformData = (data) => {
    const results = [];
    const errorMessages = [];

    data.forEach(row => {
      const result = {};
      let rowErrors = []; // 用于存储当前行的错误信息

      tableFromInfo.value.forEach(info => {
        const { copyLabel, fixType, prop } = info;
        const columnIndex = tempCol.value.indexOf(colId(tableFromInfo.value.indexOf(info) + 1) + '1');
        const cellValue = row[columnIndex];

        if (cellValue === undefined || cellValue === null || cellValue === '') {
          result[prop] = null; // 空值处理
          return;
        }

        let value = typeof cellValue === 'string' ? String(cellValue).trim() : cellValue;

        try {
          switch (fixType) {
            case 'input':
              result[prop] = value;
              break;
            case 'date':
              // // 转换 'yyyy/MM/dd HH:mm:ss' to 'yyyy-MM-dd HH:mm:ss'
              // const date =  XLSX.SSF.format('yyyy-mm-dd hh:mm:ss', value)
              // console.log(date,'date');
              // const newDate = padDateTime(date.replace(/\//g, '-'));
              // if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(newDate)) {
              //   rowErrors.push('日期时间格式错误');
              // } else {
              //   result[prop] = newDate;
              // }
              // break;
              if (typeof value === 'number') {
                value = XLSX.SSF.format('yyyy-mm-dd hh:mm:ss', value);
              } else {
                value = value.replace(/\//g, '-');
              }
              value = padDateTime(value);
              if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(value)) {
                rowErrors.push('日期时间格式错误');
              } else {
                result[prop] = value;
              }
              break;
            case 'number':
              if (!/^-?\d+(\.\d+)?$/.test(value)) {
                rowErrors.push('数字格式错误');
              } else {
                result[prop] = parseFloat(value);
              }
              break;
            case 'daterange':
              const range = value.split('-').map(v => v.trim().replace(/\//g, '-'));
              range.forEach((v, index) => {
                range[index] = padDateTime(v);
              });
              const startDate = new Date(range[0]);
              const endDate = new Date(range[1]);
              if (range.length !== 2 || !range.every(v => /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(v))) {
                rowErrors.push('时间范围格式错误');
              } else if(startDate > endDate) {
                rowErrors.push('开始时间不能大于结束时间');
                break;
              } else {
                result[prop] = range;
              }
              break;
            case 'mulSelect':
              const options = value.split(',').map(v => v.trim());
              result[prop] = options;
              // if (!options.every(option => info.dicData.some(dic => dic.label === option))) {
              //   rowErrors.push('多项选择选项错误');
              // } else {
              //   result[prop] = options;
              // }
              break;
            case 'select':
              if (!info.dicData.some(dic => dic.label === value)) {
                rowErrors.push('单项选择选项错误');
              } else {
                result[prop] = value;
              }
              break;
            case 'users':// 成员选择
              const phones = value.split(';').map(phone => phone.trim()).filter(Boolean);
              const invalidPhones = phones.filter(phone => !PHONE_REGEX.test(phone));
              if (invalidPhones.length) {
                result[prop] = null;
                 rowErrors.push('成员手机号格式错误');
              } else {
                result[prop] = value;
              }
              break;
            default:
              result[prop] = value;
          }
        } catch (error) {
          console.error(`Validation error for ${prop}: ${error.message}`);
          rowErrors.push(`未知错误: ${error.message}`);
        }
      });

      if (rowErrors.length > 0) {
        errorMessages.push({ row: row, errors: rowErrors });
      } else {
        result['id'] = generateId();
        // result['_X_ROW_KEY'] = `row_${generateId()}`;
        results.push(result);
      }
    });
  
    return { results, errorMessages };
  };

  try {
    const { results, errorMessages } = validateAndTransformData(sheetToJson);
    console.log(errorMessages,'errorMessages')
    return {
      result: results,
      len: results.length,
      failedRows: errorMessages.length,
      errorMessages: errorMessages,
    };
  } catch (error) {
    return error.message;
  }
};
const loading = ref(false)
// 读取文件
const readFile = file => {
  const reader = new FileReader();
  reader.readAsArrayBuffer(file);

  return new Promise(resolve => {
    reader.onload = e => {
      resolve(e.target.result);
    };
  });
};

// 完成导入
const handleSubmit = () => {
  if (loading.value) return;
  loading.value = true;

  setTimeout(async () => {
    try {
      console.log(importDic.value,'importDic.value')
      emits('change', importDic.value);
      await nextTick();

      // 等待浏览器空闲时再关闭 loading（确保渲染完成）
      requestIdleCallback(() => {
        loading.value = false;
        visible.value = false
      }, { timeout: 3000 }); // 最多等 3s，避免卡死

    } catch (e) {
      console.error(e);
      loading.value = false;
      
    }
  }, 0);
};
</script>

<style lang="scss"></style>