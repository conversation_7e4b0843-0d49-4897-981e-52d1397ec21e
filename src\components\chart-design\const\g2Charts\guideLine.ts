import { Chart } from '@antv/g2';
import { ChartTypeEnum, Target } from '../../types';

export const initGuideLine = (chart: Chart, chartType: ChartTypeEnum, targetList: Target[]) => {
  if (!targetList.length) {
    return;
  }
  targetList.forEach(item => {
    GuideLineInitWithType[chartType]?.(chart, item);
  });
};

const GuideLineInitWithType: {
  [K in ChartTypeEnum]?: (chart: Chart, target: Target) => void
} = {
  [ChartTypeEnum.COLUMN]: (chart, target) => {
    chart
      .lineY()
      .encode('y', target.targetValue)
      .style('lineDash', [3, 3])
      .label({
        text: () => `${target.targetName}: ${target.targetValue}`,
        position: 'right',
        dy: -8
      });
  },
  [ChartTypeEnum.BAR]: (chart, target) => {
    chart
      .lineY()
      .encode('y', target.targetValue)
      .style('lineDash', [3, 3])
      .label({
        text: () => `${target.targetName}: ${target.targetValue}`,
        position: 'right',
        dy: -8
      });
  },
  [ChartTypeEnum.LINE]: (chart, target) => {
    chart
      .lineY()
      .encode('y', target.targetValue)
      .style('lineDash', [3, 3])
      .label({
        text: () => `${target.targetName}: ${target.targetValue}`,
        position: 'right',
        dy: -8
      });
  }
};