export enum FixTypeEnum {
  /** 文本输入框 */
  INPUT = 'input',
  /** 日期时间 */
  DATE = 'date',
  /** 时间范围 */
  DATERANGE = 'daterange',
  /** 数字 */
  NUMBER = 'number',
  /** 选择框 */
  SELECT = 'select',
  /** 多选择框 */
  MUL_SELECT = 'mulSelect',
  /** 级联多选*/
  MUL_CASCADER = 'mulCascader',
  /** 地址 */
  ADDRESS = 'address',
  /** 分割线 */
  DIVIDER = 'divider',
  /** 动态表单 */
  DYNAMIC = 'dynamic',
  /** 附件上传 */
  UPLOAD = 'upload',
  /** 拍照 */
  PHOTO = 'photo',
  /** 文字说明 */
  TIPS = 'tips',
  /** 成员选择 */
  USERS = 'users',
  /** 工作组选择 */
  USER_TAG = 'userTag',
  /** 部门选择 */
  USER_DEPT = 'userDept',
  /** 单位选择 */
  BASIC_INFO = 'basicInfo',
  /** 手写签名 */
  SIGNATURE = 'signature',
  /** 签名汇总 */
  SIGNATURE_COLLECT = 'signatureCollect',
  /** 公式编辑 */
  FORMULA_EDIT = 'formulaEdit',
  /** 物品组件 */
  INVENTORY_COM = 'inventoryCom',
  /** 空间组件 */ 
  INTERSPACE_COM = 'interspaceCom',
  /** 统计指标 */
  STATISTICAL = 'statistical',
  /** 目标值 */
  TARGET_VALUE = 'targetValue',
  /** 获取用户ID */
  GET_USERCODE = 'getUsercode',
  /** 部门管理员 */
  DEPART_ADMIN = 'departAdmin',
  /** 工作组管理员 */
  WORKING_ADMIN = 'workingAdmin',
  /** 计算表 */
  CALCULATION_TABLE = 'calculationTable',
  /** 数据选择*/
  DATA_SELECTION = 'dataSelection',
}

export enum SystemTypeEnum {
  /** 提交人 */
  SUBMITTER = 'submitter',
  /** 提交时间 */
  SUBMITTER_TIME = 'submitterTime',
  /** 触发时间 */
  TRIGGER_TIME = 'triggerTime',
  /** 执行人 */
  EXECUTOR_USER = 'executorUser',
  /** 未提交人 */
  NOSUBMIT_USER = 'noSubmitUser',
  /** 最后更新人 */
  ENDUPDATE_USER = 'endUpdateUser',
  /** 最后更新时间*/
  ENDUPDATE_TIME = 'endUpdateTime',
  /** 当前用户 */
  USER_CURRENT = 'UserCurrent',
  /** 创建人 */
  CREAT_USER = 'createUser',
  /** 创建时间 */
  CREAT_TIME = 'createTime',
  /** 更新人 */
  UPDATE_USER = 'updateUser',
  /** 更新时间 */
  UPDATE_TIME = 'updateTime',
  /** 提交人所在部门 */
  SUBMITTER_THE_DEPT = 'submitterTheDept',
  /** 提交人所在工作组 */
  SUBMIT_THE_WORK = 'submitterTheWork',
  /** 提交人的部门管理员 */
  DEPARTMENT = 'departMent',
  /** 提交人的工作组管理员 */
  WORKLEAD = 'workLead',
  /** 提交人的组织管理员 */
  ORGANIZE_ADMIN = 'organizeAdmin',
  /** 未提交人的工作组领导人 */
  UNWORK_LAEAD = 'unworkLead',
  /** 未提交人的部门领导人 */
  UNDEPARTMENT = 'undepartMent',
  /** 数据生成时间，对于任务来说是提交时间，对于数据表、计算表来说是更新时间 */
  GENERATE_TIME = 'generateTime',
  /** 任务引擎数值上下限使用 */
  LOGIN_USER = 'loginUser',
  /** 应办名称配置字段 */
  TASKNAME = 'taskName',
  /** 微应用提交选择部门 */
  DEFAULT_DEPT = 'defaultDept',
  /** 发起人 */
  LAUNCH_USER = 'launchUser',
  /** 发起人所在部门 */
  LAUNCH_DEPT = 'launchDept',
  /** 发起人所在工作组 */
  LAUNCH_DUTY = 'launchDuty',
  /** 发起时间 */
  LAUNCH_TIME = 'launchTime',
  /** 微应用数据选择 */
  DATASELECTION = 'dataSelection',
  /** 单位选择上传logo */
  BASICLOGO = 'basicLogo',
  /** 提交时间年月日 */
  SUBMITTER_DATE = 'submitterDate',
  /** 触发时间年月日 */
  TRIGGER_DATE = 'triggerDate',
  
}
