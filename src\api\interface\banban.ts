// 分页响应参数
export interface ResPage<T> {
    records: T[];
    size: number;
    total: number;
}

// 分页请求参数
export interface ReqPage {
    current: number;
    size: number;
}

export namespace Banban {
    export interface reqParams {
        searchBar: string,
        urgency: string;
        handleStatus:number
        packageId?:string; 
        versionId?:string;// 版本id
        taskSource:string; // 组织id
        sort: string | number
        tenantId:string;
    }

    export interface resList {
        viewStatus:number;
        id:string|number;
        urgency:string;
        operate:number;
        name:string;
        beginDuration:string;//任务期限开始
        endDuration:string;//任务期限结束
        packageId: string;
        taskId: string;
        agree: number;
        taskSource: string;
        tenantId: string
        instantName:string;
        // [propName:string]:string
        
    }
}

export interface banbanDetailsType {
    formId: string;
    data: string;
    list: any[];
    id: string;
    type:string;
    handleId: string
}

//整体联动
export interface allDoLinkageType {
    taskId: string;
    handledId: string;
    formData: Record<string, any>;
}

