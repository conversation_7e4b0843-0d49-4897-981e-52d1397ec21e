
import {
  addressType,
  selectAllType,
  mulCascaderType,
  uploadType,
  tipsType,
  dynamicLikeField,
  signatureCollectType,
  departAdminType,
  workingAdminType,
  noSignTypes,
  signatureType,
  noTriggerTime,
  limitUser,
  dateRangeType
} from '../types/FieldTypes.ts';
export * from '../field-config/const.ts';

//DynamicTypeEnum 和 DefaultTypeEnum 值不要重复
export enum DefaultTypeEnum {
  CUSTOM,
  LINK,
  FORMULA,
  SIGN = 7,//签名组件-实名签名
  TRIGGERTIME = 8,//日期组件-触发时间
  ALLUSER = 15, //成员选择-全部成员
  EXTERNALUSER = 16,//成员选择-全部成员
  INTERNALUSER = 17,//成员选择-内部成员
}

//DynamicTypeEnum 和 DefaultTypeEnum 值不要重复
export enum DynamicTypeEnum {
  DATASELECT = 3,
  KNOWLEDGE = 4,//系统知识库
  HANDLEDATA = 5,
  TRIGGER = 6,
  ORGKNOWLEDGE = 9, //组织知识库
  GROUPSUMMARY = 10 //分组汇总
}


export const defaultTypeDic = [
  {
    label: '自定义',
    value: DefaultTypeEnum.CUSTOM,
    noShowType: [...tipsType],
  },
  {
    label: '联动采集',
    value: DefaultTypeEnum.LINK,
    noShowType: [...departAdminType, ...workingAdminType, ...tipsType],
  },
  {
    label: '算法采集',
    value: DefaultTypeEnum.FORMULA,
    noShowType: [
      ...selectAllType,
      ...mulCascaderType,
      ...addressType,
      ...dateRangeType,
      ...uploadType,
      // ...userTagType,
      // ...userDeptType,
      // ...inventoryComType,
      ...tipsType,
      // ...userAllType,
      ...dynamicLikeField,
      ...signatureCollectType,
      ...signatureType
    ],
  },
  {
    label: '实名签名',
    value: DefaultTypeEnum.SIGN,
    noShowType: [...noSignTypes]
  },
  {
    label: '触发时间',
    value: DefaultTypeEnum.TRIGGERTIME,
    noShowType: [...noTriggerTime]
  },
  {
    label: '全部成员',
    value: DefaultTypeEnum.ALLUSER,
    noShowType: [...limitUser]//成员选择可用
  },
  {
    label: '内部成员',
    value: DefaultTypeEnum.INTERNALUSER,
    noShowType: [...limitUser]//成员选择可用
  },
  {
    label: '外部成员',
    value: DefaultTypeEnum.EXTERNALUSER,
    noShowType: [...limitUser]//成员选择可用
  }
];

export enum LinkTypeEnum {
  ONE_WAY = 1,
  TWO_WAY = 2,
}

export const linkTypeDic = [
  {
    label: '非触发关系',
    value: LinkTypeEnum.ONE_WAY,
  },
  {
    label: '触发关系',
    value: LinkTypeEnum.TWO_WAY,
  },
];

export enum LinkUserTypeEnum {
  ALL = 1,
  USER = 2,
}

export const linkUserTypeDic = [
  {
    label: '提交人',
    value: LinkUserTypeEnum.ALL,
  },
  {
    label: '当前用户',
    value: LinkUserTypeEnum.USER,
  },
];

export enum FirstDoEnum {
  FILTER = '1',
  ORDER = '2',
}

export const firstDoDic = [
  {
    label: '筛选',
    value: FirstDoEnum.FILTER,
  },
  {
    label: '排序',
    value: FirstDoEnum.ORDER,
  },
];

export enum OrderByEnum {
  ASC = '1',
  DESC = '2',
}

export const orderByDic = [
  {
    label: '正序',
    value: OrderByEnum.ASC,
  },
  {
    label: '倒序',
    value: OrderByEnum.DESC,
  },
];

export enum fieldSource {
  SET = 1, //中间的组件设置 
  SETDEFAULT = 2, // 组件的默认值设置
  PREVIEW = 3, //预览
  HANDLED = 4, //应办
  TEMPLATE = 5, //字段库
  APPLICATION = 6 //微应用
}

export enum previewEnum {
   TASK = 1,
}

export enum formTypeEnum {
  TEMPLATE = 1, //模板 
  FORM = 2, // 场景任务
  KNOWLEDGE = 3, //知识库
  APPLICATION = 4 //微应用
}


export const dynamicTypeDic = [
  {
    label: '自定义',
    value: DefaultTypeEnum.CUSTOM,
  },
  {
    label: '联动采集',
    value: DefaultTypeEnum.LINK,
  },
  {
    label: '数据选择',
    value: DynamicTypeEnum.DATASELECT,
  },
  {
    label: '系统知识',
    value: DynamicTypeEnum.KNOWLEDGE,
  },
  {
    label: '组织知识',
    value: DynamicTypeEnum.ORGKNOWLEDGE,
  },
  {
    label: '会办数据汇总',
    value: DynamicTypeEnum.HANDLEDATA,
  },
  {
    label: '逐一触发',
    value: DynamicTypeEnum.TRIGGER,
  },
  {
    label: '分组汇总',
    value: DynamicTypeEnum.GROUPSUMMARY,
  }
];

export enum DynamicDataTypeEnum {
  SINGLE = 1, //单选
  SUMMARY = 2, //汇总
  MULTIPLE = 3, //多选

}

export const dynamicBasicDic = [
  {
    label: '单选',
    value: DynamicDataTypeEnum.SINGLE,
  },
  {
    label: '汇总',
    value: DynamicDataTypeEnum.SUMMARY
  }
];

export const dynamicDataTypeDic = [
  ...dynamicBasicDic,
  {
    label: '多选',
    value: DynamicDataTypeEnum.MULTIPLE
  }
];



export enum HandleTypeEnum {
  HANDLED = '1', //应办
  INSTANTSERVICE = '2',//即时办事
  Examine = '3'//微应用

}


export enum FieldItemSourceEnum {
  Phanerozoite = '1'
}

