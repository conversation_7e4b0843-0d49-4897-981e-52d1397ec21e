<template>
  <el-form-item label="空间因素">
    <el-select-v2
      v-model="factor.spaceTypeList"
      :options="scopeOptions"
      filterable
      multiple
      placeholder="请选择字段"
    />
  </el-form-item>
  <el-form-item label="设定范围">
    <div class="flex items-center gap-10px">
      <el-select-v2 v-model="scope" :options="scopeBaseList" filterable disabled />
      <el-cascader
        v-model="province"
        :props="{ value: 'code', label: 'name' }"
        :options="options"
        class="300px"
      />
      <el-checkbox v-model="factor.ban">禁用</el-checkbox>
      <HintPop content="如果勾选,所填写的空间因素限制使用" />
      <el-icon color="#ff7070" class="ml-30px cursor-pointer" @click="$emit('delete', factor.id)"
        ><DeleteFilled
      /></el-icon>
    </div>
  </el-form-item>
</template>
<script setup lang="ts">
import { spaceFactor } from '../../../types';
import { scopeBaseList } from '../../../const';
import HintPop from './HintPop.vue';
import { DeleteFilled } from '@element-plus/icons-vue';
import useAreaStore from '@/components/form-component/XTAddress/stores/useAreaStore';

const props = defineProps<{
  factor: spaceFactor;
}>();

defineEmits(['delete']);

const scopeOptions = [
  {
    label: '注册所在地',
    value: 0,
  },
  {
    label: '生产经营所在地',
    value: 1,
  },
];

const areaOption = ref<any>([]);
const province = computed({
  get: () => {
    return [props.factor.province, props.factor.city];
  },
  set: value => {
    props.factor.province = value[0];
    props.factor.city = value[1];
  },
});

const scope = ref(1);
const { getArea } = useAreaStore();

onMounted(async () => {
  areaOption.value = await getArea();
});

const initPrecision = (address: any, precision: number) => {
  precision--;
  address?.forEach((item: any) => {
    if (precision < 0) {
      delete item.children;
    } else {
      initPrecision(item.children, precision);
    }
  });
};

const options = computed(() => {
  const result = _.cloneDeep(areaOption.value);
  initPrecision(result, 1);
  return result;
});
</script>
<style lang="scss" scoped></style>
