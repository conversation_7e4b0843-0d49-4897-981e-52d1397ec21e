<!--微应用的数据选择显示的组件-->
<script setup lang="tsx">
import { getMicroDataSelectionFilterData } from '@/api/application/ComponentsFetch.ts';
import { dividerType } from '@/components/form-config/types/FieldTypes.ts';
// @ts-ignore
import { range } from 'lodash';
import {
  AvueForm, DataSelection,
  RequestComponentProps, DynamicAllField, AvueColumns, AvueOption,CommonField
} from '@/components/form-config/types/AvueTypes.ts';
import { useTableOperations } from '@/components/xt-el-crud/tools/useFormat.ts';
import DynamicField from '@/components/form-component/XTDynamic/component/dynamicField.vue';
import { useDataList } from '@/components/xt-el-crud/tools/useData.ts';
import type { TableColumnCtx } from 'element-plus'
import {ElPopover, ElInput, ElIcon } from 'element-plus';
import XtElForm from '@/components/xt-el-form/XtElForm.vue';
import XtElFormItem from '@/components/xt-el-form/XtElFormItem.vue';
import { applicationTransform, formatFormData, removeDynamicKeys } from '@/utils/field';
import { departAdminType, workingAdminType, signatureType, getUsercodeType} from '@/components/form-config/types/FieldTypes.ts';
import { fieldSource } from '@/components/form-config/const/commonDic';
import { MenuDisabledInjectKey, disabledInjectKey } from '@/components/xt-el-form/constant';
import { Search } from '@element-plus/icons-vue';


interface TrueSpan {
  prop:string;
  span:number[]
}

const props = defineProps<DataSelection & RequestComponentProps & {itemForm: AvueForm } & CommonField>();

const showFieldType = [...departAdminType, ...workingAdminType, ...signatureType, ...getUsercodeType] as string[];
provide(MenuDisabledInjectKey, true);
provide(disabledInjectKey, ref(true));



const tableData = ref<any[]>([]);//处理过的所有数据
const searchData = ref<any[]>([]);// 搜索出来的数据
const currentData = ref<any[]>([]);//被选择的处理过的数据
const originalData = ref<any[]>([]);//接口来的原始数据
const visible = ref(false);
const formColumn = ref<any[]>([]);
const flatColumn = ref<DynamicAllField[]>([])
const spanArr = ref<TrueSpan[]>([]);
const searchSpanArr = ref<TrueSpan[]>([]);
const trueSpanArr = ref<TrueSpan[]>([]);
const editVisible = ref(false);
const editData = ref<AvueColumns>()
const editOption = ref<AvueOption>({ column: [] });
const editIndex = ref(0);

const vModel = defineModel<any[]>({default:()=>([])});// 被选择的数据 要提交
// 第一步先把表格的column处理
initFormColumn();

const { spaceList,articleList,deptTreeData,dutyTreeData,allUserList } = useDataList(flatColumn.value)
const { formatterRowToText } = useTableOperations({spaceList,articleList,deptTreeData,dutyTreeData,allUserList})

function transFormColumn(col:any[]){
  const iterator:(data:any[]) => any[] = (data:any[])=>{
    return data.filter(item=>{
      return !dividerType.includes(item.type)
    }).map((item:any)=>{
      return {
        ...item,
        children:item?.children?iterator(item?.children||[]):null,
      }
    })
  }
  return iterator(col||[])
}

// 扁平化表格类的数据，用于最上面的hooks
function flatColumnFn(list:any[]){
  const column:any[] = [];
  list.forEach((item:any)=>{
    column.push(item);
    if(item.children){
      column.push(...item.children);
    }
  })
  return column
}


//获取表单column及提交数据
function initFormColumn(){
  if(!props?.query) return;
  const { showField} = props.query;
  formColumn.value = transFormColumn(showField);
  flatColumn.value = flatColumnFn(formColumn.value);
  // return initFormData()
}



const extractingFields=(list:any[],res:Record<string,any>)=>{
  list.forEach(item=>{
    if(item.children){
      res[item.prop]=[]
      item.children.forEach((it:any[])=>{
        res[item.prop].push(extractingFields(it,{}))
      })
    }else{
      res[item.prop]=item.data
      res['id']=item.id
    }
  })
  return res
}


const flatData=(list:any[])=>{
  return list.map(item => {
    const obj:Record<string,any> = {};
    extractingFields((item?.formDataList || []), obj)
    obj.fatherId = item.id;
    return obj
  })
}

const processDynamicData = (data:Record<string,any>) => {
  
  // 分离 dynamic 字段和其他字段
  const dynamicFields:any[] = [];
  let maxLength = 0;
  // 遍历找出所有 dynamic 字段并记录最长数组
  Object.entries(data).forEach(([key, value]) => {
    if (key.startsWith('dynamic:')) {
      dynamicFields.push({ key, value });
      if (value.length > maxLength) maxLength = value.length;
    }
  });

  // 如果没有 dynamic 字段则返回空数组
  if (dynamicFields.length === 0) return [data];

  // 提取静态字段 (非 dynamic 字段)
  const staticFields = Object.fromEntries(
    Object.entries(data).filter(([key, _]: [string, any]) => !key.startsWith('dynamic:'))
  );

  // 生成结果数组
  const result = [];
  for (let i = 0; i < maxLength; i++) {
    const mergedItem = { ...staticFields };

    // 合并所有 dynamic 字段的当前索引项
    dynamicFields.forEach(({ value }) => {
      if (value[i]) Object.assign(mergedItem, value[i]);
    });
    result.push(mergedItem);
  }
  return result;
};


const scatterData=(listData:any[])=>{
  const newList:any[] = []
  listData.forEach(item=>{
    newList.push(...processDynamicData(item))
  })
  return newList
}

const findDynamicLength=(objs:any)=>{
  let maxLength=0
  Object.entries(objs).forEach(([key, value]:[key:string,value:any]) => {
    if (key.startsWith('dynamic:')) {
      if (value?.length > maxLength) maxLength = value?.length;
    }
  });
  return maxLength
}

const findSpan=(prop:string)=>{
  let span=spanArr.value.find(s=>s.prop===prop)
  if (!span){
    span={
      prop,
      span:[]
    }
    spanArr.value.push(span);
    searchSpanArr.value.push(span);
  }
  return span
}


const mergeCells=(resList:any[])=>{
  resList.forEach(item=>{
    Object.entries(item).forEach(async ([key, _]: [string, any]) => {
      if(key.startsWith('dynamic')||key.startsWith('id')){
      }else{
        const max = findDynamicLength(item)
        if (max === 0) return
        const span = findSpan(key)
        span.span.push(max,...range(max-1).map(()=>0))
      }
    })
  })
}

//处理data
const transFormData=(dataList:any[])=>{
  const result = flatData(dataList);
  const scatterList=scatterData(result);
  mergeCells(result);
  return scatterList
}

// 选择数据
const chooseData = (row:any,index:number) => {
  const fatherId = row.fatherId;
  currentData.value = tableData.value.filter(item=> item.fatherId === fatherId);
  vModel.value = originalData.value.filter(item => item.id === fatherId) as any;
  // 切割span
  trueSpanArr.value = spanArr.value.map(item => {
    return {
      ...item,
      span:item.span.slice(index)
    }
  })
  visible.value = false;
}

const fetchData = async () => {
  const { fieldRule, filter, path, showFieldIds} = props.query;
  const query = { fieldRule, fieldCondition:filter, path, showFieldIds }
  if(!fieldRule || !filter.length || !path || !showFieldIds.length) return
  const res = await getMicroDataSelectionFilterData(query);
  originalData.value = res.data.data?.formDataModels || [];
  tableData.value = transFormData(res.data.data?.formDataModels || []);
  searchData.value = _.cloneDeep(tableData.value);
}

const spanMethod = ({  column, rowIndex }: {column:TableColumnCtx<any>,rowIndex:number}) => {
  for (let i = 0; i < searchSpanArr.value.length; i++) {
    const ele = searchSpanArr.value[i];
    if (column.property == ele.prop) {
      const _row = ele.span[rowIndex];
      const _col = _row > 0 ? 1 : 0;
      return { rowspan: _row, colspan: _col };
    }
    if(column.label == '操作') {
      const _row = ele.span[rowIndex];
      const _col = _row > 0 ? 1 : 0;
      return { rowspan: _row, colspan: _col }
    }
  }
};

const trueSpanMethod = ({  column, rowIndex }: {column:TableColumnCtx<any>,rowIndex:number}) => {
  for (let i = 0; i < trueSpanArr.value.length; i++) {
    const ele = trueSpanArr.value[i];
    if (column.property == ele.prop) {
      const _row = ele.span[rowIndex];
      const _col = _row > 0 ? 1 : 0;
      return { rowspan: _row, colspan: _col };
    }
    if(column.label == '操作') {
      const _row = ele.span[rowIndex];
      const _col = _row > 0 ? 1 : 0;
      return { rowspan: _row, colspan: _col }
    }
  }
};

//编辑表格
const handleEdit = (index:number)=> {
  editVisible.value = true
  editIndex.value = index
  editData.value = applicationTransform(vModel.value[0]?.formDataList || [], undefined, undefined, 2) as AvueColumns;
  editOption.value = {
    column: _.cloneDeep(formColumn.value || []),
  };
}

//提交编辑
const updateData = () => {
  const result = removeDynamicKeys(editData.value, formColumn.value)?.noVisible;
  const newData = formatFormData(result, {}, formColumn.value, null, undefined);
  vModel.value[0].formDataList = newData;
  currentData.value = transFormData(vModel.value);
  editVisible.value = false;
  // 切割span
  trueSpanArr.value = spanArr.value.map(item => {
    return {
      ...item,
      span:item.span.slice(-currentData.value.length),
    }
  })
}

function clearSearch(prop:string){
  popoverSearch.value[prop] = ''
  searchData.value = _.cloneDeep(tableData.value);
  searchSpanArr.value = _.cloneDeep(spanArr.value);
}

function handleSearch(prop:string){
  const sortList:number[] = [];

    searchData.value = tableData.value.filter(((item,index) => {
      const type = prop.split(':#')[0];
      let data = formatterRowToText(item, { prop, type }) || '';
      if(!_.isString(data)){
        data = (data as number).toString();
      }
      const result = data.includes(popoverSearch.value[prop]);
      if (result) {
        sortList.push(index)
      }
      return result;
    }))




  searchSpanArr.value = spanArr.value.map(item => {
    return {
      ...item,
      span:sortList.map(i => item.span[i]),
    }
  })
}

function dynamicSearch(prop:string){
  const sortList:number[] = [];
  const fatherList:string[] = tableData.value.reduce((sum,item) => {
    const type = prop.split(':#')[0];
    let data = formatterRowToText(item, { prop, type }) || '';
    if(!_.isString(data)){
      data = (data as number).toString()
    }
    const result = data.includes(popoverSearch.value[prop]);
    if (result) {
      return [...sum,item.fatherId]
    }else return sum
  },[]);

  searchData.value = tableData.value.filter(((item,index) => {
    if (fatherList.includes(item.fatherId)) {
      sortList.push(index);
      return true;
    }
    return false;
  }))

  searchSpanArr.value = spanArr.value.map(item => {
    return {
      ...item,
      span:sortList.map(i => item.span[i]),
    }
  })
}

const popoverSearch = ref<any>({});
function renderHeader(data: { column: any, $index: number }){
  const { column } = data;
  return (
    <span class="flex items-center gap-2">
      {column.label}
      {column.property && <ElPopover
        placement='bottom'
        width='200'
        trigger="click"
        v-slots={{ reference: () => <ElIcon class="cursor-pointer"><Search /></ElIcon> }}
      >
        <ElInput
          size='small'
          modelValue={popoverSearch.value[column.property]}
          onUpdate:modelValue={(val) => (popoverSearch.value[column.property] = val)}
          placeholder='请输入内容'
        ></ElInput>
        <div class='el-table-filter__bottom'>
            <button disabled={!popoverSearch.value} onClick={column.level === 1 ? () => handleSearch(column.property) : () => dynamicSearch(column.property)}>筛选</button>
            <button onClick={() => clearSearch(column.property)}>重置</button>
        </div>
      </ElPopover>
      }
    </span>
  )
}


watchEffect(() => {
  fetchData();
})

watchEffect(() => {
  currentData.value = transFormData(vModel.value);
  let span:any[] = [];
  for (let i = 0; i < currentData.value.length; i++) {
      span.push(i === 0 ? currentData.value.length : 0);
  }
  trueSpanArr.value = spanArr.value.map(item => {
    return {
      ...item,
      span,
    }
  })

})
</script>

<template>
  <div class="w-full">
    <el-button class="mb-2" @click="visible = true">选择数据</el-button>
    <el-table v-if="currentData?.length" :data="currentData" stripe style="width: 100%" :spanMethod="trueSpanMethod">
      <el-table-column v-for="item in formColumn" :prop="item.prop" :label="item.label" min-width="100">
        <template #default="{ row }">
          <template v-if="item.children">
            <el-table-column v-for="i in item.children" :prop="i.prop" :label="i.label" min-width="100">
              <template #default="{ row }">
                <dynamic-field v-if="!showFieldType.includes(i.fixType)" :item="i" :row :originColumns="item.children" :formatterRowToText></dynamic-field>
                <xt-el-form-item
                  v-else
                  :showLabel="false"
                  v-model="row[i.prop]"
                  :key="i.prop"
                  :item="i"
                  :item-form="row"
                  :itemColumns="formColumn"
                  :col="formColumn"
                  :source="fieldSource.APPLICATION"
                  :is-rules="false"
                  />
              </template>
            </el-table-column>
          </template>


          <template v-else>
            <dynamic-field v-if="!showFieldType.includes(item.fixType)" :item="item" :row :originColumns="formColumn" :formatterRowToText></dynamic-field>
              <xt-el-form-item
              v-else
              :showLabel="false"
              v-model="row[item.prop]"
              :key="item.prop"
              :item="item"
              :item-form="row"
              :itemColumns="formColumn"
              :col="formColumn"
              :source="fieldSource.APPLICATION"
              :is-rules="false"
            />
          </template>

        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right" v-if="!props.disabled">
        <template #default="{  $index }">
          <el-button type="primary" text @click="handleEdit($index)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="visible"
      title="数据选择"
      width="700"
    >
      <el-table :data="searchData" stripe style="width: 100%" :spanMethod="spanMethod">
        <el-table-column v-for="item in formColumn" :prop=" item.children ? '' : item.prop" :label="item.label" :render-header="renderHeader" min-width="100">
          <template #default="{ row }">
            <template v-if="item.children">
              <el-table-column v-for="i in item.children" :prop="i.prop" :label="i.label" min-width="100" :render-header="renderHeader">
                <template #default="{ row }">
                  <dynamic-field v-if="!showFieldType.includes(i.fixType)" :item="i" :row :originColumns="item.children" :formatterRowToText></dynamic-field>
                  <xt-el-form-item
                    v-else
                    :showLabel="false"
                    v-model="row[i.prop]"
                    :key="i.prop"
                    :item="i"
                    :item-form="row"
                    :itemColumns="formColumn"
                    :col="formColumn"
                    :source="fieldSource.APPLICATION"
                    :is-rules="false"
                  />
                </template>
              </el-table-column>
            </template>

            <template v-else>
              <dynamic-field v-if="!showFieldType.includes(item.fixType)" :item="item" :row :originColumns="formColumn" :formatterRowToText></dynamic-field>
                <xt-el-form-item
                v-else
                :showLabel="false"
                v-model="row[item.prop]"
                :key="item.prop"
                :item="item"
                :item-form="row"
                :itemColumns="formColumn"
                :col="formColumn"
                :source="fieldSource.APPLICATION"
                :is-rules="false"
              />
            </template>
            </template>

        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row, $index }">
            <el-button @click="chooseData(row, $index)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      v-model="editVisible"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      title="编辑数据"
      width="50%"
    >
      <el-scrollbar height="600px" v-if="editOption.column?.length">
        <xt-el-form
          v-model="editData"
          :option="editOption">
        </xt-el-form>

      </el-scrollbar>

      <template #footer>
        <el-button type="primary" @click="updateData">确定</el-button>
        <el-button @click="editVisible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>