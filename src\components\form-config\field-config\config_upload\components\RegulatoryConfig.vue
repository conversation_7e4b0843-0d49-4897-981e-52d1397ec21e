<template>
  <div class="max-w-550px">
    <template v-for="h in formLabelOptionTask" :key="h.title">
      <h3>{{ h.title }}</h3>
      <el-form-item
        v-for="item in h.option"
        :key="item.label"
        :label="item.label"
        :prop="item.prop"
        :rules="getRules(item.prop)"
      >
        <el-row :gutter="5" class="w-full" v-if="item.select.length">
          <el-col
            :span="item.select.length === 1 ? 24 : 12"
            v-for="(st, sIndex) in item.select"
            :key="sIndex"
          >
            <template v-if="!st.field">
              <component
                v-if="
                  item.fixType !== 'input' &&
                  item.fixType !== 'select' &&
                  item.fixType !== 'date' &&
                  item.fixType !== 'scope'
                "
                :is="getComponent(st.fixType, st.component, st?.showFormat)"
                v-model="regulatoryData[st.value]"
                :dic="item.dic"
                :placeholder="`请选择${item.label}`"
                :data="{ disabled: false, st }"
              />

              <div v-else-if="item.fixType === 'date'">
                <a-date-picker
                  class="w-full"
                  v-if="st.value === 'effectiveTime'"
                  v-model:value="regulatoryData[st.value]"
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  :getCalendarContainer="triggerNode => triggerNode.parentNode"
                />
                <a-date-picker
                  class="w-full"
                  v-else-if="st.value === 'failureTime' || st.value === 'pubTime'"
                  v-model:value="regulatoryData[st.value]"
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  :disabled-date="disabledDateByFailTime"
                  :getCalendarContainer="triggerNode => triggerNode.parentNode"
                />
                <el-date-picker
                  v-else-if="st.value === 'tellTime'"
                  v-model="regulatoryData[st.value]"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm:ss"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择告知时间"
                />
                <template v-else>
                  <component
                    :is="getComponent(st.fixType, st.component, st?.showFormat, st?.label)"
                    v-model="regulatoryData[st.value]"
                    :dic="item.dic"
                    placeholder="请输入"
                    :disabled="item.disabled"
                    v-bind="{ ...st, value: regulatoryData[st.value] }"
                  />
                </template>
              </div>
              <component
                v-else
                :is="getComponent(st.fixType, st.component, st?.showFormat)"
                v-model="regulatoryData[st.value]"
                :dic="item.dic"
                placeholder="请输入"
                :disabled="item.disabled"
                v-bind="{ ...st, value: regulatoryData[st.value] }"
              />
            </template>
            <template v-else>
              <avue-select
                v-model="regulatoryData[st.value]"
                :placeholder="`请选择${st.label}组件`"
                :dic="getField(st.fixType)"
              />
            </template>
          </el-col>
        </el-row>
        <div v-if="item.child?.length" class="w-full">
          <el-row v-for="(ct, cIndex) in item.child" :key="cIndex" :gutter="5">
            <el-col :span="12" v-for="(ctItem, ctInx) in ct.select" :key="ctInx">
              <!-- <el-form-item :prop="childPropValid(item)"> -->
              <template v-if="!ctItem.field">
                <component
                  :is="getComponent(ctItem.fixType, ctItem.component, ctItem?.showFormat)"
                  :placeholder="ctItem.fixType === 'input' ? '请输入' : `请选择${ct.label}`"
                  v-model="regulatoryData[ctItem.value]"
                  v-bind="{ ...ctItem, value: regulatoryData[ctItem.value] }"
                  :data="{ disabled: false }"
                />
              </template>
              <template v-else>
                <avue-select
                  v-model="regulatoryData[ctItem.value]"
                  :placeholder="`请选择${ctItem.label}组件`"
                  :dic="getField(ctItem.fixType)"
                />
              </template>
              <!-- </el-form-item> -->
            </el-col>
          </el-row>
        </div>
      </el-form-item>
    </template>
  </div>
</template>
<script setup>
import useComponents from '@/components/form-config/hooks/useComponents';
import { formLabelOptionTask } from '@/views/mainview/sceneTasks/detail/option/taskFileDic';
import { taskSourceInjectKey } from '@/components/xt-el-form/constant.ts';
import { InstitutionalLevelsDIC } from '@/views/banban/know/regulations/options';
import { useStore } from 'vuex';
import dayJs from 'dayjs';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
// import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';

const { taskBase } = storeToRefs(useTaskDetailStore());

const regulatoryData = defineModel();
const formRef = ref();

const store = useStore();
const tid = computed(() => {
  const userMsg = store.getters.userInfo;
  return userMsg.tid;
});
const userMsg = computed(() => {
  return store.getters.userInfo;
});
provide(taskSourceInjectKey, tid);

let unRequiredFieldArr = ['releaseContent', 'failureTime', 'otherNumber', 'contentSummary'];

const hasRequired = computed(() => item => {
  let valueArr = item.select.map(item => item.value);
  if (valueArr.some(s => unRequiredFieldArr.includes(s))) return false;
  return true;
});

const getField = type => {
  const arr = taskBase.value.column.filter(i => i.fixType === type);
  return arr?.length
    ? arr.map(i => {
        return {
          label: i.copyLabel,
          value: i.prop,
        };
      })
    : [];
};

const disabledDateByEffecTime = current => {
  return (
    current && current > dayJs(regulatoryData.value?.failureTime).endOf('day').subtract(1, 'day')
  );
};
const disabledDateByFailTime = current => {
  return current && current < dayJs(regulatoryData.value?.effectiveTime).endOf('day');
};

const getRules = computed(() => {
  return prop => {
    const data = regulatoryData.value || {};

    switch (prop) {
      case 'systemNameProp':
        return [
          {
            validator: (rule, value, callback) => {
              if (data.systemName || data.systemNameField) {
                callback();
              } else {
                callback(new Error('制度名称不可为空'));
              }
            },
            trigger: 'blur',
            required: true,
          },
        ];

      // case 'unit':
      //   return [
      //     {
      //       validator: (rule, value, callback) => {
      //         if (
      //           data.depId ||
      //           data.depIdField ||
      //           data.workingGroupId ||
      //           data.workingGroupIdField
      //         ) {
      //           callback();
      //         } else {
      //           callback(new Error('指定单位不可为空'));
      //         }
      //       },
      //       trigger: 'change',
      //       required: true
      //     }
      //   ];

      case 'effectiveTime':
        return [
          {
            validator: (rule, value, callback) => {
              if (data.effectiveTime || data.effectiveTimeField) {
                callback();
              } else {
                callback(new Error('生效时间不可为空'));
              }
            },
            trigger: 'change',
            required: true,
          },
        ];

      case 'publishObj':
        return [
          {
            validator: (rule, value, callback) => {
              if (
                data.pubOrgId ||
                data.pubOrgIdField ||
                data.pubWorkingGroupId ||
                data.pubWorkingGroupIdField ||
                data.pubMemberId ||
                data.pubMemberIdField
              ) {
                callback();
              } else {
                callback(new Error('发布对象不可为空'));
              }
            },
            trigger: 'change',
            required: true,
          },
        ];

      case 'pubTime':
        return [
          {
            validator: (rule, value, callback) => {
              if (data.pubTime || data.pubTimeField) {
                callback();
              } else {
                callback(new Error('发布时间不可为空'));
              }
            },
            trigger: 'change',
            required: true,
          },
        ];

      case 'notifyRange':
        return [
          {
            validator: (rule, value, callback) => {
              if (
                data.notificationOrgId ||
                data.notificationOrgIdField ||
                data.notificationWorkingGroupId ||
                data.notificationWorkingGroupIdField ||
                data.notificationMemberId ||
                data.notificationMemberIdField
              ) {
                callback();
              } else {
                callback(new Error('告知范围不可为空'));
              }
            },
            trigger: 'change',
            required: true,
          },
        ];

      case 'tellTime':
        return [
          {
            validator: (rule, value, callback) => {
              if (data.tellTime || data.tellTimeField) {
                callback();
              } else {
                callback(new Error('告知时间不可为空'));
              }
            },
            trigger: 'change',
            required: true,
          },
        ];

      default:
        return [];
    }
  };
});

watch(
  () => [regulatoryData.value.workingGroupId, regulatoryData.value.depId],
  ([w, d]) => {
    if (_.isEmpty(w) && _.isEmpty(d)) {
      regulatoryData.value.level = InstitutionalLevelsDIC[0].value;
    } else {
      regulatoryData.value.level = InstitutionalLevelsDIC[1].value;
    }
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  regulatoryData.value.orgId = userMsg.value.tenant_id;
  regulatoryData.value.orgIdName = userMsg.value.real_name;
});

const { getComponent } = useComponents();

defineExpose({
  formRef,
});
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label-wrap .el-form-item__label) {
  margin: 0 !important;
  display: flex !important;
  align-items: start !important;
}
:deep(.el-form-item__content) {
  flex-direction: column;
  align-items: start;
}
</style>
