<template>
  <div>
    <el-dialog
      v-model="visible"
      title="选择成员"
      width="800"
      :append-to-body="true"
      @close="visible = false"
    >  
      <selectDataComponent 
        :tab-list="tabList" 
        v-model="model" 
        :visible="visible" 
        :config="config" 
      />
      <template #footer>
        <div class="flex justify-end">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button type="primary" @click="visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
import selectDataComponent from '@/components/select/selectDataComponent.vue';
import { useStore } from 'vuex';
import {
  getDeptByOrgTree,
  getUserByDept,
  getGroupByOrgTree,
  getUserByGroup,
  getTenantBindCustomerGroup,
  getCustomerServiceRelUserList
} from '@/api/common/index';
import { articleType } from '@/api/common/type'
import { EnterUserNoPage } from "@/api/zy/enterprise";
import { getTreeForEx } from '@/api/taskManagement/taskDetail';

const store = useStore();
const userMsg = store.state.user.userInfo;
const model = ref<articleType.ResDataParams[]>([]);
const visible = defineModel<boolean>('visible', { default: false });
const emit = defineEmits<{
  'submit': [value: any[]];
}>();

//配置项 单选多选之类的
interface configType {
  isRadio?: boolean;
  tid?:string
}

interface DrawerProps {
  modelList: any[];
  submitApi?: (params: any) => Promise<any>;
  getTableList?: () => void; //保存后请求表格数据
  dataCallBack?: (data: any) => any; //处理提交的数据
  config?: configType;
}
const props = defineProps<DrawerProps>();


const tabList = [
  {
    name: 'first',
    label: '部门',
    api: getDeptByOrgTree,
    apiParams: { tid: props.config?.tid ?? userMsg.tid },
    leftProp: { children: 'children', label: 'deptName', value: 'id', leftSearchId: 'deptId' },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: getUserByDept,
  },
  {
    name: 'second',
    label: '工作组',
    api: getGroupByOrgTree,
    apiParams: { tid: props.config?.tid ?? userMsg.tid },
    leftProp: {
      children: 'children',
      label: 'groupName',
      value: 'id',
      leftSearchId: 'workgroupId',
    },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: getUserByGroup,
  },
  {
    name: 'customer',
    label: '客服组',
    api: getTenantBindCustomerGroup,
    apiParams: { tenantId:  userMsg.tenant_id },
    leftProp: {
      children: 'children',
      label: 'groupName',
      value: 'id',
      leftSearchId: 'customGroupId',
    },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: getCustomerServiceRelUserList,
    dataCallBack: (data: any) => {//客服组都是外部人员
      return data.map((item: any) => {
        return {
          ...item,
          innerType: 1
        };
      });
    },
  },
  {
    name: 'enterprise',
    label: '企业协作',
    api: getTreeForEx,
    apiParams: '',
    leftProp: {
      children: 'children',
      label: 'className',
      value: 'id',
      leftSearchId: 'classId',
    },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: EnterUserNoPage,
    dataCallBack: (data: any) => {
      return data.map((item: any) => {
        return {
          ...item,
          nickName: item.name,
          id: item.userId,
          innerType: 1
        };
      });
    },
  },
];

watch(
  () => visible,
  () => {
    model.value = [...props.modelList];
  },
  { deep: true, immediate: true }
);

const handleSubmit = async () => {
  if (props.config?.isRadio) {
    if (model.value.length > 1) {
      ElMessage.warning('只能选择一个数据，请重新选择');
      return;
    }
  }
  let data = model.value;
  props.dataCallBack && (data = props.dataCallBack(data));
  if (props.submitApi) {
    try {
      await props.submitApi(data);
      props.getTableList!();
      ElMessage.success('保存成功');
      visible.value = false;
    } catch (error) {
      ElMessage.warning('保存失败');
    }
  } else {
    emit('submit', data);
    // visible.value = false;
    
  }
};
</script>
