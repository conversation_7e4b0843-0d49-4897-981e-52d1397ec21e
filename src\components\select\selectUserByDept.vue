<template>
  <div>
    <selectComponent
      title="选择成员"
      :tab-list="tabList"
      v-model:modelList="modelList"
      v-model:visible="visible"
      :submitApi="submitApi"
      :getTableList="getTableList"
      :dataCallBack="dataCallBack"
    >
    </selectComponent>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { getDeptByOrgTree, getUserByDept } from '@/api/common/index';
import selectComponent from './selectComponent.vue';
import { useStore } from 'vuex';

const store = useStore();
const userMsg = store.state.user.userInfo;

const modelList = defineModel<any[]>('modelList', { default: () => [] });
const visible = defineModel<boolean>('visible', { default: false });
defineEmits<{
  (e: 'change', value: any[] | string): void;
}>();

interface DrawerProps {
  submitApi?: (params: any) => Promise<any>; //提交的接口
  getTableList?: () => void; //保存后请求表格数据的接口
  dataCallBack?: (data: any) => any; //处理提交的数据
}
defineProps<DrawerProps>();

const tabList = [
  {
    name: 'first',
    label: '部门',
    api: getDeptByOrgTree,
    apiParams: { tid: userMsg.tid },
    leftProp: { children: 'children', label: 'deptName', value: 'id', leftSearchId: 'deptId' },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: getUserByDept,
  },
];
//父组件传过来的数据
watch(
  () => visible.value,
  val => {
    if (val) {
    }
  }
);
</script>
