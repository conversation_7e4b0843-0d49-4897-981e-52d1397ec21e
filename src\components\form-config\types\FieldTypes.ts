// import { dynamicAllType } from './FieldTypes';
/* ----------------------------------- 文本 ----------------------------------- */

import { AvueColumns } from './AvueTypes.ts';
import { FixTypeEnum, SystemTypeEnum } from './field.ts';

/* ---------------------------------- 日期 --------------------------------- */
// export const rangeDateType = [
//   'daterange',
//   'timerange',
//   'datetimerange',
//   'dates',
//   'yearrange',
//   'monthrange',
// ] as const;
export const submitterType = [SystemTypeEnum.SUBMITTER] as const; //提交人
export const submitterTimeType = [SystemTypeEnum.SUBMITTER_TIME] as const; // 提交时间
export const triggerTimeType = [SystemTypeEnum.TRIGGER_TIME] as const; // 触发时间
export const executorUserType = [SystemTypeEnum.EXECUTOR_USER] as const; // 执行人
export const noSubmitUserType = [SystemTypeEnum.NOSUBMIT_USER] as const; // 未提交人
export const endUpdateUserType = [SystemTypeEnum.ENDUPDATE_USER]; // 最后更新人
export const endUpdateTimeType = [SystemTypeEnum.ENDUPDATE_TIME] as const; // 最后更新时间
export const userCurrentType = [SystemTypeEnum.USER_CURRENT] as const; //当前用户
export const updateUserType = [SystemTypeEnum.UPDATE_USER] as const; // 更新人
export const updateTimeType = [SystemTypeEnum.UPDATE_TIME] as const; //更新时间
export const submitterTheDeptType = [SystemTypeEnum.SUBMITTER_THE_DEPT] as const; // 提交人所在部门
export const submitterTheWorkType = [SystemTypeEnum.SUBMIT_THE_WORK] as const; // 提交人所在工作组
export const departMentType = [SystemTypeEnum.DEPARTMENT] as const; // 提交人的部门管理员
export const workLeadType = [SystemTypeEnum.WORKLEAD] as const; // 提交人的工作组管理员
export const organizeAdminType = [SystemTypeEnum.ORGANIZE_ADMIN] as const; // 提交人的组织管理员
export const generateTimeType = [SystemTypeEnum.GENERATE_TIME] as const; // 数据生成时间，对于任务来说是提交时间，对于数据表、计算表来说是更新时间
export const loginUserType = [SystemTypeEnum.LOGIN_USER] as const; //任务引擎数值上下限使用
export const taskNameType = [SystemTypeEnum.TASKNAME] as const; // 应办名称配置字段
export const createType = [SystemTypeEnum.CREAT_USER, SystemTypeEnum.CREAT_TIME] as const; //创建属性
// 微应用提交默认字段
export const defaultDeptType = [SystemTypeEnum.DEFAULT_DEPT] as const; //微应用提交选择部门
export const launchUserType = [SystemTypeEnum.LAUNCH_USER] as const; // 发起人
export const launchDeptType = [SystemTypeEnum.LAUNCH_DEPT] as const; // 发起人所在部门
export const launchDutyType = [SystemTypeEnum.LAUNCH_DUTY] as const; // 发起人所在工作组
export const launchTimeType = [SystemTypeEnum.LAUNCH_TIME] as const; // 发起时间
export const dataSelectionType = [SystemTypeEnum.DATASELECTION] as const; //微应用数据选择

// 数据表..系统组件的类型
export const customTypes = [
  ...submitterType,
  ...submitterTimeType,
  ...triggerTimeType,
  ...executorUserType,
  ...noSubmitUserType,
  ...submitterTheDeptType,
  ...submitterTheWorkType,
  ...departMentType,
  ...workLeadType,
  ...organizeAdminType,
];
// 任务名称可以展示的系统字段
export const systemByHandNameType = [
  ...submitterType,
  ...submitterTimeType,
  ...triggerTimeType,
  ...executorUserType,
  ...noSubmitUserType,
  ...taskNameType,
];
// 规则速递可以展示的系统字段
export const systemByRuleExpressType = [
  ...submitterType,
  ...submitterTimeType,
  ...submitterTheDeptType,
  ...submitterTheWorkType,
  ...departMentType,
  ...workLeadType,
  ...organizeAdminType,
];
// 数据联动-触发任务联动-类型是任务可以展示的系统字段
export const systemByLinkDataType = [
  ...submitterType,
  ...submitterTimeType,
  ...submitterTheDeptType,
  ...submitterTheWorkType,
  ...departMentType,
  ...workLeadType,
  ...organizeAdminType,
  ...executorUserType,
  ...triggerTimeType,
  ...noSubmitUserType,
  // ...userCurrentType,
];

// 数据联动-触发任务联动-类型是数据表/计算表 推数路径可以展示的系统字段
export const systemByLinkDataType1 = [...endUpdateUserType, ...endUpdateTimeType];

// 数据联动-触发任务联动-类型是数据表/计算表 联动任务可以展示的系统字段
export const systemfilterLinkType = [
  ...submitterType,
  ...submitterTimeType,
  ...triggerTimeType,
  ...userCurrentType,
];

//数据联动-当前任务联动可以展示的系统字段
export const systemByCurrentType = [...triggerTimeType];

//添加筛选条件排序可取的字段
export const systemByFilterSortType = [...submitterTimeType, ...triggerTimeType];
export const systemByFilterSortType1 = [...endUpdateTimeType];

// export const noRangeDateType = ['year', 'month', 'week', 'date', 'datetime', 'time'] as const;

/* ----------------------------------- 文本 ----------------------------------- */
export const inputType = [FixTypeEnum.INPUT] as const;
/* ----------------------------------- 日期 ----------------------------------- */
export const dateType = [FixTypeEnum.DATE] as const;
/* ----------------------------------- 时间范围 ----------------------------------- */
export const dateRangeType = [FixTypeEnum.DATERANGE] as const;
export const dateAllType = [...dateType, ...dateRangeType] as const;
/* ----------------------------------- 数字 ----------------------------------- */
export const numberType = [FixTypeEnum.NUMBER] as const;
/* ----------------------------------- 选择单选 ----------------------------------- */
export const selectType = [FixTypeEnum.SELECT] as const;
/* ----------------------------------- 选择多选 ----------------------------------- */
export const mulSelectType = [FixTypeEnum.MUL_SELECT] as const;
/* ----------------------------------- 级联多选 ----------------------------------- */
export const mulCascaderType = [FixTypeEnum.MUL_CASCADER] as const;
export const allSelectItemType = [...selectType, ...mulSelectType] as const;

export const multipleSelectType = [
  ...selectType,
  ...mulSelectType,
] as const;
export const selectAllType = [...selectType, ...mulSelectType, ...mulCascaderType] as const;
/* ----------------------------------- 地址 ----------------------------------- */
export const addressType = [FixTypeEnum.ADDRESS] as const;
/* ----------------------------------- 分割线 ---------------------------------- */
export const dividerType = [FixTypeEnum.DIVIDER] as const;
/* ----------------------------------- 子表单 ---------------------------------- */
export const dynamicType = [FixTypeEnum.DYNAMIC] as const;
// 表单子表单
// export const dynamicFormType = ['form'] as const;
// 表格子表单
// export const dynamicCrudType = ['crud'] as const;
// 表格子表单
// export const dynamicTriggerType = ['trigger'] as const;
// 所有子表单
// export const dynamicAllType = [
//   ...dynamicFormType,
//   ...dynamicCrudType,
//   // ...dynamicTriggerType,
// ] as const;
/* ----------------------------------- 附件 ----------------------------------- */
export const uploadType = [FixTypeEnum.UPLOAD] as const;
/* ----------------------------------- 拍照 ----------------------------------- */
export const photoType = [FixTypeEnum.PHOTO] as const;
/* ---------------------------------- 文字说明 ---------------------------------- */
export const tipsType = [FixTypeEnum.TIPS] as const;
/* ---------------------------------- 成员选择 ---------------------------------- */
export const usersType = [FixTypeEnum.USERS] as const;
/* ---------------------------------- 单位选择 ---------------------------------- */
export const basicInfoType = [FixTypeEnum.BASIC_INFO] as const;
/* ---------------------------------- 数据选择 ---------------------------------- */
// export const dataSelectType = ['dataSelect'] as const;
/* ---------------------------------- 工作组选择 ---------------------------------- */
export const userTagType = [FixTypeEnum.USER_TAG] as const;
/* ---------------------------------- 部门选择 ---------------------------------- */
export const userDeptType = [FixTypeEnum.USER_DEPT] as const;
/* ---------------------------------- 数据汇总 ---------------------------------- */
// export const dataCollectType = ['dataCollect'] as const;
/* ---------------------------------- 知识标准 ---------------------------------- */
// export const knowledgeSelectType = ['knowledgeSelect'] as const;
/* ---------------------------------- 手写签名 ---------------------------------- */
export const signatureType = [FixTypeEnum.SIGNATURE] as const;
/* ---------------------------------- 签名汇总 ---------------------------------- */
export const signatureCollectType = [FixTypeEnum.SIGNATURE_COLLECT] as const;
/* ---------------------------------- 公式编辑 ---------------------------------- */
export const formulaEditType = [FixTypeEnum.FORMULA_EDIT] as const;
/* ---------------------------------- 物品组件 ---------------------------------- */
export const inventoryComType = [FixTypeEnum.INVENTORY_COM] as const;
/* ---------------------------------- 空间组件 ---------------------------------- */
export const interspaceComType = [FixTypeEnum.INTERSPACE_COM] as const;
/* ---------------------------------- 统计指标 ---------------------------------- */
export const statisticalType = [FixTypeEnum.STATISTICAL] as const;
/* ---------------------------------- 目标值 ---------------------------------- */
export const targetValueType = [FixTypeEnum.TARGET_VALUE] as const;
/* ---------------------------------- 逐一触发 ---------------------------------- */
// export const oneByTriggerType = ['oneByTrigger'] as const;
/* ---------------------------------- 获取用户id ---------------------------------- */
export const getUsercodeType = [FixTypeEnum.GET_USERCODE] as const;
/* ---------------------------------- 知识汇总 ---------------------------------- */
// export const knowledgeCollectType = ['knowledgeCollect'] as const;

/* ---------------------------------- 会办数据汇总 ---------------------------------- */
// export const clubDataCollectType = ['clubDataCollect'] as const;
/* ---------------------------------- 多任务汇总 ---------------------------------- */
// export const multipleTaskCollectType = ['multipleTaskCollect'] as const;
/* ---------------------------------- 部门管理员 ---------------------------------- */
export const departAdminType = [FixTypeEnum.DEPART_ADMIN] as const;
/* ---------------------------------- 工作组管理员 ---------------------------------- */
export const workingAdminType = [FixTypeEnum.WORKING_ADMIN] as const;
/* ---------------------------------- 计算表 ---------------------------------- */
export const calculationTableType = [FixTypeEnum.CALCULATION_TABLE] as const;
/* ---------------------------- 字段筛选默认值 ---------------------------- */
export const defaultIncludeType = [
  ...inputType,
  ...dateAllType,
  ...selectType,
  ...mulSelectType,
  ...mulCascaderType,
  ...numberType,
  ...addressType,
];
/* ---------------------------- 特殊类型 ---------------------------- */
export const userAllType = [...usersType, ...userTagType, ...userDeptType];
// 管理员
export const adminAllType = [...departAdminType, ...workingAdminType];
// 没有值的表单
export const noDataType = [...dividerType, ...tipsType, ...basicInfoType];
// 选择类型的placeholder
export const selectPlaceholder = [...selectAllType, ...dateAllType, ...addressType];

export const userMatchType = [...usersType, ...getUsercodeType];

//值是数组类型
export const arrayType = [...dateRangeType, ...uploadType];

//工作组/部门管理员
export const userAdminType = [...departAdminType, ...workingAdminType];

//组件不可编辑时hover展示值
export const tooltipTypes = [...inputType, ...selectType];

//表格可以导入的类型
export const importTypes = [...inputType, ...numberType, ...dateAllType, ...allSelectItemType];

//值是数字的类型
export const numberValueType = [
  ...numberType,
  ...formulaEditType,
  ...calculationTableType,
  ...targetValueType,
];

//工作组/部门
export const deptGroupType = [...userTagType, ...userDeptType];

//不必填的数据类型
export const notRequiredType = [
  ...formulaEditType,
  ...statisticalType,
  ...targetValueType,
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
  ...calculationTableType,
  // ...signatureType
];

//所有组件
export const allFieldTypes = [
  ...inputType,
  ...dateAllType,
  ...selectAllType,
  ...numberType,
  ...addressType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...dynamicType,
  ...dynamicType,
  ...statisticalType,
  ...targetValueType,
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
  ...calculationTableType,
  ...basicInfoType,
  ...tipsType,
];

export const noSignTypes = allFieldTypes.filter(item => !_.includes(signatureType, item)); //只有签名可以选
export const noTriggerTime = allFieldTypes.filter(item => !_.includes(dateType, item)); //只有日期组件可以选
export const limitUser = allFieldTypes.filter(item => !_.includes(usersType, item)); //只有成员选择可以选

// 能加入子表单的类型
//@ts-ignore
export const canIntoDynamicType = [
  ...defaultIncludeType.filter(f => f !== 'mulCascader'),
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...signatureType,
  ...targetValueType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...statisticalType,
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
  ...calculationTableType,
].filter((type:FixTypeEnum) => type !== FixTypeEnum.MUL_CASCADER);

// 能表格搜索的字段
export const canSearchType = [
  ...inputType,
  ...dateAllType,
  ...selectType,
  ...mulSelectType,
  ...numberType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...calculationTableType,
  ...targetValueType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...statisticalType,
];

export const disabledType = [
  ...calculationTableType,
  ...targetValueType,
  ...formulaEditType,
  ...statisticalType,
]

// 不能显影的类型
export const noSelectRuleType = [];
// 不能重复拖入的字段
export const noRepeatField = [];
// 只有联动的类型
export const onlyLinkType = [
  ...photoType,
  ...tipsType,
  ...usersType,
  ...userDeptType,
  ,
  ...signatureType,
];
// 只能单向联动的类型
// export const onlySingleLinkType = [...tipsType];
export const onlySingleLinkType = [];
// 没有label的字段
export const noLabelField = [...dividerType];
// 类子表单字段
export const dynamicLikeField = [...dynamicType];
// 可以选质量标识的字段
export const canQuilityField = [
  ...dynamicLikeField,
  ...defaultIncludeType,
  ...dateRangeType,
  ...uploadType,
  ...photoType,
  ...userAllType,
  ...signatureType,
  ...inventoryComType,
  ...interspaceComType,
];
export const canRadioField = [...inventoryComType, ...interspaceComType, ...userAllType];

// 文件模板是表格的字段
export const fileTemplateIsTable = [...dynamicLikeField];
// 文件模板是列表的字段
export const fileTemplateIsList = [...uploadType, ...photoType];
// 文件模板是手写签名的字段
export const fileTemplateIsSign = [...signatureType, ...signatureCollectType];
export const basicSubList = [SystemTypeEnum.BASICLOGO];
/* ---------------------------- 用于 useFiledList ---------------------------- */
// 能联动的类型
export const dataLinkIncludeType = [
  ...defaultIncludeType,
  ...dateRangeType,
  ...uploadType,
  ...photoType,
  ...tipsType,
  ...userAllType,
  ...signatureType,
];

export const addQueryType = [...signatureCollectType];
// 能字段发起的字段
export const fieldConditionIncludeType = [
  ...defaultIncludeType,
  ...dateRangeType,
  ...dynamicType,
  ...uploadType,
  ...photoType,
  ...userAllType,
  ...createType,
];
// 文件配置显示的字段
export const fileSettingIncludeType = [
  ...defaultIncludeType,
  ...dateRangeType,
  ...dynamicType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
];
// 数据选择显示的字段
export const canDataSelect = [
  ...defaultIncludeType,
  ...dateRangeType,
  ...dynamicType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
];
// 会办数据汇总显示的字段
export const clubDataCollegeCanSelect = [
  ...inputType,
  ...numberType,
  ...selectAllType,
  ...addressType,
  ...dateAllType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...getUsercodeType,
  ...adminAllType,
  ...calculationTableType,
  ...submitterType,
  ...submitterTimeType,
  ...triggerTimeType,
  ...noSubmitUserType,
];
// 任务名称显示的字段
export const handleNameIncludeType = [
  ...defaultIncludeType,
  ...dateRangeType,
  ...statisticalType,
  ...dynamicLikeField,
  ...userAllType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...getUsercodeType,
  ...adminAllType,
  ...calculationTableType,
  ...systemByHandNameType,
  ...taskNameType,
];

// 值与文件自定义名称可复制类型
export const ruleByFilePasteType = [
  ...defaultIncludeType,
  ...dateRangeType,
  ...statisticalType,
  ...userAllType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...getUsercodeType,
  ...adminAllType,
  ...calculationTableType,
  ...systemByHandNameType,
  ...taskNameType,
];
// 执行角色显示的类型
export const executorRoleType = [...userAllType];
export const executorRoleDynamicType = [...userAllType];
// 可以以所选任务的被执行人触发的字段 / 以所选任务的执行人筛选匹配执行人 / 最终筛选出的表单字段
export const executorTouchField = [
  ...dynamicType,
  ...userTagType,
  ...userDeptType,
  ...usersType,
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
  ...submitterType,
  ...noSubmitUserType,
  ...executorUserType,
];
export const executorTouchSubField = [
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...departAdminType,
  ...workingAdminType,
  ...getUsercodeType,
];
//执行人-被执行对象-以所选任务的被执行对象-选择字段
export const beExecutorObjType = [
  ...statisticalType,
  ...getUsercodeType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...departAdminType,
  ...workingAdminType,
];
//添加标签-选择字段
export const addTagFieldType = [
  ...inputType,
  ...numberType,
  ...selectAllType,
  ...addressType,
  ...dateAllType,
  ...userDeptType,
  ...formulaEditType,
  ...inventoryComType,
  ...usersType,
  ...userTagType,
  ...interspaceComType,
  ...targetValueType,
  ...statisticalType,
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
  ...calculationTableType,
  ...submitterType,
  ...submitterTimeType,
  ...triggerTimeType,
  ...executorUserType,
  ...noSubmitUserType,
  ...launchUserType,
  ...launchTimeType,
];
//执行人-以所选任务的被执行人-逐一触发筛选字段
export const executorMatchConditionType = [
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...departAdminType,
  ...workingAdminType,
  ...calculationTableType,
];
// 执行人-任务-更新筛选匹配执行人-显隐/自定义
export const execTaskFilCustomType = [
  ...inputType,
  ...numberType,
  ...dateAllType,
  ...selectAllType,
  ...addressType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...dynamicType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...statisticalType,
  ...getUsercodeType,
  ...calculationTableType,
  ...adminAllType,
  ...customTypes,
];
// 执行人-任务-更新筛选匹配执行人-历史
export const execTaskFilHistoryType = [
  ...inputType,
  ...numberType,
  ...dateAllType,
  ...selectAllType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...adminAllType,
  ...getUsercodeType,
  ...calculationTableType,
  ...customTypes,
];
// 执行人-数据表-更新筛选匹配执行人-显隐/自定义
export const execDataTableFilCustomType = [
  ...inputType,
  ...numberType,
  ...dateAllType,
  ...selectAllType,
  ...addressType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...dynamicType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...statisticalType,
  ...adminAllType,
  ...endUpdateUserType,
  ...endUpdateTimeType,
];
// 执行人-数据表-更新筛选匹配执行人-历史
export const execDataTableFilHistoryType = [
  ...inputType,
  ...numberType,
  ...dateAllType,
  ...selectAllType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...adminAllType,
  ...endUpdateUserType,
  ...endUpdateTimeType,
];
// 规则速递-文件
export const ruleExpressByFileType = [
  ...inputType,
  ...numberType,
  ...dateAllType,
  ...selectAllType,
  ...addressType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...dynamicType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...statisticalType,
  ...adminAllType,
  ...getUsercodeType,
  ...calculationTableType,
  ...systemByRuleExpressType,
  ...executorUserType,
  ...noSubmitUserType,
  ...triggerTimeType,
];
// 规则速递-数据表
export const ruleExpressByDataTableType = [
  ...inputType,
  ...numberType,
  ...dateAllType,
  ...selectAllType,
  ...addressType,
  ...dynamicType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...statisticalType,
  ...getUsercodeType,
  ...adminAllType,
  ...calculationTableType,
  ...endUpdateTimeType,
  ...endUpdateUserType,
  ...updateTimeType,
];

export const ruleBySheetIncludeDynamicType = [
  ...triggerTimeType,
  ...submitterTimeType,
  ...updateTimeType,
  'date',
];

// 值与文件代码列表过滤类型
export const expreeCopyCodeFilterType = [...tipsType, ...dividerType];
// 值与文件可以设置图片尺寸的类型
export const pictureConfigType = [...photoType, ...signatureType, ...signatureCollectType];

//基础组件
export const dataLinksType = [
  ...inputType,
  ...numberType,
  ...selectAllType,
  ...dateAllType,
  ...formulaEditType,
  ...targetValueType,
  ...calculationTableType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...inventoryComType,
  ...interspaceComType,
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
];

//微应用条件分支筛选组件
export const microAppType = [
  ...inputType,
  ...numberType,
  ...selectAllType,
  ...dateAllType,
  ...dynamicType,
  ...formulaEditType,
  ...targetValueType,
  ...calculationTableType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...inventoryComType,
  ...interspaceComType,
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
  ...dataSelectionType,
  ...defaultDeptType,
  ...launchUserType,
  ...launchDeptType,
  ...launchDutyType,
  ...mulCascaderType,
];
//微应用基础组件
export const dataLinksAppType = [
  ...inputType,
  ...numberType,
  ...selectAllType,
  ...dateAllType,
  ...dynamicType,
  ...formulaEditType,
  ...targetValueType,
  ...calculationTableType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...inventoryComType,
  ...interspaceComType,
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
];

export const contrastTypes = [
  ...userAllType,
  ...inventoryComType,
  ...interspaceComType,
  ...dateRangeType,
  ...addressType,
  ...departAdminType,
  ...workingAdminType,
  ...submitterType,
  ...submitterTheDeptType,
  ...submitterTheWorkType,
  ...departMentType,
  ...organizeAdminType,
  ...workLeadType,
  ...getUsercodeType,
  ...executorUserType,
  ...noSubmitUserType,
  ...endUpdateTimeType,
  ...endUpdateUserType,
];

//当前任务/联动任务-联动表单字段
export const dataLinksDefaultField = [
  ...dataLinksType,
  ...uploadType,
  ...photoType,
  ...dynamicType,
  ...signatureType,
  ...signatureCollectType,
  ...getUsercodeType,
  ...statisticalType,
  ...departAdminType,
  ...workingAdminType,
];

//存档配置条件可选组件
export const archiveDefaultField = [
  ...dataLinksType,
  ...uploadType,
  ...photoType,
  ...signatureType,
  ...signatureCollectType,
  ...getUsercodeType,
  ...statisticalType,
  ...departAdminType,
  ...workingAdminType,
];

//表格不让联动的字段
export const dynamicUnLinkType = [
  ...getUsercodeType,
  ...departAdminType,
  ...workingAdminType,
  ...statisticalType,
  ...formulaEditType,
  ...calculationTableType,
  ...targetValueType,
  // ...signatureType,
  // ...signatureCollectType,
];

//当前任务/联动任务-联动表单-表格字段
export const dataLinksDefaultDynamicField = [
  ...inputType,
  ...numberType,
  ...selectAllType,
  ...dateAllType,
  ...formulaEditType,
  ...targetValueType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...inventoryComType,
  ...interspaceComType,
  ...uploadType,
  ...photoType,
  ...signatureType,
  ...departAdminType,
  ...workingAdminType,
];

//筛选条件-历史表单
export const filterHistoryField = [
  ...inputType,
  ...numberType,
  ...dateAllType,
  ...selectAllType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...dynamicType,
];

//筛选条件-当前表单
export const filterCurrentField = [
  ...dataLinksType,
  ...uploadType,
  ...photoType,
  ...signatureType,
  ...signatureCollectType,
  ...statisticalType,
  ...getUsercodeType,
  ...calculationTableType,
  ...dynamicType,
];

// 扩展组件-公式编辑-公式编辑
export const extentFieldByEquation = [
  ...dataLinksType,
  ...dateRangeType,
  ...dynamicType,
  ...statisticalType,
  ...adminAllType,
  ...submitterType,
  ...submitterTimeType,
  ...triggerTimeType,
  ...executorUserType,
  ...noSubmitUserType,
];

// 数据联动/历史表单字段/当前表单字段匹配的筛选条件字段
export const dataLinkHistoryMatchField = [
  ...dataLinksType,
  ...adminAllType,
  ...submitterType,
  ...submitterTimeType,
  // ...triggerTimeType,
  // ...noSubmitUserType,
  // ...endUpdateUserType,
  // ...submitterTheDeptType,
  // ...submitterTheWorkType,
  // ...departMentType,
  // ...workLeadType,
  // ...organizeAdminType,
];

//数据联动 添加筛选条件 当前表单字段匹配的筛选条件字段
export const dataLinkCurrentMatchField = [...dataLinksType];

//微应用数据联动 添加筛选条件 当前表单字段匹配的筛选条件字段
export const dataLinkCurrentMatchFieldApp = [...dataLinksAppType];

// 数据联动历史表单过滤系统类型
export const historyFilterType = [
  ...triggerTimeType,
  ...noSubmitUserType,
  ...endUpdateUserType,
  ...submitterTheDeptType,
  ...submitterTheWorkType,
  ...departMentType,
  ...workLeadType,
  ...organizeAdminType,
];
//数据汇总/选择 可选择显示的字段(表头)
export const dataCollectByTableHeadType = [
  ...inputType,
  ...numberType,
  ...selectAllType,
  ...dateAllType,
  ...uploadType,
  ...tipsType,
  ...photoType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...targetValueType,
  ...getUsercodeType,
  ...calculationTableType,
  ...addressType,
  ...usersType,
  ...userTagType,
  ...userDeptType,
  ...inventoryComType,
  ...interspaceComType,
  ...adminAllType,
  ...submitterType,
  ...submitterTimeType,
  ...triggerTimeType,
  ...noSubmitUserType,
];

// 可以被公式编辑的字段
export const canBeFormulaType = [
  ...defaultIncludeType,
  ...dateRangeType,
  ...uploadType,
  ...photoType,
  ...dynamicLikeField,
  ...userAllType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...statisticalType,
  ...adminAllType,
  ...signatureCollectType,
  ...signatureType,
  ...formulaEditType,
  ...calculationTableType,
  ...submitterType,
  ...submitterTimeType,
  ...triggerTimeType,
  ...executorUserType,
  ...noSubmitUserType,
  ...loginUserType,
  ...getUsercodeType,
];

// 添加筛选条件排序字段
export const filterSortType = [
  ...defaultIncludeType,
  ...userAllType,
  ...inventoryComType,
  ...interspaceComType,
  ...departAdminType,
  ...workingAdminType,
  ...formulaEditType,
  ...targetValueType,
  ...calculationTableType,
];

// 上传文件筛选应办数据排序字段
export const filterHandleDataSortType = [
  ...dateAllType,
  ...numberType,
  ...triggerTimeType,
  ...submitterTimeType,
];
// 规则速递值与值可以选择的字段
export const ruleValueByFieldType = [...inputType, ...dateAllType, ...numberType];

// 汇总类字段
export const SummaryFieldType = [...signatureCollectType];

// 可表格导入的字段
export const BanbanTableImportType = [
  ...inputType,
  ...numberType,
  ...dateAllType,
  ...selectAllType,
  ...usersType,
];

// 值与报表分组汇总可选择字段
export const sheetGroupSelectType = [...inputType, ...dateType, ...numberType, ...selectType];

// 显隐规则条件可用的字段
export const VisibleAndHiddenConditionType = [
  ...inputType,
  ...numberType,
  ...selectAllType,
  ...mulCascaderType,
  ...addressType,
  ...dividerType,
  ...dateAllType,
  ...uploadType,
  ...photoType,
  ...usersType,
  ...userTagType,
  ...basicInfoType,
  ...userDeptType,
  ...signatureType,
  ...signatureCollectType,
  ...formulaEditType,
  ...inventoryComType,
  ...interspaceComType,
  ...targetValueType,
  ...statisticalType,
  ...getUsercodeType,
  ...adminAllType,
  ...calculationTableType,
];

// 显隐规则显示的字段和条件
export const VisibleAndHiddenType = [...VisibleAndHiddenConditionType, ...dynamicType, ...tipsType];
// 互斥规则显示的字段
export const MutualType = [...selectAllType, ...mulCascaderType] as string[];
// 数据表-依条件过滤列表
export const dataTableFilByCondition = [
  ...uploadType,
  ...photoType,
  ...basicInfoType,
  ...signatureType,
  ...signatureCollectType,
];
// 数据表-依条件特殊字段
export const dataTableSpecialField = [
  ...dateAllType,
  ...numberType,
  ...formulaEditType,
  ...targetValueType,
  ...calculationTableType,
];

export const allDateFilterType = [
  ...submitterTimeType,
  ...triggerTimeType,
  ...dateType,
  ...endUpdateTimeType,
];
export const allUserFilterType = [
  ...usersType,
  ...submitterType,
  ...getUsercodeType,
  ...userCurrentType,
  ...endUpdateUserType,
];
const allTypes = {
  inputType,
  dateType: allDateFilterType,
  dateRangeType,
  numberType,
  selectType,
  mulSelectType,
  mulCascaderType,
  addressType,
  dividerType,
  dynamicType,
  uploadType,
  photoType,
  tipsType,
  usersType: allUserFilterType,
  createType,
  basicInfoType,
  userTagType,
  userDeptType,
  signatureType,
  signatureCollectType,
  formulaEditType,
  inventoryComType,
  interspaceComType,
  statisticalType,
  targetValueType,
  getUsercodeType: submitterType,
  departAdminType,
  workingAdminType,
  calculationTableType,
  submitterType: submitterType,
  submitterTimeType: allDateFilterType,
  triggerTimeType: allDateFilterType,
  executorUserType,
  noSubmitUserType,
  endUpdateUserType: submitterType,
  submitterTheDeptType,
  submitterTheWorkType,
  departMentType,
  workLeadType,
  organizeAdminType,
  userMatchType,
  endUpdateTimeType,
};
export type AllFieldTypes = typeof allTypes;
export type XtFieldType = AllFieldTypes[keyof AllFieldTypes][number];

export type TransToXtFieldType<T extends readonly string[]> = T[number];

export type TypesKeys = keyof AllFieldTypes;
type TypesValue<T extends TypesKeys> = AllFieldTypes[T];
export type FieldTypes = AllFieldTypes[TypesKeys];

// 处理不同类型的情况
export const separateType = <T extends TypesKeys>(
  type: string,
  params: {
    [P in T]: (types: TypesValue<P>) => void;
  }
) => {
  for (const key in params) {
    const allType = allTypes[key];
    if (_.includes(allType, type)) {
      params[key](allType);
      return;
    }
  }
};

export const createBaseList = [
  {
    copyLabel: '提交人',
    prop: 'submitter',
    fixType: SystemTypeEnum.SUBMITTER,
    id: '1',
    display: true,
  },
  {
    copyLabel: '提交时间',
    prop: 'submitterTime',
    fixType: SystemTypeEnum.SUBMITTER_TIME,
    id: '2',
    display: true,
  },
  {
    copyLabel: '触发时间',
    prop: 'triggerTime',
    fixType: SystemTypeEnum.TRIGGER_TIME,
    id: '3',
    display: true,
  },
] as AvueColumns[];

export const createList = [
  ...createBaseList,
  {
    copyLabel: '执行人',
    prop: 'executorUser',
    fixType: SystemTypeEnum.EXECUTOR_USER,
    id: '4',
    display: true,
  },
  {
    copyLabel: '未提交人',
    prop: 'noSubmitUser',
    fixType: SystemTypeEnum.NOSUBMIT_USER,
    id: '5',
    display: true,
  },
  {
    copyLabel: '最后更新人',
    prop: 'endUpdateUser',
    fixType: SystemTypeEnum.ENDUPDATE_USER,
    id: '6',
    display: true,
  },
  {
    copyLabel: '提交人所在部门',
    prop: 'submitterTheDept',
    fixType: SystemTypeEnum.SUBMITTER_THE_DEPT,
    id: '7',
    display: true,
  },
  {
    copyLabel: '提交人所在工作组',
    prop: 'submitterTheWork',
    fixType: SystemTypeEnum.SUBMIT_THE_WORK,
    id: '8',
    display: true,
  },
  {
    copyLabel: '提交人的部门领导人',
    prop: 'departLead',
    fixType: SystemTypeEnum.DEPARTMENT,
    id: '9',
    display: true,
  },
  {
    copyLabel: '提交人的工作组领导人',
    prop: 'workLead',
    fixType: SystemTypeEnum.WORKLEAD,
    id: '10',
    display: true,
  },
  {
    copyLabel: '提交人的组织管理员',
    prop: 'organizeAdmin',
    fixType: SystemTypeEnum.ORGANIZE_ADMIN,
    id: '11',
    display: true,
  },
  {
    copyLabel: '最后更新时间',
    prop: 'endUpdateTime',
    fixType: SystemTypeEnum.ENDUPDATE_TIME,
    id: '12',
    display: true,
  },
  {
    copyLabel: '数据生成时间',
    prop: 'generateTime',
    fixType: SystemTypeEnum.GENERATE_TIME,
    id: '13',
    display: true,
  },
  {
    copyLabel: '当前用户',
    prop: 'UserCurrent',
    fixType: SystemTypeEnum.USER_CURRENT,
    id: '16',
    display: true,
  },
  {
    copyLabel: 'LOGINUSER',
    prop: 'loginUser',
    fixType: SystemTypeEnum.LOGIN_USER,
    id: '100',
    display: true,
  },
  {
    copyLabel: '任务名称',
    prop: 'taskName',
    fixType: SystemTypeEnum.TASKNAME,
    id: '101',
    display: true,
  },
  {
    copyLabel: '未提交人的部门领导人',
    prop: 'undepartLead',
    fixType: SystemTypeEnum.UNDEPARTMENT,
    id: '17',
    display: true,
  },
  {
    copyLabel: '未提交人的工作组领导人',
    prop: 'unworkLead',
    fixType: SystemTypeEnum.UNWORK_LAEAD,
    id: '18',
    display: true,
  },
] as AvueColumns[];

export const dataTableDefList = [
  {
    copyLabel: '更新人',
    label: '更新人',
    prop: 'updateUser',
    fixType: SystemTypeEnum.UPDATE_USER,
    type: 'updateUser',
    id: '14',
    display: true,
  },
  {
    copyLabel: '更新时间',
    label: '更新时间',
    prop: 'updateTime',
    fixType: SystemTypeEnum.UPDATE_TIME,
    type: 'updateTime',
    id: '15',
    display: true,
  },
];

export const microAppDefList = [
  {
    copyLabel: '发起人',
    display: true,
    id: '21',
    fixType: SystemTypeEnum.LAUNCH_USER,
    prop: 'launchUser:#21',
  },
] as AvueColumns[];

export const microAppDefListForTag = [
  {
    copyLabel: '发起人',
    display: true,
    id: '105',
    fixType: SystemTypeEnum.LAUNCH_USER,
    prop: 'launchUser',
  },
  {
    copyLabel: '发起时间',
    display: true,
    id: '112',
    fixType: SystemTypeEnum.LAUNCH_TIME,
    prop: 'launchTime',
  },
] as AvueColumns[];
