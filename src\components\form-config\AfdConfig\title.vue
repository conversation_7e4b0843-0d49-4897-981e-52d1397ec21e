<template>
  <div class="widget-config-title">
    <component :is="getIcon" class="filed-icon" />
    <span>{{ data.typeLabel }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Icons from '@/images/formDesignFieldIcon';
import { FieldBaseField } from '../types/AvueTypes.ts';

const props = defineProps<{
  data: FieldBaseField;
}>();

const getIcon = computed(() => {
  return Icons[props.data.iconPath as keyof typeof Icons];
});
</script>

<style scoped lang="scss">
.widget-config-title {
  z-index: 1;
  height: 50px;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  display: flex;
  padding-left: 15px;
  align-items: center;

  svg {
    width: 20px;
    height: 20px;
  }

  span {
    margin-left: 5px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }
}
</style>