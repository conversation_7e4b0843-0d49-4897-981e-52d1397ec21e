import { Base64 } from 'js-base64';
import website from '@/config/website.js';

const useAuthHeader = () => {
  const store = useStore();

  const headers = computed<Record<string, string>>(() => {
    const result: Record<string, string> = {};
    result['Authorization'] = `Basic ${Base64.encode(
      `${website.clientId}:${website.clientSecret}`
    )}`;
    result[website.tokenHeader] = 'bearer ' + store.getters.token;

    return result;
  });

  return {
    headers,
  };
};

export default useAuthHeader;
