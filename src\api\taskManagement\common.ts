import request from '../../axios';

export const getTaskListByVersionId = (versionId: string) => {
  return request({
    url: '/scene_task_group/getTaskListByVersionId',
    method: 'get',
    params: {
      id: versionId,
    },
  });
};

// 获取当前组织的版本
export const getVersionListByGroupRequest = () => {
  return request({
    url: '/scene_task_group/getVersionList',
    method: 'get',
  });
};

// 组件获取值与值规则
export const getFormulaRules = (params: {
  fileId: string;
  taskId: string;
  templateId?: string;
}) => {
  return request({
    url: '/ruleExpress/selectFormulaRule',
    method: 'get',
    params,
  });
};

// 组件获取值与数据表规则
export const getDatalinksRules = (params: { fileType: string; taskId: string }) => {
  return request({
    url: '/ruleExpress/selectDataLinkageRule',
    method: 'get',
    params,
  });
};

// 组件获取值与数据表规则
export const getDatalinksRulesPreview = (params: { ruleExpressId?: string }) => {
  return request({
    url: '/ruleExpress/showDataLinkageRule',
    method: 'get',
    params,
  });
};

// 判断当前任务有没有应办任务
export const isInHandle = (taskId: string) => {
  return request({
    url: `/staging/selectHandle`,
    method: 'post',
    params: { taskId },
  });
};

export const getDynamicChild = (dynamicFieldId?: string) => {
  return request({
    url: `/task_form/getDynamicChild`,
    method: 'post',
    params: { dynamicFieldId },
  });
};
