<template>
  <div>
    <config-title :data="data"></config-title>
    <ConfigPermission :data="data" :show-edit="false" :has-required="false" />
    <task-select
      class="!mb-0"
      v-model="data.formulaFilter.taskId"
      is-exclude
      @select="handleSelect"
    ></task-select>
    <el-button class="w-full" size="large" @click="visible = true" v-if="data.formulaFilter?.taskId"
      >公式编辑</el-button
    >
    <el-button
      class="w-full mt-2"
      style="margin-left: 0 !important"
      size="large"
      @click="visible1 = true"
      v-if="data.formulaFilter?.taskId"
      >过滤条件</el-button
    >

    <config-span :data="data" />

    <!-- 过滤条件 -->
    <el-dialog
      title="过滤条件(条件之间为且的关系)"
      append-to-body
      destroy-on-close
      v-model="visible1"
      width="800px"
      :close-on-click-modal="false"
    >
      <fieldConditionConfig
        :field="field"
        :conditionTaskId="data.formulaFilter.taskId"
        v-model:condition="data.formulaFilter.columnList"
        v-model:andOr="linkageOperator"
        :andOrDisabled="true"
        title="添加筛选条件"
        :addSystemField="false"
      />
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="visible1 = false" round>取 消</el-button>
          <el-button type="primary" @click="visible1 = false" round>确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 公式编辑 -->
    <el-dialog
      title="公式编辑"
      append-to-body
      destroy-on-close
      v-model="visible"
      width="800px"
      :close-on-click-modal="false"
    >
      <b-formula
        v-model="data.formula"
        :formulaType="FormulaType.CAL"
        :fieldList="field"
        valText="公式编辑"
        :isCheck="true"
      />
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="visible = false" round>取 消</el-button>
          <el-button type="primary" @click="saveFormula" round>确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import TaskSelect from '@/views/mainview/sceneTasks/detail/components/common/TaskSelect.vue';
import useFieldList from '@/views/mainview/sceneTasks/detail/hooks/useFieldList';
import { FormulaType } from '@/api/formula';
import BFormula from '@/components/codemirror/BFormula.vue';
import fieldConditionConfig from '@/components/form-config/common-config/components/fieldConditionConfig.vue';
import { extentFieldByEquation, createList } from '@/components/form-config/types/FieldTypes';
import { FormulaEditAllType } from './type.ts'

const props = defineProps<{
  data: FormulaEditAllType;
}>();
const fieldList = ref<AvueColumns[]>([]);

const { getFiledList } = useFieldList(extentFieldByEquation, {});

const visible = ref(false);
const visible1 = ref(false);
const linkageOperator = ref('1');

const saveFormula = () => {
  visible.value = false;
};

const field = computed<any[]>(() => {
  return fieldList.value.map(v => {
    return {
      ...v,
      // label: v.copyLabel,
      // contentType: v.type,
      // fieldType: v.fixType,
    };
  });
});

watch(
  () => props.data.formulaFilter.taskId,
  val => {
    fieldList.value = [];
    // props.data.formula = {};
    if (val) {
      getFiledList({
        id: props.data.formulaFilter.taskId,
        unFlatDynamic: false,
        unFlatDataCollect: false,
        unFlatDataSelect: false,
        unFlatKnowledgeSelect: false,
      }).then(data => {
        fieldList.value = [...(data as AvueColumns[]), ...createList].filter(item =>
          (extentFieldByEquation as any).includes(item.fixType)
        );
      });
    } else {
      // fieldList1.value = []
    }
  },
  { immediate: true }
);

const handleSelect = () => {
  props.data.formula = {};
};
</script>

<style scoped lang="scss">
.dataBox {
  :deep(.el-checkbox-group) {
    @apply flex-(~ col) ml-10px;
  }
}

::v-deep(.container) {
  max-height: 340px; /* 限制最大高度 */
}
</style>
