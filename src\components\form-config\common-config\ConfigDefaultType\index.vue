<template>
  <el-form-item label="默认值" v-if="dynamicSet?.defaultType !== DynamicTypeEnum.GROUPSUMMARY">
    <el-select
      v-if="formType !== formTypeEnum.TEMPLATE"
      v-model="data.defaultType"
      class="mb-10px"
      @change="changeDefaultType"
    >
      <el-option
        v-for="item in (dic as Option[])"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <div
      v-if="[DynamicTypeEnum.KNOWLEDGE,DynamicTypeEnum.ORGKNOWLEDGE,DynamicTypeEnum.DATASELECT].includes(data.defaultType as DynamicTypeEnum)"
      class="flex m-2"
    >
      数据类型
      <avue-radio
        class="ml-2"
        v-model="data.dynamicMulType"
        :dic="dynamicDataTypeDic"
      ></avue-radio>
    </div>

    <!--选规则 值与值 值与数据表  -->
    <RuleSelector
      v-if="formType === formTypeEnum.FORM"
      :data="data"
      :taskId="String(taskId)"
      v-model:rulesType="rulesType"
    />

    <component
      :is="configComponent"
      :data="data"
      v-if="rulesType === 1"
    >
      <slot />
    </component>
  </el-form-item>
</template>

<script setup lang="ts">
import { ref, watch, computed, inject} from 'vue';
import { 
  defaultTypeDic, 
  DefaultTypeEnum, 
  dynamicTypeDic,
  DynamicTypeEnum, 
  dynamicDataTypeDic, 
  DynamicDataTypeEnum 
} from '@/components/form-config/const/commonDic';
import ConfigDataLink from '../ConfigDataLink/index.vue';
import configAppDataLink from '../ConfigAppDataLink/index.vue';
import ConfigGroupSummary from '../ConfigGroupSummary/index.vue';
import ConfigFormula from '../ConfigFormula/index.vue';
import ConfigValueItem from '../ConfigValueItem/index.vue';
import ConfigDataCollect from '../ConfigDataCollect/index.vue';
import configSetFields from '../configSetFields/index.vue';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { formTypeEnum } from '@/components/form-config/const/commonDic';
import RuleSelector from '../ConfigRuleSelect/index.vue'; // 引入新组件
import { findParentById }  from '@/utils/field'
import { deptGroupType } from '@/components/form-config/types/FieldTypes.ts';
import { DataLinkField,DataFilter,FormulaField,appDataLinkField,DynamicAllField } from '@/components/form-config/types/AvueTypes';
import { configDefaultValueType, } from './type';
// import { AvueColumns } from '@/components/form-config/types/common';




interface Option {
  label: string;
  value: number;
}
const { taskId } = storeToRefs(useTaskDetailStore());
const props = defineProps<configDefaultValueType>();
const allColumn = inject('allColumn', {column: []})
const rulesType = ref(1);
const formType = inject<formTypeEnum | undefined>('formType', formTypeEnum.FORM) as formTypeEnum;
const dynamicSet = ref<DynamicAllField>()


const dic= computed(() => {
  if (formType === formTypeEnum.TEMPLATE) {
    //字段库的组件只有自定义默认值
    return defaultTypeDic[0];
  } else if (props.data.isDynamic) {
    //表格内的组件都不要数据联动
    return defaultTypeDic.filter((v: any) => !_.includes(v.noShowType, props.data.fixType) && v.value !== DefaultTypeEnum.LINK);
  } else if (props.data.fixType === 'dynamic') {
    // 微应用子表单和表格只要自定义
    return formType === formTypeEnum.APPLICATION ? 
    dynamicTypeDic.filter((item) => _.includes([DefaultTypeEnum.CUSTOM, DefaultTypeEnum.LINK], item.value)) : 
    dynamicTypeDic;
  } else if(_.includes(deptGroupType,props.data.fixType) && _.includes([0,1],(props.data as any)?.limitRange)){
    //工作组部门限制范围不要数据联动/公式编辑
    if(_.includes([1],(props.data as any)?.limitRange)){
      return []
    }
     if(_.includes([0],(props.data as any)?.limitRange)){
      return defaultTypeDic.filter((item) =>_.includes([DefaultTypeEnum.CUSTOM], item.value));
    }
  }else {
    return defaultTypeDic.filter(
      (item) =>
        !_.includes(item.noShowType, props.data.fixType)
    );
  }
})


//defaultType对应的组件
const configs = {
  0: ConfigValueItem,
  1: formType === formTypeEnum.APPLICATION ? configAppDataLink : ConfigDataLink, //defaultType为1时，需要区分微应用和任务
  2: ConfigFormula,
  3: ConfigDataCollect,
  4: ConfigDataCollect,
  5: configSetFields,
  6: configSetFields,
  9: ConfigDataCollect,
  10: ConfigGroupSummary
};

const configComponent = computed(() => configs[props.data.defaultType as keyof typeof configs]);

watch(
  () => props.data.dicData,
  (val,oldVal) => {
    props.data.defaultValue = null
  },
  {
    deep: true,
  }
);

watch(
  () => props.data.defaultType,
  (val) => {
    const isFormula = _.includes([DefaultTypeEnum.FORMULA, DefaultTypeEnum.TRIGGERTIME], val)
    props.data.disabled = isFormula;

    if (val !== DefaultTypeEnum.CUSTOM) {
      // 不是自定义则清空并不必填
      delete props.data.defaultValue;
    }
    if (isFormula) {
      props.data.required = false;
    }

    if(val === DefaultTypeEnum.SIGN){
      props.data.disabled = true
    }

    if(val === DynamicTypeEnum.GROUPSUMMARY){
      (props.data as DynamicAllField).children.forEach((item: any )=> {
        item.defaultType = DefaultTypeEnum.CUSTOM
        item.disabled = false
        item.formula = {}
        item.defaultValue = null
      })
    }
  }
);

watch(
  () => allColumn,
  () => {
    dynamicSet.value = props.data.isDynamic? findParentById(allColumn?.column, props.data.id)?.parent : null
  },
  {
    deep: true,
    immediate:true
  }
)

const changeDefaultType = () => {
  rulesType.value = 1;
  props.data.ruleExpressId = '';
  (props.data as DataLinkField).dataLinks = { list: [] };
  (props.data as DataFilter).query = { list: [] };
  (props.data as FormulaField).formula = {};
  props.data.dynamicMulType = DynamicDataTypeEnum.SINGLE;
  props.data.defaultValue = null
  props.data.linkFields = [];
  (props.data as appDataLinkField).appDataLinks = undefined
};
</script>

<style scoped lang="scss"></style>
