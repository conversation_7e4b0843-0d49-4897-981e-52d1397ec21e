<script setup lang="ts">
import DialogForm from '@/views/mainview/sceneTasks/components/taskManagement/component/dialogForm.vue';
import { ref, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';
import SelectDeptBy from '@/components/form-component/XTUserDept/XTUserDept.vue';
import SelectUserByDept from '@/components/select/selectUserByDept.vue';
import { bindMicroCategorize } from "@/api/application/microGroup.ts"
import { storeToRefs } from 'pinia';
import mainStore from '@/views/microApp/group/store';

interface PackageProp {
  userIds:any[],
  deptIds:any[]
}
interface Emit {
  (e: 'initPage'): void
}
interface userType{
  nickName: string,
  id:string,
  relId:string,
  relType: 0 | 1,
}
provide('disabledUser','')

const emits = defineEmits<Emit>();

const selectVisible = ref(false);

const { activeWorkGroupId } = storeToRefs(mainStore());
const addMemberRef = useTemplateRef('addMemberRef');
const addMember = ref<PackageProp>({
  userIds: [],
  deptIds:[]
});


const addGroup = (list:userType[]) => {
  const deptIds = list?.filter(item => item.relType === 1).map(i => i.relId) || [];
  const userIds = list?.filter(item => item.relType === 0).map(i => ({...i,id:i.relId, })) || [];
  addMember.value = {
    userIds,
    deptIds,
  }
  addMemberRef.value?.open<PackageProp>(form => {
    const userIds = form.userIds?.map(i => i.id);
    const params = {
      ...form,
      userIds,
      categorizeId:activeWorkGroupId.value
    }
    bindMicroCategorize(params).then(res => {
      res.data.success &&
      addMemberRef.value?.close(() => {
        if (res.data.success) {
          ElMessage.success(res.data.msg);
          emits('initPage');
          addMember.value = {
            userIds: [],
            deptIds:[]
          }
          // initTreeData();
        }
      });
    });
  });
}

defineExpose({
  addGroup
});
</script>

<template>
  <dialog-form
    ref="addMemberRef"
    dialogType="drawer"
    title="添加成员"
    :rules="{
        groupName: { required: true, message: '请输入分类名称', trigger: 'blur' },
      }"
    v-model="addMember"
  >
    <el-form-item label="选择成员" prop="deptIds">
      <select-dept-by placeholder="请选择部门" v-model="addMember.deptIds"></select-dept-by>

      <el-card
        class="box-card"
        @click="selectVisible = true"
      >
        <template v-if="addMember.userIds.length > 0">
          <el-tag
            v-for="item in addMember.userIds"
            :key="item.id"
            style="margin: 0 5px"
            size="large"
          >{{ item?.nickName }}
          </el-tag>
        </template>
        <div v-else class="select_placeholder">{{ '请选择成员' }}</div>
      </el-card>

      <selectUserByDept data-type="string" v-model:model-list="addMember.userIds" v-model:visible="selectVisible"></selectUserByDept>

    </el-form-item>
  </dialog-form>
</template>

<style scoped lang="scss">
.group {
  background-color: #eee;
  color: #333;
}

.disabled {
  background: #f6f6f6;
}

.select_placeholder {
  color: #a8abb2;
  font-size: 14px;
}
.box-card {
  cursor: pointer;
  width: 100%;

  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
</style>
