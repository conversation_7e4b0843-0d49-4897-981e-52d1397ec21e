<script setup>
import handleAuditIcon from '@/images/handle/handleAuditIcon.svg';

const vModel = defineModel();

const tabActiveName = defineModel('tabActiveName');

const props = defineProps({
  tenantList: {
    type: Array,
    default: () => []
  }
});

const infoStatusList = ['未审核', '审核成功', '审核失败'];


const reToHandle = () => {
  router.push({
    path: '/m/deptInviteCheck',
    query: {
      idCard: vModel.value.idCard,
      tenantName: vModel.value.tenantName,
      nickName: vModel.value.nickName,
      attachFiles: vModel.value.attachFiles,
      id: vModel.value.id,
      agreeStatus: vModel.value.agreeStatus
    }
  });
};

</script>

<template>
  <div @click="reToHandle">
    <a-flex vertical gap="12" class="card-item">
      <a-flex gap="8" align="center" justify="space-between" class="item-title">
        <a-flex align="center">
          <handleAuditIcon />
        </a-flex>
        <div class="flex-1">
          信息审核
        </div>
      </a-flex>
      <a-flex vertical gap="4" class="item-content">
        <div>
          用户名：{{ vModel.nickName }}
        </div>
        <div>
          组织来源：{{ vModel.tenantName }}
        </div>
        <div>
          审核状态：{{ infoStatusList[vModel.agreeStatus] }}
        </div>
      </a-flex>
    </a-flex>
  </div>
</template>

<style scoped lang="scss">
.card-item {
  margin: 8px 16px;
  padding: 12px 0;
  background: #ffffff;
  border-radius: 8px;

  .item-tip {
    padding: 0 12px;
  }

  .item-title {
    padding: 0 12px;
  }

  .item-content {
    background: #F7F8FA;
    margin: 0 12px;
    padding: 12px 12px;
  }
}
</style>