import { DropdownItem, MarkMenuTypeEnum, SortEnum, WidgetGroupEnum } from '@/components/chart-design/types/widget.ts';
import { ElDropdownItem } from 'element-plus';
import { ElSelectDropdown } from 'xt-component';
import { SortDic } from '@/components/chart-design/const/widget.ts';
import { storeToRefs } from 'pinia';
import useChartDesignStore from '@/components/chart-design/store/useChartDesignStore.ts';

const generator: DropdownItem = {
  key: MarkMenuTypeEnum.SORT,
  label: '排序方式',
  component: function(params) {
    return defineComponent(() => {
      const { metricsList, dimensionsList } = storeToRefs(useChartDesignStore());

      const clearOther = (value: string) => {
        if (value === SortEnum.NONE) {
          return;
        }
        switch (params.refType) {
          case WidgetGroupEnum.DIMENSIONS:
            // 如果在维度，则清空指标
            metricsList.value?.forEach(i => i.sort = SortEnum.NONE);
            break;
          case WidgetGroupEnum.METRICS:
            // 如果在指标，则清空维度和指标
            metricsList.value?.forEach(i => {
              if (i.id !== params.item.id) {
                i.sort = SortEnum.NONE;
              }
            });
            dimensionsList.value?.forEach(i => i.sort = SortEnum.NONE);
            break;
          default:
        }
      };

      return () => {
        return (
          <ElDropdownItem>
            <ElSelectDropdown
              options={SortDic}
              v-model={params.item.sort}
              defaultSelected={SortEnum.NONE}
              onChange={(value) => clearOther(value)}
            >
              {this.label}
            </ElSelectDropdown>
          </ElDropdownItem>
        );
      };
    });
  }
};

export default generator;