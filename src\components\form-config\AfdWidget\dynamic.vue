<template>
  <div class="widget-dynamic hover" :class="{ 'required-title': column.required,active: selectWidget.prop == column.prop }">
    <h3 style="margin: 10px" v-show="column.showLabel">{{ column.copyLabel }}</h3>
    <draggable
      class="widget-dynamic__body"
      :class="{ 'widget-dynamic__body_form': column.dynamicType !== 'crud' }"
      :list="column.children"
      :group="{ name: 'form' }"
      ghost-class="ghost"
      :animation="200"
      item-key="prop"
      @add="handleWidgetTableAdd($event, column)"
      @end="$emit('change')"
      :disabled="configDisabled"
    >
      <template #item="{ element, index }">
        <div
          class="widget-dynamic__item hover-item drag"
          v-if="column.dynamicType === 'crud'"
          :class="{ 'active-item': selectWidget.prop == element.prop, required: element.required }"
          @click.stop="handleWidgetTableSelect(element)"
        >
          <div class="wf-table" :style="[`width:${tableWidth}`]">
            <div class="wf-table__header" v-if="element.showLabel">{{ element.copyLabel }}</div>
            <div class="wf-table__body">
              <widget-item :item="element" :params="column.params" :source="1"></widget-item>
              <widget-button
                v-if="(selectWidget.prop == element.prop) && !configDisabled"
                type="table-item"
                @delete="handleWidgetTableDelete(column, index)"
                @copy="handleWidgetTableClone(column, element)"
              ></widget-button>
            </div>
          </div>
        </div>
        <el-col
          v-else
          class="w-full hover-item"
          :md="element.span || 12"
          :xs="24"
          :offset="element.offset || 0"
          @click.stop="handleWidgetTableSelect(element)"
        >
          <el-form-item
            class="widget-item drag bg-white hover:!bg-white pointer-events-none !static"
            :label="element.copyLabel"
            :prop="element.prop"
            :size="data.size || element.size || 'default'"
            :class="[
              { 'active-item': selectWidget.prop == element.prop, required: element.required },
              'avue-form__item--' + element.labelPosition || '',
            ]"
            :label-width="element.labelWidth"
          >
            <widget-item :item="element" :params="column.params" :source="1"></widget-item>
            <widget-button
              v-if="selectWidget.prop == element.prop"
              @delete="handleWidgetTableDelete(column, index)"
              @copy="handleWidgetTableClone(column, element)"
            ></widget-button>
          </el-form-item>
        </el-col>
      </template>
      <template #footer v-if="column.children?.length <= 0">
        <el-empty size="50" style="width: 100%" description="拖拽字段至此"></el-empty>
      </template>
    </draggable>
    <widget-button
      v-if="(selectWidget.prop == column.prop) && !configDisabled"
      type="group"
      @delete="handleWidgetDelete(index)"
      @copy="handleWidgetCloneTable(index)"
      @clear="handleWidgetClear(index)"
    ></widget-button>
  </div>
</template>
<script>
import { Edit } from '@element-plus/icons-vue';
import { canIntoDynamicType } from '@/components/form-config/types/FieldTypes';
import { generateId, generateUniqueName } from '@/components/form-config/utils';
import Draggable from 'vuedraggable';
import WidgetButton from './button.vue';
import WidgetItem from './item.vue';
import { ElMessage } from 'element-plus';
import { fieldSource,DefaultTypeEnum } from '@/components/form-config/const/commonDic';
import { draggable } from 'element-plus/es/components/color-picker/src/utils/draggable.mjs';
import { formTypeEnum } from '@/components/form-config/const/commonDic';



export default {
  name: 'widget-dynamic',
  props: ['data', 'column', 'select', 'index','dels','formType'],
  emits: ['update:select', 'change','update:dels'],
  components: { WidgetItem, WidgetButton, Draggable },
  data() {
    return {
      selectWidget: this.select,
      showHidden: true,
      tableWidth: '300px',
      configDisabled:inject('disabled',false)
    };
  },
  methods: {
    handleSelectWidget(index) {
      this.selectWidget = this.data.column[index];
    },
    handleWidgetClear(index) {
      this.data.column[index].children = [];
      this.selectWidget = this.data.column[index];
      this.$emit('change');
    },
    handleWidgetDelete(index) {
      if (this.data.column.length - 1 === index) {
        if (index === 0) this.selectWidget = {};
        else this.handleSelectWidget(index - 1);
      } else this.handleSelectWidget(index + 1);

      this.$nextTick(() => {
        if(this.data.column[index].mid){
          this.delList.push(this.data.column[index].mid)
        }
        console.log(this.delList,'this.delList')
        this.data.column.splice(index, 1);
        this.$emit('change');
      });
    },
    handleWidgetCloneTable(index) {
      let cloneData = this.deepClone(this.data.column[index]);
      cloneData.prop = generateId(cloneData.fixType);
      let startIndex = cloneData.prop.indexOf('#');
      cloneData.id = cloneData.prop.slice(startIndex + 1);
      // 复制组件时删除标题
      cloneData.copyLabel = ''
      //复制组件时删除模板id
      cloneData.mid = ''
      cloneData.children.forEach(t => {
        t.prop = generateId(t.fixType);
        let startIndex = t.prop.indexOf('#');
        t.id = t.prop.slice(startIndex + 1);
        t.mid = ''
      });
      this.data.column.splice(index + 1, 0, cloneData);
      this.$nextTick(() => {
        this.handleSelectWidget(index + 1);
        this.$emit('change');
      });
    },
    handleWidgetTableAdd(evt, column) {
      console.log(column,'column');
      const dynamicType = column.dynamicType;
      let newIndex = evt.newIndex;
      const item = evt.item;

      if (newIndex == 1 && newIndex > column.children?.length - 1) newIndex = 0;
      const curcolumn = column.children[newIndex];
      const copyLabelList = column.children.filter(v => v.copyLabel === curcolumn.copyLabel)

      // if (copyLabelList.length > 1) {
      //   column.children.splice(newIndex, 1);
      //   ElMessage.warning('不可添加重名的组件')
      //   return
      // }
      if (['crud', 'form'].includes(dynamicType) && !canIntoDynamicType.includes(column.children[newIndex].fixType)) {
        column.children.splice(newIndex, 1);
        this.$message.warning('该字段无法加入到子表单或表格');
        return;
      }

      const data = this.deepClone(column.children[newIndex]);

      if (copyLabelList.length > 1) {
        data.copyLabel = generateUniqueName(data.copyLabel, copyLabelList.map(i => i.copyLabel));
      
      }
      if (!data.prop) data.prop = generateId(data.fixType);
      let startIndex = data.prop.indexOf('#');
      if (!data.id) data.id = data.prop.slice(startIndex + 1);

      data.subfield = true;
      delete data.icon;

      data.isDynamic = true;
      if (this.column.dynamicType === 'form') {
        data.isForm = true;
        data.isCrud = false;
        // data.isTrigger = false;
        data.defaultType = 0
        data.span = 24
        data.query = { list:[] }
        data.dataLinks = { list:[] }
      }else {
        data.isForm = false;
        data.isCrud = true;
        data.isTrigger = false;
        data.defaultType = 0
        data.span = 24
        data.query = { list:[] }
        data.dataLinks = { list:[] }
      }
      column.children[newIndex] = { ...data, width: this.tableWidth };
      this.selectWidget = column.children[newIndex];

      this.$emit('change');
    },
    handleWidgetTableSelect(data) {
      console.log(data,'data')
      this.selectWidget = data;
    },
    handleWidgetTableClone(column, item) {
      const data = this.deepClone(item);
      data.prop = generateId(data.fixType);
      let startIndex = data.prop.indexOf('#');
      data.id = data.prop.slice(startIndex + 1);
      data.mid = ''
      column.children[column.children?.length] = { ...data };
      this.$nextTick(() => {
        this.selectWidget = column.children[column.children?.length - 1];
        this.$emit('change');
      });
    },
    handleWidgetTableDelete(column, index) {
      if (column.children?.length - 1 == index) {
        if (index == 0) this.selectWidget = column;
        else this.selectWidget = column.children[index - 1];
      } else this.selectWidget = column.children[index + 1];
      this.$nextTick(() => {
        if (column.children[index].mid) {
          this.dels.push(column.children[index].mid)
        }
        column.children.splice(index, 1);
        this.$emit('change');
      });
    },
  },
  computed:{
    delList: {
      get() {
        return this.dels
      },
      set (v){
        this.$emit('update:dels', v);
      }
    }
  },
  watch: {
    select(val) {
      this.selectWidget = val;
    },
    selectWidget: {
      handler(val) {
        this.$emit('update:select', val);
      },
      deep: true,
    },
  },
};
</script>
