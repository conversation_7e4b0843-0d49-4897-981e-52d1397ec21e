<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 45</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-910.000000, -1560.000000)">
            <g id="编组-45" transform="translate(910.000000, 1560.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-5" transform="translate(10.000000, 42.000000)" font-family="PingFangSC-Regular, PingFang SC"
                   font-size="14" font-weight="normal" line-spacing="23">
                    <text id="MID返回文本中从指定位置开始的指定数目">
                        <tspan x="0" y="15" fill="#3F70FF">MID</tspan>
                        <tspan x="25.55" y="15" fill="#3A3A3A">返回文本中从指定位置开始的指定数目的字</tspan>
                        <tspan x="0" y="38" fill="#3A3A3A">符。</tspan>
                        <tspan x="0" y="62" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="62" fill="#3F70FF">MID</tspan>
                        <tspan x="74.55" y="62" fill="#3A3A3A">(文本,开始位置_数字,指定数目)</tspan>
                        <tspan x="0" y="86" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="86" fill="#3F70FF">MID</tspan>
                        <tspan x="74.55" y="86" fill="#3A3A3A">("伴办让办事不再困难",4,6)返回"办</tspan>
                        <tspan x="0" y="109" fill="#3A3A3A">事不再困难"。</tspan>
                    </text>
                </g>
                <text id="MID" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">MID</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
