<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>DATEIF</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-1183.000000, -350.000000)">
            <g id="DATEIF" transform="translate(1183.000000, 350.000000)">
                <g id="if备份">
                    <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                    <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                    <g id="编组-2" transform="translate(10.000000, 42.000000)">
                        <text id="DATEDIF函数可以计算两个日期时间相" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="23" letter-spacing="1.5">
                            <tspan x="0" y="15" fill="#3F70FF">DATEDIF</tspan>
                            <tspan x="68.446" y="15" fill="#3A3A3A">函数可以计算两个日期时间相差的</tspan>
                            <tspan x="0" y="38" fill="#3A3A3A">年数、月数、天数。</tspan>
                            <tspan x="139.5" y="38" letter-spacing="0.4" fill="#3F70FF"></tspan>
                            <tspan x="0" y="62" letter-spacing="0.4" fill="#3A3A3A">·用法：</tspan>
                            <tspan x="50.6" y="62" letter-spacing="0.4" fill="#3F70FF">DATEDIF</tspan>
                            <tspan x="111.346" y="62" letter-spacing="0.4" fill="#3A3A3A">(开始时间,结束时间,[单位]),单</tspan>
                            <tspan x="0" y="85" letter-spacing="0.4" fill="#3A3A3A">位可以是"y" 、"M"、"d"</tspan>
                            <tspan x="0" y="109" letter-spacing="0.4" fill="#3A3A3A">·示例：</tspan>
                            <tspan x="50.6" y="109" letter-spacing="0.4" fill="#3F70FF">DATEDIF</tspan>
                            <tspan x="111.346" y="109" letter-spacing="0.4" fill="#3A3A3A">( </tspan>
                        </text>
                        <g id="编组-4" transform="translate(117.000000, 97.000000)">
                            <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="52" height="16"></rect>
                            <text id="下单时间：" font-family="PingFangSC-Regular, PingFang SC" font-size="10" font-weight="normal" line-spacing="16" fill="#3F70FF">
                                <tspan x="2" y="11">下单时间：</tspan>
                            </text>
                            <g id="编组-30" transform="translate(0.000000, 2.000000)"></g>
                        </g>
                        <g id="编组-4备份" transform="translate(178.000000, 97.000000)">
                            <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="52" height="16"></rect>
                            <text id="支付时间：" font-family="PingFangSC-Regular, PingFang SC" font-size="10" font-weight="normal" line-spacing="16" fill="#3F70FF">
                                <tspan x="2" y="11">支付时间：</tspan>
                            </text>
                            <g id="编组-30" transform="translate(0.000000, 2.000000)"></g>
                        </g>
                        <text id="，" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#3A3A3A">
                            <tspan x="171" y="109">，</tspan>
                        </text>
                        <text id="，" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#3A3A3A">
                            <tspan x="232" y="109">，</tspan>
                        </text>
                        <text id="，" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#3A3A3A">
                            <tspan x="263" y="109">，</tspan>
                        </text>
                        <text id="如果" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#3A3A3A">
                            <tspan x="273" y="111">如果</tspan>
                        </text>
                        <text id="下单时间是2023/7/17，付款时间为" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" letter-spacing="0.8" fill="#3A3A3A">
                            <tspan x="0" y="136">下单时间是2023/7/17，付款时间为</tspan>
                        </text>
                        <text id="18，计算得到差为1天。" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" letter-spacing="0.8" fill="#3A3A3A">
                            <tspan x="0" y="161">18，计算得到差为1天。</tspan>
                        </text>
                        <text id="2023/7/" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" letter-spacing="1.3" fill="#3A3A3A">
                            <tspan x="240" y="136">2023/7/</tspan>
                        </text>
                    </g>
                    <text id="DATEDIF" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="23" fill="#3A3A3A">
                        <tspan x="11" y="20">DATEDIF</tspan>
                    </text>
                </g>
                <text id="&quot;d&quot;" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="23" fill="#3A3A3A">
                    <tspan x="248" y="152">"d"</tspan>
                </text>
                <text id="）" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#3A3A3A">
                    <tspan x="268" y="154">）</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>