<template>
  <el-drawer v-model="visible" title="表单预览" size="50%" class="afd-drawer" append-to-body :destroy-on-close="true"
    v-if="visible">
    <div v-loading="loading">
      {{formData}}
      <xt-el-form  ref="formRef" v-model="formData" :option="option" :taskPackageId="taskPackageId" :source="3"></xt-el-form>
    </div>
    <span class="afd-drawer__footer">
      <el-button @click="handleSubmit" type="primary">{{ submitText || '提交' }}</el-button>
      <el-button @click="hide">取消</el-button>
    </span>
  </el-drawer>
</template>


<script setup lang="ts">
import { ref } from 'vue';
import XtElForm from '@/components/xt-el-form/XtElForm.vue';
import { PackageIdInjectKey } from '@/components/form-config/utils/injectKeys';
import { getAllLinkAndFormulaProps,extractFirstPart,shouldProcessWithFalseFlag } from '@/utils/field';
import { useFormFieldLinkage } from '@/views/banban/handle/useLink';
import { inject } from 'vue';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { storeToRefs } from 'pinia';
import { ElMessageBox } from "element-plus";
import { previewTypeKey } from '@/components/xt-el-form/constant.ts';
import { previewEnum } from '@/components/form-config/const/commonDic';
import { clearCache, getFormulaList } from '@/api/banban'

// Props
defineProps({
  submitText: {
    type: String,
    default: () => '提交',
  },
});
const computedLinkData = ref<any[]>([]);//公式编辑 联动的依赖prop数组
const visibleData = ref<any[]>([])

// Inject
const taskPackageId = inject(PackageIdInjectKey);
const { taskId, hides } = storeToRefs(useTaskDetailStore()) as any;
const loading = ref(false);
const previewType = inject(previewTypeKey, undefined);

// Reactive state
const visible = ref(false);
const formData = ref<Record<string, any>>({});
const option = ref({
  column: [] as any[],
});

const formRef = ref();
if(previewType === previewEnum.TASK){
  useFormFieldLinkage(option, formData, computed(() => taskId.value), undefined, computedLinkData, loading);
}

const handleClearCache = async () => {
  await clearCache(taskId.value);
};
// Methods
async function show(column: any[]) {
  formData.value = {};
  computedLinkData.value = [];
  option.value.column = [];
  //任务的预览才掉整体联动的接口
  if(previewType === previewEnum.TASK){
   await handleClearCache()
   const { data: data1 } = await getFormulaList(taskId.value)
   const formulaData = data1.data //值与值公式
    visibleData.value = hides.value?.groups || [];
    //判断是否有显隐
    if (visibleData.value?.length && shouldProcessWithFalseFlag(visibleData.value, column)) {
      column.forEach(v => {
        v.visibleBool = false
      })
      option.value.column = column
    } else {
      column.forEach(v => {
        v.visibleBool = true
      })
      option.value.column = column
    }
    computedLinkData.value = getAllLinkAndFormulaProps(option.value.column, visibleData.value, formulaData);
    console.log(computedLinkData.value, 'computedLinkData.value')
  }else{
    option.value.column = _.cloneDeep(column);
  }
   

  visible.value = true;
}

function hide() {
  visible.value = false;
}

function handleSubmit() {
  formRef.value?.validate((valid: boolean, done: any) =>  {
    if (valid) {
      done();
      ElMessageBox.alert(formData.value as any, '表单结果预览', {
        confirmButtonText: '确定',
      })
  }
})
}

// Expose methods for parent components
defineExpose({ show, hide, handleSubmit });
</script>
