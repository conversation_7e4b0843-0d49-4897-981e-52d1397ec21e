<template>
  <div class="xtTipsConfig">
    <config-no-tips-title :data="data" />
    <el-form-item label="文字内容" v-if="showValue">
      <el-input
        type="textarea"
        v-model="data.value"
        placeholder="请输入文字内容"
        :rows="10"
      ></el-input>
    </el-form-item>
    <config-permission :data="data" :has-required="false" :show-edit="false" />
    <!-- <config-data-link :data="data" isFormItem /> -->
    <!-- <config-default-type :data="data" /> -->
    <config-span :data="data" />
  </div>
</template>

<script setup lang="ts">
import ConfigNoTipsTitle from '@/components/form-config/common-config/ConfigNoTipsTitle/index.vue';
import { TipsAllField } from './type';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic.ts';

const props = defineProps<{
  data: TipsAllField;
}>();

const showValue = ref(true);

watchEffect(() => {
  props.data.content = props.data.value;
});

watchEffect(() => {
  if(props.data.defaultType === DefaultTypeEnum.CUSTOM){
    showValue.value = true;
  }else{
      showValue.value = false;
      props.data.value = '';
  }
});
</script>
