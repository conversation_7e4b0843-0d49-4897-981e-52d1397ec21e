<template>
  <div class="xtTips">
    <div class="xtTips-content">
      {{ model || content }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { TipsAllField } from '@/components/form-config/types/AvueTypes';

defineProps<TipsAllField>();

const model = defineModel({ required: false });
</script>

<style scoped lang="scss">
.xtTips {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  &-title {
    font-size: 14px;
    font-weight: 500;
    color: #424242;
    line-height: 22px;
  }

  &-content {
    margin-top: 5px;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 22px;
    white-space: pre-wrap;

    :deep(p) {
      margin: 0;
    }
  }
}
</style>
