<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 38</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-570.000000, -955.000000)">
            <g id="编组-38" transform="translate(570.000000, 955.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <text id="MOD函数可以获取两数相除的余数。-·用" font-family="PingFangSC-Regular, PingFang SC"
                      font-size="14" font-weight="normal" line-spacing="23">
                    <tspan x="10" y="57" fill="#3F70FF">MOD</tspan>
                    <tspan x="42.97" y="57" fill="#3A3A3A">函数可以获取两数相除的余数。</tspan>
                    <tspan x="10" y="81" fill="#3A3A3A">·用法：</tspan>
                    <tspan x="59" y="81" fill="#3F70FF">MOD</tspan>
                    <tspan x="91.97" y="81" fill="#3A3A3A">(被除数,除数)</tspan>
                    <tspan x="10" y="105" fill="#3A3A3A">·示例：</tspan>
                    <tspan x="59" y="105" fill="#3F70FF">MOD</tspan>
                    <tspan x="91.97" y="105" fill="#3A3A3A">(4,3)返回1,也就是4/3的余数</tspan>
                </text>
                <text id="MOD" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">MOD</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
