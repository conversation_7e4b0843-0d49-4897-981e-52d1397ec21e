<template>
  <el-select class="w-full" v-bind="$attrs" :placeholder="placeholder" :clearable="clearable">
    <el-option
      v-for="item in dicts"
      :key="item[props.value]"
      :label="item[props.label]"
      :value="item[props.value]"
      @click="$emit('select',item[props.value],item)"
    />
  </el-select>
</template>

<script setup>
const prop = defineProps({
  dic: {
    type: Array,
    default: () => [],
  },
  dicData: {
    type: Array,
    default: () => [],
  },
  props: {
    default: () => {
      return {
        label: 'label',
        value: 'value',
      };
    },
  },
  placeholder: {
    type: String,
    default: () => '请选择',
  },
  clearable: {
    type: Boolean,
    default: () => true,
  },
});

defineEmits(['select'])

const dicts = computed(() => (!_.isEmpty(prop.dic) ? prop.dic : prop.dicData));
</script>

<style scoped lang="scss"></style>
