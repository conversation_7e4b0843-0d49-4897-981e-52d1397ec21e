 import {
   AllDefaultValueType,
   FieldBaseField,
   TitleField,
   basePermissionType,
   singleType,
   dataValueType
 } from '@/components/form-config/types/AvueTypes';

/* 工作组选择 */
export type UserTagField = basePermissionType& dataValueType & {
  isRestrictedWorkgroups: Boolean;
  limitRange?: number,
  limitIds?: string[]
  isLimit: boolean;
};
export type UserTagAllField = FieldBaseField  & TitleField & UserTagField & AllDefaultValueType & 
singleType;