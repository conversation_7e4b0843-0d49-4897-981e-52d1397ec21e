<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 38</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-230.000000, -1240.000000)">
            <g id="编组-38" transform="translate(230.000000, 1240.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <text id="SUMPRODUCT函数可以将数组间对应" font-family="PingFangSC-Regular, PingFang SC" font-size="14"
                      font-weight="normal" line-spacing="23">
                    <tspan x="10" y="57" fill="#3F70FF">SUMPRODUCT</tspan>
                    <tspan x="109.12" y="57" fill="#3A3A3A">函数可以将数组间对应的元素相</tspan>
                    <tspan x="10" y="80" fill="#3A3A3A">乘，并返回乘积之和，适用于加权求和。</tspan>
                    <tspan x="10" y="104" fill="#3A3A3A">·用法：</tspan>
                    <tspan x="59" y="104" fill="#3F70FF">SUMPRODUCT</tspan>
                    <tspan x="158.12" y="104" fill="#3A3A3A">(数组,数组...)</tspan>
                    <tspan x="10" y="128" fill="#3A3A3A">·示例：</tspan>
                    <tspan x="59" y="128" fill="#3F70FF">SUMPRODUCT</tspan>
                    <tspan x="158.12" y="128" fill="#3A3A3A">([1,2,3],[0.1,0.2,0.3])返</tspan>
                    <tspan x="10" y="151" fill="#3A3A3A">回1.4，也就是 1×0.1 + 2×0.2 + 3×0.3的值</tspan>
                </text>
                <text id="SUMPRODUCT" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">SUMPRODUCT</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
