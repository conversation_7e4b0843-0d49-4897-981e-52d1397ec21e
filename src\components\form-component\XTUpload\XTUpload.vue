<template>
  <div class="w-full">
    <avue-upload
      v-if="!$attrs.disabled"
      v-model="model"
      v-bind="$attrs"
      :uploadPreview="previewBack"
      :limit="$attrs.fileNumber"
    ></avue-upload>
    <template v-else>
      <div
        v-for="item in model"
        class="hover:bg-gray-100 px-2 flex items-center rounded"
        @click="previewBack(item)"
      >
        <el-icon class="color-[#909399]"><Document /></el-icon>
        <span class="ml-2 color-[#409eff] overflow-hidden whitespace-nowrap text-ellipsis">{{
          item?.name
        }}</span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useFormItem } from 'element-plus';

const { formItem } = useFormItem();

const model = defineModel<any>({ default: () => [] });
function previewBack(file: { name: string; url: string }) {
  const officeEnd = ['docx', 'doc', 'xlsx', 'xls', 'pptx'];
  const startUrl = 'https://view.officeapps.live.com/op/view.aspx?src=';
  const fileType = file.url.substring(file.url.lastIndexOf('.') + 1);

  const openUrl = officeEnd.includes(fileType) ? startUrl + file.url : file.url;
  window.open(openUrl, '_blank');
  return;
}

watch(
  () => model.value,
  () => {
    formItem?.validate('blur');
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list__item-file-name {
  display: inline-block;
  max-width: 200px; /* 设置一个最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
