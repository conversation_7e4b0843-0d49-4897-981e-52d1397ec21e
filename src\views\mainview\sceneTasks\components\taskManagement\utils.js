export function mergeTreeData(tree) {
  if (!Array.isArray(tree)) {
    return [];
  }

  function processNode(node) {
    if (node.children && Array.isArray(node.children)) {
      // 给 children 中的数据添加标识
      const markedChildren = node.children.map(child => ({
        ...child,
        source: 'GRO<PERSON>',
        name:child.groupName
      }));

      if (node.relTasks && Array.isArray(node.relTasks)) {
        // 给 taskList 中的数据添加标识
        const markedTaskList = node.relTasks.map(task => ({
          ...task,
          source: 'TASK',
          name:task.taskName
        }));
        // 合并带有标识的数据
        node.children = markedChildren.concat(markedTaskList);
      } else {
        node.children = markedChildren;
      }
      // 递归处理子节点
      node.children.forEach(processNode);
    }
    return node;
  }

  return tree.map(processNode);
}
