<template>
  <div class="w-full">
    <avue-crud :option="option" :data="vModel" v-if="isArray"> </avue-crud>

    <el-input v-else class="w-full" v-model="vModel" disabled></el-input>
  </div>
</template>
<script setup lang="ts">
import { StatisticalAllType } from '@/components/form-config/types/AvueTypes';

defineProps<StatisticalAllType>();
const vModel = defineModel<any>();
const option = ref({
  column: [
    {
      label: '成员选择',
      prop: 'userName',
    },
    {
      label: '数值',
      prop: 'value',
    },
  ],
  addBtn: false,
  menu: false,
  refreshBtn: false,
  columnBtn: false,
  gridBtn: false,
  header: true,
});

const isArray = computed(() => _.isArray(vModel.value));
</script>
<style lang="scss" scoped>
:deep(.avue-crud) .avue-crud__header {
  min-height: 0;
}
</style>
