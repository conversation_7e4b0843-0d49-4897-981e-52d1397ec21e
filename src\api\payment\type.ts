//积分记录
export interface pageType{
  current: number;
  size: number;

}
export interface InReqParams{
  actionType?: number;
  startTime?: string;
  endTime?: string;
  invoiceStatus?: number//开票状态 0未开票 1开票中 2已开票 3开票失败
}

export interface ReqParams extends pageType,InReqParams{}
export interface InResData {
  id: number;
  operateType: number; //操作类型
  dosage: number; //用量
  points: number; //消耗/充值金额
  operateUserName: string;
  operateTime: string;
  
}


//发票抬头
export namespace invoceHeader {
  export interface InReqParams{
    tenantId?: number;
    startTime?: string;
    endTime?: string;
    invoiceStatus?: number,
    titleType?:number
  }
  export interface ReqParams extends pageType,InReqParams{
    
  }

  export interface ResData {
    id?: string;
    pointOrderRecordIds?:string[]
    titleType: number,
    titleName: string;//发票抬头
    phone: string;
    depositBank: string;
    depositAccount: string;
    operateTime?: string;
    address: string;
    taxpayerIdentification: string;
    invoiceStatus?:number;
    invoiceId?: string,
    applyAmount?:string,
    invoiceMark?:string
  }
}


//发票

export namespace invoice{
  export interface InReqParams{
    tenantName?: string;
    startTime?: string;
    endTime?: string;
    invoiceStatus?: number
  }
  export interface ReqParams extends pageType,InReqParams{}

  export interface rejectType{
    id: string,
    invoiceMark?: string
  }
  export interface uploadType{
    id: string,
    invoiceUrl: string
  }
}