import request from '@/axios';

export const getTree = () => {
  return request({
    url: '/calTable/tree',
    method: 'post',
    data: {}
  });
};

export const submit = (form: any) => {
  return request({
    url: '/calTable/submit',
    method: 'post',
    data: form
  });
};

export const remove = (id: string) => {
  return request({
    url: '/calTable/remove',
    method: 'post',
    params: {
      ids: id
    }
  });
};

export const getCalByVersionId = (versionId: string, excludeId?: string) => {
  return request({
    url: '/calTable/getByVersionId',
    method: 'post',
    params: {
      versionId,
      excludeId
    }
  });
};

export const getFieldList = (calTableId: string, simple?: boolean) => {
  return request({
    url: '/calTableField/list',
    method: 'post',
    params: { simple },
    data: {
      calTableId
    }
  });
};

export const submitField = (form: any) => {
  return request({
    url: '/calTableField/submit',
    method: 'post',
    data: form
  });
};

export const removeField = (id: string) => {
  return request({
    url: '/calTableField/remove',
    method: 'post',
    params: {
      ids: id
    }
  });
};

export const detailField = (id: string) => {
  return request({
    url: '/calTableField/detail',
    method: 'post',
    data: {
      id
    }
  });
};
