<template>
  <div>
    <config-title :data="data"></config-title>
    <config-permission :data="data" /><el-form-item label="选择字段">
      <el-select v-model="data.userComponentType" placeholder="请选择当前表单的组件" clearable>
        <el-option v-for="item in selectList" :key="item.prop" :label="item.copyLabel" :value="(item.prop as string)" />
      </el-select>
    </el-form-item>
    <!-- <ConfigDataLink :data="data" /> -->
    <config-span :data="data"></config-span>
  </div>
</template>

<script setup lang="ts">
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import { findParentById }  from '@/utils/field'
import { usersType, getUsercodeType, userTagType } from '@/components/form-config/types/FieldTypes.ts';
import { WorkingAdminAllType } from './type';
import { AvueColumns } from '@/components/form-config/types/AvueTypes'
const types = [...usersType, ...getUsercodeType, ...userTagType]

const props = defineProps<{
  data: WorkingAdminAllType;
  column: {column: AvueColumns[]}
}>();

const selectList = ref<AvueColumns[]>([])

watch(() =>
  props.column.column,
  (val) => {
    if (!val.length) return
    if(props.data.isDynamic){
      const parent = findParentById(val,props.data.id)?.parent
      // const flatColumn = colsToFlatDynamic([parent], params)
      selectList.value = parent?.children?.filter((item:AvueColumns) => _.includes(types, item.fixType))
      return
    }
    selectList.value = val.filter(item => _.includes(types, item.fixType))
  },
  { immediate: true, deep: true })

</script>

<style scoped lang="scss"></style>
