<template>
  <div>
    <selectComponent
      title="选择成员"
      :tab-list="tabList"
      v-model:modelList="modelList"
      v-model:visible="visible"
      :submitApi="submitApi"
      :getTableList="getTableList"
      :dataCallBack="dataCallBack"
      :config="config"
    >
    </selectComponent>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { getGroupByOrgTree, getUserByGroup } from '@/api/common/index';
import selectComponent from './selectComponent.vue';
import { useStore } from 'vuex';

const store = useStore();
const userMsg = store.state.user.userInfo;

const modelList = defineModel<any[]>('modelList', { default: () => [] });
const visible = defineModel<boolean>('visible', { default: false });

//配置项 单选多选之类的
interface configType {
  isRadio?: boolean;
  workIds?:string[];
  tid?: string;
}

interface DrawerProps {
  submitApi?: (params: any) => Promise<any>; //提交的接口
  getTableList?: () => void; //保存后请求表格数据的接口
  dataCallBack?: (data: any) => any; //处理提交的数据
  config?:configType
}
defineProps<DrawerProps>();

const tabList = [
  {
    name: 'first',
    label: '工作组',
    api: getGroupByOrgTree,
    apiParams: { tid: userMsg.tid },
    leftProp: {
      children: 'children',
      label: 'groupName',
      value: 'id',
      leftSearchId: 'workgroupId',
    },
    rightProp: { label: 'nickName', id: 'id' },
    getDataApi: getUserByGroup,
  },
];
//父组件传过来的数据
watch(
  () => visible.value,
  val => {
    if (val) {
    }
  }
);
</script>
