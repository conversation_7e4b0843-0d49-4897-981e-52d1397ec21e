<template>
  <iconpark-icon
    :color
    :height="iconSize+'px'"
    :name="iconName"
    :size="iconSize"
    :width="iconSize+'px'"
    class="cursor-pointer"
    @mouseenter="hover = true"
    @mouseleave="hover = false"
  />
</template>

<script
  lang="ts"
  setup
>
const props = withDefaults(defineProps<{
  iconName: string
  iconSize?: number
  iconColor?: string
  iconHoverColor?: string
}>(), {
  iconSize: () => 20,
  iconColor: () => '#303133'
});

const hover = ref(false);

const color = computed(() => {
  const hoverColor = props.iconHoverColor ?? props.iconColor;
  return hover.value ? hoverColor : props.iconColor;
});

</script>

<style
  lang="scss"
  scoped
>

</style>