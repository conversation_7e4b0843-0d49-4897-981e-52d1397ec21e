<script setup>
import dayjs from 'dayjs';

defineOptions({
  name: 'xt-m-date-no-range'
});

const { disabled, type, format, dateFormatType } = toRefs(useAttrs());

const dateType = {
  'year': 'year',
  'month': 'month',
  'date': 'year-month',
  'datetime': undefined,
  'time': undefined
};

const valueFormatType = {
  0: 'YYYY-01-01 00:00:00',
  1: 'YYYY-MM-01 00:00:00',
  2: 'YYYY-MM-DD 00:00:00',
  3: 'YYYY-MM-DD HH:mm:00',
  4: 'YYYY-MM-DD HH:mm:ss',
  5: 'YYYY-MM-DD'
};

const vModel = defineModel();

const attrs = useAttrs();

const show = ref(false);


const onConfirmDateTime = (dateArr) => {
  const dateOption = dateArr[0]?.selectedValues || [];
  const timeOption = dateArr[1]?.selectedValues || [];
  vModel.value = dayjs(dateOption.join('-') + ' ' + timeOption.join(':')).format('YYYY-MM-DD HH:mm:ss');
  show.value = false;
};

const columnTabs = computed(() => {
  switch (attrs.type) {
    case 'datetime':
      return ['选择日期', '选择时间'];
    default:
      return ['选择日期'];
  }
});

const columnsDateType = computed(() => {
  switch (attrs.type) {
    case 'year':
      return ['year'];
    case 'month':
      return ['year', 'month'];
    case 'date':
      return ['year', 'month', 'day'];
    default:
      return ['year', 'month', 'day'];
  }
});
const columnsTimeType = computed(() => {
  if (attrs.type === 'datetime') {
    if (_.includes(attrs.format, ':ss')) {
      return ['hour', 'minute', 'second'];
    } else {
      return ['hour', 'minute'];
    }
  }
});
const textValueFormat=computed(()=>{
  if (type.value==='year'){
    return 'YYYY';
  }else if (type.value==='month'){
    return 'YYYY-MM';
  }else if (type.value==='date'){
    return 'YYYY-MM-DD';
  }else if (attrs.type === 'datetime') {
    if (_.includes(attrs.format, ':ss')) {
      return 'YYYY-MM-DD HH:mm:ss';
    } else {
      return 'YYYY-MM-DD HH:mm';
    }
  }
})

const showText = computed({
  get() {
    if (_.isEmpty(vModel.value))return '';
    const v = dayjs(vModel.value).format(textValueFormat.value);
    return v
  },
  set(v) {
    vModel.value = v;
  }
});

const currentDate = ref(vModel.value?.split(' ')[0]?.split('-') || dayjs().format('YYYY-MM-DD').split('-'));

const currentTime = ref(vModel.value?.split(' ')[1]?.split(':') || ['00','00','00']);
</script>

<template>
  <div>
    <van-field v-bind="$attrs" type=""
               v-model="showText"
               @click="show=true"
               readonly label=""
               placeholder="请输入文本" />
    <van-popup v-model:show="show"
               position="bottom"
               :style="{ height: '50%' }">
      <van-picker-group
        :title="$attrs.label"
        :tabs="columnTabs"
        @confirm="onConfirmDateTime"
      >
        <van-date-picker
          v-model="currentDate"
          :columns-type="columnsDateType"
        />
        <van-time-picker v-model="currentTime" :columns-type="columnsTimeType" />
      </van-picker-group>

    </van-popup>
  </div>

</template>

<style scoped lang="scss">

</style>