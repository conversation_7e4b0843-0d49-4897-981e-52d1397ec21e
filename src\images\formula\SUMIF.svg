<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 44</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-570.000000, -1240.000000)">
            <g id="编组-44" transform="translate(570.000000, 1240.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <text id="SUMIF函数可以使满足单一条件的数字相" font-family="PingFangSC-Regular, PingFang SC"
                      font-size="14" font-weight="normal" line-spacing="23">
                    <tspan x="10" y="57" fill="#3F70FF">SUMIF</tspan>
                    <tspan x="52.588" y="57" fill="#3A3A3A">函数可以使满足单一条件的数字相加并返</tspan>
                    <tspan x="10" y="80" fill="#3A3A3A">回和。</tspan>
                    <tspan x="10" y="104" fill="#3A3A3A">·用法：</tspan>
                    <tspan x="59" y="104" fill="#3F70FF">SUMIF</tspan>
                    <tspan x="101.588" y="104" fill="#3A3A3A">(数组_判断区域, 逻辑表达式, 数</tspan>
                    <tspan x="10" y="127" fill="#3A3A3A">组_求和区域)</tspan>
                    <tspan x="10" y="151" fill="#3A3A3A">·示例：</tspan>
                    <tspan x="59" y="151" fill="#3F70FF">SUMIF</tspan>
                    <tspan x="101.588" y="151" fill="#3A3A3A">([A,B,C,D,A], "A", [1,1,1,1,1])，可</tspan>
                    <tspan x="10" y="174" fill="#3A3A3A">得到A种类对应的数字求和结果为2。</tspan>
                </text>
                <text id="SUMIF" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">SUMIF</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
