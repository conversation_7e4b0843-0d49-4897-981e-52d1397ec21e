import request from '@/axios';
import { DataLinkField } from '@/components/form-config/types/AvueTypes';
import { TaskDetail } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType';

const prefix = '/nbxt-service-task/task';
export const getTaskDetailRequest = (id: string) => {
  return request({
    url: `/task/getTask`,
    method: 'post',
    params: {
      id,
    },
  });
};

export const getTaskList = (taskPackageId: string) => {
  return request({
    url: `${prefix}/list`,
    method: 'post',
    params: {
      taskPackageId,
    },
  });
};

export const saveTask = (data: TaskDetail) => {
  return request({
    url: `/task/submit`,
    method: 'post',
    data,
  });
};
//校验任务配置是否循环
export const verifyLinkage = (taskId: string) => {
  return request({
    url: `/linkage/verifyLinkage`,
    method: 'post',
    data: { taskId },
  });
};

export const getFormCol = (taskId: string) => {
  return request({
    url: `/task_form/getByTask`,
    method: 'post',
    params: {
      taskId,
    },
  });
};

export const getLinkData = (
  data: DataLinkField & { taskPackageId: string; previousShouldDoId: string }
) => {
  return request({
    url: `/nbxt-service-task/shouldDo/linkData`,
    method: 'post',
    data,
  });
};
// 删除当前
export const delTask = (id: string) => {
  return request({
    url: `/task/delTask`,
    method: 'post',
    params: { id },
  });
};

// 获取已授权组织列表
export const getTenantList = (id: string) => {
  return request({
    url: `/tenant_authorize/getTenantList`,
    method: 'post',
    params: { id },
  });
};
// 执行人端查看分类列表
export const getTreeForEx = (id: string) => {
  return request({
    url: `/tenant_authorize/getTreeForEx`,
    method: 'post',
    params: { id },
  });
};

// 查看当前组织管理员
export const getTenantAdmin = (tenantId: string) => {
  return request({
    url: `/common/getTenantAdmin`,
    method: 'get',
    params: { tenantId },
  });
};

// 获得互斥规则
export const getMutualExclusions = (data: any) => {
  return request({
    url: `/mutual_exclusion_rules/queryByActualIdAndType`,
    method: 'post',
    data,
  });
};

export const getFlashNameByTaskId = (taskId: string) => {
  return request({
    url: `/task_name/getFlashNameByTaskId`,
    method: 'get',
    params: { taskId },
  });
};
