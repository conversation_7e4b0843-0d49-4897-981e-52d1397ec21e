import request from '@/axios';

//获取超市列表
export const getMarketList = (data:Object) =>{
  return request({
    url: '/task_market/page',
    method: 'post',
    params: data,
    data:{}
  })
}

//获取超市列表的分类
export const getMarketTypeList = () =>{
  return request({
    url: '/market_type/list',
    method: 'get',
  })
}
export const getVersionTree = (id:string) =>{//获取下载版本树结构
  return request({
    url: '/task_market/getTree',
    method: 'post',
    params: {
      id
    },
  })
}

export const marketDownload = (params:any) =>{//下载
  return request({
    url: '/task_market/download',
    method: 'post',
    data: params
  })
}

export const marketDel = (ids:string) =>{//下架
  return request({
    url: '/task_market/remove',
    method: 'post',
    params: { ids }
  })
}

export const marketDetail = (id:string) =>{//应用超市详情
  return request({
    url: '/task_market/detail',
    method: 'get',
    params: { id }
  })
}

export const taskDownloadList = (current:number,size:number) =>{//下载进度列表
  return request({
    url: '/download_logs/page',
    method: 'post',
    data:{},
    params: {
      current,
      size
    }
  })
}

