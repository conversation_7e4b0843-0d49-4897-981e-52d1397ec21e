import { DropdownItem, MarkMenuTypeEnum, MarkTypeEnum } from '@/components/chart-design/types/widget.ts';
import { FormulaSummaryDic, NumberSummaryDic, SummaryDic } from '@/components/chart-design/const/widget.ts';
import { ElSelectDropdown } from 'xt-component';
import { ElDropdownItem } from 'element-plus';

const generator: DropdownItem = {
  key: MarkMenuTypeEnum.COLLECT,
  label: '汇总方式',
  component: function(params) {
    const summaries = SummaryDic;
    switch (params.item.type) {
      case MarkTypeEnum.NUMBER:
        summaries.push(...NumberSummaryDic);
        break;
      case MarkTypeEnum.FORMULA:
        summaries.push(...FormulaSummaryDic);
        break;
      default:
        break;
    }
    return defineComponent(() => {
      return () => {
        return (
          <ElDropdownItem>
            <ElSelectDropdown
              options={summaries}
              v-model={params.item.summary}
            >
              {this.label}
            </ElSelectDropdown>
          </ElDropdownItem>
        );
      };
    });
  }
};

export default generator;