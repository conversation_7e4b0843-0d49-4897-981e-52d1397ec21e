import { defineStore } from 'pinia';
import { Product, Model, Permission } from '../types';
import { randomLenNum } from '@/utils/util';

export const useSolutionStore = defineStore('SolutionStore', () => {
  const shelvesPackage = reactive({
    name: '', // 事集名称
    versionNumber: '', // 版本号
    description: '', // 事集介绍
    memberCard: '', // 会员卡
    billDay: '', // 账单日
    payDay: '', // 扣款日
    graceDay: '', // 宽限时间
  });
  const shelvesProductsDTOList = ref<Product[]>([]);

  // 添加产品
  const addProduct = () => {
    shelvesProductsDTOList.value.push({
      shelvesProducts: {
        id: randomLenNum(true),
        name: '',
        taskGroupIds: [],
        description: '',
      },
      shelvesModelDTOList: [],
    });
  };

  // 添加型号
  const addModel = (productId: number) => {
    const product = shelvesProductsDTOList.value.find(p => p.shelvesProducts.id === productId);
    if (product) {
      product.shelvesModelDTOList.push({
        shelvesModel: {
          id: randomLenNum(true),
          name: '',
        },

        shelvesPlanDetailList: [],
      });
    }
  };

  // 添加权限方案
  const addPermission = (Model: Permission[]) => {
    Model.push({
      id: randomLenNum(true),
      name: '',
      principalFactorList: [],
      spaceFactorList: [],
      timeFactorList: [],
      eventFactorList: [],
    });
  };

  // 添加因素
  const addFactor = (factor: Permission, type: string) => {
    switch (type) {
      case 'principalFactorList':
        factor.principalFactorList.push({
          id: randomLenNum(true),
          industryTypeId: '',
          ban: false,
        });
        break;
      case 'spaceFactorList':
        factor.spaceFactorList.push({
          id: randomLenNum(true),
          spaceTypeList: '',
          ban: false,
          scope: '',
          province: '',
          city: '',
        });
        break;
      case 'timeFactorList':
        factor.timeFactorList.push({
          id: randomLenNum(true),
          timeType: '',
          ban: false,
          timePeriodList: [],
          number: 0,
          spaceTypeList: '',
        });
        break;
      case 'eventFactorList':
        factor.eventFactorList.push({
          id: randomLenNum(true),
          eventType: 0,
          quantity: 0,
          measureType: '',
        });
        break;
      default:
        break;
    }
  };
  return {
    shelvesPackage,
    shelvesProductsDTOList,
    addProduct,
    addModel,
    addPermission,
    addFactor,
  };
});
