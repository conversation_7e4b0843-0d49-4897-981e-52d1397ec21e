<template>
  <div class="ruleMain">
    <div class="flex justify-between items-center">
      <span>选择选项后，才会显示所设置的其他字段</span>
      <el-button type="primary" link :icon="Plus" @click="addRule">添加条件</el-button>
    </div>
    <div class="ruleMain_form">
      <el-table :data="ruleTableData" class="w-full">
        <el-table-column prop="dicLabel" label="选项内容" align="center">
          <template #default="{ row }">
            <xt-cascader
              v-if="isCascader"
              class="w-full"
              v-model="row.dicValue"
              :dic="data.dicData"
              :props="{ label: 'label', value: 'label' }"
              clearable
              :multiple="true"
            ></xt-cascader>
            <xt-select
              v-else
              v-model="row.dicValue"
              :dic-data="data.dicData"
              multiple
              :props="{ label: 'label', value: 'label' }"
            ></xt-select>
          </template>
        </el-table-column>
        <el-table-column prop="displayField" label="显示字段" align="center">
          <template #default="{ row }">
            <xt-select
              v-model="row.displayField"
              :dic-data="broColumn"
              :props="{ label: 'copyLabel', value: 'prop' }"
              multiple
              collapse-tags
              collapse-tags-tooltip
            ></xt-select>
          </template>
        </el-table-column>
        <el-table-column prop="menu" :width="50">
          <template #default="{ $index }">
            <el-button :icon="Delete" type="danger" link @click="removeRule($index)"></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cascaderIncludes } from '@/utils/formUtils';
import { Delete, Plus } from '@element-plus/icons-vue';
import { AvueColumns } from '@/components/form-config/types/AvueTypes';
import { SelectColumnTypeEnum } from '../const';
import { SelectAllField, SelectRules } from '../type'

const props = defineProps<{
  data: SelectAllField;
  broColumn: AvueColumns[];
}>();

const ruleTableData = ref<SelectRules[]>([]);
defineExpose({ ruleTableData });

const addRule = () => {
  (ruleTableData.value ??= []).push({
    dicValue: [],
    displayField: [],
  });
};

const removeRule = (index: number) => {
  ruleTableData.value.splice(index, 1);
};

const isCascader = computed(() => props.data.type === SelectColumnTypeEnum.CASCADER);

onMounted(() => {
  // 初始化rule
  const dicLabels = props.data.dicData.map(i => i.label);

  ruleTableData.value = _.cloneDeep(props.data.dicRules || []).map(item => {
    // 处理字典判断dicValue是否存在于dic中
    if (isCascader.value) {
      // 如果级联
      item.dicValue = (item.dicValue as string[][]).filter(i => {
        // 判断每一层都是否存在
        if (!_.isArray(i)) {
          return false;
        }
        return cascaderIncludes(props.data.dicData, i);
      });
    } else {
      item.dicValue = item.dicValue.filter(i => {
        if (_.isArray(i)) {
          return false;
        }
        return _.includes(dicLabels, i);
      });
    }

    // 处理显示的字段
    item.displayField = item.displayField?.filter(i =>
      props.broColumn.map(j => j.prop).includes(i)
    );
    return item;
  });
});
</script>

<style scoped></style>
