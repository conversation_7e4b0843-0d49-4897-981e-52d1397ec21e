<script setup>
import handleAppIcon from '@/images/handle/handleAppIcon.svg';
import { ViewMode } from '@/views/banban/application/flowApp/option/enum';

const vModel = defineModel();

const tabActiveName = defineModel('tabActiveName');

const router = useRouter();

const props = defineProps({
  tenantList: {
    type: Array,
    default: () => []
  }
});

const TaskStatus = {
  Detail: 'Detail',
  Delay: 'Delay',
  Assignment: 'Assignment',
  Offline: 'Offline',
  Stop: 'Stop'
};

const getTenantName = computed(() => {
  return props.tenantList.find(item => item.tenantId === vModel.value.tenantId)?.tenantName;
});

const reToHandle = () => {
  if (tabActiveName.value !== 4) {
    const { instanceId, microAppId, appName, flwTaskId, startNodeFlag, tenantId } = vModel.value;
    router.push({
      path: '/m/shouldDo/microDetail',
      query: {
        instanceId,
        appId: microAppId,
        flwTaskId,
        name: appName,
        type: 3,
        startNodeFlag,
        isExamine: 1,
        tenantId
      } //isExamine 是否审核入口 1 是 0 否
    });
  } else {
    const { instanceId, appName, microAppId } = vModel.value;
    router.push({
      path: '/m/shouldDo/microDetail',
      query: {
        instanceId,
        appId: microAppId,
        name: appName,
        type: 3,
        disabled: 1,
        viewMode: ViewMode.View
      }
    });
  }
};
//微应用转
const commandHandle = (value) => {
  switch (value) {
    case TaskStatus.Assignment:
      return;
  }
};

</script>

<template>
  <div @click="reToHandle">
    <a-flex vertical gap="12" class="card-item">
      <a-flex gap="8" align="center" justify="space-between" class="item-title">
        <a-flex align="center">
          <handleAppIcon />
        </a-flex>
        <div class="flex-1">
          {{ vModel.createBy }}提交的{{ vModel.appName }}
        </div>
        <a-flex justify="center" align="center">
<!--          <el-dropdown @command="commandHandle">-->
<!--            <el-icon @click.stop>-->
<!--              <More />-->
<!--            </el-icon>-->
<!--            <template #dropdown>-->
<!--              <el-dropdown-menu>-->
<!--                <el-dropdown-item-->
<!--                  :command="TaskStatus.Assignment"-->
<!--                  v-if="[0].includes(tabActiveName)"-->
<!--                >转-->
<!--                </el-dropdown-item-->
<!--                >-->
<!--              </el-dropdown-menu>-->
<!--            </template>-->
<!--          </el-dropdown>-->
        </a-flex>
      </a-flex>
      <a-flex vertical gap="4" class="item-content">
        <div>
          发起时间：{{ vModel.createTime }}
        </div>
        <div>
          组织来源：{{ getTenantName }}
        </div>
      </a-flex>
    </a-flex>
  </div>
</template>

<style scoped lang="scss">
.card-item {
  margin: 8px 16px;
  padding: 12px 0;
  background: #ffffff;
  border-radius: 8px;

  .item-tip {
    padding: 0 12px;
  }

  .item-title {
    padding: 0 12px;
  }

  .item-content {
    background: #F7F8FA;
    margin: 0 12px;
    padding: 12px 12px;
  }
}
</style>