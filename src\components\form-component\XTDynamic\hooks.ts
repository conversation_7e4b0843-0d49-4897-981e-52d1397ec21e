import { AvueForm } from '@/components/form-config/types/AvueTypes.ts';
import { ElMessage } from 'element-plus';
import { Ref } from 'vue';
import { delDynamic } from '@/api/banban';


export const handleTable = (vModel: Ref<AvueForm[] | null[]>, type: string) => {

    const selectionData = ref<AvueForm[]>([])
    const handleSelectionChange = (selection: AvueForm[]) => {
        selectionData.value = selection
    };

    function filterArrayById(arr: string[], json: any[]) {
        return json.filter(item => !arr.some((v: any) => v === item.id));
    }
    const batchDeleteData = async(noHttp:boolean = false) => {
        if (!selectionData.value.length) {
            ElMessage.warning('请至少选择一项数据')
            return
        }
        let ids = []
        if(type ==='form'){
            ids = selectionData.value
        }else {
            ids = selectionData.value.map(v => v.id)
        }
        // const fieldIds = ids.filter(v => v.startsWith('#'))
        const delIds = ids.filter(v => !v.startsWith('#'))
        if (delIds.length && !noHttp) {
            await handleDelete(delIds.join(','))
        }
        vModel.value = filterArrayById(ids, vModel.value)
    }

    const handleDelete = async (ids: string) => {
        await delDynamic({ ids })
        ElMessage.success('删除成功')
        return true
    }
    

    // const getTableList = async () => {
    //     const { records } = await banbanDetailsTablePage({
    //         handledId: handleId as string,
    //         parentId: '0'
    //     })
        
    // }
    



    return {
        selectionData,
        batchDeleteData,
        handleDelete,
        handleSelectionChange
    }



}
