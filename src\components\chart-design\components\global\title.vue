<template>
  <div
    :style="style"
    class="flex w-full px-16px text-16px leading-24px box-border font-500"
  >{{ title }}
  </div>
</template>

<script
  lang="ts"
  setup
>
import { CommonStyle, TitlePositionEnum } from '../../types';

const props = defineProps<CommonStyle>();

const style = computed(() => {
  return {
    color: props.titleColor,
    justifyContent: props.titlePosition === TitlePositionEnum.CENTER ?
      props.titlePosition : `flex-${props.titlePosition ?? TitlePositionEnum.LEFT}`
  };
});

</script>

<style
  lang="scss"
  scoped
>

</style>