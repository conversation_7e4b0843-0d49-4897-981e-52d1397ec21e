<template>
  <slot :data="data" :taskPackageId="taskPackageId">
    <div v-if="data.fixType === FixTypeEnum.DYNAMIC" class="w-full">
      <el-button class="w-full" type="primary" plain @click="dynamicVisible = true">设置</el-button>
      <el-dialog v-model="dynamicVisible" title="默认值" width="50%" :destroy-on-close="true">
        <xt-el-form
          ref="form"
          v-model="dynamicValue"
          :option="option"
          :taskPackageId="taskPackageId"
          :source="fieldSource.SETDEFAULT"
        ></xt-el-form>
        <template #footer>
          <div>
            <el-button @click="handleSubmit(data)" type="primary">确定</el-button>
            <el-button @click="dynamicVisible = false">取消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
    <div v-else class="w-full">
      <!-- {{data}} -->
      <component
        v-model="(data as CustomDefaultType).defaultValue"
        :is="getComponent(data.fixType, data.component, data?.showFormat)"
        v-bind="data"
        :dic="(data as unknown as SelectField).dicData"
        :placeholder="noPlaceHolder ? '' : getPlaceholder(data, '默认值')"
        :disabled="false"
        :taskPackageId="taskPackageId"
        :workParentId="bindWorksByParentId"
      >
      </component>
    </div>
  </slot>
</template>

<script setup lang="ts">
import XtElForm from '@/components/xt-el-form/XtElForm.vue';
import { PackageIdInjectKey } from '@/components/form-config/utils/injectKeys';
import useComponents from '@/components/form-config/hooks/useComponents';
import {
  CustomDefaultType,
  FieldBaseField,
} from '@/components/form-config/types/AvueTypes';
import { fieldSource } from '@/components/form-config/const/commonDic';
import { SelectField,InputAllField } from '@/components/form-config/types/AvueTypes';
import { storeToRefs } from 'pinia';
import usePackageTreeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTreeStore.ts';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
const { bindWorksByParentId } = storeToRefs(usePackageTreeStore());


const taskPackageId = inject(PackageIdInjectKey);

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<
  { data:FieldBaseField & InputAllField } & {
    noPlaceHolder?: boolean;
  }
>();
const dynamicVisible = ref(false);
const dynamicValue = ref({});
const option = ref<any>({});
const form = ref();

onMounted(() => {
  // option.value.column = [_.cloneDeep(props.data)];
});

watch(
  () => dynamicVisible.value,
  (newVal) => {
    if(!newVal)return
    option.value.column = [_.cloneDeep(props.data)];
  }
);

const handleSubmit = (data: CustomDefaultType) => {
  const values = Object.values(dynamicValue.value);
  data.defaultValue = [...values.flat()];
  dynamicVisible.value = false;
  // form.value.validate((valid: boolean, done: Function) => {
  //   if (valid) {
  //     done();
  //     const values = Object.values(dynamicValue.value);
  //     data.defaultValue = [...values.flat()];
  //     dynamicVisible.value = false;
  //   }
  // });
};

const { getComponent, getPlaceholder } = useComponents();
</script>
