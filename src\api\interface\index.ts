// 分页响应参数
export interface ResPage<T> {
    records: T[];
    size: number;
    total: number;
}

// 分页请求参数
export interface ReqPage {
    current: number;
    size: number;
}

//档案门类
export namespace Catalog {
    export interface ReqParams extends ReqPage {
       search: string
    }
    export interface ReqSearch {
        name: string;
        code: string;
    }
    export interface ResList {
        code: string;
        content: string;
        name: string;
        children?: ResList[];
        [propName: string]: any;
    }
}

//机构代码
export namespace Agency {
    export interface ReqParams extends ReqPage {
        search: string
    }
    export interface ReqSearch {
        name: string;
        code: string;
    }
    export interface ResList {
        code: string;
        content: string;
        [propName: string]: any;
    }
}