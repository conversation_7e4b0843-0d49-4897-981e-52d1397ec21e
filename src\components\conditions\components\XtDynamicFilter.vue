<template>
  <div class="custom-selector w-full">
    <el-popover placement="bottom" ref="popoverRef" :width="500" :visible="visible">
      <template #default>
        <div class="flex flex-wrap gap-10px">
          <div
            v-for="(item, index) in dynamicDic"
            :key="index"
            class="flex items-center justify-center text-bold option-item"
            @click="handleSelect(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </template>
      <template #reference>
        <el-input
          v-model="label"
          ref="triggerBtn"
          @click="visible = true"
          placeholder="请选择"
          readonly
          class="w-full"
        >
        </el-input>
      </template>
    </el-popover>
    <!-- 输入框部分 -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const enum TimeRangeEnum {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  TOMORROW = 'tomorrow',

  LAST_7_DAYS = 'last7Days',
  LAST_30_DAYS = 'last30Days',

  THIS_WEEK = 'thisWeek',
  LAST_WEEK = 'lastWeek',
  NEXT_WEEK = 'nextWeek',

  THIS_MONTH = 'thisMonth',
  LAST_MONTH = 'lastMonth',
  NEXT_MONTH = 'nextMonth',

  THIS_QUARTER = 'thisQuarter',
  LAST_QUARTER = 'lastQuarter',
  NEXT_QUARTER = 'nextQuarter',

  THIS_YEAR = 'thisYear',
  LAST_YEAR = 'lastYear',
  NEXT_YEAR = 'nextYear',
}

interface TimeRangeOption {
  label: string;
  value: TimeRangeEnum;
}

const dynamicDic: TimeRangeOption[] = [
  {
    label: '今天',
    value: TimeRangeEnum.TODAY,
  },
  {
    label: '昨天',
    value: TimeRangeEnum.YESTERDAY,
  },
  {
    label: '明天',
    value: TimeRangeEnum.TOMORROW,
  },
  {
    label: '近7天',
    value: TimeRangeEnum.LAST_7_DAYS,
  },
  {
    label: '近30天',
    value: TimeRangeEnum.LAST_30_DAYS,
  },
  {
    label: '本周',
    value: TimeRangeEnum.THIS_WEEK,
  },
  {
    label: '上周',
    value: TimeRangeEnum.LAST_WEEK,
  },
  {
    label: '下周',
    value: TimeRangeEnum.NEXT_WEEK,
  },
  {
    label: '本月',
    value: TimeRangeEnum.THIS_MONTH,
  },
  {
    label: '上月',
    value: TimeRangeEnum.LAST_MONTH,
  },
  {
    label: '下月',
    value: TimeRangeEnum.NEXT_MONTH,
  },
  {
    label: '本季度',
    value: TimeRangeEnum.THIS_QUARTER,
  },
  {
    label: '上季度',
    value: TimeRangeEnum.LAST_QUARTER,
  },
  {
    label: '下季度',
    value: TimeRangeEnum.NEXT_QUARTER,
  },
  {
    label: '今年',
    value: TimeRangeEnum.THIS_YEAR,
  },
  {
    label: '去年',
    value: TimeRangeEnum.LAST_YEAR,
  },
  {
    label: '明年',
    value: TimeRangeEnum.NEXT_YEAR,
  },
];

const visible = ref(false);
const vModel = defineModel({ default: () => '' });
const popoverRef = ref();
const triggerBtn = ref<any>();

const label = computed({
  get() {
    return vModel.value ? dynamicDic.find(item => item.value == vModel.value)?.label : '';
  },
  set(v) {
    vModel.value = dynamicDic.find(item => item.label == v)?.value;
  },
});

const handleSelect = (item: TimeRangeOption) => {
  label.value = item.label;
  visible.value = false;
};

const checkClickOutside = (event: any) => {
  try {
    const popoverEl = popoverRef.value?.popperRef?.contentEl?.parentElement;
    const triggerEl = triggerBtn.value?.$el;

    if (visible.value && !popoverEl?.contains(event.target) && !triggerEl?.contains(event.target)) {
      console.log(event.target, 'event.target');

      visible.value = false;
    }
  } catch (e) {
    console.error('Error handling click:', e);
  }
};

onMounted(() => {
  // document.addEventListener('click', checkClickOutside);
});

onBeforeUnmount(() => {
  // document.removeEventListener('click', checkClickOutside);
});
</script>

<style scoped>
.custom-selector {
  position: relative;
  display: inline-block;
}

.custom-popup {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 2023;
  margin-top: 4px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.popup-content {
  padding: 10px;
}

.options {
  margin: 10px 0;
}

.option-item {
  padding: 15px 10px;
  cursor: pointer;
}

.option-item:hover {
  background-color: #f5f7fa;
}

.custom-footer {
  text-align: right;
  padding: 10px;
  border-top: 1px solid #e4e7ed;
}

.el-icon-arrow-down {
  transition: transform 0.3s;
}

.el-icon-arrow-down.is-reverse {
  transform: rotate(180deg);
}
</style>
