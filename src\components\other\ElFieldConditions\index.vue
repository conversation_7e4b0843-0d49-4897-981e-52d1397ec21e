<template>
  <div :key="listKey" class="w-full flexList-8px">
    <el-button :icon="Plus" class="w-110px" plain type="primary" @click="handleAdd">
      {{ addBtnText }}
    </el-button>

    <condition-custom-item
      v-for="(item, index) in conditions"
      v-model:field-id="item.prop"
      v-model:result="item.value"
      v-model:symbol="item.operator"
      :field-list="fieldList"
      class="flex-1 bg-#F4F9FD border-none!"
    >
      <template #customBtn>
        <el-button link @click="handleDelete(index)" class="ml-16px">
          <iconpark-button icon-name="shanchu" />
        </el-button>
      </template>
    </condition-custom-item>
  </div>
</template>

<script lang="ts" setup>
import type { Field, FieldCondition } from '@/types/OriginTypes.ts';
import { Plus } from '@element-plus/icons-vue';
import { computedWithControl } from '@vueuse/core';
import { IconparkButton } from 'xt-component';
import ConditionCustomItem from '@/components/conditions/conditionCustomItem.vue';

withDefaults(
  defineProps<{
    fieldList: Field[];
    addBtnText?: string;
  }>(),
  {
    addBtnText: () => '添加条件',
  }
);
const emits = defineEmits<{
  delete: [index: number];
}>();

const conditions = defineModel<FieldCondition[]>({ default: () => [] });

const listKey = computedWithControl(
  () => null,
  () => Math.random()
);

//字段
const handleAdd = () => {
  conditions.value = _.concat(conditions.value, reactive({})) as FieldCondition[];
};

const handleDelete = (index: number) => {
  conditions.value = conditions.value.filter((_, i) => i !== index);
  emits('delete', index);
  listKey.trigger();
};
</script>

<style lang="scss" scoped></style>
