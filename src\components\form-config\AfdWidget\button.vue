<template>
  <div class="widget-button pointer-events-auto">
    <!-- <el-button
      v-if="['group'].includes(type)"
      class="clear"
      title="清空"
      circle
      plain
      size="small"
      type="warning"
      :icon="Brush"
      @click.stop="$emit('clear')"
    ></el-button> -->
    <el-button-group class="widget-button-group">
      <el-button
        class="copy !rounded-l-10px"
        title="复制"
        circle
        plain
        size="default"
        @click.stop="$emit('copy')"
      >
        <el-icon :size="15">
          <CopyDocument />
        </el-icon>
      </el-button>
      <el-button
        class="delete !rounded-r-10px"
        title="删除"
        circle
        plain
        size="default"
        @click.stop="$emit('delete')"
      >
        <el-icon :size="15">
          <CircleClose />
        </el-icon>
      </el-button>
    </el-button-group>
  </div>
</template>

<script setup>
import { CircleClose, CopyDocument } from '@element-plus/icons-vue';
import { computed, toRefs } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'item',
  },
});
defineEmits(['copy', 'delete', 'clear']);

const { type } = toRefs(props);
const style = computed(() => {
  let top = 0;
  let right = 0;
  switch (type.value) {
    case 'group-item':
    case 'table-item':
    case 'group':
      top = 10;
      right = 10;
      break;
    case 'divider':
    case 'tips':
      top = 0;
      right = 0;
      break;
    default:
      top = -30;
      right = 0;
      break;
  }
  return {
    right: `${right}px`,
    top: `${top}px`,
  };
});
</script>
<style lang="scss" scoped>
.widget-button {
  &-group {
    position: absolute;
    z-index: 1002;
    right: v-bind('style.right');
    top: v-bind('style.top');
  }

  .el-button {
    @apply px-10px py-4px;
    height: inherit;
  }

  //.delete {
  //  right: v-bind('style.right');
  //  bottom: v-bind('style.bottom');
  //}
  //
  //.copy {
  //  right: calc(v-bind('style.right') + 30px);
  //  bottom: v-bind('style.bottom');
  //}
  //
  //.clear {
  //  right: calc(v-bind('style.right') + 60px);
  //  bottom: v-bind('style.bottom');
  //}
}
</style>
