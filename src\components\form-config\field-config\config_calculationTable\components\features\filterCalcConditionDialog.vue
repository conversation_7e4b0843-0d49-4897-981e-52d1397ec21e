<script setup lang="ts">
import { ref } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import type { FormInstance } from 'element-plus';
import FieldItem from './fieldItem.vue'
import { conditionEnum } from '../const';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore.ts';
import { storeToRefs } from 'pinia';
import { AvueColumns } from '@/components/form-config/types/AvueTypes';
import { CalculationTableFilter, CalTableFilter} from '../type';
import { getCalFieldByCalTableId, getDataPathChildrenByVersionId } from '@/api/taskManagement/componentFetch';

interface PropsType {
  range:string
}
interface OriginRange {
  path:string
  pathName:string
  pathType:string
}


const props = defineProps<PropsType>()

const { taskBase } = storeToRefs(useTaskDetailStore());


const visible = defineModel<boolean>('visible', { default: false });
const filter = defineModel<CalculationTableFilter>('filter',{default:{conditions:[]}})


const ruleFormRef = ref<FormInstance>();
const conditions = ref<Array<CalTableFilter>>(filter.value.conditions || []);
const fieldList = ref<AvueColumns[]>([]);
const originRange = ref<OriginRange>({} as OriginRange);

const handleAdd = () => {
  console.log(filter.value);
  conditions.value.push({ conditionType:conditionEnum.MatchingTaskFields });
};
const handleDelete = (index: number) => {
  _.pullAt(conditions.value || [], index);
};

const handleSubmit = () => {
  filter.value.conditions = conditions.value;
  visible.value = false;
}

const OnCancel = () => {

  visible.value = false;
}

const getOriginTable = async (id:string) => {
  const { data:res } = await getCalFieldByCalTableId(id);
  originRange.value = res?.data as OriginRange;
  filter.value.pathId = originRange.value.path;
  filter.value.pathType = originRange.value.pathType;
  getFieldListDic();
}


async function getFieldListDic(){
  const { data } = await getDataPathChildrenByVersionId({
    type:originRange.value.pathType,
    path: originRange.value.path,
  });
  if(!_.isEmpty(data?.data)){
    fieldList.value = data?.data.map((i:any) => {
      return {
        label:i.label,
        fieldType:i.fieldType,
        contentType:i.contentType,
        fixType:i.fixType || i.fieldType,
        prop:i.fieldType + ':#' + i.value,
        content:i.content,
        ...i.content,
        disabled:false
      }
    })
  }
}




watch(
  () => props.range,
  (v) => {
    if(!v) return;
    getOriginTable(props.range);
    filter.value.conditions = []
  }
)

watch(
  () => visible.value,
  (v) => {
    if(v){
      conditions.value =  _.cloneDeep(filter.value.conditions) || [];
      getOriginTable(props.range);

    }
  }
)

</script>

<template>
  <el-drawer v-model="visible" title="过滤条件">
    <el-form ref="ruleFormRef">
      <n-space vertical :size="16">
        <el-form-item
          label="选择范围"
        >
          <avue-input disabled :model-value="originRange.pathName ?? '计算表'"></avue-input>
        </el-form-item>


        <el-button :icon="Plus as any" type="primary" plain @click="handleAdd">添加条件</el-button>
        <template v-if="conditions?.length">
          <field-item
            v-for="(item, index) in conditions"
            :key="index"
            v-model:conditionType="item.conditionType"
            v-model:field-id="item.prop"
            v-model:result="item.value"
            v-model:conditions="item.operator"
            :fieldList="fieldList"
            :range="originRange.pathName || ''"
            :column="taskBase.column || []"
            @delete="handleDelete(index)"
          ></field-item>
        </template>
      </n-space>

    </el-form>

    <template #footer>
      <el-button @click="OnCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>

  </el-drawer>
</template>

<style scoped lang="scss">

</style>