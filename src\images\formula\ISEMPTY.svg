<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title> ISEMPTY</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-910.000000, -1845.000000)">
            <g id="if备份" transform="translate(910.000000, 1845.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-2" transform="translate(10.000000, 42.000000)" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="23">
                    <text id="ISEMPTY函数可以用来判断值是否为空">
                        <tspan x="0" y="15" fill="#3F70FF">ISEMPTY</tspan>
                        <tspan x="60.354" y="15" fill="#3A3A3A">函数可以用来判断值是否为空文本、空</tspan>
                        <tspan x="0" y="38" fill="#3A3A3A">对象或者空数组</tspan>
                        <tspan x="98" y="38" letter-spacing="0.4" fill="#3F70FF"></tspan>
                        <tspan x="0" y="62" letter-spacing="0.4" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="50.6" y="62" letter-spacing="0.4" fill="#3F70FF">ISEMPTY</tspan>
                        <tspan x="113.754" y="62" letter-spacing="0.4" fill="#3A3A3A">(文本)</tspan>
                        <tspan x="0" y="86" letter-spacing="0.4" fill="#3A3A3A">·示例：略</tspan>
                    </text>
                </g>
                <text id="ISEMPTY" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">ISEMPTY</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>