<template>
  <el-select-v2
    v-model="vModel"
    v-bind="$attrs"
    filterable
    :options="dic"
    :props="{
      value: props.value,
      label: props.label,
    }"
    v-if="type === SelectColumnTypeEnum.SELECT"
  >
  </el-select-v2>
  <el-radio-group v-model="vModel" v-else>
    <el-radio
      :value="v.label"
      v-for="v in dic"
      :key="v.label"
      :label="v.label"
      @click.prevent="$attrs.disabled ? '' : toggleSelection(v)"
      :disabled="v.disabled"
    >
      {{ v.label }}
    </el-radio>
  </el-radio-group>
</template>
<script setup lang="ts">
import { SelectColumnTypeEnum } from '@/components/form-config/const/commonDic';
defineProps<{
  dic: { label: string; disabled: boolean; value: string }[];
  props: { label: string; value: string };
  source: number;
  type: SelectColumnTypeEnum;
}>();

const vModel = defineModel<string>({ default: '' });

const toggleSelection = ({ label, disabled }: { label: string; disabled: boolean }) => {
  if (disabled) return;
  if (vModel.value === label) {
    vModel.value = ''; // 取消选中
  } else {
    vModel.value = label; // 选中
  }
};


</script>
<style lang="scss" scoped></style>
