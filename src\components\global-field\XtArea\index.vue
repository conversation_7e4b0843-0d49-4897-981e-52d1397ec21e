<template>
  <div class="XtArea w-full">
    <el-cascader
      :popper-class="isMobile()?'m_cascader':''"
      v-bind="attrs"
      v-model="pcas"
      :props="areaProps"
      ref="myCascader"
      @expand-change="changeE"
      :options="pcasDic"
      clearable
    
      class="w-full"
    ></el-cascader>
    <el-input
      v-bind="attrs"
      v-if="showAddress"
      v-model="address"
      placeholder="请输入 详细地址"
      type="textarea"
      style="margin-top: 10px"
    ></el-input>
  </div>
</template>

<script>
export default {
  inheritAttrs: false,
};
</script>

<script setup>
import func from '@/utils/func';
import { deepClone } from '@/utils/util';
import { useVModel } from '@vueuse/core';
import { useAttrs } from 'element-plus';
import useAreaStore from '@/components/form-config/stores/useAreaStore';
import { isMobile } from '@/utils/client';
import { useFormItem } from 'element-plus';

const { formItem } = useFormItem();

const props = defineProps({
  modelValue: {},
  addressPrecision: {
    type: Number,
    default: () => 0,
  },
  props: {},
  haveAddress: {
    type: Boolean,
    default: () => true,
  },
  unInitData: {
    type: Boolean,
  },
  addressDisplay: {
    type: Boolean,
  },
});
const emits = defineEmits(['update:modelValue']);
const attrs = useAttrs({
  excludeKeys: computed(() => ['value']),
});
const modelValue = useVModel(props, 'modelValue', emits);
const myCascader = ref();

function validateValue() {
  formItem?.validate?.('change').catch(err => {
    console.log(err);
  });
}
//值相关
const pcas = computed({
  get: () => {
    const valueArray = modelValue.value?.value;
    return Array.isArray(valueArray) && valueArray.length > 0
      ? valueArray[valueArray.length - 1]
      : [];
  },
  set: v => {
    emitValue(v?.flat(), 'value');
    nextTick(() => {
      let labels = myCascader.value
        .getCheckedNodes()
        ?.map(item => {
          return item.pathLabels;
        })
        .flat();
      emitValue(labels || null, 'label');
    });
  },
});

const changeE=()=>{
  nextTick(()=>{
    const el=myCascader.value.contentRef;
    if (el) el.scrollLeft=el.scrollWidth;
  })
}

const address = computed({
   get: () => {
     const addr = modelValue.value?.address;
     return typeof addr === 'string' ? addr : '';
  },
  set: v => {
    emitValue(v, 'address');
  },
});

const emitValue = (v, key) => {
  modelValue.value = { address: '',...props.modelValue, [key]: v };
};

watchPostEffect(() => {
  if (!pcas.value.length && func.isEmpty(address.value)) {
    modelValue.value = undefined;
  }
});

watch(
  () => modelValue.value,
  () => {
    nextTick(() => {
      validateValue(); // 主动触发校验
    });
  },
  {immediate:true, deep:true}
)

//显示相关
const areaProps = {
  expandTrigger: 'click',
  value: 'code',
  label: 'name',
  ...props.props,
  checkStrictly: true,
};

const { getArea } = useAreaStore();
const options = ref([]);

onMounted(async () => {
  
  if (!props.unInitData) {
    options.value = await getArea();
  }
});

const showAddressPrecision = 3; //显示级数-1

// const showAddress = computed(() => props.addressPrecision > showAddressPrecision);
const showAddress = computed(() => props.addressDisplay);//详细地址是否可见


const initPrecision = (address, precision) => {
  precision--;
  address?.forEach(item => {
    if (precision < 0) {
      delete item.children;
    } else {
      initPrecision(item.children, precision);
    }
  });
};
const pcasDic = computed(() => {
  const result = deepClone(options.value);
  initPrecision(result, showAddress.value ? showAddressPrecision : props.addressPrecision);
  return result;
});
</script>

<style lang="scss">
.m_cascader_area{
  width: 90%;
  overflow: scroll;
}
</style>
