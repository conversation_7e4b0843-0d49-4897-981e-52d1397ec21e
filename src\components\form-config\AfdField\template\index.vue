<template>
  <div class="pt-4">
    <h3 class="m-0 pl-5">
      字段库
      <el-input v-model="title" style="max-width: 200px">
        <template #append>
          <el-button :icon="Search" @click="getList" />
        </template>
      </el-input>
    </h3>

    <afd-field :fields="fieldBtnList" @field-click="fieldClick" :type="1">
      <template #header="{ data }">
        {{ data.title }}
        <el-button type="primary" text @click.stop="$emit('change', data.list)">导入全部</el-button>
      </template>
    </afd-field>
  </div>
</template>

<script setup lang="ts">
import { getFieldTempListNoPage } from '@/api/taskManagement/fieldTemp/index'
import { FieldTemp } from '@/api/taskManagement/fieldTemp/type'
import { ref, computed } from 'vue';
import AfdField from '../index.vue';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts';
import usePackageTreeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTreeStore.ts';
import { storeToRefs } from 'pinia';
import { Search } from '@element-plus/icons-vue'

const { versionId } = storeToRefs(usePackageTreeStore());

const props = defineProps<{
  taskPackageId?: string;
  fieldClick: (item: AvueColumns, templateId?: string) => void
}>();

defineEmits(['change']);

const title = ref()
const tableData = ref<FieldTemp.ResList[]>([])

//处理返回的数据
const processData = (data: any, templateId: string) => {
  if (!_.isArray(data)) return []
  return data.map(item => {
    const newItem = { ...JSON.parse(item.content), mid: item.id, templateId, id: '', prop: '' }
    if (item.children) {
      newItem.children = processData(item.children, templateId);
    }
    return newItem;
  });

};

//模板的数据，模板拉过去的组件id和prop不变
const fieldBtnList = computed(() => {
  const typeList = tableData.value.map((v) => {
    const arr = processData(v.details, v.id)
    // console.log(arr, 'arr')
    return { list: arr, title: v.name }
  })
  return typeList
})

//获取列表
const getList = async () => {
  const { data } = await getFieldTempListNoPage({ sceneId: versionId.value as any}, { title: title.value })
  console.log(data, 'tableData')
  tableData.value = data.data.records
}

watch(
  () => props.taskPackageId,
  () => {
    getList()
  },
  { immediate: true }
)


</script>