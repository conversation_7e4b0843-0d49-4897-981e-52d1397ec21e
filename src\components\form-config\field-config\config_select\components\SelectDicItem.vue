<template>
  <div class="flex flex-nowrap items-center gap-10px">
    <n-input
      v-bind="$attrs"
      :default-value="node.label"
      :disabled="node.isOther || disabled"
      @focus="$emit('focus', $event)"
      maxlength="50"
      @blur="handleBlur"
    />
    <div class="flex items-center gap-5px">
      <el-button v-if="showChildAdd" link size="small" @click="handleAddChildFields(node)">
        <DicAddIcon />
      </el-button>
      <el-button link size="small" @click="$emit('remove')" class="!m-0">
        <DicDelIcon />
      </el-button>
      <el-button link class="hover:cursor-move !m-0" size="small">
        <DicMoveIcon />
      </el-button>
    </div>
  </div>
</template>

<script setup>
import DicAddIcon from './icons/DicAddIcon.svg';
import DicMoveIcon from './icons/DicMoveIcon.svg';
import DicDelIcon from './icons/DicDelIcon.svg';
import useOutVModel from '@/hooks/useOutVModel.js';
import { formContextKey } from 'element-plus';
import { computedInject } from '@vueuse/core';
import { generateId } from '@/components/form-config/utils';

const props = defineProps(['node', 'level', 'cascaderLevel', 'isCascader']);
const emits = defineEmits(['remove', 'blur', 'focus']);
const { vModel } = useOutVModel();

const handleBlur = e => {
  vModel.value = e.target.value;
  emits('blur');
};

const disabled = computedInject(formContextKey, source => {
  return source?.disabled ?? false;
});

const showChildAdd = computed(() => props.level <= props.cascaderLevel && props.isCascader);

//添加子节点
const handleAddChildFields = node => {
  const children = node?.children || [];
  children.push({ label: `${node.label}-${children.length + 1}`,key:generateId()  });
  node.children = children;
};
</script>

<style scoped lang="scss"></style>
