import { ComponentPublicInstance, Ref } from 'vue';
import ElDrawerForm from '../components/other/ElDrawerForm.vue';

const useElDrawerForm = <T extends {
  [key in string]?: any
}>() => {
  const drawerForm = ref<InstanceType<typeof ElDrawerForm>>();
  const form = ref<T>({} as T) as Ref<T>;

  const refSet = (el: Element | ComponentPublicInstance | null) => {
    if (el) {
      drawerForm.value = el as (InstanceType<typeof ElDrawerForm>);
    }
  };

  const setTitle = (title: string) => {
    drawerForm.value?.setTitle(title);
  };

  return {
    refSet,
    form,
    open: (newForm: T, title: string) => {
      form.value = newForm;
      setTitle(title);
      drawerForm.value?.open();
    },
    setTitle,
    close: () => {
      drawerForm.value?.close();
      form.value = {} as T;
    }
  };
};

export default useElDrawerForm;
