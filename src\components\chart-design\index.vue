<template>
  <div class="size-full relative">
    <!--中间图表-->
    <chart-widget
      :style="{right: `${props.configWidth}px`,left:0}"
      class="absolute inset-y-0 px-16px"
    />
    <!--右边配置-->
    <chart-config
      :style="{ 'width': `${props.configWidth}px`}"
      class="absolute inset-y-0 right-0 xtBorder-l"
    />
  </div>
</template>

<script
  lang="ts"
  setup
>
import ChartWidget from './components/widget/index.vue';
import ChartConfig from './components/config/index.vue';
import { DesignProps, PreviewDataType } from './types';
import { watchPausable } from '@vueuse/core';
import useChartDesignStore from './store/useChartDesignStore.ts';
import { storeToRefs } from 'pinia';

const props = withDefaults(defineProps<{
  previewData?: PreviewDataType,
  configWidth?: number,
  selectWidth?: number
}>(), {
  previewData: () => ({
    chartData: [],
    targetList: [],
    series: []
  }),
  configWidth: 340
});

const chart = defineModel<DesignProps>({ default: ({}) });

const store = useChartDesignStore();
const { syncData, $reset } = store;
const {
  dimensionsList,
  metricsList,
  auxiliaryList,
  chartType,
  chartStyle,
  previewData
} = storeToRefs(store);

watch(() => props.previewData, (v) => {
  previewData.value = _.cloneDeep(v);
}, {
  immediate: true,
  deep: true
});

const { pause: outPause, resume: outResume } = watchPausable([dimensionsList,
  metricsList,
  auxiliaryList,
  chartType,
  chartStyle], () => {
  inPause();
  chart.value = {
    chartType: chartType.value,
    dimensions: dimensionsList.value,
    metrics: metricsList.value,
    auxiliary: auxiliaryList.value,
    chartStyle: chartStyle.value
  };
  nextTick(() => {
    inResume();
  });
}, {
  deep: true
});

const { pause: inPause, resume: inResume } = watchPausable(chart, (v) => {
  outPause();
  syncData(v);
  outResume();
}, {
  immediate: true
});

onBeforeRouteLeave(() => {
  $reset();
});
</script>

<style
  lang="scss"
  scoped
>

</style>