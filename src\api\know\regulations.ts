import request from '@/axios';
export const getFormColumData = (data:any) => {
  return request({
    url: '/infomation/electronicLedger/getTableData',
    method: 'put',
    data: {
      ...data
    }
  });
};

export const getFormDataFile = (handledIds:any) => {
  return request({
    url: '/infomation/electronicLedger/getHandleFiles',
    method: 'get',
    params: {
      handledIds
    }
  });
};
export const getHistoryDataToFile = (data:any) => {
  return request({
    url: '/infomation/electronicLedger/historyDataToFile',
    method: 'put',
    data: {
      ...data
    }
  });
};
export const addRegulations = (data:any) => {
  return request({
    url: '/rules/submit',
    method: 'post',
    data: {
      ...data
    }
  });
};
export const getRegulationsDetail = (id:string) => {
  return request({
    url: '/rules/detail',
    method: 'get',
    params: {
      id
    }
  });
};

//规章制度分页
export const getRegulationsPage = (current:number,size:number,data:any) => {
  return request({
    url: '/rules/page',
    method: 'post',
    data: {
      ...data
    },
    params:{
      current,
      size
    }
  });
};

//回收站
export const getRegulationsRecyclePage = (current:number,size:number,data:any) => {
  return request({
    url: '/rules/recycle',
    method: 'post',
    data: {
      ...data
    },
    params:{
      current,
      size
    }
  });
};

//获取编码
export const getRegulationsNumbers = (data:any) => {
  return request({
    url: '/rules/generateNumbers',
    method: 'post',
    data: {
      ...data
    }
  });
};

//删除
export const removeRegulations = (ids:any) => {
  return request({
    url: '/rules/remove',
    method: 'post',
    params: {
      ids
    }
  });
};
//撤回
export const withdrawRegulations = (id:any) => {
  return request({
    url: '/rules/withdraw',
    method: 'get',
    params: {
      id
    }
  });
};
//恢复
export const recoverRegulations = (id:any) => {
  return request({
    url: '/rules/recover',
    method: 'get',
    params: {
      id
    }
  });
};
//查询历史版本
export const previewBindRegulations = (id:any) => {
  return request({
    url: '/rules/viewPrevious',
    method: 'get',
    params: {
      id
    }
  });
};
//查询历史版本
export const bindRegulations = (data:any) => {
  return request({
    url: '/rules/binding',
    method: 'post',
    data: {
      ...data
    }
  });
};