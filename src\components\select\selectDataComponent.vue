<template>
  <div>
    <!-- 选中的数据 -->
    <div class="w-full btn p-1 mb-2 min-h-[100px]">
      <el-tag
        class="mr-1"
        v-for="(item, index) in vModel"
        :key="item[activeObj.rightProp.id]"
        :closable="item.id !== disabledUser"
        @close="deleteData(item, index)"
        style="margin: 0 5px"
      >
        {{ item[activeObj.rightProp.label] }}
      </el-tag>
    </div>
    <!-- 切换tab -->
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        :key="item.name"
        v-for="item in tabList"
      ></el-tab-pane>
    </el-tabs>
    <div class="w-full btn1 p-1 mb-2 flex min-h-[200px]">
      <!-- 左侧数据 -->
      <div style="border-right: 1px solid #e6e6e6; max-height: 400px" class="mr-3 p-2">
        <el-scrollbar class="tree_scrollbar">
          <el-tree-v2
            :height="400"
            v-if="activeName"
            style="width: 400px"
            node-key="id"
            :data="leftList"
            :show-checkbox="showCheck"
            :check-strictly="true"
            :props="activeObj.leftProp"
            @node-click="handleNodeClick"
          />
          <!-- <el-tree-v2
            :height="400"
            v-else-if="activeName === 'second'"
            style="width: 400px"
            node-key="id"
            :data="leftList"
            :show-checkbox="showCheck"
            :check-strictly="true"
            :props="tabList[1].leftProp"
            @node-click="handleNodeClick"
          />
          <el-tree-v2
            :height="400"
            v-else-if="activeName === 'customer'"
            style="width: 400px"
            node-key="id"
            :data="leftList"
            :show-checkbox="showCheck"
            :check-strictly="true"
            :props="tabList[2].leftProp"
            @node-click="handleNodeClick"
          />
          <el-tree-v2
            :height="400"
            v-else-if="activeName === 'enterprise'"
            style="width: 400px"
            node-key="id"
            :data="leftList"
            :show-checkbox="showCheck"
            :check-strictly="true"
            :props="tabList[3].leftProp"
            @node-click="handleNodeClick"
          /> -->
        </el-scrollbar>
      </div>
      <!-- 右侧数据 -->
      <div style="max-height: 400px" class="w-full">
        <el-scrollbar class="tree_scrollbar">
          <div>
            <el-input v-model="user" placeholder="请输入内容" clearable></el-input>
          </div>
          <el-button v-if="!props.max" @click="handleCheckAllChange" text type="primary">{{
            checkAll ? '取消全选' : '全选'
          }}</el-button>
          <el-button v-if="!props.max" @click="handleInvertChange" text type="primary"
            >反选</el-button
          >
          <el-checkbox-group v-model="checkList" :max="props.max || undefined">
            <div v-for="item in rightData" :key="item[activeObj.rightProp.id]">
              <el-checkbox
                :label="item"
                :value="item"
                :checked="item.checked"
                :disabled="item.disabled"
              >
                <div class="flex items-center">
                  {{ item[activeObj.rightProp.label] }}
                  <span class="ml-20px" v-if="isFilterWork">
                    <el-tag> {{ item.innerType === 1 ? '外部' : '内部' }}</el-tag>
                  </span>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, Ref } from 'vue';
import { ElMessage } from 'element-plus';
import { userRangeTypeKey } from '@/components/xt-el-form/constant.ts';
import { UserRangeEnum } from '@/components/form-config/const/commonDic';

const workIds1 = inject<Ref<string[]>>('taskGroupList');
const userRange = inject(userRangeTypeKey, UserRangeEnum.ALL); //成员选择配置用户范围 1.全部 2.内部 3.外部

interface leftPropType {
  children: string;
  label: string;
  value: string;
  leftSearchId: string;
}

interface rightPropType {
  label: string;
  id: string;
}
interface configType {
  isRadio?: boolean;
  // isFilterWork?: boolean; //是否过滤工作组
  workIds?: string[]; //分组绑定的工作组集合
}

interface tabType {
  name: string; //tab的name
  label: string; //tab的label
  api?: (params: any) => Promise<any>; //左侧请求的api
  apiParams?: any; //左侧请求的参数
  getDataApi?: (params: any) => Promise<any>; //请求右侧数据的api
  getDataParams?: any;
  leftProp: leftPropType; //左边tree的prop
  rightProp: rightPropType; //右边data的label和value
  dataCallBack?: (data: any) => any;
}

interface DrawerProps {
  tabList: tabType[];
  visible: boolean;
  config?: configType;
  isFilterWork?: boolean;
  max?: number;
}

const props = withDefaults(defineProps<DrawerProps>(), {
  visible: false,
  isFilterWork: true,
});
console.log(props.tabList, '=-=====');

console.log(props.max);

defineEmits<{
  (e: 'update:modelValue', val: any[]): void;
}>();

const isFilter = computed(() => {
  return props.config?.workIds || workIds1?.value;
});

const vModel = defineModel<any[]>({ required: false, default: [] });
const showCheck = ref(false);
const activeName = ref(props.tabList[0].name);
const leftList = ref<any[]>([]);
const checkList = ref<any[]>([]); //右侧当前选中的列表
// const chooseList = ref<any[]>([]); //所有选中的列表
const dataList = ref<any[]>([]);
const leftItem = ref({} as any);
const user = ref('');
const activeObj = ref<any>(props.tabList[0]); //当前是tab的数据
const disabledUser = inject('disabledUser');

// // 工作组：内部/外部 部门:内部 客服组:外部 企业协作: 外部
// const filterTabList = computed(() => { 
//   //根据用户范围过滤一遍
//   if (userRange === UserRangeEnum.INTERNAL) {
//     return  props.tabList.filter((item: any) => _.includes(['部门','工作组'],item.label)); // 内部用户
//   } else if (userRange === UserRangeEnum.EXTERNAL) {
//     activeName.value = 'second'
//     return  props.tabList.filter((item: any) => _.includes(['工作组','客服组','企业协作'],item.label)); // 外部用户
//   }
//   return  props.tabList
// });

//右侧的数据列表-搜索
const rightData = computed(() => {
  return user.value
    ? dataList.value.filter(item =>
        item[activeObj.value.rightProp.label].toLowerCase().includes(user.value)
      )
    : dataList.value;
});

//全选/取消全选
const checkAll = computed(() => {
  return checkList.value.length === dataList.value.length ? true : false;
});

//watch checkList 选中的dataList checked：true 反之为checked: false
function compareArrays(a: any[], b: any[]) {
  const idSet = new Set(a.map(item => item[activeObj.value.rightProp.id]));
  const result = [...b];
  for (const item of result) {
    if (idSet.has(item[activeObj.value.rightProp.id])) {
      item.checked = true;
    } else {
      item.checked = false;
    }
  }
  for (const item of result) {
    if (disabledUser && item.id === disabledUser) {
      item.disabled = true;
    } else {
      item.disabled = false;
    }
  }
  return result;
}

watch(
  () => props.visible,
  val => {
    if (val) {
      const idSet = new Set(vModel.value.map(item => item[activeObj.value.rightProp.id]));
      for (const item of dataList.value) {
        if (idSet.has(item[activeObj.value.rightProp.id])) {
          item.checked = true;
        } else {
          item.checked = false;
        }
      }
      checkList.value = dataList.value.filter(v => v.checked);
    }
  }
);

watch(
  () => checkList.value,
  val => {
    //单选
    if (checkList.value.length > 1 && props.config?.isRadio) {
      ElMessage.warning('该组件是单选,只能选择一个数据');
      checkList.value = [];
      return;
    }
    //选中的右侧数据，checked标识为true
    const ids = val.map(v => v[activeObj.value.rightProp.id]);
    dataList.value.forEach(item => {
      if (ids.includes(item[activeObj.value.rightProp.id])) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
    // //所有选中的数据跟右侧数据对比，如果有id相同的并且checked为true的，数据加上，如果不是则去掉
    const idSet = new Set(vModel.value.map(item => item[activeObj.value.rightProp.id]));
    for (const item of dataList.value) {
      if (item.checked && !idSet.has(item[activeObj.value.rightProp.id])) {
        vModel.value.push(item);
      } else if (!item.checked && idSet.has(item[activeObj.value.rightProp.id])) {
        const index = vModel.value.findIndex(
          obj => obj[activeObj.value.rightProp.id] === item[activeObj.value.rightProp.id]
        );
        vModel.value.splice(index, 1);
      }
    }
  },
  { deep: true }
);

watch(
  () => activeName.value,
  async val => {
    const arr = props.tabList.filter(v => v.name === val);
    if (arr.length) {
      activeObj.value = arr[0];
      const { data } = await activeObj.value.api(activeObj.value?.apiParams);
      if (!_.isEmpty(isFilter.value) && activeName.value === 'second') {
        let flatTree = flattenTree(data.data);
        const ids = props.config?.workIds || workIds1?.value;
        leftList.value = flatTree.filter((item: any) => ids?.includes(item.id));
      } else {
        leftList.value = data.data || [];
      }
    }
  },
  { immediate: true }
);

const flattenTree = (treeData: any, flattenedArray: any = []) => {
  treeData.forEach((node: any) => {
    flattenedArray.push({ ...node, children: [] });
    if (node.children && node.children.length > 0) {
      flattenTree(node.children, flattenedArray);
    }
  });
  return flattenedArray;
};
//全选
const handleCheckAllChange = () => {
  checkList.value = checkAll.value ? [] : dataList.value;
};

//反选
const handleInvertChange = () => {
  const choosemids = checkList.value.map(item => item[activeObj.value.rightProp.id]);
  checkList.value = dataList.value.filter(
    item => !choosemids.includes(item[activeObj.value.rightProp.id])
  );
};

//点击tab
const handleClick = (tab: any, event: any) => {
  console.log(tab, event);
  activeName.value = tab.props.name;
  // 切换tab时右侧数据清空
  dataList.value = [];
};

//删除选中数据
const deleteData = (item: any, index: number) => {
  vModel.value.splice(index, 1);
  dataList.value.forEach(v => {
    if (v[activeObj.value.rightProp.id] === item[activeObj.value.rightProp.id]) {
      v.checked = false;
    }
  });
  checkList.value = dataList.value.filter(v => v.checked);
};

//点击树形
const handleNodeClick = async (node: any) => {
  console.log(node, 'node', activeObj.value);
  leftItem.value = node;
  const params: Record<string, any> = {
    [activeObj.value.leftProp.leftSearchId]: node[activeObj.value.rightProp.id],
  };
  //企业协作多传一个参数
  if (activeObj.value.name === 'enterprise') {
    params.tenantId = node.tenantId;
  }
  if (activeObj.value.name === 'categorize') {
    params.relType = 0; // 选成员
  }
  const { data } = await activeObj.value.getDataApi(params);
  let result = data.data;
  if (activeObj.value.dataCallBack) result = activeObj.value.dataCallBack(result);
  //根据用户范围过滤一遍
  if (userRange === UserRangeEnum.INTERNAL) {
    result = result.filter((item: any) => item.innerType === 0); // 内部用户
  } else if (userRange === UserRangeEnum.EXTERNAL) {
    result = result.filter((item: any) => item.innerType === 1); // 外部用户
  }
  dataList.value = compareArrays(vModel.value, result);
  checkList.value = dataList.value.filter(item => item.checked);
};
</script>

<style lang="scss" scoped>
.btn {
  color: #aaaaaa;
  cursor: pointer;
  border: 1px dashed #aaaaaa;
  border-radius: 5px;
}

.btn1 {
  color: #aaaaaa;
  border: 1px solid #aaaaaa;
}

.el-form-item__label {
  align-items: center;
}
</style>
