<template>
  <xt-el-form class="flex-1" :option="option" v-model="form" :key="formKey"></xt-el-form>
</template>

<script setup lang="ts">
import { AvueOption } from '../../form-config/types/AvueTypes.ts';
import { ColExtra, initBasicInfoColumn } from './XTBasicInfo.ts';
import { objectToArray } from '@/const/BasicInfoOption.ts';
import { getOrgInfo } from '../../../api/orgInfo/index.js';
import XtElForm from '../../xt-el-form/XtElForm.vue';
import { taskSourceInjectKey, handleTypeInjectKey } from '@/components/xt-el-form/constant.ts';
import { HandleTypeEnum } from '@/components/form-config/const/commonDic';

defineOptions({
  inheritAttrs: false,
});

const taskSource = inject(taskSourceInjectKey, null); //任务组织来源
const handleType = inject(handleTypeInjectKey, HandleTypeEnum.HANDLED);

const props = withDefaults(
  defineProps<{
    extraCol?: ColExtra;
  }>(),
  {
    extraCol: () => {
      return {};
    },
  }
);

const filteredExtraCol = computed(() => {
  return _.omit(initBasicInfoColumn(props.extraCol), [
    'manageStatus',
    'staffSize',
    'logo',
    'officialSealStatus',
    'securityCode',
  ]);
});
const option = computed<AvueOption>(() => {
  return {
    disabled: true,
    menuBtn: false,
    labelWidth: 120,
    labelPosition: 'top',
    gutter: 2,
    column: objectToArray(filteredExtraCol.value || {}),
  };
});

const form = shallowRef({});
const formKey = ref(0);
watch(
  () => option.value,
  (newVal, oldVal) => {
    if (!_.isEqual(newVal, oldVal)) {
      formKey.value += 1;
    }
  },
  { deep: true }
);

onMounted(() => {
  getOrgInfo(handleType === HandleTypeEnum.Examine ? taskSource?.value : undefined).then(res => {
    form.value = res.data?.data || {};
  });
});
</script>

<style scoped lang="scss"></style>
