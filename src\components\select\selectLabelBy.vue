<template>
  <div class="select_label">
    <slot :openSelect="openSelect" name="default">
      <el-card
        class="box-card"
        :class="{ disabled: disabled }"
        @click="disabled ? void 0 : openSelect()"
      >
        <template v-if="bindLabels.length > 0">
          <el-tag
            v-for="item in bindLabels"
            :key="item.id"
            :closable="closable"
            @close="unCheckDept(item)"
            style="margin: 0 5px"
            >{{ item.labelName }}</el-tag
          >
        </template>
        <div v-else class="select_label_placeholder">{{ placeholder }}</div>
      </el-card>
    </slot>
    <el-dialog :title="title" v-model="visible" @close="dialogClose">
      <div class="select_wrapper">
        <div class="select_top">
          <el-card class="box-card">
            <template v-if="bindLabels.length > 0">
              <el-tag
                v-for="item in bindLabels"
                closable
                @close="unCheckDept(item)"
                style="margin: 5px"
                size="large"
                >{{ item.labelName }}</el-tag
              >
            </template>

            <div v-else class="select_label_placeholder">{{ placeholder }}</div>
          </el-card>
        </div>
        <div class="select_bottom">
          <el-scrollbar class="tree_scrollbar" v-if="sourceType === 'task'">
            <el-tree
              :data="labelTreeData"
              ref="deptTreeRef"
              node-key="id"
              :current-node-key="treeId"
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
              highlight-current
              :props="{ label: 'name' }"
            >
            </el-tree>
          </el-scrollbar>
          <el-divider direction="vertical" class="h-full" v-if="sourceType === 'task'" />
          <!-- 右侧数据 -->
          <div class="w-full">
            <el-scrollbar class="tree_scrollbar">
              <div class="flex items-center gap-30px mb-10px">
                <el-input placeholder="标签名称搜索" class="w-150px" v-model="searchKey" />
                <div class="flex w-full items-center justify-around">
                  <span>已选 {{ bindLabels.length }} / {{ list.length }}</span>
                  <el-checkbox v-model="checkedAll" @change="handleChecked"></el-checkbox>
                </div>
              </div>
              <div v-for="item in list" :key="item.id">
                <el-checkbox
                  :label="item"
                  :value="item"
                  v-model="item.checked"
                  @change="val => handleCheckChange(val, item)"
                >
                  {{ item.labelName }}
                </el-checkbox>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button type="primary" @click="confirm"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { CheckboxValueType } from 'element-plus';
import useLabelStore, { LabelProps } from '@/piniaStore/banban/useLabelStore.ts';
import { getVersionListByGroupRequest } from '@/api/taskManagement/common';
import { storeToRefs } from 'pinia';
import { ElMessage, ElTree } from 'element-plus';

interface selectUserByProps {
  dataType?: 'array' | 'string';
  multiple?: boolean;
  placeholder?: string;
  closable?: boolean;
  disabled?: boolean;
  openLoad?: boolean;
  title?: string;
  sourceType?: 'app' | 'task';
  appId?: string;
}

interface versionProp {
  id: string;
  name: string;
}

const visible = ref(false);
const props = withDefaults(defineProps<selectUserByProps>(), {
  dataType: 'array',
  multiple: false,
  placeholder: '请选择标签',
  closable: false,
  openLoad: false,
  disabled: false,
  title: '选择标签',
  sourceType: 'task',
});
const isTypeArray = computed(() => {
  return props.dataType === 'array';
});
const vModel = defineModel<string[] | string>({ required: false, default: [] });
const deptTreeRef = ref<InstanceType<typeof ElTree>>();
const currentSelectDept = ref<string>('');
const searchKey = ref('');
const checkedAll = ref(false);

const { list, labelTreeData, bindLabels, rightAllList } = storeToRefs(useLabelStore());
const { findLabelByIds, initLabelList, initAllLabelList } = useLabelStore();
const emit = defineEmits<{
  (e: 'change', value: LabelProps[]): void;
}>();

const handleChecked = (checked: CheckboxValueType) => {
  if (checked) {
    bindLabels.value = _.cloneDeep(list.value);
  } else {
    bindLabels.value = [];
  }
};

const initChooseLabel = async () => {
  if (props.sourceType == 'task') {
    let res = await getVersionListByGroupRequest();
    labelTreeData.value = res.data.data.map((item: { id: string; groupName: string }) => ({
      ...item,
      name: item.groupName,
    }));
    await handleNodeClick(labelTreeData.value[0]);
    nextTick(() => {
      if (labelTreeData.value.length) {
        deptTreeRef.value?.setCheckedKeys([labelTreeData.value[0].id]);
      }
    });
  } else {
    if (!props.appId) {
      ElMessage.warning('请传入微应用id');
      return;
    }
    initLabelList('', props.appId, props.sourceType);
  }
};

let treeId = ref('');

watch(
  () => bindLabels.value,
  val => {
    list.value.forEach((item: any) => {
      if (val.length && val.find((el: any) => el.id === item.id)) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
  },
  { deep: true }
);
watch(
  () => searchKey.value,
  (val: string) => {
    list.value = rightAllList.value.filter((f: LabelProps) => f.labelName.includes(val));
  }
);
const openSelect = async () => {
  visible.value = true;
  await initChooseLabel();
};
const handleNodeClick = async (data: versionProp) => {
  treeId.value = data.id;
  await initLabelList(data.id, '', props.sourceType);
};

const handleCheckChange = (e: CheckboxValueType, item: any) => {
  if (e) {
    bindLabels.value.push(_.cloneDeep(item));
  } else {
    bindLabels.value = bindLabels.value.filter((f: LabelProps) => f.id !== item.id);
  }
  checkedAll.value = bindLabels.value.length === list.value.length;
};
const clearValidate = () => {
  currentSelectDept.value = '';
  bindLabels.value = [];
  deptTreeRef.value?.setCheckedKeys([], false);
};

const initBindList = (n: string | string[]) => {
  bindLabels.value = []
  let echoData = findLabelByIds(n);
  echoData.forEach(item => {
    bindLabels.value.push(item);
  });
};

watch(
  vModel,
  async n => {
    if (_.isEmpty(n)) {
      clearValidate();
    }
    await initAllLabelList();
    initBindList(n);
  },
  { immediate: true, deep: true }
);
const unCheckDept = (s: LabelProps) => {
  if (bindLabels.value.some((item: any) => item.id === s.id)) {
    bindLabels.value = bindLabels.value.filter((f: any) => f.id !== s.id);
  }
};

const dialogClose = () => {
  initBindList(vModel.value);
};
const confirm = () => {
  vModel.value = isTypeArray.value
    ? bindLabels.value.map((item: LabelProps) => item.id)
    : bindLabels.value.map((item: LabelProps) => item.id).join(',');
  visible.value = false;
  emit('change', []);
};

export interface selectUserIns {
  clearValidate: () => void;
  modelList: any;
  openSelect: (callback: (value: string | string[]) => void) => void;
}
defineExpose<selectUserIns>({
  clearValidate,
  openSelect,
  modelList: bindLabels.value,
});
</script>

<style scoped lang="scss">
:deep(.el-tabs__nav-wrap .el-tabs__nav-scroll) {
  display: flex;
  justify-content: start;
}
:deep(.el-tabs__nav-wrap .el-tabs__nav) {
  display: flex;
  justify-content: space-between;
  width: 100px;
}
.select_label {
  width: 100%;
}
.select_wrapper {
  display: flex;
  flex-direction: column;
  .select_bottom {
    border: 1px solid rgb(228, 228, 228);
    height: 250px;
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
  }
}
.tree_scrollbar {
  padding: 10px;
  min-width: 400px;
}
.user_scrollbar {
  padding: 10px;
  width: 100%;
  border-left: 1px solid #eee;
}
.box-card {
  cursor: pointer;
  width: 100%;
  :deep(.el-card__body) {
    padding: 0px;
    padding-left: 5px;
    border-radius: 0;
    min-height: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
  }
}
.select_label_placeholder {
  color: #a8abb2;
  font-size: 14px;
}
.disabled {
  background: #f6f6f6;
}
</style>
