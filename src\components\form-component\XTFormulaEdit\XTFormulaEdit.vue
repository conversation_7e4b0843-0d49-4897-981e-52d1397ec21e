<template>
  <div class="w-full">
    <el-input disabled v-model="vModel"></el-input>
  </div>
  
</template>
<script setup lang="ts">
import { AvueColumns,FormulaField,formulaFilterType } from '@/components/form-config/types/AvueTypes.ts';
import { formulaCalculate } from '@/api/banban/field';
import { fieldSource } from '@/components/form-config/const/commonDic';
import { handleResponseData } from '@/utils/field';
import { formulaCaltype } from '@/api/interface/fieldType';


const vModel = defineModel<string|number>({ default: '' });

const props = defineProps<{
    item: AvueColumns & formulaFilterType & FormulaField;
    source:number
}>()

onMounted(() => {
  if (props.source !== fieldSource.SET && props.item.formulaFilter.taskId && props.item.formula?.template) {
    //调公式编辑的接口
    console.log('请调用公式编辑的接口')
    Calculate()
  }
});

const Calculate = async() => {
  const params = {
    formula: props.item.formula?.template,
    formulaFilter: props.item.formulaFilter,
    type:1
  }
  const { data } = await formulaCalculate(params as formulaCaltype)
  vModel.value = handleResponseData(data.data)
}


</script>
<style lang="scss" scoped>




</style>
