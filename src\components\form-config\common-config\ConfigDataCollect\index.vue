<template>
  <div class="!mb-5px w-full">
    <el-button :icon="Edit" class="w-full" @click="visible = true" size="large"
      >添加筛选条件</el-button
    >
  </div>
  <el-dialog
    v-model="visible"
    title="编辑筛选条件"
    append-to-body
    destroy-on-close
    width="1000px"
    :close-on-click-modal="false"
  >
    <containerContent
      ref="container"
      :data="query.list"
      @add="addQuery"
      @delete="handleGroupDel"
      :main-width="900"
    >
      <template #item="{ item, index }">
        <n-card class="mb-2">
          <div class="mb-2">
            <h3>显隐规则</h3>
            <span>触发任务的联动条件与当前表单的联动条件之间为</span>
            <avue-select
              class="w-110px! px-10px satisfy-select"
              v-model="item.logicBetween"
              :dic="isAlsoEnum1"
              :clearable="false"
            >
            </avue-select>
            <span>的关系</span>
          </div>

          <!-- 触发任务 -->
          <n-card class="mb-2">
            <el-form-item label="选择联动任务">
              <selectTask
                :taskList="taskList"
                v-model:taskId="item.linkageTaskId"
                v-model:fieldList="item.linkFieldList"
                v-model:type="item.linkageTaskType"
                @click="handleChangeTask(index)"
              />
            </el-form-item>
            <fieldConditionConfig
              :field="item.linkFieldList"
              :conditionTaskId="item.linkageTaskId"
              v-model:condition="item.linkageColumnList"
              v-model:andOr="item.linkageOperator"
              :logicBetween="item.logicBetween"
              title="添加触发任务的联动条件"
            />
          </n-card>
          <!-- 当前任务 -->
          <n-card class="mb-2">
            <fieldConditionConfig
              :fieldList="fieldList1"
              :conditionTaskId="taskId"
              v-model:condition="item.columnList"
              v-model:andOr="item.logic"
              :current="true"
              :current-field="data"
              :logicBetween="item.logicBetween"
              title="添加当前任务的联动条件"
            />
          </n-card>
        </n-card>
        <!-- 筛选条件 -->
        <n-card class="mb-2">
          <n-card class="mb-2">
            <h3>筛选条件</h3>
            <div>
              <el-form-item 
                :label="isKnowData ? '选择知识库': '选择推数路径'" 
                required
              >
                <selectTask
                  :taskList="isKnowData ? knowData : taskList"
                  :defaultType="(data.defaultType as DynamicTypeEnum)"
                  v-model:taskId="item.linkageField.path"
                  v-model:type="item.linkageField.pathType"
                  v-model:fieldList="item.pushFieldList"
                  @click="handleChangeTask1(index)"
                />
              </el-form-item>
              <accordTitle
                title="添加条件"
                v-model="item.linkageField.filterOperator"
                @add="handleAdd(item, index)"
              />
              <filterConditionItem
                class="mb-3"
                v-for="(filterColumn, itemIndex) in item.linkageField.filterColumnList"
                :key="filterColumn.idx"
                :filterColumn="filterColumn"
                v-model:linkTaskId="item.linkageTaskId"
                v-model:pushTaskId="item.linkageField.path"
                :conditionTaskId="taskId"
                :data="data"
                :taskList="taskList"
                :knowData="knowData"
                :pathType="item.linkageField.pathType"
                v-model:pushFieldList="item.pushFieldList"
                v-model:linkFieldList="item.linkFieldList"
                @delete="handleDelete(index, itemIndex)"
              />
            </div>
            <div class="mt-10">
              <h3>极值规则</h3>
              <div class="mb-2 flex">
                根据
                <el-select v-model="item.extremeRule!.extremumType" clearable class="ml-2 mr-2 w-200px">
                  <el-option
                    v-for="(e) in numberDic(item.pushFieldList, item.linkageField.pathType)"
                    :key="e.id"
                    :label="e.copyLabel"
                    :value="e.id"
                  />
                </el-select>
                取
                <avue-select
                  class="w-200px ml-2 mr-2"
                  :dic="filterSortFieldType(item.pushFieldList,item.extremeRule!.extremumType )"
                  v-model="item.extremeRule!.extremumOption"
                ></avue-select>
              </div>
              <div class="flex">
                根据
                <el-select v-model="item.extremeRule!.componentId" clearable class="ml-2 mr-2 w-200px">
                  <el-option
                    v-for="e in sortField(item.pushFieldList, item.linkageField.pathType)"
                    :key="e.id"
                    :label="e.copyLabel"
                    :value="e.id"
                  />
                </el-select>
                取
                <avue-select
                  class="w-200px ml-2 mr-2"
                  :dic="sortFetchOption"
                  v-model="item.sortRule!.extremumItem"
                ></avue-select>
              </div>
            </div>
          </n-card>
          <n-card>
            <h3>展示字段</h3>
            <template  v-if="data.fixType === 'dynamic' && item.linkageField!.path"  >
                <addDynamicLink
                    :data="data" 
                    :taskList="isKnowData ? knowData : taskList"
                    v-model:pushTaskId="item.linkageField!.path" 
                    :pushDynamicField="item.pushFieldList"
                    v-model:dynamicFields="item.dynamicFields"
                    :pathType="item.linkageField.pathType"
                    >
                </addDynamicLink>
            </template>
            <showFields 
              v-else
              :field-list="item.pushFieldList" 
              v-model:showField="item.showField" 
              v-model:showFieldIds="item.showFieldIds" 
              :data="data">
            </showFields>
          </n-card>
        </n-card>
      </template>
    </containerContent>
    <template #footer>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { DataFilterType} from './type';
import containerContent from '../components/containerContent/index.vue';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { storeToRefs } from 'pinia';
import { DataCollectItem,DataCollectType } from '@/api/interface/configDataLink.ts';
import { Edit } from '@element-plus/icons-vue';
import fieldConditionConfig from '../components/fieldConditionConfig.vue';
import filterConditionItem from '../components/filterConditionItem.vue';
import { sortFetchOption, fetchOption1, isAlsoEnum1, fetchOption2 } from '@/components/form-config/utils/option.js';
import useFieldList from '@/views/mainview/sceneTasks/detail/hooks/useFieldList';
import { ElMessage } from 'element-plus';
import selectTask from '../components/selectTask.vue';
import accordTitle from '../components/accordTitle/index.vue';
import { fieldConditionIncludeType } from '@/components/form-config/types/FieldTypes.ts';
import { getPushTaskList } from '@/api/taskManagement/task';
import usePackageTreeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTreeStore.ts';
import { pushTaskListType } from '@/api/interface/task';
import { filterSortType } from '@/components/form-config/types/FieldTypes.ts';
import showFields from '../components/showFields.vue';
import { systemByFilterSortType, createList, dateType,numberType, systemByFilterSortType1 } from '@/components/form-config/types/FieldTypes';
import { AvueColumns } from '@/components/form-config/types/AvueTypes'
import addDynamicLink from '../components/addDynamicLink/index.vue';
import { generateId } from '@/components/form-config/utils';
import { editKnowType } from '@/api/interface/knowLedgeType'
import { getKnowledgeList } from '@/api/banban/knowLedge'
import { DynamicTypeEnum } from '@/components/form-config/const/commonDic';
import { getOrganizeTree } from '@/api/banban/knowLedge'
import { FixTypeEnum,SystemTypeEnum } from '@/components/form-config/types/field.ts';

const { versionId } = storeToRefs(usePackageTreeStore());
const props = defineProps<DataFilterType>();
const { taskId } = storeToRefs(useTaskDetailStore()) as { taskId: any };
const visible = ref(false);

const query = ref<DataCollectType>({ list: [] });

//排序的数据提交时间
// const systemField = computed(() => {
//   return createList.filter(item => systemByFilterSortType.includes(item.fixType));
// });

const isKnowData = computed(() => {
  return _.includes([DynamicTypeEnum.KNOWLEDGE,DynamicTypeEnum.ORGKNOWLEDGE], props.data.defaultType)
})

const numberDic = (pushFieldList: AvueColumns[], pathType: number) => {
  const filterType:SystemTypeEnum[] = pathType === 0 ? systemByFilterSortType : systemByFilterSortType1
  const systemField = createList.filter(item => filterType.includes(item.fixType as SystemTypeEnum));
  const types = [...numberType, ...dateType]
  const arr = pushFieldList?.filter(item => _.includes(types,item.fixType));
  return [...arr, ...systemField]
}

const filterSortFieldType = (pushFieldList: AvueColumns[], id:string) => {
  const type = pushFieldList?.find(item => item.id === id)?.fixType
  return _.includes(numberType,type) ? fetchOption2: fetchOption1

}

const sortField = (pushFieldList: AvueColumns[], pathType: number) => {
  const filterType:SystemTypeEnum[] = pathType === 0 ? systemByFilterSortType : systemByFilterSortType1
  const systemField = createList.filter(item => filterType.includes(item.fixType as SystemTypeEnum));
  const arr = pushFieldList?.filter(item => _.includes(filterSortType,item.fixType));
  return [...arr, ...systemField]
}

//相关的当前组件
const linkFields = computed(() => {
  const currentFieldProps = query.value.list
    .filter(item => item.columnList)
    .flatMap(item => item.columnList?.filter(column => column.prop).map(v => v.prop));
  const currentFilterProps = query.value.list
    .filter(item => item.linkageField?.filterColumnList)
    .flatMap(item => item.linkageField?.filterColumnList?.filter(column => column.currentFormProp).map(v => v.currentFormProp));
  return _.uniq([...currentFieldProps, ...currentFilterProps]);
});


//任务
const taskList = ref<pushTaskListType[]>([]);
const getList = () => {
  getPushTaskList({ version: versionId.value as any || '', type: '0' }).then(res => {
    taskList.value = res.data.data;
  });
};
getList();

//组织知识库-取当前事集下的知识库
const filterKnowList = (knowData:editKnowType[]) => {
  return knowData.find(v => v.id === versionId.value as any)?.organizations
}
const knowData = ref<editKnowType[]>()
//系统知识库
const getKnowList = async() => {
  const {data} = props.data.defaultType === DynamicTypeEnum.KNOWLEDGE ?
  await getKnowledgeList({}) : await getOrganizeTree({}) 
  const res = props.data.defaultType === DynamicTypeEnum.KNOWLEDGE ? data.data : filterKnowList(data.data)
  knowData.value = res.map((v:editKnowType)=> {
    return {
      ...v,
      label: v.kbName,
      value: v.id
    }
  })
}

watch(
  () => props.data.defaultType,
  (val) => {
    if([DynamicTypeEnum.KNOWLEDGE,DynamicTypeEnum.ORGKNOWLEDGE].includes(val as DynamicTypeEnum)){
      getKnowList()
    }
  },
  { immediate: true  }
)

const checkData = () => {
  if (_.isArray(query.value.list)) {
    const haveEmpty = query.value.list.some(i => {
      if(props.data.fixType === FixTypeEnum.DYNAMIC){
        return (
        !i.linkageField?.path ||
        !i.dynamicFields?.length ||
        i.dynamicFields?.some(v=> !v.currentProp || !v.pushProp) 
      );
      }else{
        return (
        !i.linkageField?.path ||
        !i.showField.length 
      );
      }
    });
    if (haveEmpty) {
      ElMessage.warning('推数路径、展示字段不能为空');
      return false;
    }
  }
  return true;
};

//添加条件组
const addQuery = () => {
  if (!checkData()) return;
  (query.value.list ??= []).push({
    idx: generateId(),
    logicBetween: '1',
    linkageField: {
      path: '',
      pathType: 0,
    },
    extremeRule: {
      extremumType: '',
      extremumOption: undefined,
    },
    sortRule: { componentId: '', extremumItem: '' },
    showField: [],
    showFieldIds: [],
    dynamicFields: [{ currentProp:'', pushProp:'', idx: '' }]
  });
};

//添加筛选条件
const handleAdd = (item: DataCollectItem, index: number) => {
  if (!item.linkageField?.path) {
    ElMessage.warning('请先选择推数路径');
    return;
  }
  if (!item.linkageField?.filterOperator) {
    ElMessage.warning('请先选择条件的逻辑关系(且/或)');
    return;
  }
  if (_.isArray(item.linkageField.filterColumnList)) {
    const haveEmpty = item.linkageField.filterColumnList?.some(
      i =>
        _.isEmpty(i.dataProp) ||
        _.isEmpty(i.operator) 
    );
    if (haveEmpty) {
      ElMessage.warning('有空条目请勿添加新的条目');
      return;
    }
  }
  (query.value.list[index].linkageField!.filterColumnList ??= []).push({
    idx: generateId(),
    dataProp: '',
    filterType: 'TRIGGER_TASK_FIELD',
  });
};

//删除条件组
const handleGroupDel = (index: number) => {
  query.value.list?.splice(index, 1);
};

//删除筛选条件
const handleDelete = (index: number, itemIndex: number) => {
  query.value.list[index].linkageField?.filterColumnList?.splice(itemIndex, 1);
};

//提交
const handleSubmit = () => {
  if (!checkData()) return;
  const arr = query.value.list.map((i:any) => {
        delete i.pushFieldList
        delete i.linkFieldList
        return i
    })
  props.data.query = _.cloneDeep({list: arr});
  props.data.linkFields = _.cloneDeep(Array.from(new Set(linkFields.value as string[])));
  visible.value = false;
};

//联动任务改动清除选项
const handleChangeTask = (index: number) => {
  query.value.list[index].linkageColumnList = [];
};

//推数路径改动清除选项
const handleChangeTask1 = (index: number) => {
  console.log(index, 'wowowo' )
  query.value.list[index].showField = [];
  query.value.list[index].extremeRule!.extremumType = '';
  query.value.list[index].sortRule!.componentId = '';
  query.value.list[index].dynamicFields = []
  query.value.list[index].linkageField!.filterColumnList = [{
    idx: generateId(),
    dataProp: '',
    filterType: 'TRIGGER_TASK_FIELD',
  }]
  query.value.list[index].linkageField!.filterOperator = '1'
}; 

const fieldList1 = computed(() => {
  return fieldList.value.map(v => {
    return {
      ...v,
      // label: v.copyLabel,
      // value: v.id,
      // fieldType: v.type,
    };
  });
});
//字段
const { getFiledList, fieldList } = useFieldList(fieldConditionIncludeType);
getFiledList({
  id: taskId.value || '',
  unFlatDynamic: false,
  changeToAble: true,
  unFlatDataCollect: false,
  unFlatDataSelect: false,
  unFlatKnowledgeSelect: false,
});

watch(
    () => visible.value,
    (val) => {
        if(val) {
           query.value = _.cloneDeep(props.data.query) || {list:[]}
        }
    },
    { immediate: true }
)
</script>

<style scoped lang="scss">
.dataBox {
  :deep(.el-checkbox-group) {
    @apply flex-(~ col) ml-10px;
  }
}
</style>
