<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <title>WORKDAY</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-230.000000, -2735.000000)">
            <g id="WORKDAY" transform="translate(230.000000, 2735.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-5" transform="translate(10.000000, 42.000000)" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="23">
                    <text id="获取给定日期之前或者之后指定工作日数的日" fill="#3A3A3A">
                        <tspan x="0" y="15">获取给定日期之前或者之后指定工作日数的日期，</tspan>
                        <tspan x="0" y="38">工作日不包括周末和任何其他指定日期。</tspan>
                    </text>
                    <text id="·用法：WORKDAY(日期,工作日数," letter-spacing="-0.2">
                        <tspan x="0" y="66" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="48.2" y="66" fill="#3F70FF">WORKDAY</tspan>
                        <tspan x="118.032" y="66" fill="#3A3A3A">(日期,工作日数)</tspan>
                    </text>
                    <text id="·示例：略" fill="#3A3A3A">
                        <tspan x="0" y="118">·示例：略</tspan>
                    </text>
                </g>
                <text font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">WORKDAY</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>