<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 38</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-910.000000, -670.000000)">
            <g id="编组-38" transform="translate(910.000000, 670.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <g id="编组-7" transform="translate(10.000000, 42.000000)">
                    <text id="COUNTIF函数可以获取数组中满足条件" font-family="PingFangSC-Regular, PingFang SC"
                          font-size="14" font-weight="normal" line-spacing="23">
                        <tspan x="0" y="15" fill="#3F70FF">COUNTIF</tspan>
                        <tspan x="61.054" y="15" fill="#3A3A3A">函数可以获取数组中满足条件的参数个</tspan>
                        <tspan x="0" y="38" fill="#3A3A3A">数。</tspan>
                        <tspan x="0" y="62" fill="#3A3A3A">·用法：</tspan>
                        <tspan x="49" y="62" fill="#3F70FF">COUNTIF</tspan>
                        <tspan x="110.054" y="62" fill="#3A3A3A">(数组,"条件")</tspan>
                        <tspan x="0" y="86" fill="#3A3A3A">·示例：</tspan>
                        <tspan x="49" y="86" fill="#3F70FF">COUNTIF</tspan>
                        <tspan x="110.054" y="86" fill="#3A3A3A">(</tspan>
                    </text>
                    <g id="编组-30" transform="translate(115.000000, 75.000000)">
                        <rect id="矩形" fill="#EAF3FF" x="0" y="0" width="72" height="16"></rect>
                        <text id="子表单｜性别" font-family="PingFangSC-Regular, PingFang SC" font-size="10"
                              font-weight="normal" line-spacing="16" fill="#3F70FF">
                            <tspan x="6" y="11">子表单｜性别</tspan>
                        </text>
                    </g>
                </g>
                <text id="，" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="197" y="131">，</tspan>
                </text>
                <text id="，备份-3" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="238" y="131">，</tspan>
                </text>
                <text id="返回3，也" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="248" y="131">返回3，也</tspan>
                </text>
                <text id="就是课程的数量可得到子表单中性别填的是" font-family="PingFangSC-Regular, PingFang SC"
                      font-size="14" font-weight="normal" fill="#3A3A3A">
                    <tspan x="17" y="153">就是课程的数量可得到子表单中性别填的是</tspan>
                </text>
                <text id="的数据条数。" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="17" y="178">的数据条数。</tspan>
                </text>
                <text id="&quot;" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="285" y="153">"</tspan>
                </text>
                <text id="&quot;备份" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="308" y="153">"</tspan>
                </text>
                <text id="女" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="292" y="153">女</tspan>
                </text>
                <text id="&quot;" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="202" y="131">"</tspan>
                </text>
                <text id="&quot;备份" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="225" y="131">"</tspan>
                </text>
                <text id=")" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="231" y="131">)</tspan>
                </text>
                <text id="女" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      fill="#3A3A3A">
                    <tspan x="209" y="131">女</tspan>
                </text>
                <text id="COUNTIF" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">COUNTIF</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
