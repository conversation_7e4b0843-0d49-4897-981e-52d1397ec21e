import { fieldListType } from '@/api/interface/configDataLink.ts'


export interface formulaCaltype {
    formula: string;
    formulaFilter?: {
        columnList: fieldListType[];
        taskId: string
    };
    type: number
}

//数据联动 添加筛选条件
export interface doLinkageType {
    componentId: string,
    formData: any,
    microAppId?: string|number,
    handledId?: string,
    taskId?: string,
    copyLabel?: string
}

//值与值 值与数据表
export interface doLinkageRuleType {
    ruleId: string,
    formData?: any,
    // handledId: string,
    taskId: string,
    type: number
}
