import request from '@/axios';
import { editKnowType, tempType } from '@/api/interface/knowLedgeType'

/**
 * 获取知识库列表
 */
export const getKnowledgeList = (data: any) => {
    return request({
        url: "/kb/page",
        method: 'post',
        data
    });
};

/**
 * 新增/编辑知识库
 */

export const editKnowledge = (data: editKnowType) => {
    return request({
        url: '/kb/submit',
        method: 'post',
        data
    });
};

/**
 * 删除知识库
 */

export const removeKnowledge = (params: { ids: string }) => {
    return request({
        url: '/kb/remove',
        method: 'post',
        params
    });
};

/**
 * 查询模板
 */

export const getTempLateInfo = (params: { kbId: string }) => {
    return request({
        url: '/kb/template/listById',
        method: 'post',
        params
    });
};

/**
 * 保存模板
 */

export const submitTempLateInfo = (data: tempType) => {
    return request({
        url: '/kb/template/submit',
        method: 'post',
        data
    });
};
/**
 * 获取知识库数据
 */

export const getKnowData = (data: any, params: { current: number, size: number }) => {
    return request({
        url: '/kb/data/page',
        method: 'post',
        data,
        params
    });
};

/**
 * 保存知识库数据
 */

export const submitKnowData = (data: any) => {
    return request({
        url: '/kb/data/submit',
        method: 'post',
        data
    });
};


// 下载模板
export const exportTemp = (params: { kbId: string }) => {
    return request({
        url: '/kb/data/importTemplate',
        method: 'post',
        params
    });
};

// 导入校验
export const importCheck = (data: { kbId: string; link: string }) => {
    return request({
        url: '/kb/data/importCheck',
        method: 'post',
        data
    });
};

// 实际导入
export const importKnowData = (data: { kbId: string; importId: string }) => {
    return request({
        url: '/kb/data/doImport',
        method: 'post',
        data
    });
};

// 删除知识库
export const removeKnowData = (dataIds: string ) => {
    return request({
        url: '/kb/data/remove',
        method: 'post',
        params: { dataIds }
    });
};

// 发布
export const adminPublish = (data: any ) => {
    return request({
        url: '/publish/publish',
        method: 'post',
        data: { ...data }
    });
};
// 获取组织管理员
export const getTenantAdmin = () => {
    return request({
        url: '/publish/queryAdmin',
        method: 'get',
    });
};
// 获取详情
export const getAdminDetails = (baseId:string) => {
    return request({
        url: '/publish/query',
        method: 'get',
        params: { baseId }
    });
};

// 列表tree     组织知识库----------
export const getOrganizeTree = (data: any) => {
    return request({
        url: "/organization/queryTree",
        method: 'get',
        data
    });
};
// 新增编辑     tree
export const editOrganizeTree = (data: editKnowType) => {
    return request({
        url: '/organization/insertOrUpdateBase',
        method: 'post',
        data
    });
};
// 删除     tree
export const deleteOrganizeTree = (id:any) => {
    return request({
        url: '/organization/deleteBase',
        method: 'post',
        params:{
            id
        }
    });
};
// 获取模板     table
export const getOrganizeTemplate = (kbId:any) => {
    return request({
        url: '/organization/template/queryTemplate',
        method: 'post',
        params:{
            kbId,
        }
    });
};
// 保存模板     table
export const submitOrganizeTempLate = (data: tempType) => {
    return request({
        url: '/organization/template/insertOrUpdate',
        method: 'post',
        data
    });
};
// 列表     table
export const getOrganizePage = (baseId: any, current: number, size: number ) => {
    return request({
        url: '/organization/data/queryPage',
        method: 'post',
        data:{
            current,
            size
        },
        params:{
            baseId,

        }
    });
};
// 新增编辑     table
export const submitOrganizeData = (data: any) => {
    return request({
        url: '/organization/data/updateOrInsert',
        method: 'post',
        data
    });
};
// 删除    table
export const deleteOrganizeData = (dataIds:any) => {
    return request({
        url: '/organization/data/removeData',
        method: 'post',
        params:{
            dataIds,
        }
    });
};
// 下载模板
export const OrganizeExportTemp = (params: { kbId: string }) => {
    return request({
        url: '/organization/template/importTemplate',
        method: 'get',
        params
    });
};
// 导入校验
export const OrganizeImportCheck = (data: { kbId: string; link: string }) => {
    return request({
        url: '/organization/template/checkTemplate',
        method: 'post',
        data
    });
};
// 实际导入
export const importOrganizeData = (data: { kbId: string; importId: string }) => {
    return request({
        url: '/organization/template/importTheTemplate',
        method: 'post',
        data
    });
};
