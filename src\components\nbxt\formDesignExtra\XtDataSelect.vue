<template>
  <div class="w-full">
    <el-button type="primary" text @click="openDialog">选择数据</el-button>
    <el-dialog
      class="xtDataSelect"
      v-model="dialogVisible"
      title="选择数据"
      width="70%"
      append-to-body
      destroy-on-close
    >
      <xt-el-table 
        :columns="crudOption.column" 
        :table-data="tableData" 
        :operate=" item.dynamicMulType === DynamicDataTypeEnum.MULTIPLE ? false : true"
      >
        <template #header="{ selection }" v-if="item.dynamicMulType === DynamicDataTypeEnum.MULTIPLE">
          <el-button @click="selectMul(selection)" type="primary" class="mb-2">确认选中</el-button>
        </template>
        <template #operation="{ row }" v-if="item.dynamicMulType === DynamicDataTypeEnum.SINGLE">
          <el-button text type="primary" @click="selectData(row)">选择数据</el-button>
        </template>
      </xt-el-table>
      
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { AvueOption, DataSelectField,AvueColumns, AvueForm } from '@/components/form-config/types/AvueTypes';
import { ElMessageBox } from 'element-plus';
import { filterDollarProperties } from '@/utils/field';
import xtElTable from '@/components/xt-el-crud/xt-el-table.vue';
import {DynamicDataTypeEnum } from '@/components/form-config/const/commonDic';
import { isEmptyVmodel } from '@/utils/field';


defineOptions({
  inheritAttrs: false,
});

const props = defineProps<
  DataSelectField & {
    disabled?: boolean;
    taskPackageId?: string;
    isDynamic?: boolean;
    source?: number;
    tableData?: any[],
    item: AvueColumns
  }
>();

const form = defineModel<AvueForm[]>({
  default: () => {
    return [];
  },
});
const emits = defineEmits(['change']);
// ========================下方表单显示=============================

const option = ref<AvueOption>({ column: [] });

const getOption = () => {
  const column = _.cloneDeep(props.showFields || []);
  column.forEach((item:AvueColumns) => {
    item.required = false;
    Reflect.set(item, 'minWidth', 250);
  });
  return {
    disabled: props.disabled,
    column,
  };
};


watch(
  () => props.tableData,
  (n) => {
    console.log(n,'n')
    page.value.total = n?.length
  }
);
onMounted(() => {
  nextTick(() => {
    option.value = getOption();
  });
  
});

// ========================数据选择=============================
const dialogVisible = ref(false);

const page = ref({
  currentPage: 1,
  pageSize:10,
  total: props.tableData?.length,
});

const crudOption = computed(() =>
  Object.assign(
    {
      header: false,
      editBtn: false,
      delBtn: false,
    },
    option.value
  )
);

const openDialog = () => {
  dialogVisible.value = true;
  // initData();
};


const selectData = (row: any) => {
  ElMessageBox.confirm('确定选择这条数据吗', '提示', {
    type: 'warning',
  }).then(() => {
    const obj = filterDollarProperties(row)
    const newObj = { ...form.value[0], ...obj}
    form.value[0] = _.cloneDeep(newObj);
    emits('change')
    dialogVisible.value = false;
  });
};


const selectMul = (selectData: AvueForm[]) => {
  const filteredData = selectData.map(row => filterDollarProperties(row));
  const existingData = isEmptyVmodel(form.value) ? [] : _.cloneDeep(form.value);

  const existingMap = new Map(existingData.map(item => [item.id, item]));

  // 自动处理替换或新增
  filteredData.forEach((row:AvueForm) => {
    existingMap.set(row.id, row);
  });

  form.value = Array.from(existingMap.values());
  emits('change');
  dialogVisible.value = false;
};

</script>

<style lang="scss">
.xtDataSelect {
  .el-dialog__body {
    @apply p-0;
  }
}
</style>
