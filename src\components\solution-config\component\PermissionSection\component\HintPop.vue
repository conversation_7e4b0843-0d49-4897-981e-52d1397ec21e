<template>
  <el-popover placement="top" popper-style="padding: 10px;">
            <template #reference>
              <span class="text-center"
                ><el-icon :size="15" color="#808080"><QuestionFilled /></el-icon
              ></span>
            </template>
            <template #default>{{ content }}</template>
          </el-popover>
</template>
<script setup lang="ts">
import {ref,reactive} from "vue"

defineProps<{content:string}>()
</script>
