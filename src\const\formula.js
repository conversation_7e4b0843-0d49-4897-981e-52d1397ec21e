import * as formulajs from '@formulajs/formulajs';
import store from '@/store/index.js';
import { dayFormat, formatFullDate, fullFormat } from '@/const/dateFormat.js';
import { executeNetWorkDays, executeWorkday } from '@/api/taskManagement/formula';
import dayjs from 'dayjs';

const formula = [
  {
    label: '常用函数',
    children: [
      {
        label: 'IF',
        func: formulajs['IF'],
      },
      {
        label: 'SUM',
        func: formulajs['SUM'],
      },
    ],
  },
  {
    label: '数学函数',
    children: [
      {
        label: 'AVERAGE',
        func: formulajs['AVERAGE'],
      },
      {
        label: 'COUNT',
        func: formulajs['COUNT'],
      },
      {
        label: 'COUNTIF',
        func: formulajs['COUNTIF'],
      },
      {
        label: 'MAX',
        func: formulajs['MAX'],
      },
      {
        label: 'MIN',
        func: formulajs['MIN'],
      },
      {
        label: 'MOD',
        func: formulajs['MOD'],
      },
      {
        label: 'RAND',
        func: formulajs['RAND'],
      },
      {
        label: 'ROUND',
        func: formulajs['ROUND'],
      },
      {
        label: 'SUMIF',
        func: formulajs['SUMIF'],
      },
      {
        label: 'SUMPRODUCT',
        func: formulajs['SUMPRODUCT'],
      },
      {
        label: 'SUMIFS',
        func: formulajs['SUMIFS'],
      },
    ],
  },
  {
    label: '文本函数',
    children: [
      {
        label: 'CONCATENATE',
        func: formulajs['CONCATENATE'],
      },
      {
        label: 'LEFT',
        func: formulajs['LEFT'],
      },
      {
        label: 'LEN',
        func: formulajs['LEN'],
      },
      {
        label: 'MID',
        func: formulajs['MID'],
      },
      {
        label: 'RIGHT',
        func: formulajs['RIGHT'],
      },
      {
        label: 'TEXT',
        func: v => _.toString(v),
      },
      {
        label: 'ISEMPTY',
        func: _.isEmpty,
      },
    ],
  },
  {
    label: '日期函数',
    children: [
      {
        label: 'DATE',
        func: (year, month, day) => formatFullDate(formulajs['DATE'](year, month, day)),
      },
      {
        label: 'DAY',
        func: formulajs['DAY'],
      },
      {
        label: 'DAYS',
        func: formulajs['DAYS'],
      },
      {
        label: 'HOUR',
        func: formulajs['HOUR'],
      },
      {
        label: 'MINUTE',
        func: formulajs['MINUTE'],
      },
      {
        label: 'MONTH',
        func: formulajs['MONTH'],
      },
      {
        label: 'TODAY',
        func: () => formatFullDate(formulajs['TODAY']()),
      },
      {
        label: 'YEAR',
        func: formulajs['YEAR'],
      },
      {
        label: 'DATEDIF',
        func: formulajs['DATEDIF'],
      },
      {
        label: 'NETWORKDAYS',
        func: (startDate, endDate) => {
          if (!startDate || !endDate) {
            return '';
          }
          const sd = dayjs(startDate);
          const ed = dayjs(endDate);
          if (!sd.isValid() || !ed.isValid()) {
            return '';
          }

          return executeNetWorkDays(sd.format(dayFormat), ed.format(dayFormat));
        },
      },
      {
        label: 'WORKDAY',
        func: (date, days) => {
          const d = dayjs(date);
          if (!d.isValid()) {
            return null;
          }
          const dayNum = _.toNumber(days);
          if (!_.isFinite(dayNum)) {
            return d.format(fullFormat);
          }

          return executeWorkday(d.format(fullFormat), dayNum);
        },
      },
    ],
  },
  {
    label: '逻辑函数',
    children: [
      {
        label: 'AND',
        func: formulajs['AND'],
      },
      {
        label: 'OR',
        func: formulajs['OR'],
      },
    ],
  },
  {
    label: '高级函数',
    children: [
      {
        label: 'GETUSERNAME',
        func: () => {
          return store.getters.userInfo['real_name'];
        },
      },
    ],
  },
];

export const getFormulaFuncList = () => {
  return formula.map(item => item.children).flatMap(item => item);
};

export const createEvalFunc = () => {
  getFormulaFuncList().forEach(item => {
    window[item.label] = item.func;
  });
};

// 公式计算
export function evalString(result) {
  try {
    // console.log(result);

    // const evalR = eval(result);
    const evalR = new Function(`return ${result}`)();
    const error = _.isError(evalR);
    return error ? '' : evalR;
  } catch (e) {}
  return '';
}

export default formula;
