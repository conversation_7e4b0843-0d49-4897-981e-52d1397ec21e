<script setup>
import { taskSourceInjectKey, tenantIdInjectKey } from '@/components/xt-el-form/constant.ts';
import { getDeptByOrgTree, getGroupByOrgTree, getUserByDept, getUserByGroup } from '@/api/common/index';
import { useStore } from 'vuex';
import { getUserListByTid } from '@/api/orgInfo';
import { toArray } from 'tree-lodash';
import { stringToArray } from './utils/util.ts'

defineOptions({
  name: 'xt-m-select-user'
});
const store = useStore();
const userMsg = computed(() => {
  return store.state.user.userInfo;
});

const taskSource = inject(taskSourceInjectKey, null);//任务组织来源
const tenantId = inject(tenantIdInjectKey, null);//任务组织来源
const disabledUser = inject('disabledUser');//禁用的用户
const activeKey = ref(0);
const searchValue = ref('');

const fieldList = ref({
  title: 'deptName',
  key: 'id',
  children: 'children'
});
const fieldData = ref({
  title: 'nickName',
  key: 'id'
});

const { placeholder, dataType, isRadio, disabled } = defineProps({
  placeholder: {
    type: String,
    default: '请选择空间'
  },
  dataType: {
    type: String,
    default: 'string'
  },
  dialogType: {
    type: String,
    default: () => 'drawer'
  },
  isRadio: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const filterUserData = (data, disUser) => {
  data.forEach(item => {
    if (item[fieldData.value.key] === disUser) {
      item.disabled = true;
    }
  });
  return data;
};

const allItemData = ref([]);
getUserListByTid(taskSource?.value || userMsg.value.tid).then(res => {
  allItemData.value = res.data.data;
  initData();
});
const show = ref(false);

const treeList = ref([]);

const vModel = defineModel();

const itemData = ref([]);


const checkedKeys = ref([]);

const checkedObjs = defineModel('checkRow', { default: [] });

const allIn = ref(false);

const filterItemData = computed({
  get() {
    return itemData.value.filter(item => item[fieldData.value.title]?.indexOf(searchValue.value) >= 0);
  },
  set(v) {
    itemData.value = v;
  }
});

watchEffect(() => {
  if (itemData.value.length <= 0) return allIn.value = false;
  allIn.value = itemData.value.every(item => checkedKeys.value.includes(item[fieldData.value.key]));
});

const allInDie = () => {
  if (allIn.value) {
    itemData.value.forEach(item => {
      const index = checkedKeys.value.findIndex(s => s === item[fieldData.value.key]);
      if (index === -1) {
        checkedKeys.value.push(item[fieldData.value.key]);
        checkedObjs.value.push(item);
      }
    });
  } else {
    itemData.value.forEach(item => {
      const index = checkedKeys.value.findIndex(s => s === item[fieldData.value.key]);
      if (index !== -1) {
        checkedKeys.value.splice(index, 1);
        checkedObjs.value.splice(index, 1);
      }
    });
  }
};
const dataTypeArray = computed(() => {
  return dataType === 'array';
});

const checkHandle = (checkedKey, { checkedNodes, checked, node, event }) => {
  const currentKey = node.key;
  if (isRadio) {
    if (checked) {
      checkedKeys.value = [currentKey];
    } else {
      checkedKeys.value = [];
    }
    return checkedObjs.value = checkedKeys.value.map(item => allItemData.value.find(it => it[fieldData.value.key] === item));
  }
  if (checked) {
    checkedKeys.value.push(currentKey);
  } else {
    checkedKeys.value.splice(checkedKeys.value.findIndex(s => s === currentKey), 1);
  }
  checkedObjs.value = checkedKeys.value.map(item => allItemData.value.find(it => it[fieldData.value.key] === item));
};


const initData = () => {
  if (!_.isEmpty(vModel.value)) {
    checkedKeys.value = stringToArray(vModel.value);
    checkedObjs.value = checkedKeys.value.map(item => allItemData.value.find(it => it[fieldData.value.key] === item));
  }else{
    checkedKeys.value=[]
    checkedObjs.value=[]
  }
};

watch(() => vModel.value, () => {
  console.log(vModel.value, 'vModel');
  initData();
}, {
  deep: true
});

const selectHandle = (selectedKeys, selectedNodes) => {
  if (!selectedKeys[0]) return;
  if (activeKey.value === 0) {//部门
    getUserByDept({ deptId: selectedKeys[0] }).then(res => {
      itemData.value = filterUserData(res.data.data, disabledUser);
    });
  } else {
    getUserByGroup({ workgroupId: selectedKeys[0] }).then(res => {
      itemData.value = filterUserData(res.data.data, disabledUser);
    });
  }
};

const submit = () => {
  vModel.value = dataTypeArray.value ? checkedKeys.value : checkedKeys.value.join(',');
  show.value = false;
};
const cancel = () => {
  initData();
  show.value = false;
};

const changeTabs = (tabs) => {
  getListTree(tabs);
};

const getListTree = (tabs = 0) => {
  if (tabs === 0) {//部门
    getDeptByOrgTree({ tid: taskSource?.value || userMsg.value.tid }).then(res => {
      treeList.value = res.data.data;
    });
  } else {//工作组
    getGroupByOrgTree({ tid: taskSource?.value || userMsg.value.tid }).then(res => {
      treeList.value = res.data.data;
      toArray(treeList.value).forEach(item => {
        item[fieldList.value.title] = item.groupName;
      });
    });
  }
};
getListTree();

const open = () => {
  if (!disabled) {
    show.value = true;
  }
};

</script>

<template>
  <div>
    <div @click="open" :class="{'bg_disabled':disabled}" class="w-full min-h-40px border_dashed py-10px px-10px">
      <a-space wrap v-if="checkedObjs.length>0" class="h-full">
        <el-tag type="primary" v-for="item in checkedObjs">{{ item?.[fieldData.title] }}</el-tag>
      </a-space>
      <div v-else class="color-gray">{{ placeholder }}</div>
    </div>
    <component
      :is="`el-${dialogType}`"
      direction="btt"
      size="100%"
      append-to-body
      class="m_drawer"
      :with-header="false"
      @close="show=false"
      v-model="show"
    >
      <a-flex vertical gap="10" class="h-full">
        <div class="h-50% overflow-scroll border_card py-10px px-10px">
          <el-tag class="mx-5px my-5px" type="primary" v-for="item in checkedObjs">{{ item?.[fieldData.title] }}
          </el-tag>
        </div>
        <div>
          <a-tabs v-model:activeKey="activeKey" @change="changeTabs">
            <a-tab-pane :key="0" tab="部门"></a-tab-pane>
            <a-tab-pane :key="1" tab="工作组"></a-tab-pane>
          </a-tabs>
        </div>
        <a-flex gap="10" class="h-50%">
          <div class="s_card border_card overflow-scroll">
            <a-tree
              :field-names="fieldList"
              defaultExpandAll
              @select="selectHandle"
              :tree-data="treeList"
            >
            </a-tree>
          </div>
          <div class="s_card border_card">
            <a-input-search v-model:value="searchValue"
                            class="w-full p-5px" placeholder="搜索" />
            <a-tree
              class="h-250px overflow-scroll"
              :field-names="fieldData"
              :checkedKeys="checkedKeys"
              defaultExpandAll
              checkable
              @check="checkHandle"
              :tree-data="filterItemData"
            >
            </a-tree>
          </div>
        </a-flex>
        <a-flex justify="space-between" align="center" gap="10" class="h-50px">
          <div v-if="!isRadio">
            <a-checkbox @change="allInDie" v-model:checked="allIn">全选</a-checkbox>
          </div>
          <div v-else></div>
          <a-space>
            <a-button type="primary" @click="submit">确认</a-button>
            <a-button @click="cancel">取消</a-button>
          </a-space>

        </a-flex>
      </a-flex>
    </component>
  </div>
</template>

<style scoped lang="scss">
.s_card {
  border: 1px solid red;
  width: 50%;
  overflow: scroll;
}

.border_card {
  border: 1px solid #eee;
}

.border_dashed {
  border: 1px dashed #eee;
}

:deep(.ant-tabs-nav) {
  margin: 0;
}

.bg_disabled {
  background: #eee;
}
</style>