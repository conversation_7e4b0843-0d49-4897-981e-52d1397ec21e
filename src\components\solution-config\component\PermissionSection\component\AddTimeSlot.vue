<template>
  <div class="flex flex-col">
    <el-button @click="$emit('add')" type="primary" size="small" class="mt-20px w-120px"
      >+ 添加时间段</el-button
    >
    <div
      v-for="(item, index) in slotList"
      :key="item.id"
      class="border-1 border-#ebebeb border-solid p-10px mt-10px"
    >
      <el-form-item label="时间因素">
        <el-space alignment="flex-start" class="h-32px">
          <el-input
            v-model.number="item.start"
            class="w-100px"
            placeholder="起始时间"
            clearable
            type="number"
            :min="getMinStart(index)"
            :disabled="index > 0"
            @input="validateStart(index)"
            @blur="enforceInteger('start', index)"
          />

          <el-divider />

          <!-- 结束时间输入框 -->
          <el-input
            v-model.number="item.end"
            class="w-100px"
            placeholder="结束时间"
            clearable
            type="number"
            :min="getMinEnd(index)"
            @blur="enforceInteger('end', index)"
          />
          <el-select-v2
            v-model="item.timeType"
            class="w-100px"
            :options="unitList"
            placeholder="请选择"
          />
          <div>
            <el-checkbox v-model="item.ban">禁用</el-checkbox>
            <HintPop content="如果勾选,所填写的时间因素限制使用" />
            <el-icon
              color="#ff7070"
              class="ml-30px cursor-pointer"
              @click="$emit('delete', item.id)"
              ><DeleteFilled
            /></el-icon>
          </div>
        </el-space>
      </el-form-item>
    </div>
  </div>
</template>
<script setup lang="ts">
import { DeleteFilled } from '@element-plus/icons-vue';
import { unitList } from '../../../const';
import { slotType } from '../../../types';
import HintPop from './HintPop.vue';

const props = defineProps<{ slotList: slotType[] }>();

defineEmits(['add', 'delete']);

// 监听slotList变化，确保连续性
watch(
  () => [...props.slotList],
  newVal => {
    // 确保每个时间段的start等于前一个时间段的end
    for (let i = 1; i < newVal.length; i++) {
      if (newVal[i].start !== newVal[i - 1].end) {
        newVal[i].start = newVal[i - 1].end + 1;
      }
    }
  },
  { deep: true }
);

// 获取起始时间的最小值
const getMinStart = (index: number) => {
  if (index === 0) return 0;
  return props.slotList[index - 1]?.end || 0;
};

// 获取结束时间的最小值
const getMinEnd = (index: number) => {
  return props.slotList[index]?.start || 0;
};

// 验证起始时间输入
const validateStart = (index: number) => {
  const item = props.slotList[index];

  // 确保起始时间不小于前一段的结束时间
  if (index > 0) {
    const prevEnd = props.slotList[index - 1]?.end || 0;
    if (item.start < prevEnd) {
      item.start = prevEnd + 1;
    }
  }

  // 如果起始时间大于结束时间，自动调整结束时间
  if (item.start > item.end) {
    item.end = item.start + 1;
  }
  // 更新下一段的起始时间
  if (index < props.slotList.length - 1) {
    props.slotList[index + 1].start = item.end + 1;
  }
};

// 验证结束时间输入
const validateEnd = (index: number) => {
  const item = props.slotList[index];

  // 确保结束时间不小于起始时间
  if (item.end <= item.start) {
    item.end = item.start + 1;
  }

  // 更新下一段的起始时间
  if (index < props.slotList.length - 1) {
    props.slotList[index + 1].start = item.end + 1;
  }
};

// 强制转换为整数
const enforceInteger = (field: 'start' | 'end', index: number) => {
  const item = props.slotList[index];
  let value = item[field];

  // 处理空值情况
  if (value === null || isNaN(value as number)) {
    if (field === 'start') {
      // 起始时间：第一段设为0，后续段设为前一段的结束时间
      value = index > 0 ? props.slotList[index - 1].end + 1 : 0;
    } else {
      // 结束时间设为起始时间
      value = item.start + 1;
    }
  }

  // 四舍五入取整
  const intValue = Math.round(Number(value));
  item[field] = intValue;

  // 重新验证输入
  if (field === 'start') {
    validateStart(index);
  } else {
    validateEnd(index);
  }
  // 当字段是结束时间时，更新后续时间段
  if (field === 'end') {
    // 更新下一段的起始时间
    if (index < props.slotList.length - 1) {
      props.slotList[index + 1].start = value + 1;
      // 验证下一段
      validateStart(index);
    }

    // 递归更新所有后续时间段
    for (let i = index + 1; i < props.slotList.length; i++) {
      // 确保起始时间等于前一段的结束时间
      if (i > 0) {
        props.slotList[i].start = props.slotList[i - 1].end + 1;
        // 验证本段
        validateStart(i);
      }

      // 确保结束时间不小于起始时间
      validateEnd(i);
    }
  }

  // 当字段是起始时间时（只可能发生在第一段）
  if (field === 'start' && index === 0) {
    // 更新本段结束时间
    validateStart(0);

    // 递归更新所有后续时间段
    for (let i = 1; i < props.slotList.length; i++) {
      props.slotList[i].start = props.slotList[i - 1].end + 1;
      validateStart(i);
      validateEnd(i);
    }
  }
};

</script>
<style lang="scss" scoped></style>
