<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import { DataSelectionAllField, DataSelectionQuery } from '../type';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConditionItem from './dataSelectionFieldItem.vue';
import SatisfyTitle from '@/views/mainview/sceneTasks/detail/components/common/SatisfyTitle.vue';
import { ElMessage } from 'element-plus';
import ShowFields from '../../components/showFields.vue';
import { getAllPublishedMicroApp } from '@/api/application/index.ts'
import { useStore } from 'vuex'
import useFieldList from '@/views/banban/application/flowApp/hooks/useFieldList.ts';
import { ruleExpressByFileType } from '@/components/form-config/types/FieldTypes.ts';

interface App {
  microAppId:string;
  appName:string;
}

const props = defineProps<{
  data: DataSelectionAllField;
}>();

const { getFiledList, fieldList } = useFieldList([...ruleExpressByFileType]);
const { getFiledList:getOutFiledList, fieldList:OutFieldList } = useFieldList([...ruleExpressByFileType]);


const store = useStore();
const userMsg = store.state.user.userInfo;

const visible = ref(false);
const query = ref<DataSelectionQuery>({ filter: [],showFieldIds:[], showField:[], fieldRule:1, path:'' });
const appList = ref<App[]>([]);

const handleDelete = (index: number) => {
  _.pullAt(query.value.filter || [], index);
};

const handleAdd = () => {
  if (_.isArray(query.value.filter)) {
    const haveEmpty = query.value.filter.some(
      i => _.isEmpty(i.fieldId) || _.isEmpty(i.conditions)
    );
    if (haveEmpty) {
      ElMessage.warning('有空条目请勿添加新的条目');
      return;
    }
  }
  (query.value.filter ??= []).push({});
};


//提交
const handleSubmit = () => {
  console.log(query.value);
  // const arr = query.value.list.map((i:any) => {
  //   delete i.pushFieldList
  //   delete i.linkFieldList
  //   return i
  // })
  // props.data.query = _.cloneDeep({list: arr});
  // props.data.linkFields = _.cloneDeep(Array.from(new Set(linkFields.value as string[])));

  props.data.query = _.cloneDeep(query.value);
  visible.value = false;
};

async function getAppList(){
  const { data } = await getAllPublishedMicroApp(userMsg.tenant_id);
  appList.value = data.data || [];
}

watchEffect(async () => {
  if(!query.value.path) return;
  getFiledList({
    id: query.value.path,
    unFlatDynamic: true,
    unFlatDataCollect: true,
    unFlatDataSelect: true,
    flatFilterType: ruleExpressByFileType,
  })
  getOutFiledList({
    id: query.value.path,
    unFlatDynamic: false,
    unFlatDataCollect: false,
    unFlatDataSelect: false,
    flatFilterType: ruleExpressByFileType,
  })
  // showFieldList.value = data.data?.appFormVOList?.map(i => {
  //   const { content, ...other } = i;
  //   return {
  //     label:i.title,
  //     ...other,
  //     ...content,
  //     disabled:false
  //   }
  // }
  // ) || [];
})

onMounted(() => {
  getAppList()
})

watch(
  () => visible.value,
  (val) => {
    if(val) {
      query.value = _.cloneDeep(props.data.query) || { filter: [],showFieldIds:[], showField:[], fieldRule:1, path:'' }
    }
  },
  { immediate: true }
)

</script>

<template>
  <config-title :data="data"></config-title>
  <config-permission :data="data" :hasRequired="false" />
  <el-form-item label="默认值">
    <el-button type="text" @click="visible = true"> + 添加筛选条件</el-button>
    <el-select
      :model-value="'1'"
      placeholder=""
    >
      <el-option label="自定义" value="1"></el-option>
    </el-select>
  </el-form-item>

  <el-dialog
    v-model="visible"
    title="编辑筛选条件"
    append-to-body
    destroy-on-close
    width="600px"
    :close-on-click-modal="false"
    top="5vh"
  >
    <n-card>
      <h3>筛选条件</h3>
      <el-form-item label="选择微应用">
        <el-select v-model="query.path" placeholder="请选择" class="w-[200px] mb-2">
          <el-option
            v-for="item in appList"
            :key="item.microAppId"
            :label="item.appName"
            :value="item.microAppId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <n-space vertical :size="20" style="max-height: 401px; overflow-y: scroll" class="mb-4">
        <satisfy-title v-model="query.fieldRule" btn-width="270px" @add="handleAdd"  :disabledRule="false"/>
        <template v-if="query.filter?.length">
          <ConditionItem
            v-for="(item, index) in query.filter"
            :key="index"
            :data="item"
            :fieldList="fieldList"
            propLabel="copyLabel"
            :curRuleType="'0'"
            @delete="handleDelete(index)"
          ></ConditionItem>
        </template>
        <el-empty v-else description="请点击添加条件"></el-empty>
      </n-space>
    </n-card>
    <n-card class="mt-2">
      <h3>展示字段</h3>
      <showFields
        :field-list="OutFieldList.map(i => ({...i,disabled:false})) as any[]"
        v-model:showField="query.showField"
        v-model:showFieldIds="query.showFieldIds"
        :data="data"
      >
      </showFields>
    </n-card>

    <template #footer>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </template>
  </el-dialog>

</template>

<style scoped lang="scss">

</style>