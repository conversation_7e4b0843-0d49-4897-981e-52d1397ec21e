<template>
    <el-dialog  
        v-model="visible"
        title="预览"
        width="700"
    >
        <div class="mb-10">
            选择数据表  <el-input v-model="data.dataTableName" disabled class="w-200px"></el-input>
        </div>
        <div v-for="(_,index) in data.fieldConditionStr" :key="index" class="mb-5">
            <el-input v-model="data.fieldConditionStr[index]" disabled></el-input>
        </div>
        <div class="mb-20 mt-10">
            取满足以上筛选条件的 <el-input v-model="data.linkageFieldName" class="w-200px" disabled></el-input> 字段的最后更新的一条进行联动
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { dataLinksRulePreviewType } from '@/api/interface/formula';

withDefaults(
    defineProps<{
        data: dataLinksRulePreviewType;
    }>(),
    {
        data: () => {
            return {dataTableName: '', fieldConditionStr:[], linkageFieldName:''}
        },
    }
);

const visible = defineModel('visible', { default: false })


</script>