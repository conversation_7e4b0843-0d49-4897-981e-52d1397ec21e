<script setup lang="ts">
import { getDetailFromWechat, getByMicroTaskActorIdFromWechat, getKnowledgeDetailFromWechat, getDeptInviteVerifyRecordInfoFromWechat } from '@/api/wechat/index.ts'
import { getHandleDetai } from '@/api/banban';
import { HandleTypeEnum } from '@/components/form-config/const/commonDic.ts';
import { ElMessage } from 'element-plus';
import { useKnowLedgeStore } from '@/views/banban/know/taskKnowLedge/store';
const router = useRouter();
const route = useRoute();

enum UrlTypeEnum {
  APPLICATION = 3, // 微应用审核
  HANDLED = 1, // 应办做
  CHECK = 2, // 信息审核
  KNOW = 4 // 周知详情
}

const knowLedgeStore = useKnowLedgeStore();

const loading = ref(true);

const id = route.query.relId as string;

const urlConfig = {
  [UrlTypeEnum.HANDLED]: '/m/shouldDo/taskDetail',
  [UrlTypeEnum.APPLICATION]: '/m/shouldDo/microDetail',
  [UrlTypeEnum.CHECK]: '/m/deptInviteCheck',
  [UrlTypeEnum.KNOW]: '/m/knowable/notice/detail',
} as const;

// 去微应用
async function toApp(relId:string,url:string){
  const appInfo = await getByMicroTaskActorIdFromWechat(relId);
  const { instanceId, appName, microAppId, flwTaskId, startNodeFlag, tenantId } = appInfo.data.data;
  router.replace({
    path:url,
    query: {
      instanceId,
      appId: microAppId,
      flwTaskId,
      name: appName,
      type: 3,
      startNodeFlag,
      isExamine: 1,
      tenantId,
    },
  })
}

// 去应办
async function toHandle(relId:string,url:string){
  const handleInfo = await getHandleDetai({id:relId});
  const { viewStatus, agree, status } = handleInfo.data.data || {};
  if(status === 0){
    router.replace({
      path: url,
      query: {
        handleId: relId,
        disabled: agree === 1 || viewStatus === 2 ? 1 : 0,
        submitType: null,
        type: HandleTypeEnum.HANDLED,
        isControl: 1, // 是否要控制跳转
      }
    });
  }else {
    router.replace({
      path: '/m/shouldDo',
    })
    ElMessage.warning('已处理')
  }
}

// 去信息审核
async function toCheck(relId:string,url:string){
  const checkInfo = await getDeptInviteVerifyRecordInfoFromWechat(relId);
  const { idCard, tenantName, nickName, attachFiles, id, agreeStatus } = checkInfo.data.data;
  if(agreeStatus === 0){
    router.replace({
      path: url,
      query: {
        idCard,
        tenantName,
        nickName,
        attachFiles,
        id,
        agreeStatus
      }
    });
  }else{
    router.replace({
      path: '/m/shouldDo',
    })
    ElMessage.warning('已处理')
  }
}

// 去周知
async function toKnow(relId:string,url:string){
  const knowInfo = await getKnowledgeDetailFromWechat(relId);
  knowLedgeStore.setDetail(knowInfo.data.data || {});
  router.replace({
    path:url
  })
}

async function initJump(){
  try {
    const res = await getDetailFromWechat(id);
    const { messageType, relId } = res.data.data || {}
    const url = urlConfig[messageType as UrlTypeEnum];
    if(messageType === UrlTypeEnum.APPLICATION){
      await toApp(relId,url)
    }else if(messageType === UrlTypeEnum.HANDLED){
      await toHandle(relId,url)
    }else if (messageType === UrlTypeEnum.CHECK){
      await toCheck(relId,url)
    }else if(messageType === UrlTypeEnum.KNOW){
      await toKnow(relId,url)
    }
  }catch(error){
    router.replace({
      path: '/m/shouldDo',
    })
  }
}

onMounted(async () => {
  initJump()
})


</script>

<template>
  <div
    v-loading="loading"
    element-loading-text="加载中"
    class="h-screen"
  ></div>

</template>

<style scoped lang="scss">

</style>