<template>
  <n-card class="mb-20px">
    <div class="text-right">
      <el-button type="danger" text @click="$emit('delete')">删除</el-button>
    </div>
    
    <div class="w-520px ml-auto mr-auto">
      <el-form-item :label="`选择${title}字段`" label-position="left" required :labelWidth>
        <el-select v-model="item.pathField" placeholder="请选择" class="w-400px" clearable>
          <el-option
            v-for="item in pathFieldList"
            :key="item.prop"
            :label="item.copyLabel"
            :value="(item.prop as string)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="`选择${title}方式`" label-position="left" required :labelWidth>
        <el-select v-model="item.method" placeholder="请选择" class="w-400px" clearable>
          <el-option
            v-for="item in summaryTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="绑定字段" label-position="left" required :labelWidth>
        <el-select v-model="item.field" placeholder="请选择" class="w-400px" clearable>
          <el-option
            v-for="item in currentFieldList"
            :key="item.prop"
            :label="item.copyLabel"
            :value="(item.prop as string)"
            :disabled="isPropExist((item.prop as string))"
          />
        </el-select>
      </el-form-item>
    </div>
  </n-card>
 
  
</template>

<script setup lang="ts">
import { groupItem } from '@/api/interface/configDataLink';
import { AvueColumns} from '@/components/form-config/types/AvueTypes.ts';
import { numberValueType } from '@/components/form-config/types/FieldTypes.ts';

const props = withDefaults(defineProps<{
    title?: string;
    item: groupItem;
    pathFieldList: AvueColumns[],
    currentFieldList: AvueColumns[],
    typeList: {label: string, value: string}[],
    group: groupItem[]

}>(), {
    title: '分组',
  //   item: () =>{
  //     return { field: '', pathField: '', method: '', idx: generateId() };
  // },
});

const labelWidth = ref(120)
defineEmits(['delete']);

const isPropExist = (prop: string) => {
  return props.group.some(i => i.field === prop);
}

//分组汇总-添加汇总字段时候如果选数字、公式编辑、计算表、目标值以外的字段只能计数；如果选数字、公式编辑、计算表、目标值可以求和、最大值、最小值、平均、计数
const summaryTypes = computed(()=> {
  if(props.title === '汇总'){
    const fixType = props.pathFieldList.find(v => v.prop === props.item.pathField)?.fixType
    if(!_.includes(numberValueType, fixType)){
      return props.typeList.filter(v => v.value === '5')
    }else {
      return props.typeList
    }
  }
  return props.typeList
})

watch(
  () => props.item.pathField,
  () => {
    props.item.field = '';
  }
)

</script>