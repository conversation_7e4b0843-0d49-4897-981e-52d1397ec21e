<template>
  <n-card :content-style="{ padding: '10px', position: 'relative',width:'100%',display:'flex',justifyContent: 'space-between', paddingRight: '16px' }">
    <a-row :gutter="[8,8]" class="flex-1">
      <!-- 推送任务 -->
      <el-col :span="8" v-if="pushTaskLabel">
        <el-input :model-value="pushTaskLabel" disabled></el-input>
      </el-col>
      <a-col :span="pushTaskLabel?8:16">
        <el-select-v2
          class="w-full"
          v-model="fieldId"
          @change="fieldChange"
          :options="filterFieldList"
          :props="{
          value:fieldKey,
          label:fieldLabel,
        }"
          filterable
          placeholder="请选择字段"
        />
      </a-col>
      <a-col
        v-if="fieldId"
        :span="8"
      >
        <el-select-v2
          v-model="symbol"
          :options="symbolList"
          class="w-full"
          placeholder="请选择规则"
        />
      </a-col>
      <a-col
        v-if="symbol"
        :span="20"
      >
        <component
          :is="getFieldComponent"
          v-if="refreshEl"
          v-model="result"
          :field="field"
          class="!w-full"
        ></component>
      </a-col>
    </a-row>
      <slot name="customBtn" v-if="delBtn">
        <el-button type="danger" link class=" self-start mr-[-5px] ml-10px" @click="$emit('delete',fieldId)"
        >删除
        </el-button>
      </slot>
  </n-card>
</template>
<script setup lang="ts">
import { computed, watch } from 'vue';
import { getFieldSymbolConfig, getFieldSymbolList, transChineseSymbol } from './utils/fieldUtils.ts';

import { Field } from '@/types/OriginTypes.ts';
import { MethodSymbolEnum } from './option/field.ts';
import { XtFieldType } from '@/components/form-config/types/FieldTypes.ts';
import { SymbolListTypes } from '@/components/conditions/types/types.ts';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts'

/**
 *
 */
const props = withDefaults(defineProps<{
  //使用的字段key
  fieldKey?: string;
  //使用的字段Label
  fieldLabel?: string;
  //是否删除按钮
  delBtn?:boolean
  //字段列表
  fieldList: Field[] | any[];
  //任务名称
  pushTaskLabel?: string|undefined;
  //是否默认加入判空规则
  needEmpty?: boolean;
  //当前任务的当前组件不可选,推数或者联动也有可能选到当前任务，除了当前currentField.id还得加一个current的判断
  current?: boolean;
  currentField?: AvueColumns|undefined;
  /**
   * 过滤规则的方法，回调函数包含三个参数必须返回对应的symbol
   * @param result 默认的规则列表
   * @param type 字段类型
   * @param isDynamic 是否子表单类字段
   */
  filterSymbolMethod?: (result: MethodSymbolEnum[], type: XtFieldType, isDynamic: boolean) => MethodSymbolEnum[];
  //自定义的规则配置,与默认配置项不同需要配置，参考/src/components/conditions/option/field.ts------export const SymbolList
  filterSymbolField?: SymbolListTypes[];
}>(), {
  fieldKey: () => 'prop',
  fieldLabel: () => 'copyLabel',
  delBtn: () => true,
  needEmpty: () => true,
  filterSymbolMethod: (result: MethodSymbolEnum[], _type: XtFieldType, _isDynamic: boolean) => result,
  filterSymbolField:()=>[],
});

const emit=defineEmits(['delete','change']);

const fieldId = defineModel<any>('fieldId');

const symbol = defineModel<MethodSymbolEnum>('symbol');

const result = defineModel<any>('result');

const refreshEl=ref(true);

//当前选择的字段
const field = computed(() => {
  const findItem=filterFieldList.value.find(
    item => item[props.fieldKey] === fieldId.value
  )
  delete findItem?.['value'];
  return findItem;
});


//当前选择字段是否我子表单字段
const isDynamic = computed(() => {
  return _.includes(field.value?.prop, 'dynamic');
});

const filterFieldList=computed(()=>{
  return _.cloneDeep(props.fieldList).map(item=>{
    return {
      ...item,
      disabled:props.current && item[props.fieldKey].includes(props.currentField?.[props.fieldKey] || '')
    }
  })
})

const fieldType = computed(() => {
  return field.value?.fixType ||  field.value?.type || ''
});

//当前字段规则集字典[]
const symbolList = computed(() => {
  const symbolList = getFieldSymbolList(fieldType.value, props.needEmpty,isDynamic.value);
  const customSymbolList=props.filterSymbolField.find(item => _.includes(item.types, fieldType.value))?.symbols;
  const filterSymbolList = props.filterSymbolMethod(customSymbolList||symbolList, fieldType.value, isDynamic.value);
  return transChineseSymbol(filterSymbolList);
});


//获取当前字段所选规则对应组件
const getFieldComponent = computed(() => {
  const symbolConfig = getFieldSymbolConfig(fieldType.value,isDynamic.value);
  const customSymbolConfig=props.filterSymbolField.find(item => _.includes(item.types, fieldType.value))?.components;
  const item = (customSymbolConfig||symbolConfig?.components||[])?.find(item => _.includes(item.symbols, symbol.value));
  return item?.component;
});

const fieldChange=(e:any) => {
  const fieldItem=filterFieldList.value.find(item=>item[props.fieldKey]===e)
  emit('change',fieldItem)
}

onMounted(() => {
  watch(
    fieldId,
    () => {
      symbol.value = undefined;
    }
  );

  watch(
    symbol,
    () => {
      result.value = undefined;
      refreshEl.value=false
      nextTick(()=>{
        refreshEl.value=true
      })
    }
  );
});
</script>
<style lang="scss" scoped></style>
