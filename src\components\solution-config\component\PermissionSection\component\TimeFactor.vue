<template>
  <el-form-item label="时间基点">
    <div class="flex items-center gap-10px w-full">
      <el-select-v2 v-model="base" :options="timeBaseList" />
      <el-input type="number" v-model="factor.number"></el-input>
      <el-select-v2 v-model="factor.timeType" :options="unitList" placeholder="请选择" />
      <el-icon color="#ff7070" class="ml-30px cursor-pointer" @click="$emit('delete', factor.id)"
        ><DeleteFilled
      /></el-icon>
    </div>

    <AddTimeSlot
      @add="handleAddTimeSlot"
      @delete="handleDelTimeSlot"
      :slot-list="props.factor.timePeriodList"
    />
  </el-form-item>
</template>
<script setup lang="ts">
import { timeBaseList, unitList } from '../../../const';
import { timeFactor } from '../../../types';
import { randomLenNum } from '@/utils/util';
import AddTimeSlot from './AddTimeSlot.vue';
import { DeleteFilled } from '@element-plus/icons-vue';

const props = defineProps<{
  factor: timeFactor;
}>();

defineEmits(['delete'])

const base = ref(1);

const handleAddTimeSlot = () => {
  props.factor.timePeriodList.push({
    id: randomLenNum(true),
    start: 0,
    end: 0,
    timeType: '',
    ban: false,
  });
};

const handleDelTimeSlot = (id: number) => {
  props.factor.timePeriodList = props.factor.timePeriodList.filter(item => item.id !== id);
};
</script>
<style lang="scss" scoped></style>
