<template>
  <n-space vertical :size="20">
    <el-select-v2
    v-model="taskId" :options="taskList!" :props="prop" filterable clearable style="width: 500px;"
      @change="handleClick" />
  </n-space>
</template>
<script setup lang="ts">
import { getFieldListByTaskId } from '@/api/taskManagement/task';
import { pushTaskListType } from '@/api/interface/task';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts';
import { getTempLateInfo,getOrganizeTemplate } from '@/api/banban/knowLedge'
import { DynamicTypeEnum } from '@/components/form-config/const/commonDic';
import { editKnowType } from '@/api/interface/knowLedgeType'
import { fullDatetimeFormat } from '@/components/form-config/const/commonDic';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { getAppFormById } from '@/api/application';


const props = withDefaults(
  defineProps<{
    title?: string;
    disabled?: boolean;
    width?: string;
    taskList: editKnowType[] | pushTaskListType[] | undefined;
    isFilter?: boolean;
    prop?: { label: string; value: string; };
    defaultType?: DynamicTypeEnum
  }>(),
  {
    title: '请选择',
    disabled: false,
    width: '!w-200px',
    isFilter: true,
    prop: () => ({ label: 'label', value: 'value' })
  }
);

const emits = defineEmits(['click']);

const taskId = defineModel('taskId', { default: '' });
const fieldList = defineModel<AvueColumns[]>('fieldList', { default: [] });
const type = defineModel<number>('type');


const handleClick = () => {
  emits('click');
}

const format = ref<string | null>(null)

const handleChangeScreenTask = async (val: string) => {
  const arr:any = props.taskList?.filter(v => val === v.value);
  if (arr?.length) {
    //求任务的组件列表，返回给父组件
    type.value = arr[0].type; //0 任务 1 数据表 2计算表 3 微应用
    let data:any = {};
    switch (type.value) {
      case 3:
        const resObj2 = await getAppFormById(val);
        data = resObj2?.data;
        break; // 获取微应用表单
      default:
        const resObj1 = await getFieldListByTaskId({ type: arr[0].type, path: val });
        data = resObj1?.data;
        break;
    }
    const transformField = (field: any) => {
      if(!field.content && (field.fieldType === FixTypeEnum.DATE || field.type === FixTypeEnum.DATE)){
        format.value = fullDatetimeFormat
      }
      const prop = field?.content?.prop || `${field.fieldType||field.type}:#${field.value}`
      return {
        ...field.content,
        format: field.content?.format || format,
        // fieldType: field.fieldType||field.type,
        // contentType: field.contentType||field.type,
        disabled: false,
        copyLabel: field.content?.copyLabel || field.label,
        id: field.value||field.id,
        // label: field.content?.label || field.label||field.title,
        prop,
        fixType: field.content?.fixType || field.fieldType,
        type: field.content?.type || field.contentType,//type只有日期 选择等几个特殊组件有，以前的type废弃了
        children: field.children?.map((child: any) => transformField(child)),
      }
    }

    const resData = data.data.map((item: any) => transformField(item));
    fieldList.value = [...resData];
  }
};

//系统知识库/组织知识库
const handelChangeKnowledge = async (val: string) => {
  const { data } = props.defaultType === DynamicTypeEnum.KNOWLEDGE ?
  await getTempLateInfo({ kbId: val }) : await getOrganizeTemplate(val)
  fieldList.value = data.data.map((item: any) => {
    return {
      ...item.content,
    }
  })

};

watch(
  () => taskId.value,
  val => {
    // console.log('asdasdasdsa',val);
    fieldList.value = [];
    if (val) {
      [DynamicTypeEnum.KNOWLEDGE,DynamicTypeEnum.ORGKNOWLEDGE,].includes(props.defaultType as DynamicTypeEnum) ?
        handelChangeKnowledge(val) : handleChangeScreenTask(val);
    }
  },
  {
    immediate: true,
  }
);

onMounted(() => {
  // getList()
});
</script>
<style lang="scss" scoped></style>
