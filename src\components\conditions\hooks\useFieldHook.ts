import {
  FilterColType
} from '@/views/banban/application/flowApp/approvalProcess/scWorkflow/component/flowCondition/types/types.ts';
import { defaultIncludeType } from '@/components/form-config/types/FieldTypes.ts';
import { GetFieldListParams, HookOptions } from '@/components/conditions/types/types.ts';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts';
import { handleJsonByArray } from '../utils/utils.ts';
import { colsToFlatDynamic } from '../utils/formColUtils.ts';


export const useFieldHook=  (filterCol: FilterColType=defaultIncludeType,options:HookOptions) => {
  const { self = true, outTask = false, initColumn = ref([]) } = options;
  const fieldList = shallowRef<AvueColumns[]>([]);

  const getFiledList=(params: GetFieldListParams)=>{
    const { formColumn, changeToAble = false } = params;
    //初始化表单
    formColumn.push(...initColumn.value||[]);
    let result: AvueColumns[];
    // 处理formColumn
    result = handleJsonByArray(formColumn)
    // 处理子表单（）
    result = colsToFlatDynamic(result, params,params.isOriginData);

    if (changeToAble) {
      result.forEach(item => (item.disabled = false));
    }
    // 过滤字段
    if (filterCol instanceof Function) {
      result = result.filter(filterCol);
    } else {
      result = result.filter(item => filterCol.includes(item.fixType));
    }
    fieldList.value=result;
    return result
  }

  return{
    getFiledList
  }

};