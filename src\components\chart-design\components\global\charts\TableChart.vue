<template>
  <div
    ref="tableContainer"
    class="flex items-center justify-center"
  >
    <a-table
      v-if="props.metrics.length"
      :columns
      :data-source="chartData ?? []"
      :pagination="false"
      :scroll="{x:'100%',y:`${height*0.8}px`}"
      class="flex-1"
    >
      <template #headerCell="{title}">
        <div :style="{color:chartStyle.tableHeaderColor??'#333'}">{{ title }}</div>
      </template>
    </a-table>
  </div>
</template>

<script
  lang="tsx"
  setup
>
import { ChartProps, TableAlignEnum, TableStyle } from '../../../types';
import { useElementSize } from '@vueuse/core';

const props = defineProps<ChartProps<TableStyle>>();

const tableContainer = ref();
const { height } = useElementSize(tableContainer);

const columns = computed(() => {
  const style = {
    color: props.chartStyle.tableContentColor ?? '#333'
  };
  const r: any[] = [
    {
      align: 'center',
      title: '序号',
      width: 70,
      fixed: 'left',
      customRender: ({ index }: { index: number }) => <div style={style}>{index + 1}</div>
    }
  ];
  r.push(...props.metrics.map(item => {
    return {
      align: props.chartStyle.tableAlign ?? TableAlignEnum.LEFT,
      title: item.title,
      dataIndex: item.id,
      width: 200,
      customRender: ({ text }: { text: any }) => <div style={style}>{_.toString(text)}</div>
    };
  }));
  return r;
});

</script>

<style
  lang="scss"
  scoped
>

</style>