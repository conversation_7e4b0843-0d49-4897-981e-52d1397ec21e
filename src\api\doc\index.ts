import request from '@/axios';

//帮助文档-目录树
export const getFolderTreeList = () => {
  return request({
    url: '/aided/getTrees',
    method: 'get',
  });
};
//帮助文档-新增目录
export const addFolderTree = (params:any) => {
  return request({
    url: '/aided/insertTree',
    method: 'post',
    data:{
      ...params
    }
  });
};
//帮助文档-修改目录
export const updateFolderTree = (params:any) => {
  return request({
    url: '/aided/updateTrees',
    method: 'post',
    data:{
      ...params
    }
  });
};
//帮助文档-修改目录
export const sortFolderTree = (params:any) => {
  return request({
    url: '/aided/drag',
    method: 'post',
    data:{
      aidedTrees:params
    }
  });
};
//帮助文档-删除目录
export const delFolderTree = (aidedTreeId:any) => {
  return request({
    url: '/aided/deleteTrees',
    method: 'post',
    params:{
      aidedTreeId
    }
  });
};