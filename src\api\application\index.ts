import request from '@/axios';

//获取微应用文件夹列表
export const getMicroAppFolderList = () => {
  return request({
    url: '/microAppFolder/list',
  })
}

//获取文件夹详情
export const getMicroAppFolderDetail = (id: string) => {
  return request({
    url: '/microAppFolder/detail',
    params: { id },
  })
}

//查找文件夹下的应用
export const getAppByFolderId = (id: string) => {
  return request({
    url: `/microApp/list/${id}`,
  })
}
//删除微应用文件夹
export const delAppFolder = (id: string) => {
  return request({
    method: 'post',
    url: `/microAppFolder/remove`,
    params: {
      id
    }
  })
}

// 查看应用详情
export const getDetailById = (id: string) => {
  return request({
    url: `/microApp/detail?id=${id}`,
  })
}

// 保存微应用
export const saveAppDetail = (data: any) => {
  return request({
    url: `/microApp/saveDetail`,
    method: 'post',
    data
  })
}

// 发布微应用
export const releaseAppDetail = (data: any) => {
  return request({
    url: `/microApp/deploy`,
    method: 'put',
    data
  })
}


// 查看应用详情
export const getMicroAppFormById = (id: string) => {
  return request({
    url: `/micro-app-form-data-model/info?instanceId=${id}`,
  })
}

// 提交微应用表单
export const submitAppDetail = (data: any) => {
  return request({
    url: `/micro-app-form-data-model/save`,
    method: 'post',
    data
  })
}

// 重新发起提交微应用表单
export const reissueSubmitAppDetail = (data: any) => {
  return request({
    url: '/micro-app-form-data-model/restart',
    method: 'put',
    data
  })
}

// 获取微应用表单
export const getAppFormColumn = (id: string) => {
  return request({
    url: `/microApp/detail`,
    method: 'get',
    params: {
      id
    }
  })
}
// 获取微应用表单提交数据
export const getAppFormDetail = (current: number, size: number, microAppId: string, params: any) => {
  return request({
    url: `/micro-app-common/getDataManagePage`,
    method: 'get',
    params: {
      current, size, microAppId, ...params
    },
  })
}
// 获取微应用表单提交数据打印文件
export const getAppFormPrintFile = (params: { microAppId: string, instanceId: string }) => {
  return request({
    url: `/micro-app-form-data-model/view`,
    method: 'get',
    params: {
      ...params
    },
  })
}

// 获得 我发布的
export const getMySubmitApp = (current: number, size: number, data: any) => {
  return request({
    url: `/micro-app-common/getLaunchInstancePage`,
    method: 'post',
    data,
    params: {
      current, size
    },
  })
}


// 撤销微应用
export const revokeMySubmitApp = (data: any) => {
  return request({
    url: `/microApp/revoke`,
    method: 'put',
    data
  })
}

// 查看文件
export const getAppFile = (instanceId: string) => {
  return request({
    url: '/micro-app-common/getInstanceFile',
    params: { instanceId }
  })
}

// 推演路径
export const getInstancePath = (instanceId: string) => {
  return request({
    url: '/micro-app-common/getInstancePath',
    params: { instanceId }
  })
}


// 获取所有微应用
export const getAllAppList = (tenantId: string) => {
  return request({
    url: '/microApp/listAll',
    params: { tenantId }
  })
}

// 获取所有微应用
export const getCopyPermissionMap = (microAppId: string, nodeKey: string, instanceId: string) => {
  return request({
    url: '/micro-app-common/getKnowledgePermission',
    params: { microAppId, nodeKey, instanceId }
  })
}

// 我审批的
//
export const getMyApproveApp = (params: any) => {
  return request({
    url: `/micro-app-common/getMyApprovalPage`,
    params
  })
}

// 获取微应用的表单数据
export const getAppFormById = (microAppId: string,instanceId?:string) => {
  return request({
    url: `/microApp/getAppForm`,
    params: { microAppId,instanceId }
  })
}


// 获取当前微应用路径推演
export const getFuturePathTrend = (data: any) => {
  return request({
    url: '/micro-app-common/futurePathTrend',
    method: 'post',
    data
  })
}

//获取发起人的权限
export const getPromoterPermissionVO = (params: any) => {
  return request({
    url: `/micro-app-common/getFlwTaskPermissionVO`,
    method: 'get',
    params
  })
}

// 获取所有已发布的微应用
export const getAllPublishedMicroApp = (tenantId: string) => {
  return request({
    url: '/micro-app-common/getAllPublishedMicroApp',
    params: { tenantId }
  })
}

// 微应用提交校验接口
export const appRuleCheck = (data: any) => {
  return request({
    url: '/rule/check',
    method: 'post',
    data
  })
}

// 获取查看文件的接口
export const getPrintFileHttp = (params: any) => {
  return request({
    url: '/micro-app-form-data-model/view',
    params
  })
}

// 计算表
// 获取计算表 
export const getCalculatePage = (current: number, size: number, data: any) => {
  return request({
    url: "/microCal/page",
    method: 'post',
    params: {
      current, size
    },
    data,
  })
}
// 新增修改计算表 
export const addCalculatePage = (data: any) => {
  return request({
    url: "/microCal/submit",
    method: 'post',
    data,
  })
}
// 删除计算表 
export const deleteCalculatePage = (ids: string) => {
  return request({
    url: "/microCal/remove",
    method: 'post',
    params: { ids },
  })
}
// 删除计算表 
export const copyCalculatePage = (id: string) => {
  return request({
    url: "/microCal/copy",
    method: 'get',
    params: { id },
  })
}

// 根据微应用id集合获取表单
export const getMicrAppForm = (data: any) => {
  return request({
    url: '/micro-app-common/getFormModelList',
    method: 'put',
    data
  })
}
// 获取计算表详情
export const getCalculateDetail = (id: string) => {
  return request({
    url: '/microCal/detail',
    method: 'get',
    params: { id }
  })
}
// 详情发布
export const publishDetail = (id: string, type: string) => {
  return request({
    url: '/microCal/publish',
    method: 'get',
    params: { id, type }
  })
}
// 字段保存
export const submitField = (data: any) => {
  return request({
    url: '/microCal/submitField',
    method: 'post',
    data
  })
}

// 微应用检测分支
export const checkBranch = (data: any) => {
  return request({
    url: `/micro-app-common/checkBranch`,
    method: 'put',
    data
  })
}

export const getInitiatorSelected = (params: any) => {
  return request({
    url: `/micro-app-common/getInitiatorSelected`,
    method: 'get',
    params,
  })
}
// 微应用申请编辑，用户点击编辑的时候调用此接口
export const applyEditMicro = (data: any) => {
  return request({
    url: `/micro-app-common/applyEdit`,
    method: 'put',
    data,
  })
}

// 迁移微应用
export const moveMicroApp = (params: any) => {
  return request({
    url:'/microApp/move',
    method: 'post',
    params
  })
}

// 数据管理 批量导入
export const dataManageImportHttp = (data: any) => {
  return request({
    url:'/micro-app-common/dataManageImport',
    method: 'post',
    data
  })
}

export const getAdminTenantList = () => {
  return request({
    url:'/blade-system/tenant/queryTenantAdmin',
    method: 'get'
  })
}

export const isAdminHttp = (microAppId: string) => {
  return request({
    url:'/microApp/checkBalance',
    method: 'get',
    params:{microAppId}
  })
}
// 微应用转办
export const transferMicroHttp = (data: any) => {
  return request({
    url:'/microApp/transfer',
    method: 'post',
    data
  })
}

// 数据管理导出   获取微应用表单(all version from  )
export const getAllVersionForm = (microAppId: string) => {
  return request({
    url: `/micro-app-common/getAllVersionForm`,
    method: 'get',
    params: {
      microAppId
    }
  })
}

// 数据管理导出接口
export const exportDataManage = (data: any) => {
  return request({
    url: `/micro-app-common/dataManageExport`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 数据管理删除数据
export const deleteMicroAppData = (data:any) => {
  return request({
    url: `/micro-app-common/appInstanceDelete`,
    method: 'post',
    data,
  })
}

//与我有关页面分页数据
//

export const getRelatedToMePage = (params: any) => {
  return request({
    url: `/micro-app-common/relate/to/me`,
    params
  })
}