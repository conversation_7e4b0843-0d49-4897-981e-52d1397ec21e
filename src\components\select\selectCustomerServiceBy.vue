<template>
  <div class="select_customer">
    <slot :data="emitCustomer">
      <el-card
        class="box-card"
        :class="{ disabled: disabled }"
        @click="disabled ? void 0 : openSelect()"
      >
        <template v-if="emitCustomer.length > 0">
          <el-tag
            v-for="item in emitCustomer"
            :key="item.id"
            :closable="!disabled && closable"
            @close="unCheckCustomer(item, true)"
            style="margin: 0 5px"
            size="large"
          >{{ item.groupName }}</el-tag
          >
        </template>
        <div v-else class="select_customer_placeholder">{{ placeholder }}</div>
      </el-card>
    </slot>
    <dialog-form ref="userFormRef" :title="title" data-type="Array">
      <div class="select_wrapper">
        <div class="select_top">
          <el-card class="box-card">
            <template v-if="checkCustomer.length > 0">
              <el-tag
                v-for="item in checkCustomer"
                closable
                @close="unCheckCustomer(item)"
                style="margin: 5px"
                size="large"
              >{{ item.groupName }}</el-tag
              >
            </template>

            <div v-else class="select_customer_placeholder">{{ placeholder }}</div>
          </el-card>
        </div>
        <div class="select_bottom">
          <el-scrollbar class="tree_scrollbar">
            <el-tree
              :data="customerTreeData"
              ref="deptTreeRef"
              node-key="id"
              :expand-on-click-node="false"
              :show-checkbox="multiple"
              :check-strictly="multiple"
              @check-change="checkChange"
              :current-node-key="currentSelectCustomer"
              @current-change="handleCurrentChange"
              highlight-current
              :props="{ label: 'groupName' }"
            >
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
    </dialog-form>
  </div>
</template>

<script setup lang="ts">
import DialogForm, {
  DialogFormIns,
} from '@/views/mainview/sceneTasks/components/taskManagement/component/dialogForm.vue';
// import useDeptTreeStore, { DeptProps } from '@/piniaStore/banban/useDeptTreeStore.ts';
import useCustomerTreeStore, { CustomerProps } from '@/piniaStore/banban/useCustomerTreeStore.ts';
import { storeToRefs } from 'pinia';
import { ElMessage, ElTree } from 'element-plus';
import { taskSourceInjectKey } from '@/components/xt-el-form/constant.ts';

//配置项 单选多选之类的
interface configType {
  isRadio: boolean;
}
interface selectUserByProps {
  dataType?: 'array' | 'string';
  multiple?: boolean;
  placeholder?: string;
  closable?: boolean;
  disabled?: boolean;
  openLoad?: boolean;
  title?: string;
  config?: configType;
  isRadio?: boolean;
}
const props = withDefaults(defineProps<selectUserByProps>(), {
  dataType: 'array',
  multiple: false,
  placeholder: '请选择',
  closable: false,
  openLoad: false,
  disabled: false,
  title: '客服列表',
});
const isTypeArray = computed(() => {
  return props.dataType === 'array';
});
const vModel = defineModel<string[] | string>({ required: false, default: [] });
const deptTreeRef = ref<InstanceType<typeof ElTree>>();
const userFormRef = ref<DialogFormIns>();
const currentSelectCustomer = ref<string>('');
const checkCustomer = ref<CustomerProps[]>([]);
const taskSource = inject(taskSourceInjectKey, null); //任务组织来源

const { customerTreeData } = storeToRefs(useCustomerTreeStore());
const { findCustomerByIds, initCustomerTreeData } = useCustomerTreeStore();
const emitCustomer = ref<CustomerProps[]>([]);
const emit = defineEmits<{
  (e: 'change', value: CustomerProps[]): void;
}>();
const openSelect = async (callback?: (value: string | string[]) => void) => {
  props.openLoad && (await initCustomerTreeData(taskSource?.value));
  setTimeout(() => {
    emitCustomer.value.forEach(s => deptTreeRef.value?.setChecked(s, true, false));
  });
  userFormRef.value?.open(() => {
    const resValue = isTypeArray.value
      ? checkCustomer.value.map(t => t.id)
      : checkCustomer.value.map(t => t.id).join(',');
    if (props?.isRadio) {
      if (checkCustomer.value.length > 1) {
        ElMessage.warning('只能选择一个数据，请重新选择');
        return;
      }
    }
    userFormRef.value?.close(() => {
      emitCustomer.value = _.cloneDeep(checkCustomer.value);
      vModel.value = resValue;
      emit('change', emitCustomer.value);
      callback && callback(resValue);
    });
  });
};
const handleCurrentChange = (data: CustomerProps) => {
  if (!props.multiple) {
    checkCustomer.value.length = 0;
    checkChange(data, true);
  }
};
// const nodeClickHandle = (data, { checked }) => {
//   deptTreeRef.value?.setChecked(data, !checked, false);
// };
const checkChange = (s: CustomerProps, e: boolean) => {
  const idx = checkCustomer.value.findIndex(t => s.id === t.id);
  if (e) {
    if (idx === -1) {
      checkCustomer.value.push(s);
    }
  } else {
    checkCustomer.value.splice(idx, 1);
  }
};
const clearValidate = () => {
  currentSelectCustomer.value = '';
  checkCustomer.value.length = 0;
  emitCustomer.value.length = 0;
  deptTreeRef.value?.setCheckedKeys([], false);
};
initCustomerTreeData(taskSource?.value);
watch(
  vModel,
  n => {
    if (_.isEmpty(n)) {
      clearValidate();
    }
    //@ts-ignore
    emitCustomer.value = findCustomerByIds(n).filter(t => t !== null);
  },
  { immediate: true }
);
const unCheckCustomer = (s: CustomerProps, c: boolean = false) => {
  if (props.multiple) {
    deptTreeRef.value?.setChecked(s, false, false);
  } else {
    checkCustomer.value.length = 0;
  }
  if (c) {
    emitCustomer.value = emitCustomer.value.filter(t => t.id !== s.id);
    vModel.value = isTypeArray.value
      ? emitCustomer.value.map(t => t.value)
      : emitCustomer.value.map(t => t.value).join(',');
    emit('change', emitCustomer.value);
  }
};
export interface selectUserIns {
  clearValidate: () => void;
  modelList: any;
  openSelect: (callback: (value: string | string[]) => void) => void;
}
defineExpose<selectUserIns>({
  clearValidate,
  openSelect,
  modelList: emitCustomer,
});
</script>

<style scoped lang="scss">
.select_customer {
  width: 100%;
}
.select_wrapper {
  display: flex;
  flex-direction: column;
  .select_bottom {
    height: 250px;
    display: flex;
    & > div {
      flex: 1;
    }
  }
}
.tree_scrollbar {
  padding: 10px;
}
.user_scrollbar {
  padding: 10px;
  width: 100%;
  border-left: 1px solid #eee;
}
.box-card {
  cursor: pointer;
  width: 100%;
  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
.select_customer_placeholder {
  color: #a8abb2;
  font-size: 14px;
}
.disabled {
  background: #f6f6f6;
}
</style>
