<template>
  <div class="flex-(~ col gap-10px)" :key="tableKey">
    <el-scrollbar :max-height="height">
      <div
        v-for="(item, index) in vModel"
        :key="item.id"
        class="px-20px outline-(1 dashed #ccc) pt-20px relative"
      >
      <el-checkbox :key="item.id" :value="item.id" v-model="item.checked" v-if="source === fieldSource.HANDLED" ></el-checkbox>
        <xt-el-form-item-container
          :form="vModel[index]"
          :columns="columns[index]"
          :prop-prefix="[...itemProp, `${index}`]"
          :source="source"
        ></xt-el-form-item-container>
        <el-button
          class="absolute top-10px right-10px"
          link
          type="danger"
          @click="removeItem(index,item.id)"
          v-if="!disabled && $attrs.defaultType !== DynamicTypeEnum.GROUPSUMMARY && handleType !== HandleTypeEnum.Examine"
        >
          <template #icon>
            <el-icon :size="16">
              <Delete />
            </el-icon>
          </template>
        </el-button>
        <el-button
          class="absolute top-10px right-10px"
          link
          type="danger"
          @click="removeItem(index,item.id)"
          v-if="!itemDisabled && (handleType === HandleTypeEnum.Examine)"
        >
          <template #icon>
            <el-icon :size="16">
              <Delete />
            </el-icon>
          </template>
        </el-button>
      </div>
  </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { XtElFormItemType, AvueForm } from '@/components/form-config/types/AvueTypes.ts';
import XtElFormItemContainer from '@/components/xt-el-form/XtElFormItemContainer.vue';
import { DynamicTypeEnum, fieldSource, HandleTypeEnum } from '@/components/form-config/const/commonDic';
import { handleTable } from '@/components/xt-el-form/hooks'
import { disabledInjectKey, handleTypeInjectKey } from '@/components/xt-el-form/constant.ts';

defineOptions({
  inheritAttrs: false,
});

const props = withDefaults(
  defineProps<{
    columns?: XtElFormItemType[][];
    itemProp?: string[];
    disabled: boolean;
    source?: number//组件的使用场景 1中间的组件配置 2 组件的默认值设置 3 预览 4 应办
    height?: string;
    col:AvueForm[];
    itemDisabled?: boolean;
  }>(),
  {
    columns: () => [],
    height: '500'
  }
);

const handleType = inject(handleTypeInjectKey, HandleTypeEnum.HANDLED);

const vModel = defineModel<AvueForm[]>({ default: () => [] });
const originData = defineModel<AvueForm[]>('originData', { default: () => [] });

// 监听 vModel 变化，同步到 originData
// watch(
//   vModel,
//   (newVal) => {
//     if (newVal) {
//       // 深度同步数据到源数据
//       newVal.forEach((item, index) => {
//         const originIndex = originData.value.findIndex(origin => origin.id === item.id);
//         if (originIndex !== -1) {
//           // 更新现有项
//           originData.value[originIndex] = _.cloneDeep(item);
//         } else {
//           // 添加新项
//           originData.value.push(_.cloneDeep(item));
//         }
//       });

//       // 移除在 vModel 中不存在的项
//       originData.value = originData.value.filter(origin =>
//         newVal.some(item => item.id === origin.id)
//       );
//     }
//   },
//   { deep: true }
// );

const {selectionData, batchDeleteData, handleDelete } = handleTable(vModel,originData,'form')
const tableKey = ref(1)
const attrs = useAttrs();

watch(
  () =>vModel.value,
  (val) => {
    console.log('vModel', val);
    selectionData.value = val.filter(v => v.checked).map(v=>v.id)
  },
  { immediate: true, deep:true }
)

provide('col', props.col)

const disabled = computed(() => attrs.defaultType === DynamicTypeEnum.GROUPSUMMARY);
provide(disabledInjectKey, disabled);

// const selectionData = ref<AvueForm[]>([]);

const removeItem = async(index: number, id: string) => {
  if(!id.includes('#')){
    await handleDelete(id);
  }

  // 从显示数据中删除
  vModel.value?.splice(index, 1);

  // 从源数据中删除
  const originIndex = originData.value.findIndex(item => item?.id === id);
  if (originIndex !== -1) {
    originData.value.splice(originIndex, 1);
  }
};
//更新表格
const updateTable = () => {
  tableKey.value += 1
}

//批量删除
// const batchDeleteData = () => {
//   if (!selectionData.value.length) {
//     ElMessage.warning('请至少选择一项数据')
//     return
//   }
//   vModel.value = vModel.value.filter(v => !selectionData.value.includes(v.id));
// }

defineExpose({
  batchDeleteData,
  updateTable
});
</script>

<style scoped></style>
