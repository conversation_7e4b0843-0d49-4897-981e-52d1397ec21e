<template>
  <div class="widget-config h-full">
    <div v-if="data && Object.keys(data).length > 0" class="h-full flex flex-col">
      <widget-title :data="data"></widget-title>
      <el-scrollbar wrap-class="bg-white" view-class="px-15px py-20px">
        <el-form labelPosition="top" :disabled="configDisabled">
          <component :is="getComponent" :data="data" :column="column"></component>
        </el-form>
      </el-scrollbar>
    </div>
    <div v-else class="h-full flex align-center justify-center">
      <el-empty description="拖拽字段进行配置"></el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import WidgetTitle from './title.vue';
import ConfigComponent from '../install.js';
import { mulSelectType } from '@/components/form-config/types/FieldTypes';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';

const props = defineProps(['data', 'column']);

provide('allColumn', props.column);
const configDisabled = inject('disabled', false);

const getComponent = computed(() => {
  const prefix = 'config_';
  const { fixType } = props.data;
  let result = '';
  if (mulSelectType.includes(fixType)) result = FixTypeEnum.SELECT;
  else result = fixType;
  return ConfigComponent[(prefix + result) as keyof typeof ConfigComponent];
});
</script>
