export const sortFetchOption = [
  {
    label: '正序',
    value: 1,
  },
  {
    label: '倒序',
    value: 2,
  },
];

export const inlineOption = [
  {
    label: '根据数据生成时间',
    value: 1,
  },
  {
    label: '数字类型',
    value: 2,
  },
];
export const fetchOption1 = [
  {
    label: '最新',
    value: 0,
  },
  {
    label: '最旧',
    value: 1,
  },
];
export const fetchOption2 = [
  {
    label: '最大',
    value: 0,
  },
  {
    label: '最小',
    value: 1,
  },
];
export const fetchOption3 = [
  {
    label: '最新',
    value: 2,
  },
  {
    label: '最旧',
    value: 3,
  },
];
export const sortOption = [
  {
    label: '文本',
    value: 1,
  },
  {
    label: '日期时间',
    value: 2,
  },
  {
    label: '数字',
    value: 3,
  },
  {
    label: '选择',
    value: 4,
  },
  {
    label: '地址',
    value: 5,
  },
  {
    label: '成员选择',
    value: 6,
  },
  {
    label: '工作组选择',
    value: 7,
  },
  {
    label: '部门选择',
    value: 8,
  },
  {
    label: '获取当前用户ID',
    value: 9,
  },
];
export const screenRadioList = [
  {
    label: '历史表单字段',
    value: 1,
  },
  {
    label: '当前表单字段',
    value: 2,
  },
  {
    label: '自定义',
    value: 3,
  },
];
export const dataLinkRadioList = [
  {
    label: '触发任务字段',
    value: 1,
  },
  {
    label: '当前表单字段',
    value: 2,
  },
  {
    label: '自定义',
    value: 3,
  },
];

export const isAlsoEnum = [
  {
    value: '1',
    label: '且',
  },
  {
    value: '2',
    label: '或',
  },
];

export const isAlsoEnum1 = [
  {
    value: '1',
    label: '且',
  },
  {
    value: '0',
    label: '或',
  },
];



