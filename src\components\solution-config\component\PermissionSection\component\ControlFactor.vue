<template>
  <el-form-item label="事件因素">
    <div class="flex items-center gap-10px">
      <el-select-v2
        v-model="factor.eventType"
        :options="permissionFactorList"
        filterable
        :props="{ label: 'option' }"
        placeholder="请选择字段"
        class="w-400px"
      />
      <el-icon color="#ff7070" class="ml-30px cursor-pointer" @click="$emit('delete', factor.id)"
        ><DeleteFilled
      /></el-icon>
    </div>
  </el-form-item>
  <el-form-item :label="label">
    <div class="flex items-center gap-10px">
      <el-input v-model="factor.quantity" type="number" />
      <avue-select :dic="unitList" v-model="factor.measureType" />
    </div>
  </el-form-item>
</template>
<script setup lang="ts">
import { controlFactor } from '../../../types';
import { permissionFactorList } from '../../../const';
import { DeleteFilled } from '@element-plus/icons-vue';

const props = defineProps<{
  factor: controlFactor;
}>();

const eventContent = computed(() => {
  return permissionFactorList.find(item => item.value === props.factor.eventType);
});

const label = computed(() => {
  return eventContent.value?.fields[0].field;
});

const unitList = computed(() => {
  return eventContent.value?.fields[0].units;
});

defineEmits(['delete']);
</script>
<style lang="scss" scoped></style>
