<template>
  <div class="flex-(~ col) w-full items-start">
    <p class="color-#f44" v-if="item?.defaultType === DefaultTypeEnum.SIGN && !model">签名调用失败，请先进行核验</p>
    <div class="flex">
      <el-button icon="EditPen" v-if="!disabled" @click="visible = true">签名</el-button>
      <el-button icon="Close" v-if="model && !disabled" @click="clearSign" type="danger" plain>清除签名</el-button>
    </div>
    
    <el-image :src="model" v-if="model" class="max-w-400px h-200px" fit="contain"/>

    <el-dialog v-model="visible" append-to-body destroy-on-close @opened="resize" width="40%">
      <van-signature @submit="onSubmit" :key="key" :line-width="10"/>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { putFile } from '@/api/resource/oss';
import { ElMessage } from 'element-plus';
import { Base64 } from 'js-base64';
import { AvueColumns} from '@/components/form-config/types/AvueTypes.ts';
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic';
import { useFormItem } from 'element-plus'
 
const { formItem } = useFormItem()
defineProps<{
  disabled?: boolean;
  item?: AvueColumns
}>();

const visible = ref(false);

const model = defineModel<string>();

const key = ref(Math.random());

const resize = () => {
  key.value = Math.random();
};

const onSubmit = (data: { image: string }) => {
  if (data.image) {
    // 此时可以自行将文件上传至服务器
    const formData = new FormData();
    formData.append(
      'file',
      new File([Base64.toUint8Array(data.image.replace('data:image/png;base64,', ''))], 'test.png')
    );
    putFile(formData)
      .then((res: any) => {
        model.value = res.data.data?.link;
      })
      .then(() => {
        visible.value = false;
      });
  } else {
    ElMessage.warning('请签名');
  }
};

watch(
  () => model.value,
  () => {
    formItem?.validate('change')
  },
  {
    immediate:true
  }
)

const clearSign = () => {
  model.value =''
}
</script>

<style scoped lang="scss">
:deep(.van-signature__footer .van-button:first-child) {
  display: none;
}
</style>
