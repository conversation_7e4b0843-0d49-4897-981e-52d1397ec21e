<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :before-close="handleClose"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="wechat-bind-dialog">
      <!-- 二维码区域 -->
      <div v-if="qrcodeUrl" class="qrcode-container">
        <img :src="qrcodeUrl" class="qrcode-image">
<!--        <qrcode-vue :value="qrcodeUrl" alt="微信绑定二维码" size="200" class="qrcode-image" />-->
        <p class="qrcode-tip">请使用微信扫描二维码完成绑定</p>
      </div>

      <!-- 加载状态 -->
      <div v-else class="loading-container">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>正在生成二维码...</p>
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
<!--        <el-button type="primary" @click="refreshQRCode" :loading="refreshing">-->
<!--          刷新二维码-->
<!--        </el-button>-->
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'

const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: '微信绑定'
  },
  // 弹窗宽度
  width: {
    type: String,
    default: '400px'
  },
  // 轮询间隔(毫秒)
  pollingInterval: {
    type: Number,
    default: 3000
  }
})

const emit = defineEmits(['bind-success', 'close', 'update:visible'])

// 响应式数据
const visible = ref(false)
const qrcodeUrl = ref('')
const expireCountdown = ref(0)
const checkInterval = ref(null)
const refreshing = ref(false)

// 打开弹窗
const open = async (initCallback) => {
  visible.value = true
  await initQRCode(initCallback)
}

// 初始化二维码
const initQRCode = async (initCallback) => {
  try {
    qrcodeUrl.value = ''
    const result = await initCallback();
    qrcodeUrl.value = result.qrcodeUrl;
    expireCountdown.value = result.expiresIn || props.defaultExpiresIn

    // 开始倒计时
    const countdownInterval = setInterval(() => {
      expireCountdown.value -= 1
      if (expireCountdown.value <= 0) {
        clearInterval(countdownInterval)
      }
    }, 1000)

    // 开始轮询绑定状态
    startPolling(result.pollingCallback)
  } catch (error) {
    ElMessage.error('生成二维码失败: ' + error.message)
    handleClose()
  }
}

// 开始轮询绑定状态
const startPolling = (pollingCallback) => {
  stopPolling() // 先停止之前的轮询

  checkInterval.value = setInterval(async () => {
    try {
      const result = await pollingCallback();
      if (result.data.data?.binded === true) {
        handleBindSuccess()
      } else if (result.status === 'expired') {
        ElMessage.warning('二维码已过期，请刷新')
      }
    } catch (error) {
      console.error('轮询绑定状态失败:', error)
    }
  }, props.pollingInterval)
}

// 停止轮询
const stopPolling = () => {
  if (checkInterval.value) {
    clearInterval(checkInterval.value)
    checkInterval.value = null
  }
}

// 处理绑定成功
const handleBindSuccess = () => {
  stopPolling()
  ElMessage.success('微信绑定成功')
  emit('bind-success')
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  stopPolling()
  visible.value = false
  emit('close')
  emit('update:visible', false)
}

// 组件卸载时清理
onUnmounted(() => {
  stopPolling()
})

// 暴露方法给父组件
defineExpose({
  open,
  close: handleClose
})
</script>

<style scoped>
.wechat-bind-dialog {
  text-align: center;
}

.qrcode-container {
  padding: 20px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  display: block;
  margin: 0 auto 15px;
  border: 1px solid #eee;
}

.qrcode-tip {
  color: #666;
  margin-bottom: 10px;
}

.qrcode-expire {
  color: #e6a23c;
  font-size: 14px;
}

.loading-container {
  padding: 40px 0;
  color: #999;
}

.loading-icon {
  animation: rotating 2s linear infinite;
  margin-bottom: 10px;
  font-size: 24px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.dialog-footer {
  text-align: center;
}
</style>