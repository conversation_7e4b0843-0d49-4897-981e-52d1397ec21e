
// 分页请求参数
export interface ReqPage {
    current: number;
    size: number;
}

//创建组织
export interface submitCreateOrg{
    tenantName:string;
    phone: string;
    
}

//加入组织
export interface submitJionOrg{
    tenantId:string;
    userName:string;
    userId:number;
}

//加入组织
export interface orgListType{
    tenantName:string;
    tenantId:string;
    // [propName: string]: any;
}

export interface orgListReq extends ReqPage {
    tenantName:string;
}