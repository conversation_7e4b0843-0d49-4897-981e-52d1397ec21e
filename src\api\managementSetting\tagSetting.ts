import request from '@/axios';

//获取标签分页列表
export const getTagList = (current:number|string,size:number|string,data:Object) =>{
  return request({
    url: '/label/page',
    method: 'post',
    data: data,
    params: {
      current,
      size
    }
  })
}

//获取标签详情
export const getTagDetail = (id:string) =>{
  return request({
    url: '/label/detail',
    method: 'get',
    params: {
      id
    }
  })
}

//获取标签相关文件清单
export const getTagFileList = (id:string) =>{
  return request({
    url: '/label/ruleFileList',
    method: 'get',
    params: {
      id
    }
  })
}


//删除标签
export const delTag = (id: number) =>{
  return request({
    url: '/label/remove',
    method: 'post',
    params: {
      id
    }
  })
}

//新增修改标签
export const addTag = (data: Object) =>{
  return request({
    url: '/label/submit',
    method: 'post',
    data: data
  })
}

//获取版本
export const getVersion = () =>{
  return request({
    url: '/scene_task_group/getVersionList',
    method: 'get',
  })
}

//获取职责
export const getDutyList = () =>{
  return request({
    url: '/duty/list',
    method: 'post',
  })
}

//新增标签名称--大目录
export const addTargetName = (data:object) =>{
  return request({
    url: '/taskTarget/addTargetName',
    method: 'post',
    data: data
  })
}

//目标分页列表
export const targetPage = (current:number,size:number,data:object) =>{
  return request({
    url: '/taskTarget/targetPage',
    method: 'post',
    data: data,
    params:{
      current,
      size
    }
  })
}

//选择目标分页列表
export const selectTargetPage = (current:number,size:number,data:object) =>{
  return request({
    url: '/taskTarget/chooseTargetPage',
    method: 'post',
    data: data,
    params:{
      current,
      size
    }
  })
}

//添加或编辑目标
export const addEditTarget = (data:object) =>{
  return request({
    url: '/taskTarget/editTarget',
    method: 'post',
    data: data
  })
}

//删除目标
export const removeTarget = (ids:string) =>{
  return request({
    url: '/taskTarget/remove',
    method: 'post',
    params: {ids}
  })
}

//获取全部目标列表
export const getTargetDicAll = (targetType?:string) =>{
  return request({
    url: '/taskTarget/targetNameList',
    method: 'get',
    params: {targetType}
  })
}

//获取未添加的目标列表
export const getTargetDicUn = (nameId:number,targetType:string) =>{
  return request({
    url: '/taskTarget/notAddedTargetName',
    method: 'post',
    data: {nameId,targetType}
  })
}

export const getCanType = (targetName:string) =>{
  return request({
    url: '/taskTarget/targetValueSynchronization',
    method: 'get',
    params: {targetName}
  })
}
//文件配置选择标签
export const getChooseLabel = (isBind:Boolean) =>{
  return request({
    url: '/label/fileConfig/chooseLabel',
    method: 'get',
    params: {isBind}
  })
}
//根据版本获取标签列表
export const labelListByVersion = (versionId?:string) =>{
  return request({
    url: '/label/labelList',
    method: 'get',
    params: {versionId}
  })
}
//当前组织下未绑定版本标签
export const noBindLabelList = () =>{
  return request({
    url: '/label/notBindLabelList',
    method: 'get',
  })
}
//当前组织下所有标签列表
export const allLabelListApi = () =>{
  return request({
    url: '/label/allLabelList',
    method: 'get',
  })
}
//当前组织下所有标签列表
export const microAppLabelList = (params:{microAppId:string}) =>{
  return request({
    url: '/label/microAppLabelList',
    method: 'get',
    params
  })
}




