<template>
  <config-title :data="data" />
  <config-permission :data="data" />
  <!-- <config-data-link :data="data" isFormItem /> -->
  <config-default-type :data="data"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import { SignatureAllField } from './type';
// import ConfigDataLink from './common/ConfigDataLink.vue';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';

defineProps<{
  data: SignatureAllField;
}>();
</script>
