import request from '../../axios';
import { SuperiorTypeEnum } from '@/views/banban/know/workDash/types/chartEdit.ts';

export const submitDuty = (data: any) => {
  return request({
    url: '/duty/submit',
    method: 'post',
    data
  });
};

export const getDutyList = () => {
  return request({
    url: '/duty/list',
    method: 'post'
  });
};

export const getDutySelect = () => {
  return request({
    url: '/duty/select',
    method: 'post'
  });
};

export const removeDuty = (ids: string[]) => {
  return request({
    url: '/duty/remove',
    method: 'post',
    params: {
      ids: _.join(ids)
    }
  });
};

export const exchangeDuty = (id?: string, oldIndex?: number, newIndex?: number) => {
  return request({
    url: '/duty/exchange',
    method: 'post',
    data: {
      id,
      oldIndex,
      newIndex
    }
  });
};

export const chartDetail = (id?: string) => {
  return request({
    url: '/dutyCharts/detail',
    method: 'get',
    params: {
      id
    }
  });
};

export const chartSave = (data: any) => {
  return request({
    url: '/dutyCharts/submit',
    method: 'post',
    data
  });
};

export const chartList = (superiorType: SuperiorTypeEnum, superiorId?: string, all?: boolean) => {
  return request({
    url: '/dutyCharts/listCharts',
    method: 'post',
    data: {
      superiorType,
      superiorId,
      all
    }
  });
};

export const chartRemove = (id: string) => {
  return request({
    url: '/dutyCharts/remove',
    method: 'post',
    params: {
      ids: id
    }
  });
};

export const chartExchange = (
  previous: string | undefined | null,
  next: string | undefined | null,
  id: string,
  oldIndex: number,
  newIndex: number
) => {
  return request({
    url: '/dutyCharts/exchange',
    method: 'post',
    data: {
      previous,
      next,
      id,
      oldIndex,
      newIndex
    }
  });
};

export const getPreviewData = (data: any) => {
  return request({
    url: '/dutyCharts/preview',
    method: 'post',
    data
  });
};