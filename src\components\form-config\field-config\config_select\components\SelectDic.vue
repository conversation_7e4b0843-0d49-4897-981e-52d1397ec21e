<template>
  <el-form-item label="选项内容">
    <div :class="repeat ? 'isRepeat' : undefined" class="p-2px">
      <n-tree
        class="h-200px"
        :key="treeKey"
        virtual-scroll
        ref="tree"
        block-line
        :draggable="canDraggable && !configDisabled"
        :data="data.dicData"
        key-field="key"
        :selectable="false"
        :allow-drop="allowDrop"
        @drop="handleDrop"
        :render-label="renderLabel"
      />
      <div
        v-if="!configDisabled"
        class="gap-10px"
        style="display: flex; justify-content: flex-end; background-color: white; margin-top: 20px"
      >
        <select-import :data="data" v-if="isCascaderType" />
        <select-edit-batch :data="data" :handleAddFields="handleAddFields" :updateKey v-if="!isCascaderType"/>
        <n-button text type="primary" @click="handleAddFields(false)" :focusable="false"
          >添加选项
        </n-button>
        <n-button
          text
          type="primary"
          :disabled="haveOther"
          @click="handleAddFields(true)"
          :focusable="false"
          >添加其他
        </n-button>
      </div>
    </div>
    <transition name="el-fade-in-linear">
      <div v-show="repeat" class="el-form-item__error">同层级有重复选项</div>
    </transition>
  </el-form-item>
</template>

<script setup>
import selectImport from '@/components/form-config/field-config/config_select/components/SelectImport.vue';
import SelectEditBatch from '@/components/form-config/field-config/config_select/components/SelectEditBatch.vue';
import { computed, nextTick, ref } from 'vue';
import { ElMessage } from 'element-plus';
import SelectDicItem from '@/components/form-config/field-config/config_select/components/SelectDicItem.vue';
import { haveRepeat, addKeys } from '@/components/form-config/field-config/config_select/utils';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { generateId } from '@/components/form-config/utils';

const props = defineProps(['data']);

const configDisabled = inject('disabled',false)

const treeKey = ref(0);

const isCascaderType = computed(() => props.data.selectType === 4 || props.data.fixType === FixTypeEnum.MUL_CASCADER);//层级选择/级联多选
//判断有没有重复
const repeat = ref(false);
//进行重复选项验证
watch(
  () => props.data.dicData,
  value => {
    repeat.value = haveRepeat(value);
  },
  {
    deep: true,
    immediate: true,
  }
);

//兼容一下历史数据
onMounted(() => {
  addKeys(props.data.dicData);
});
//输入框聚焦
const focusInput = event => {
  event.currentTarget.select();
  canDraggable.value = false;
};

const isCascader = computed(() => props.data.type === 'cascader' || props.data.fixType === FixTypeEnum.MUL_CASCADER);
//不是级联的时候去除子项
watchEffect(() => {
  if (!isCascader) {
    props.data.dicData.forEach(item => delete item.children);
  }
});

//级联级别 为几级-1的数
const cascaderLevel = 3;

const canDraggable = ref(true);
const tree = ref(null);


const haveOther = computed(() => props.data.dicData.some(item => !!item.isOther));

// 输入框展示
const renderLabel = ({ option }) => {
  let [nodeSiblings, nodeIndex, level, pNode] = findSiblingsAndIndex(option, props.data.dicData);

  return h(SelectDicItem, {
    node: option,
    level,
    cascaderLevel,
    isCascader: isCascader.value,
    modelValue: option.label,
    disabled: option.isOther,
    maxlength: 50,
    'onUpdate:modelValue': value => (option.label = value),
    onFocus: focusInput,
    onBlur: () => (canDraggable.value = true),
    onRemove: () => {
      nodeSiblings.splice(nodeIndex, 1);
      if (!nodeSiblings?.length) {
        delete pNode.children;
      }
    },
  });
};

//节点拖动规则
const allowDrop = ({ dropPosition, node, phase }) => {
  const [dragNodeSiblings, dragNodeIndex, level] = findSiblingsAndIndex(node, props.data.dicData);
  if (dropPosition === 'inside') {
    if (!isCascader.value) {
      //不是cascader不能拖到二级
      return false;
    } else if (level > cascaderLevel) {
      //是不是cascader不能拖到二级
      return false;
    }
  }
  return true;
};

const findSiblingsAndIndex = (node, nodes, level = 1, pNode) => {
  if (!nodes) {
    return [null, null, null, null];
  }
  for (let i = 0; i < nodes.length; ++i) {
    const siblingNode = nodes[i];
    if (siblingNode.key === node.key) {
      return [nodes, i, level, pNode];
    }
    const [siblings, index, le, pn] = findSiblingsAndIndex(
      node,
      siblingNode.children,
      level + 1,
      siblingNode
    );
    if (siblings && index !== null) {
      return [siblings, index, le, pn];
    }
  }
  return [null, null, null, null];
};

//添加节点
const handleAddFields = (other, la = '') => {
  const label = other ? '其他' : la || `未命名${props.data.dicData.length + 1}`;
  const isOther = !!other;
  props.data.dicData.push({ label, isOther ,key:generateId()});

  nextTick(() => {
    tree.value.scrollTo({ key: label });
  });
};
//获得节点级数
const childrenLevel = (node, level = 1) => {
  if (node?.children) {
    level += 1;
    node.children.forEach(item => {
      level = childrenLevel(item, level);
    });
  }
  return level;
};
//拖拽方法
const handleDrop = ({ node, dragNode, dropPosition }) => {
  const dataRef = { value: props.data.dicData };

  const [dragNodeSiblings, dragNodeIndex, l, p] = findSiblingsAndIndex(dragNode, dataRef.value);

  if (dragNodeSiblings === null || dragNodeIndex === null) {
    return;
  }
  dragNodeSiblings.splice(dragNodeIndex, 1);
  if (!dragNodeSiblings?.length) {
    delete p.children;
  }

  const [nodeSiblings, nodeIndex, nodeLevel] = findSiblingsAndIndex(node, dataRef.value);

  if (dropPosition === 'inside') {
    const cL = childrenLevel(dragNode);

    if (nodeLevel + cL > cascaderLevel + 1) {
      ElMessage.warning(`节点级数不能大于${cascaderLevel + 1}`);
      dragNodeSiblings.splice(dragNodeIndex, 0, dragNode);
      return;
    }

    if (node.children) {
      node.children.unshift(dragNode);
    } else {
      node.children = [dragNode];
    }
  } else if (dropPosition === 'before') {
    if (nodeSiblings === null || nodeIndex === null) {
      return;
    }
    nodeSiblings.splice(nodeIndex, 0, dragNode);
  } else if (dropPosition === 'after') {
    if (nodeSiblings === null || nodeIndex === null) {
      return;
    }
    nodeSiblings.splice(nodeIndex + 1, 0, dragNode);
  }
  props.data.dicData = Array.from(dataRef.value);
};

const updateKey = () => {
  treeKey.value += 1;
};
</script>

<style lang="scss" scoped>
.isRepeat {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}
</style>