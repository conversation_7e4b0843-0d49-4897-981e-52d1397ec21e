<template>
  <!-- <avue-select
    v-model="data.defaultType"
    :dic="dic"
    class="mb-10px"
    :clearable="false"
  >
  </avue-select> -->
  <div class="flex-(~ col gap-10px) w-full">
    <config-data-link-dialog ref="linkDialog" :data="data" />
  </div>
</template>

<script setup lang="ts">
import { PackageIdInjectKey } from '@/components/form-config/utils/injectKeys';
import ConfigDataLinkDialog from './components/ConfigDataLinkDialog.vue';
import { configDataLinkType } from './type';

defineProps<configDataLinkType & {
  isFormItem?: boolean;
}>();

defineOptions({
  inheritAttrs: false,
});

const taskPackageId = inject(PackageIdInjectKey);


const linkDialog = ref();
</script>

<style scoped lang="scss"></style>
