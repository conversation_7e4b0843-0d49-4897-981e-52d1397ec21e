<template>
  <div class="w-full">
    <slot :data="modelList">
      <el-card
        class="box-card"
        :class="{ disabled: disabled }"
        @click="disabled ? void 0 : openSelect()"
      >
        <template v-if="modelList.length > 0">
          <el-tag
            v-for="(item, index) in modelList"
            :key="item.id"
            :closable="!disabled && closable && item.id !== disabledUser"
            @close="unCheckUser(index)"
            style="margin: 0 5px"
            size="large"
            >{{ item.nickName }}
          </el-tag>
        </template>
        <div v-else class="select_placeholder">{{ placeholder }}</div>
      </el-card>
      <div class="group p-2 mb-2 mt-2" v-if="isShowDept">
        <span class="font-bold">部门: </span
        ><span v-for="item in deptList" :key="item.id" class="mr-1">{{ item.deptName }};</span>
      </div>
      <div class="group p-2" v-if="isShowWork">
        <span class="font-bold">工作组: </span>
        <span v-for="item in workGroupList" :key="item.id" class="mr-1">{{ item.groupName }};</span>
      </div>
    </slot>
    <selectUserGroup
      v-if="dialogVisible"
      :modelList="modelList"u
      @submit="handleSubmit"
      v-model:visible="dialogVisible"
      :config="{ isRadio, tenantId: tenantId || userMsg.tenant_id }"
    ></selectUserGroup>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { getDeptByUser, getGroupByUser } from '@/api/common/index.ts';
import selectUserGroup from '../component/selectUser.vue';
import { useFormItem, ElMessage } from 'element-plus';
import {
  taskSourceInjectKey,
  tenantIdInjectKey,
  taskIdInjectKey,
  userRangeTypeKey,
} from '@/components/xt-el-form/constant.ts';
import { useStore } from 'vuex';
import { getTaskBindWorkGroupIdsByTaskId } from '@/api/taskManagement/taskPackage';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore.ts';
import { storeToRefs } from 'pinia';
import { isEmptyValue } from '@/utils/field';
import { getUserListByTid } from '@/api/orgInfo';
import { userType } from '@/api/common/type';
import { selectUserByUser } from './XTUsers';

const store = useStore();
const userMsg = store.state.user.userInfo;

const { formItem } = useFormItem();
const { taskId: taskId1 } = storeToRefs(useTaskDetailStore());

const props = withDefaults(defineProps<selectUserByUser>(), {
  dataType: 'array',
  multiple: false,
  placeholder: '请选择',
  initData: false,
  taskPackageId: '',
  closable: true,
  disabled: false,
  isShowDept: false,
  isShowWork: false,
  isRadio: false,
});

const dialogVisible = ref(false);
const vModel = defineModel<string | string[]>();
const modelList = ref<userType.ResUserParams[]>([]);
const deptList = ref<any[]>([]);
const workGroupList = ref<any[]>([]);
const taskSource = inject(taskSourceInjectKey, null); //任务组织来源
const tenantId = inject(tenantIdInjectKey, null); //任务组织来源
const taskGroupList = ref<any[]>([]);
const taskId = inject(taskIdInjectKey, null); //任务Id
const disabledUser = inject('disabledUser');
function validateValue() {
  formItem?.validate?.('blur').catch(err => {
    console.log(err);
  });
}
//获取任务绑定的工作组
const getTaskGroupList = async () => {
  try {
    const { data } = await getTaskBindWorkGroupIdsByTaskId({
      taskId: (taskId?.value || taskId1.value) as string,
    });
    taskGroupList.value = data.data;
  } catch (error) {}
};

onMounted(async () => {
  if (taskId?.value || taskId1.value) {
    await getTaskGroupList();
  }
});

provide('taskGroupList', taskGroupList);
provide(userRangeTypeKey, props.userRange); //用户范围

//选中成员
const handleSubmit = (n: userType.ResUserParams[]) => {
  if (props?.isRadio && modelList.value.length > 1) {
    ElMessage.warning('只能选择一个数据，请重新选择');
    return;
  }
  dialogVisible.value = false;
  modelList.value = n;
  vModel.value = props.dataType === 'array' ? n.map(t => t.id) : n.map(t => t.id).join(',');
  showInfo();
};
//部门管理员 工作组管理员
const showInfo = () => {
  if (!modelList.value.length) {
    deptList.value = [];
    workGroupList.value = [];
    return;
  }
  const ids = modelList.value.map(t => t.id);
  if (props.isShowDept) {
    getDept(ids as string[]);
  }
  if (props.isShowWork) {
    getGroup(ids as string[]);
  }
};

const openSelect = () => {
  dialogVisible.value = true;
};

const unCheckUser = (index: number) => {
  modelList.value.splice(index, 1);
  vModel.value =
    props.dataType === 'array'
      ? modelList.value.map(t => t.id)
      : modelList.value.map(t => t.id).join(',');
  showInfo();
};
const allUserList = ref<any[]>();
const initAllUserList = async (tid?: string) => {
  allUserList.value = (await getUserListByTid(tid || userMsg.tid)).data.data;
};
initAllUserList(taskSource?.value);
//根据部门ids获取部门
const getDept = async (ids: string[]) => {
  const params = {
    tenantId: tenantId?.value || userMsg.tenant_id,
    userIds: ids,
  };
  const { data } = await getDeptByUser(params);
  deptList.value = data.data;
};

//根据用户ids获取工作组
const getGroup = async (ids: string[]) => {
  const params = {
    tenantId: tenantId?.value || userMsg.tenant_id,
    userIds: ids,
  };
  const { data } = await getGroupByUser(params);
  workGroupList.value = data.data;
};

watchEffect(async () => {
  if (!isEmptyValue(allUserList.value)) {
    validateValue();
    const vModelArray =
      props.dataType === 'array'
        ? vModel.value
        : (vModel.value as string)?.split(',').map(id => id.trim());
    if (!_.isArray(vModelArray)) {
      if (isEmptyValue(vModelArray)) {
        modelList.value = [];
      }
      return;
    }
    modelList.value = allUserList.value!.filter(user => vModelArray!.includes(user.id));
    showInfo();
  }
});

defineExpose({
  modelList: modelList,
});
</script>

<style lang="scss" scoped>
.group {
  background-color: #eee;
  color: #333;
}

.disabled {
  background: #f6f6f6;
}

.select_placeholder {
  color: #a8abb2;
  font-size: 14px;
}

.box-card {
  cursor: pointer;
  width: 100%;

  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
</style>
