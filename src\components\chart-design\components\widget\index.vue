<template>
  <div class="flex-(~ col) gap-16px pt-16px">
    <!--top-->
    <div class="flexList shrink-0">
      <tags-base
        v-for="item in TopTags[chartType]"
        v-model="refsStore[item.refType].value"
        v-bind="item"
      />
    </div>

    <!--main-->
    <Chart
      :auxiliary="auxiliaryList"
      :chart-style="chartStyle"
      :chart-type="chartType"
      :dimensions="dimensionsList"
      :from-list="false"
      :metrics="metricsList"
      :preview-data="previewData"
      class="flex-1 shadow-[0_0_12px_0_#F5F5F5]"
    />
  </div>
</template>

<script
  lang="ts"
  setup
>
import { storeToRefs } from 'pinia';
import { TopTags } from '../../const/widget.ts';
import useChartDesignStore from '../../store/useChartDesignStore.ts';
import TagsBase from './components/TagsBase.vue';
import Chart from '../global/index.vue';

const refsStore = storeToRefs(useChartDesignStore());

const {
  chartType,
  metricsList,
  dimensionsList,
  chartStyle,
  auxiliaryList,
  previewData
} = refsStore;
</script>

<style
  lang="scss"
  scoped
></style>
