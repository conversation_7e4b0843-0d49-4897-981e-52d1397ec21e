<template>
  <div class="border-1 border-#ebebeb border-solid p-20px">
    <el-form label-position="left">
      <EventInfo
        v-model:name="solutionStore.shelvesPackage.name"
        v-model:description="solutionStore.shelvesPackage.description"
        v-model:versionNumber="solutionStore.shelvesPackage.versionNumber"
        v-model:memberCard="solutionStore.shelvesPackage.memberCard"
      />

      <el-button @click="solutionStore.addProduct" type="primary" size="small"
        >+ 添加产品</el-button
      >
      <div v-for="(product, pInx) in solutionStore.shelvesProductsDTOList" :key="product.shelvesProducts.id">
        <ProductSection :product="product" :index="pInx" :rackingData="rackingData" @delete="delProduct" />
      </div>
      <!-- <SubmitSection /> -->
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { useSolutionStore } from './store/index';
import EventInfo from './component/EventInfo.vue';
import ProductSection from './component/ProductSection.vue';
import { rackGroupType } from '@/views/mainview/sceneTasks/components/taskManagement/types';
// import SubmitSection from './component/SubmitSection.vue';

withDefaults(defineProps<{ rackingData?: rackGroupType[] }>(), {
  rackingData: () => [],
});

const solutionStore = useSolutionStore();

const delProduct = (id:number) => {
  solutionStore.shelvesProductsDTOList = solutionStore.shelvesProductsDTOList.filter(item => item.shelvesProducts.id  !== id)
}
</script>
