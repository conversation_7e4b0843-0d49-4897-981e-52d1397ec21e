import request from '@/axios';

// 查询来源消息类型
export const getDetailFromWechat = (id:string) =>
  request({
    url: '/wxCommon/transfer/detail',
    method: 'get',
    params: {
      id
    }
  });

// 获取微应用信息
export const getByMicroTaskActorIdFromWechat = (id:string) =>
  request({
    url: '/wxCommon/getByMicroTaskActorId',
    method: 'get',
    params: {
      id
    }
  });


//获取周知信息
export const getKnowledgeDetailFromWechat = (id:string) =>
  request({
    url: '/knowledge/detail',
    method: 'get',
    params: {
      id
    }
  });

// 获取信息审核
export const getDeptInviteVerifyRecordInfoFromWechat = (id:string) =>
  request({
    url: 'dept_invite_verify_record/info',
    method: 'get',
    params: {
      id
    }
  });
