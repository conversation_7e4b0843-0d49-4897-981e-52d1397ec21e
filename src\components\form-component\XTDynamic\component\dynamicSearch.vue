<template>
  <el-icon size="22" color="#606266" @click="handleClick" class="ml-5">
    <Search />
  </el-icon>

  <el-drawer 
    v-model="visible" 
    title="搜索"
    direction="rtl" 
    size="30%" 
    :append-to-body="true"
    :destroy-on-close="true"

  > 
    <el-button type="primary" @click="reset">重置</el-button>
    <xt-el-form
      v-model="editData" 
      :option="editOption">
    </xt-el-form>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-drawer>

 

</template>

<script setup lang="ts">
import XtElForm from '@/components/xt-el-form/XtElForm.vue';
import { XtElFormItemType,AvueColumns,AvueForm} from '@/components/form-config/types/AvueTypes';
import { AvueOption } from '@/components/form-config/types/AvueTypes';
import { fieldDefault,isEmptyValue } from '@/utils/field';
import { canSearchType, disabledType } from '@/components/form-config/types/FieldTypes'
import { FixTypeEnum } from '@/components/form-config/types/field.ts';

const props = withDefaults(
  defineProps<{
    originColumns:AvueColumns[];
    col: AvueColumns[];
    searchData: AvueForm 
  }>(),
  {

  }
);
const emits = defineEmits(['change']);

const visible = ref(false);
const editData = ref<AvueForm>()
const editOption = ref<AvueOption>({ column: [] });
provide('col', props.col)
const handleClick = () => {
  visible.value = true;
  const filteredColumns = props.originColumns
    .filter(item => item.display === true && _.includes(canSearchType,item.fixType))
    .map(item => {
      // 如果 fixType 在 disabledType 中，就替换成 'input'
      if (_.includes(disabledType,item.fixType)) {
        return { ...item, fixType: FixTypeEnum.INPUT,required: false };
      }
      return {...item,required: false};
  });
  editOption.value = {
    column: _.cloneDeep(filteredColumns as XtElFormItemType[] || []),
  };

}

const reset = () => { 
  editData.value = {}
}
const handleConfirm = () => {
  visible.value = false;
  emits('change', editData.value)
}

watchEffect(() => {
  editData.value = _.cloneDeep(props.searchData)

})

</script>

<style scoped></style>