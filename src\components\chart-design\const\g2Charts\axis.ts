import { CommonInitCallBack } from '../../types/g2Charts.ts';
import { AxisType, EChartsAxisStyle } from '../../types';

type DicType = {
  value: 'x' | 'y',
  label: AxisType,
  labelSpacing: number
}

const axisDic: DicType[] = [
  {
    value: 'x',
    label: 'xAxis',
    labelSpacing: 5
  },
  {
    value: 'y',
    label: 'yAxis',
    labelSpacing: 13
  }
];

export const AxisCommonInit: CommonInitCallBack<EChartsAxisStyle> = (chart, chartStyle) => {
  axisDic.forEach(axis => {
    chart.axis(axis.value, {
      title: chartStyle[`${axis.label}Label`] ?? '',
      labelFontSize: chartStyle[`${axis.label}LabelSize`] ?? 12,
      labelFill: chartStyle[`${axis.label}LabelColor`] ?? '#333',
      transform: [{
        type: 'rotate',
        optionalAngles: [chartStyle[`${axis.label}LabelRotate`] ?? 0],
        recoverWhenFailed: false
      }],
      labelSpacing: axis.labelSpacing,
      lineLineWidth: 1.1,
      lineStroke: chartStyle[`${axis.label}LineColor`] ?? '#333',
      lineStrokeOpacity: 100,
      line: true,
      labelAutoRotate: false
    });
  });
};