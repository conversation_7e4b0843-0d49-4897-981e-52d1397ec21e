<template>
  <div ref="dataContainer" class="overflow-hidden box-border flex justify-center items-center px-20px">
    <span :style="{ color: chartStyle.textColor, fontSize: size, lineHeight: `${height}px` }"
      class="overflow-hidden content-center">
      {{ data }}
    </span>
  </div>
</template>

<script lang="ts" setup>
import { useElementSize } from '@vueuse/core';
import { ChartProps, TextStyle } from '../../../types';

const props = defineProps<ChartProps<TextStyle>>();

const data = computed(() => props.chartData[0]?.[props.metrics[0]?.id] ?? 0);

const dataContainer = ref();

const { width, height } = useElementSize(dataContainer);

const size = computed(() => {
  return computeSize(width.value, height.value, _.toString(data.value)) + 'px';
});

const computeSize = (pWidth: number, pHeight: number, data: string) => {
  // 创建容器
  const container = document.createElement('div');
  container.innerText = data;
  container.style.position = 'absolute';
  document.body.appendChild(container);
  // 找到不会溢出的字体大小
  let fontSize = 10;
  while (fontSize < _.min([pWidth, pHeight])!) {
    container.style.fontSize = fontSize + 'px';
    if (container.clientWidth > pWidth || container.clientHeight > pHeight) {
      fontSize -= 2;
      break;
    }
    fontSize += 2;
  }
  // 消除容器
  container.remove();
  return fontSize;
};

</script>

<style lang="scss" scoped></style>