import { formulaCalculate } from '@/api/banban/field';
import { handleResponseData } from '@/utils/field';
import {
  getCalTableComponentValue,
  getStatisticalIndicatorsRequest,
  getTargetValueById
} from '@/api/taskManagement/componentFetch';
import { SubmitterEnum } from '@/components/form-config/const/commonDic';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore.ts';
import { fieldSource } from '@/components/form-config/const/commonDic';
import { 
  getGroupAdminByUserIds, 
  getGroupById, 
  getDeptAdminByUserIds, 
  getDeptById,
  getGroupAdminByGroupIds, 
  getDeptAdminByDeptIds 
} from '@/api/common/index'
import { useStore } from 'vuex';
import { tenantIdInjectKey, handleIdInjectKey } from '@/components/xt-el-form/constant.ts'
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { getSignature, getTriggerTimeByHandle, getAllRangeUser } from '@/api/common/index'
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';


interface ColumnData {
  userName: string
  value: string
  userId: string
}
export function useRequest(props:any, vModel:any,itemForm: any) {
  const store = useStore();
  const userMsg = store?.state.user.userInfo;
  const tenantId = inject(tenantIdInjectKey, null);//任务组织来源
  const handleId = inject(handleIdInjectKey, undefined);//应办id

  const { item, source } = props;
  const { taskBase } = storeToRefs(useTaskDetailStore());

  // 工作组管理员/部门管理员绑定成员选择/部门选择获取所有管理员
  const dataList = ref<any[]>([])

  const handleGetDeptAdmin = (userComponentType:string, ids: string[] ) => {
    if(userComponentType.includes(FixTypeEnum.USER_DEPT)){
      getDeptAdminByDept(ids)
    } else {
      getDeptAdminList(ids)
    }
  }

  const getDeptAdminByDept = async (ids: string[]) => {
    const { data } = await getDeptAdminByDeptIds({ids})
    dataList.value = data.data
    vModel.value = dataList.value.map(v => v.id)
  }
  
  const getDeptAdminList = async (ids: string[]) => {
    if (!_.isArray(ids) || !ids.length) return
    const params = {
      tenantId: tenantId?.value || userMsg.tenant_id,
      userIds: ids,
      isFilterOrg: props.item?.isFilterOrg
    }
    const { data } = await getDeptAdminByUserIds(params)
    dataList.value = data.data
    vModel.value = dataList.value.map(v => v.id)
  }

  const getDeptByIdList = async () => {
    if (!vModel.value) return
    const { data } = await getDeptById(vModel.value)
    dataList.value = data.data
  }

  const getGroupByIdList = async () => {
    if (!vModel.value) return
    const { data } = await getGroupById(vModel.value)
    dataList.value = data.data
  }
  // 工作组管理员/部门管理员绑定成员选择/工作组选择获取所有管理员
  const handleGetGroupAdmin = (userComponentType:string, ids: string[] ) => {
    if(userComponentType.includes(FixTypeEnum.USER_TAG)){
      getGroupAdminByDept(ids)
    } else {
      getGroupAdminList(ids)
    }
  }

  const getGroupAdminByDept = async (ids: string[]) => {
    const { data } = await getGroupAdminByGroupIds({ ids})
    dataList.value = data.data
    vModel.value = dataList.value.map(v => v.id)
  }
  const getGroupAdminList = async (ids: string[]) => {
    if (!_.isArray(ids) || !ids.length) return
    const params = {
      tenantId: tenantId?.value || userMsg.tenant_id,
      userIds: ids
    }
    const { data } = await getGroupAdminByUserIds(params)
    dataList.value = data.data
    vModel.value = dataList.value.map(v => v.id)
  }
  if (item.fixType === FixTypeEnum.DEPART_ADMIN || item.fixType === FixTypeEnum.WORKING_ADMIN) {
    watch(
      () => itemForm.value[item.userComponentType as string],
      (n: any) => {
        if (_.isArray(n) && n.length) {
          if (item.fixType === FixTypeEnum.DEPART_ADMIN) handleGetDeptAdmin(item.userComponentType,n)
          if (item.fixType === FixTypeEnum.WORKING_ADMIN) handleGetGroupAdmin(item.userComponentType,n)
        } else if(_.includes(item.userComponentType,FixTypeEnum.GET_USERCODE) && n){
          n = [n]
          if (item.fixType === FixTypeEnum.DEPART_ADMIN) handleGetDeptAdmin(item.userComponentType,n)
          if (item.fixType === FixTypeEnum.WORKING_ADMIN) handleGetGroupAdmin(item.userComponentType,n)
        }
         else {
          vModel.value = []
          dataList.value = []
        }
      },
      {
        immediate: true,
        deep: true
      }
    );

  }

   // 成员选择组件
  const getuserByDefaultType = async () => {
    if (![DefaultTypeEnum.ALLUSER, DefaultTypeEnum.EXTERNALUSER, DefaultTypeEnum.INTERNALUSER ].includes(item?.defaultType)) return
    const innerTypeObj = {
      [DefaultTypeEnum.ALLUSER]: undefined,
      [DefaultTypeEnum.EXTERNALUSER]: 1,
      [DefaultTypeEnum.INTERNALUSER]: 0
    }
    const { data } = await getAllRangeUser({ innerType: innerTypeObj[item.defaultType as keyof typeof innerTypeObj], tenantId: tenantId?.value || userMsg?.tenant_id })
    vModel.value = data.data.map((v:any) => v.userId)
    console.log(vModel.value,222)
  }

  // 签名组件
  const getUserInfo = async () => {
    if (item?.defaultType !== DefaultTypeEnum.SIGN) return
    const { data } = await getSignature({ userId: userMsg?.user_id, tenantId: tenantId?.value || userMsg?.tenant_id })
    vModel.value = data.data.signature
  }

  // 应办触发时间
  const getTriggerTime = async () => {
    if (item?.defaultType !== DefaultTypeEnum.TRIGGERTIME || !handleId) return
    const { data } = await getTriggerTimeByHandle(handleId!)
    vModel.value = data.data
  }
  //公式编辑组件
  const Calculate = async () => {
    if (source !== fieldSource.SET && item.formulaFilter.taskId && item.formula?.template) {
      const params = {
        formula: item.formula?.template,
        formulaFilter: item.formulaFilter,
        type: 1
      }
      const { data } = await formulaCalculate(params)
      vModel.value = handleResponseData(data.data)
    }

  }

  function getSubmitter() {
    const DicSubmitter: string[] = [SubmitterEnum.All, SubmitterEnum.Current]
    if (DicSubmitter.includes(item.submitterType)) return [];
    const userColumn = (taskBase.value as TaskBase).column?.find(column => column.id === item.usersComponentIds);
    if (!userColumn) return [];
    return userColumn.defaultValue;

  }


  // 统计指标
  const StatisticalRequest = async () => {
    const submitter = getSubmitter();
    const params = {
      timeRange: item.timeRange,
      versionId: item.versionId,
      taskId: item.taskId,
      statisticalRate: item.statisticalRate,
      submitter,
      submitterType: item.submitterType,
      usersComponentIds: item.usersComponentIds
    }
    getStatisticalIndicatorsRequest<{ list: ColumnData[], value: string }>(params)
      .then(res => {
        vModel.value = item.submitterType ? res.data.data?.list[0].value || '' : res.data.data?.value;
      });
  }


  // 目标值
  const TargetValueRequest = async () => {
    if (!item.targetId) return;
    getTargetValueById(item.targetId).then(res => {
      vModel.value = res.data?.data?.targetValue;
    });
  }

  const CalculationRequest = async () => {
    console.log(item);
    const { calTableFieldId, calTableId, filter } = item;
    if (!calTableFieldId || !calTableId) return;
    const params = {
      calTableFieldId,
      calTableId,
      filter,
      formData: itemForm.value,
      filterBoolean: !!filter?.conditions.length
    }
    const res = await getCalTableComponentValue(params);
    if (res.data?.data) {
      vModel.value = res.data.data
    }
  }


  return {
    Calculate,
    StatisticalRequest,
    TargetValueRequest,
    CalculationRequest,
    dataList,
    getDeptByIdList,
    getGroupByIdList,
    getDeptAdminList,
    getGroupAdminList,
    getUserInfo,
    getTriggerTime,
    getuserByDefaultType
  };
}