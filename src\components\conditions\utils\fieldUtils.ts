import { XtFieldType } from '@/components/form-config/types/FieldTypes.ts';
import { CommonSymbol, MethodSymbolEnum, SymbolItem, SymbolLinkage } from '@/components/conditions/option/field.ts';
import { dynamicSymbolList, SymbolList } from '../option/field.ts';
import { SymbolListTypes } from '@/components/conditions/types/types.ts';

/**
 * 直接获取规则字典{label,value}
 * @param type 字段类型
 * @param needEmpty 是否包含空选项
 * @returns
 */
export const getFieldSymbolDic = (
  type?: XtFieldType,
  needEmpty: boolean = true,
) => {
  const resultSymbolList=getFieldSymbolList(type,needEmpty)
  return transChineseSymbol(resultSymbolList);
};
/**
 *  获取字段对应的规则symbol数组[]
 * @param type 字段类型
 * @param needEmpty 是否包含空选项
 * @param isDynamic
 * @returns
 */
export const getFieldSymbolList = (
  type?: XtFieldType,
  needEmpty: boolean = true,
  isDynamic: boolean = false
) => {
  const symbol = getFieldSymbolConfig(type,isDynamic);
  let resultSymbolList = !_.isNil(symbol)? symbol.symbols : CommonSymbol;
  if (!needEmpty) resultSymbolList = resultSymbolList.filter(item => !CommonSymbol.includes(item));
  return resultSymbolList;
};



/**
 * 获取默认配置中的单个字段配置
 * @param type
 * @param isDynamic
 */
export const getFieldSymbolConfig = (type?: XtFieldType,isDynamic: boolean = false) => {
  return (isDynamic?dynamicSymbolList:SymbolList).find(item => _.includes(item.types, type));
};

export const transChineseSymbol = (resultSymbolList: MethodSymbolEnum[] = []) => {
  return resultSymbolList.map(item => {
    return {
      label: SymbolItem[item].Chinese,
      value: item
    };
  });
};

export const getFieldSymbolDicBySymbol = (
  type?: XtFieldType,
  customSymbolList: SymbolListTypes[]=SymbolList,
) => {
  const symbol = customSymbolList.find(item => _.includes(item.types, type));
  const resultSymbolList = !_.isNil(symbol) ? symbol.symbols : [];
  return resultSymbolList.map(item => {
    return {
      label: SymbolItem[item].Chinese,
      value: item
    };
  });
};

export const getFieldComponent = (
  symbol?: MethodSymbolEnum,
  type?: XtFieldType,
  isDynamic: boolean = false
) => {
  const list = isDynamic ? dynamicSymbolList : SymbolList;
  const symbolItem = list.find(item => _.includes(item.types, type));
  const item = symbolItem?.components?.find(item => _.includes(item.symbols, symbol));

  return item?.component;
};


export const getLinkageSymbolConfig = (type?: XtFieldType) => {
  return SymbolLinkage.find(item => _.includes(item.types, type));
};

export const getLinkageSymbolDic= (type?: XtFieldType) => {
  return getLinkageSymbolConfig(type)?.symbols;
};

export const getLinkageSymbolResult= (type?: XtFieldType,needSelf:boolean=true) => {
  const configResult=getLinkageSymbolConfig(type)?.result;
  let result=configResult?.find(item=>_.includes(item.types,type))?.result || [];

  if (needSelf){
    if (!_.includes(result,type)){
      type&&result.unshift(type)
    }
  }
  console.log(configResult,result,type,"getLinkageSymbolResult");
  return result
};