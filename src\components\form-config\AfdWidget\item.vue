<template>
  <component
    v-model="item.defaultValue"
    :is="getComponent(item.fixType as FixTypeEnum)"
    v-bind="item"
    :dic="(item as unknown as SelectField).dicData"
    :multiple="false"
    :placeholder="getPlaceholder(item)"
    :unInitData="false"
    :source="source"
    :item="item"
    class="w-full"
  >
  </component>
</template>

<script setup lang="ts">
import useComponents from '../hooks/useComponents.ts';
import {  FieldBaseField, SelectField, TitleField,CustomDefaultType, InputField } from '../types/AvueTypes.ts';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';

defineProps<{
  item: FieldBaseField & TitleField & CustomDefaultType & InputField;
  source?: number//组件的使用场景 1中间的组件配置 2 组件的默认值设置 3 预览 4 应办
}>();

defineOptions({
  name: 'widget-form-item',
});

const { getComponent, getPlaceholder } = useComponents();
</script>
