<template>
    <div>
        <avue-checkbox 
          class="dataBox" 
          all 
          v-model="showFieldIds" 
          placeholder="请选择内容" 
          :dic="showFormField" 
          :props="{label: 'copyLabel',value: 'id'}"
          />
    </div>
</template>

<script setup lang="ts">
import { AvueColumns} from '@/components/form-config/types/AvueTypes.ts';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
const props = defineProps<{
    data: AvueColumns;
    fieldList: AvueColumns[];
}>();

const vModel = defineModel<AvueColumns[]>('showField');
const showFieldIds = defineModel<string[]>('showFieldIds');

const showFormField = computed(() => {
    console.log(props.fieldList,'props.fieldList')
    let result: AvueColumns[] = [];
    switch (props.data.fixType) {
        case FixTypeEnum.SIGNATURE_COLLECT:
            result = props.fieldList.filter(item => item.fixType === FixTypeEnum.SIGNATURE);
            break;
        case FixTypeEnum.DATA_SELECTION:
          result = props.fieldList;
          break;
        default:
            break;
    }
    return result;
});


watch(
  () => showFieldIds.value, 
  (val) => {
    vModel.value = props.fieldList.filter(i => _.includes(val, i.id));
  },
  {deep:true}
)


</script>

<style scoped lang="scss">
.dataBox {
  :deep(.el-checkbox-group) {
    @apply flex-(~ col) ml-10px;
  }
}
</style>