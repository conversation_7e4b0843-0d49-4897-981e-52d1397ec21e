<template>
  <XtImportDialog
    v-model="visible"
    @cancel="handleCancel"
    @tempDown="downLoadTemplate"
    :on-success="handleSuccess"
    :upload-server="false"
    :check="handleCheck"
    @submit="handleSubmit"
  >
    <div class="w-full h-full flex items-center">
      <n-button text type="primary" @click="handleImport()" :focusable="false">批量导入</n-button>
    </div>
  </XtImportDialog>
</template>

<script setup>
import XtImportDialog from '@/components/nbxt/XtImportDialog.vue';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { addKeys } from '../utils';

const props = defineProps(['data']);

const visible = ref(false);

const handleImport = () => {
  visible.value = true;
};

//取消
const handleCancel = () => {
  visible.value = false;
};

const tempUrl = '/file/selectImportTemplate.xlsx';

//下载模板
const downLoadTemplate = () => {
  saveAs(tempUrl, '选项导入模板.xlsx');
};

let fileRaw = null;
const handleSuccess = (response, uploadFile, uploadFiles) => {
  fileRaw = uploadFile.raw;
};

const importDic = ref([]);
const defaultReason = '校验失败';

//进行校验
const handleCheck = () => {
  return new Promise(async (resolve, reject) => {
    try {
      //获取模板
      const tempV = await fetch(tempUrl).then(value => {
        return value.body.getReader().read();
      });
      const tempExcel = XLSX.read(tempV.value);
      //获得上传的excel
      const newV = await readFile(fileRaw);
      const newExcel = XLSX.read(newV);
      const result = checkXlsx(tempExcel, newExcel);

      if (!_.isString(result)) {
        importDic.value = result.result;
        resolve(result.len);
      } else {
        reject(result);
      }
    } catch (e) {
      console.log(e);
      reject(defaultReason);
    }
  });
};

const tempCol = ['A2', 'B2', 'C2', 'D2'];
//模板验证
const checkXlsx = (tempExcel, newExcel) => {
  if (!tempExcel.SheetNames.length || !newExcel.SheetNames.length) {
    return defaultReason;
  }

  const tempSheet = tempExcel.Sheets[tempExcel.SheetNames[0]];
  const newSheet = newExcel.Sheets[newExcel.SheetNames[0]];
  //对比A1 A2 B2 C2 D2 此处为模板部分
  if (
    !tempCol.every(item => {
      return tempSheet[item]?.v === newSheet[item]?.v;
    })
  ) {
    return '模板校验不通过';
  }
  //检查数据部分
  let sheetToJson = XLSX.utils.sheet_to_json(newSheet, {
    header: 1,
  });
  sheetToJson = sheetToJson.filter((item, index) => index > 1 && !_.isEmpty(item));
  if (_.isEmpty(sheetToJson)) {
    return '请确定有符合条件的数据';
  }
  //验证4000行总行数
  if (sheetToJson.length > 4000) {
    return '总行数超过4000行';
  }
  //进行筛选
  sheetToJson = sheetToJson.filter(item => {
    if (item.length > 4) {
      //只要四级的
      return false;
    }
    for (let i = 0; i < item.length; i++) {
      if (i > 0 && _.isEmpty(item[i - 1])) {
        //过滤中间空的
        return false;
      } else if (item[i]?.length > 50) {
        //过滤字数超过50的
        return false;
      }
    }
    return true;
  });
  //去重
  sheetToJson = _.uniqWith(sheetToJson, _.isEqual);
  //生成数据
  const dicFlat = sheetToJson.map(arrayToDic);
  const r = groupArray([[], ...dicFlat]);

  //验证每一级的条数
  const checkLevel = checkLevelNum(r);
  if (!checkLevel) {
    return '某一级数据超过300条';
  }

  return {
    result: r,
    len: sheetToJson.length,
  };
};
//数组转dic
const arrayToDic = array => {
  const zipKey = [];
  for (let i = 0; i < array.length; i++) {
    const repeat = _.repeat('children[0].', i);
    zipKey.push(repeat + 'label');
  }
  return _.zipObjectDeep(zipKey, array);
};

//数组组合
const groupArray = array => {
  return _.reduce(array, (prev, curr, index, list) => {
    const find = _.find(prev, o => o.label === curr.label);
    if (_.isEmpty(find)) {
      //没找到就push
      return [...prev, curr];
    } else {
      //找到就处理下一级
      if (curr.children) {
        if (find.children) {
          find.children.push(...curr.children);
          find.children = groupArray([[], ...find.children]);
        } else {
          find.children = [...curr.children];
        }
      }
    }
    return prev;
  });
};

//验证每级条数
const checkLevelNum = array => {
  if (!_.isArray(array)) {
    return true;
  } else if (array.length > 300) {
    console.log('超出数量', array);
    return false;
  }
  for (let i = 0; i < array.length; i++) {
    const checkChildren = checkLevelNum(array[i]?.children);
    if (!checkChildren) {
      return false;
    }
  }
  return true;
};

//读取文件
const readFile = file => {
  const reader = new FileReader();
  reader.readAsArrayBuffer(file);

  return new Promise(resolve => {
    reader.onload = e => {
      resolve(e.target.result);
    };
  });
};

//完成导入
const handleSubmit = () => {
  addKeys(importDic.value);
  props.data.dicData = importDic.value;
  visible.value = false;
};
</script>

<style lang="scss"></style>
