<template>
  <div>
    <el-dialog
      v-model="visible"
      title="选择物品"
      width="800"
      :append-to-body="true"
      @close="visible = false"
    >  
      <selectDataComponent 
        :tab-list="tabList" 
        v-model="model" 
        :visible="visible" 
        :config="config" 
      />
      <template #footer>
        <div class="flex justify-end">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button type="primary" @click="visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import selectDataComponent from '@/components/select/selectDataComponent.vue';
import { useStore } from 'vuex';
import { getArticleByOrgTree, getArticleByGroup } from '@/api/common/index';
import { articleType } from '@/api/common/type'

const store = useStore();
const userMsg = store.state.user.userInfo;
const model = ref<articleType.ResDataParams[]>([]);
const visible = defineModel<boolean>('visible', { default: false });
const emit = defineEmits<{
  'submit': [value: any[]];
}>();

//配置项 单选多选之类的
interface configType {
  isRadio?: boolean;
  tid?:string
}

interface DrawerProps {
  modelList: any[];
  submitApi?: (params: any) => Promise<any>;
  getTableList?: () => void; //保存后请求表格数据
  dataCallBack?: (data: any) => any; //处理提交的数据
  config?: configType;
}
const props = defineProps<DrawerProps>();


const tabList = [
    {
        name: "first",
        label: "物品选择",
        api: getArticleByOrgTree,
        apiParams: { tid: props.config?.tid ?? userMsg.tid },
        leftProp: { children: "children", label: "warehouseName", value: "id", leftSearchId: "groupIds" },
        rightProp: { label: "goodsName", id: "id" },
        getDataApi: getArticleByGroup
    }
]

watch(
  () => visible,
  () => {
    model.value = [...props.modelList];
  },
  { deep: true, immediate: true }
);

const handleSubmit = async () => {
  if (props.config?.isRadio) {
    if (model.value.length > 1) {
      ElMessage.warning('只能选择一个数据，请重新选择');
      return;
    }
  }
  let data = model.value;
  props.dataCallBack && (data = props.dataCallBack(data));
  if (props.submitApi) {
    try {
      await props.submitApi(data);
      props.getTableList!();
      ElMessage.success('保存成功');
      visible.value = false;
    } catch (error) {
      ElMessage.warning('保存失败');
    }
  } else {
    emit('submit', data);
    // visible.value = false;
    
  }
};
</script>
