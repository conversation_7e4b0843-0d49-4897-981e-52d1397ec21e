<template>
  <div
    :style="{'background-color': chartStyle.backgroundColor ?? 'white'}"
    class="w-full h-full box-border relative"
  >
    <Title
      class="absolute left-0 top-16px"
      v-bind="chartStyle"
    />
    <div
      v-if="showChart"
      class="absolute inset-16px top-40px"
    >
      <component
        :is="componentName"
        class="size-full"
        v-bind="{...$props,...requiredPreviewData}"
      />
    </div>
    <el-empty
      v-else
      class="size-full text-text-secondary"
      description="description"
      image="/img/empty.png"
    >
      <template #description>
        <span
          v-if="fromList"
        >暂无数据</span>
        <span
          v-else
        >拖拽左侧字段到上方<span class="text-primary-base">维度</span>、<span class="text-primary-base">指标栏</span>来添加数据</span>
      </template>
    </el-empty>
  </div>
</template>

<script
  lang="ts"
  setup
>
import Title from './title.vue';
import Charts from './charts';
import { ChartTypeEnum, GlobalProps, PreviewDataType } from '../../types';
import { ShowChart } from '../../const/widget.ts';

const props = withDefaults(
  defineProps<Partial<GlobalProps> & {
    fromList?: boolean;
  }>(),
  {
    chartType: () => ChartTypeEnum.TEXT,
    dimensions: () => [],
    metrics: () => [],
    auxiliary: () => [],
    chartStyle: () => ({}),
    previewData: () => ({}),
    fromList: () => true
  }
);

const componentName = computed(() => {
  return Charts[props.chartType + '_chart'];
});

const requiredPreviewData = computed<PreviewDataType>(() => {
  return {
    chartData: props.previewData.chartData ?? [],
    targetList: props.previewData.targetList ?? [],
    series: props.previewData.series ?? []
  };
});

const showChart = computed(() =>
  ShowChart[props.chartType](props.dimensions, props.metrics) &&
  !_.isEmpty(requiredPreviewData.value.chartData)
);
</script>

<style
  lang="scss"
  scoped
>

</style>