import { 
  FieldBaseField, 
  CustomDefaultType, 
  DataLinkField, 
  FormulaField, 
  DataFilter, 
  appDataLinkField,
  ruleType,
  templateField,
  permissionType,
  linkFieldsType,
  SelectField,DynamicField
} from '@/components/form-config/types/AvueTypes';

//默认值 

export type configDefaultValueType = {
  data: FieldBaseField & Partial<permissionType> & (CustomDefaultType | DataLinkField | FormulaField | DataFilter | appDataLinkField) 
  & ruleType & Partial<templateField> & Partial<SelectField> & Partial<DynamicField> & Partial<linkFieldsType>;
}