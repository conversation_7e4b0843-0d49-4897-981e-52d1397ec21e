import { ChartProps, EChartsCommonStyle, LegendPositionEnum } from '../types';
import { Chart } from '@antv/g2';
import { watchDebounced } from '@vueuse/core';
import { LegendPositionDic } from '../const/g2Charts/legend.ts';
import { CommonInit } from '../const/g2Charts';
import { initGuideLine } from '../const/g2Charts/guideLine.ts';
import { SortEnum } from '../types/widget.ts';

const useG2Option = <T extends EChartsCommonStyle>(
  props: ChartProps<T>,
  optionChart: (chart: Chart) => void
) => {
  const g2Container = ref();

  let chart: any;

  onMounted(() => {
    watchDebounced([
      () => props.chartData,
      () => props.chartStyle,
      () => props.targetList
    ], () => {
      renderBarChart();
    }, {
      deep: true,
      immediate: true,
      debounce: 500
    });
  });

  const renderBarChart = () => {
    if (chart) {
      chart.clear();
    } else {
      chart = new Chart({
        container: g2Container.value,
        autoFit: true
      });
    }

    // 数据、排序
    const sorts = [...props.dimensions, ...props.metrics]
      .filter(item => item.sort === SortEnum.ASC || item.sort === SortEnum.DESC);
    chart.data({
      type: 'inline',
      value: props.chartData,
      transform: sorts.map(item => ({
        type: 'sort',
        callback: (a: any, b: any) => {
          const value = a[item.id];
          const other = b[item.id];
          const gt = item.sort === SortEnum.ASC ? 1 : -1;

          if (_.eq(value, other)) {
            return 0;
          }
          if (_.gt(value, other)) {
            return gt;
          }
          return -gt;
        }
      }))
    });
    // 独特配置
    optionChart(chart);

    // 图例
    if (props.chartStyle.showLegend) {
      chart.legend('color', {
        itemMarker: 'circle',
        position: LegendPositionDic
          .find(item => item.value === (props.chartStyle.legendPosition ?? LegendPositionEnum.BOTTOM))!
          .value,
        layout: {
          justifyContent: 'center',
          alignItems: 'center'
        }
      });
    } else {
      chart.legend('color', false);
    }

    // 辅助指标
    initGuideLine(chart, props.chartType, props.targetList);

    // 统一配置
    CommonInit[props.chartType]?.forEach(init => init(chart, props.chartStyle, props.chartType));

    // 渲染
    try {
      chart.render();
    } catch (_) {
    }
  };

  return {
    g2Container
  };
};

export default useG2Option;