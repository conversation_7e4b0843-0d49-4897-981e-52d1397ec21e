export enum SelectTypeEnum {
  RADIO,
  CHECKBOX,
  SINGLE_SELECT,
  MULTIPLE_SELECT,
  CASCADER,
}

export enum SelectColumnTypeEnum{
  RADIO = 'radio',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  CASCADER = 'cascader'
}

export const selectTypeDic = [
  {
    value: 0,
    label: '单项选择',
    column: {
      type: SelectColumnTypeEnum.RADIO,
    },
  },
  {
    value: 2,
    label: '下拉选择',
    column: {
      type: SelectColumnTypeEnum.SELECT,
      multiple: false,
    },
  }
];

export const mulSelectTypeDic = [
  {
    value: 1,
    label: '多项选择',
    column: {
      type: SelectColumnTypeEnum.CHECKBOX,
    },
  },
  {
    value: 3,
    label: '下拉多选',
    column: {
      type: SelectColumnTypeEnum.SELECT,
      multiple: true,
    },
  },
  {
    value: 4,
    label: '层级选择',
    column: {
      type: SelectColumnTypeEnum.CASCADER,
      multiple: false,
      component: 'XtCascader',
    },
  },
];


