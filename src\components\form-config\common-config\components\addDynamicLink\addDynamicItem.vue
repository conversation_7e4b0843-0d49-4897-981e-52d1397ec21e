<template>
    <div class="mt-2">
        <n-card class="mt-1 mb-1">
            <el-row :gutter="10">
                <el-col :span="6">
                  <el-select v-model="dynamicItem.currentProp" clearable>
                    <el-option
                      v-for="item in currentFieldList"
                      :key="item.prop"
                      :label="item.copyLabel"
                      :value="(item.prop as string)"
                      :disabled="isPropExist((item.prop as string))"
                    />
                  </el-select>
                </el-col>
                <el-col :span="2" class="!flex flex-items-center flex-justify-center">取</el-col>
                <el-col :span="6">
                  <el-input v-model="pushTaskLabel" disabled></el-input>
                    <!-- <selectTask 
                        v-model:taskId="pushTaskId" 
                        :disabled="true" width="w-full" 
                        :taskList="taskList"
                        title="请选择推送数据路径">
                    </selectTask> -->
                </el-col>
                <el-col :span="6">
                    <avue-select 
                        class="w-full" 
                        :dic="filterList"
                        :props="{ label: 'copyLabel', value: 'prop', type: 'prop' }" 
                        v-model="dynamicItem.pushProp"
                        placeholder="请选择字段">
                    </avue-select>
                </el-col> 
                <el-col :span="2" class="!flex flex-items-center flex-justify-center">
                    <el-icon @click="$emit('delete')" color="#f44">
                        <Delete />
                    </el-icon>
                </el-col>
            </el-row>

        </n-card>

    </div>
</template>

<script lang="ts" setup>
import { 
  FieldBaseField, 
  AvueColumns,
  FormulaField
} from '@/components/form-config/types/AvueTypes.ts';
import { DynamicAllField }from  '@/components/form-config/types/AvueTypes'
import { pushTaskListType } from '@/api/interface/task';
import { dynamicType } from '@/api/interface/configDataLink.ts';
import {
  FieldTypes,
  separateType,
  dynamicUnLinkType,
} from '@/components/form-config/types/FieldTypes';
import { 
  submitterType, 
  submitterTimeType, 
  triggerTimeType, 
  endUpdateTimeType,
  endUpdateUserType, 
  createList 
} from '@/components/form-config/types/FieldTypes';
import { editKnowType } from '@/api/interface/knowLedgeType'
import { generateId } from '@/components/form-config/utils';
import {SystemTypeEnum} from '@/components/form-config/types/field'

const systemLinkTypes = [...submitterType, ...submitterTimeType, ...triggerTimeType]
const systemDataLinkTypes = [...endUpdateTimeType, ...endUpdateUserType]


const props = withDefaults(defineProps<{
    data: FieldBaseField ;
    taskList: pushTaskListType[] | editKnowType[];
    dynamicItem:dynamicType;
    dynamicFields:dynamicType[]
    pushDynamicField: AvueColumns[];
    isDynamic?:boolean;
    pathType?: number
}>(), {
  dynamicItem: () =>{
    return { currentProp: '', pushProp: '', idx: generateId() };
  },
  isDynamic: false,
  pathType: 0,
  pushDynamicField: () => []
});

defineEmits(['delete']);

const pushTaskId = defineModel<string>('pushTaskId');

const pushTaskLabel = computed(() => {
  return props.taskList.find(item => item.value === pushTaskId.value)?.label;
});

//系统字段
const systemFields = computed(() => {
  if(props.isDynamic) return []
  const types =  props.pathType === 0 ? systemLinkTypes : systemDataLinkTypes
  return createList.filter(item => types.includes(item.fixType as SystemTypeEnum));
})

//当前表格组件列表
const currentFieldList = computed(() => {
  return (props.data as DynamicAllField).children.filter((i:AvueColumns & FormulaField ) => !_.includes(dynamicUnLinkType,i.fixType) && _.isEmpty(i.formula))
})

//当前表格组件的类型
const fieldType = computed(() => {
  const filteredItem = currentFieldList.value.find(item => item.prop === props.dynamicItem?.currentProp);
  return filteredItem ? filteredItem.fixType : ''
})

const isPropExist = (prop: string): boolean => {
  return props.dynamicFields && props.dynamicFields.some(item => item.currentProp === prop);
};

//推数路径表格的组件匹配当前表格组件的类型
const filterList = computed(() => {
  let result = [...props.pushDynamicField, ...systemFields.value];
  const filterResult = <T extends FieldTypes>(types: T) => {
    result = result.filter((i:any) => _.includes(types, i.fixType ));
  };
  separateType(fieldType.value, {
    inputType: types => {
      filterResult(types);
      // result.push(createList[0]);
    },
    dateType: types => {
      const dataTypes = props.pathType === 0 ? [...submitterTimeType, ...triggerTimeType] : endUpdateTimeType
      const newTypes = props.isDynamic? types: [...types, ...dataTypes]
      filterResult(newTypes);
      // result.push(createList[1]);
    },
    dateRangeType: filterResult,
    numberType: filterResult,
    // 选择
    selectType: types => {
      filterResult(types);
    },
    mulSelectType: filterResult,
    mulCascaderType: filterResult,
    addressType: filterResult,
    uploadType: filterResult,
    photoType: filterResult,
    tipsType: filterResult,
    usersType: types => {
      //展示字段那里 成员选择可以取其他任务的提交人(submitUser)?或成员选择 日期时间可以取其他任务日期时间或触发时间(triggerTime)或提交时间(submitTime) (会办和数据选择)
      //数据表 最后更新人和最后更新时间
      const userType = props.pathType === 0 ? submitterType : endUpdateUserType
      const newTypes = props.isDynamic? types: [...types,...userType ]
      filterResult(newTypes);
    },
    userTagType: filterResult,
    userDeptType: filterResult,
    signatureType: filterResult,
    signatureCollectType: filterResult,
    inventoryComType: filterResult,
    interspaceComType: filterResult,
    departAdminType: filterResult,
    workingAdminType: filterResult,
    dynamicType: filterResult,
  });

  return result;
});

watch(
  () => props.dynamicItem.currentProp,
  () => {
    props.dynamicItem.pushProp = ''
  }
);


</script>