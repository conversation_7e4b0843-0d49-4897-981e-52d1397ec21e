<template>
  <config-title :data="data" />
  <config-permission :data="data" :show-edit="false" :has-required="false" />

  <el-form-item label="计算范围">
    <el-date-picker
      v-model="data.timeRange"
      clearable
      :editable="false"
      type="daterange"
      range-separator="-"
      placeholder="年月日-年月日"
      value-format="YYYY-MM-DD"
      @select="handleChange"
    ></el-date-picker>
  </el-form-item>

  <el-form-item label="数据路径">
    <span v-if="!data.submitterType" class="text-green cursor-pointer flex items-center" @click="addFilterFn"><el-icon><Plus></Plus></el-icon> 添加筛选条件</span>
    <span v-else class="cursor-pointer text-gray" @click="addFilterFn">
      提交人等于: {{ currentChoose }}
    </span>
    <xt-select
      v-model="data.versionId"
      :dic="versionList"
      :props="{ label: 'groupName', value: 'id' }"
      @change="(id:string) =>(handleChange(),handGetTaskListByVersionId(id)) "
    >
    </xt-select>

    <xt-select
      class="my-3"
      :dic="taskList"
      v-model="data.taskId"
      @select="handleChange"
      filterable
    ></xt-select>

    <xt-select
      v-model="data.statisticalRate"
      :dic="statisticalDic"
      @select="handleChange"
    ></xt-select>
  </el-form-item>


  <config-span :data="data" />

  <el-dialog title="筛选条件" v-model="visible" width="500">
    <el-form label-position="top">
      <el-form-item label="提交人等于">
        <avue-select :dic="submitterDic" v-model="dialogForm.submitterType">
        </avue-select>
      </el-form-item>
      <el-form-item label="成员选择" v-if="dialogForm.submitterType === SubmitterEnum.User">
        <avue-select :dic="usersOption" v-model="dialogForm.usersComponentIds">
        </avue-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="visible = false">取 消</el-button>
        <el-button @click="submit" type="primary">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import { statisticalDic, submitterDic, SubmitterEnum } from './const';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { getStatisticalIndicatorsRequest } from '@/api/taskManagement/componentFetch';
import { getVersionListByGroupRequest, getTaskListByVersionId  } from '@/api/taskManagement/common.ts';
import { StatisticalAllType } from './type';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';

interface VersionType {
  groupName:string
  id:string
}
interface DialogForm {
  submitterType:string
  usersComponentIds?:string
}

const props = defineProps<{
  data: StatisticalAllType;
}>();

const { data } = toRefs(props);

const versionList = ref<VersionType[]>([]);
const taskList = ref<any[]>([]);
const visible = ref<boolean>(false);
const dialogForm = ref<DialogForm>({submitterType:''});


const {  taskBase } = storeToRefs(useTaskDetailStore());


const addFilterFn = () => {
  visible.value  = true;
}

const usersOption = computed(() => {
  const userFields = (taskBase.value as TaskBase).column?.filter(column => column.fixType === 'users') || [];
  return userFields.map(i=>{
    return {
      label:i.copyLabel,
      value:i.id
    }
  })
})

const currentChoose = computed(()=> {
  const submitterType = props.data.submitterType;
  if(submitterType === SubmitterEnum.User){
    return usersOption.value?.find(i => i.value === props.data.usersComponentIds)?.label
  }else{
    return submitterDic.find(i => i.value === submitterType)?.label
  }
})

// 当五个依赖性改变时数据
const handleChange = _.throttle(async () => {
  const {  timeRange, versionId, taskId, statisticalRate, submitterType, usersComponentIds } = props.data;
  if(!versionId) return;
  const params = {
    timeRange,
    versionId,
    taskId,
    statisticalRate,
    // submitter,
    submitterType,
    usersComponentIds
  }
  const { data:res } = await getStatisticalIndicatorsRequest(params);
  props.data.defaultValue = submitterType ?  res?.data.list?.[0]?.value || '' : res.data.value;

},500)

const handleGetVersionList = async () => {
  const { data: res } = await getVersionListByGroupRequest();
  versionList.value = res.data || [];
}

const handGetTaskListByVersionId = async (versionId:string) => {
  const { data: res } = await getTaskListByVersionId(versionId);
  taskList.value = res.data || [];
}

handleGetVersionList();

if(props.data.versionId){
  handGetTaskListByVersionId(props.data.versionId);
}

const submit = () => {
  props.data.submitterType = dialogForm.value.submitterType as SubmitterEnum;
  props.data.usersComponentIds = dialogForm.value.usersComponentIds as string;
  visible.value = false;
  handleChange()
}

</script>
<style lang="scss" scoped>




</style>
