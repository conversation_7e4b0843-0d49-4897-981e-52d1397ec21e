<script setup lang="jsx">
import {
  getAppFormColumn,
  getAppFormPrintFile,
  exportDataManage,
  getRelatedToMePage
} from '@/api/application';
import { downloadFile } from '@/utils/client';
import { getOrgInfo } from '@/api/orgInfo';
import { getDic } from '@/views/banban/application/flowApp/approvalProcess/scWorkflow/options/dic';
import { ViewMode } from '@/views/banban/application/flowApp/option/enum.ts';
import { addToWps } from '@/api/banban/ledger';
import PrintFile from '@/views/banban/know/electronicLedger/components/printFile.vue';
import { ElMessage, ElLink, ElCheckbox } from 'element-plus';
import { Loading as LoadingIcon } from '@element-plus/icons-vue'
import { unref } from 'vue'
import ExportDataManageDialog from '@/views/banban/application/flowApp/approvalProcess/components/exportDataManageDialog.vue';
import { downloadXls } from '@/utils/util';

const router=useRouter()

const route=useRoute()

const appDetail=ref({})

const loading=ref(false)

const crudRef=ref(null);

const query=ref({})

const id=ref(route.query.id)


const page = ref({
  total: 0,
  currentPage: 1,
  pageSize: 20
})
const noFormData=ref([]);

const orgInfo=ref({})
const getOrgInfoHandle = async ()=>{
  if (!orgInfo.value.id){
    orgInfo.value=(await getOrgInfo()).data?.data || {}
  }
  return orgInfo.value
}

//获取表单column及提交数据
const initFormColumn=()=>{
  getAppFormColumn(id.value).then(async res=>{
    appDetail.value=res.data.data;
    await getOrgInfoHandle();
    return initFormData()
  })
}

initFormColumn()

const searchHandle=(form,done)=>{
  initFormData()

  done()
}
const searchQuery=computed(()=>{
  return {
    createStartTime: query.value.createTime?.[0] || '',
    createEndTime: query.value.createTime?.[1] || '',
    finishStartTime: query.value.endTime?.[0] || '',
    finishEndTime: query.value.endTime?.[1] || '',
    instanceState: query.value.instanceState,
    createUserName: query.value.createBy,
  };
})

const initFormData=()=>{
  page.value.currentPage = 1;
  page.value.pageSize = 20;
  loading.value = true;
  getRelatedToMePage({
    current:page.value.currentPage,
    size:page.value.pageSize,
    microAppId:id.value,
    ...searchQuery.value,
  }).then(res=>{
    page.value.total=res.data.data.total
    page.value.pageSize=res.data.data.size
    page.value.current=res.data.data.current
    noFormData.value = res.data.data.records;
    loading.value = false;
  })
}

const searchOption=ref({
  span:5,
  menuSpan: 4,
  submitText:"搜索",
  column:[
    {
      label:"发起时间",
      prop:"createTime",
      valueFormat:"YYYY-MM-DD",
      format:"YYYY-MM-DD",
      type:"daterange",
    },
    {
      label:"发起人",
      prop:"createBy"
    },
    {
      label:"完成时间",
      prop:"endTime",
      valueFormat:"YYYY-MM-DD",
      format:"YYYY-MM-DD",
      type:"daterange",
    },
    {
      label:"审批状态",
      prop:"instanceState",
      type:"select",
      dataType:"string",
      dicData:getDic("instanceState")
    },
  ]
})

const currentFile = ref({});
const printDrawer = ref(false);
// 查看查看文件
async function toPrint(row){
  const res = await getAppFormPrintFile({
    instanceId:row.instanceId,
    microAppId:id.value
  });
  if(!res.data.data) return;
  const name = row.createBy + '.' + res.data.data.split('.').pop() ;
  addToWps({fileName:name,fileUrl:res.data.data}).then(res=>{
    if(res.data.success){
      let data = res.data.data;
      currentFile.value = {
        fileId: data.fileId,
        fileName: data.name,
        fileUrl: data.url,
        size: data.size,
        downBtn:true
      }
      printDrawer.value = true;
    }
  })
}

const prevFile=(row)=>{
  getAppFormPrintFile({//之前的查看文件
    instanceId:row.instanceId,
    microAppId:id.value
  }).then(res=>{
    if (res.data.data){
      downloadFile(res.data.data)
    }
  })
}

// 跳到详细页面去看数据
const rowView = (row) => {
  const { instanceId} = row;
  router.push({
    path: '/banban/application/editFlowApp',
    query: {
      instanceId,
      appId:id.value,
      name: appDetail.value.appName,
      type: 3, //代办微应用
      disabled:1,
      viewMode:ViewMode.View,
    },
  });
}

const SelectionCell= ({ value, intermediate = false, onChange, }) => {
  return (
    <ElCheckbox
      onChange={onChange}
      modelValue={value}
      indeterminate={intermediate}
    />
  )
}

const v2Columns = [
  {
    key: 'selection',
    width: 70,
    cellRenderer: ({ rowData }) => {
      const onChange = (value) => (rowData.checked = value)
      return <SelectionCell value={rowData.checked} onChange={onChange} />
    },
    headerCellRenderer: () => {
      const _data = unref(noFormData)
      const onChange = (value) =>
        (noFormData.value = _data.map((row) => {
          row.checked = value
          return row
        }))
      const allSelected = _data.every((row) => row.checked)
      const containsChecked = _data.some((row) => row.checked)

      return (
        <SelectionCell
          value={allSelected}
          intermediate={containsChecked && !allSelected}
          onChange={onChange}
        />
      )
    },
  },
  {
    title:"审批编号",
    dataKey:"serialNumber",
    key:"serialNumber",
    width: 250,
  },
  {
    title:"发起人",
    dataKey:"createBy",
    key:"createBy",
    width: 250,
  },
  {
    title:"发起人部门",
    dataKey:"createDept",
    key:"createDept",
    width: 200,
  },
  {
    title:"发起时间",
    dataKey:"createTime",
    key:"createTime",
    width: 220,
  },
  {
    title:"完成时间",
    dataKey:"endTime",
    key:"endTime",
    width: 220,
  },
  {
    title:"审批状态",
    dataKey:"instanceState",
    key:"instanceState",
    width: 130,
    cellRenderer:({cellData}) => (
      <span>{getDic("instanceState").find(t=>t.value == cellData).label}</span>
    )
  },
  {
    title:"打印文件",
    dataKey:"printFile",
    key:"printFile",
    width: 100,
    cellRenderer:({rowData}) => (
      <>
        <ElLink onClick={() => toPrint(rowData)} type="primary">文件打印</ElLink>
      </>
    )
  },
  {
    title:"文件配置",
    dataKey:"attachFiles",
    key:"attachFiles",
    width: 100,
    cellRenderer:({cellData}) => (
      <>
        {
          cellData?.map(file => {
            return <ElLink type="primary" onClick={() => downloadFile(file.fileUrl)}>{file.fileName}</ElLink>
          })
        }
      </>
    )
  },
  {
    title:"操作",
    dataKey:"handle",
    key:"handle",
    width: 100,
    cellRenderer:({rowData,rowIndex}) => (
      <div class="flex gap-4">
        <ElLink type="primary" onClick={() => rowView(rowData, rowIndex)}>查看</ElLink>
      </div>
    )
  },


]

const getNextFormData=()=>{
  loading.value = true;
  getRelatedToMePage({
    current:page.value.currentPage,
    size:page.value.pageSize,
    microAppId:id.value,
    ...searchQuery.value,
  }).then(res=>{
    page.value.total=res.data.data.total
    page.value.pageSize=res.data.data.size
    page.value.current=res.data.data.current
    noFormData.value.push(...res.data.data.records || []);
    loading.value = false;
  })
}

const toEnd = () => {
  page.value.currentPage = page.value.currentPage + 1
  getNextFormData()
}


/***** 导出相关 ******/
const dialogVisible = ref(false);

async function handleExport(formQuery) {

  const checkedList = noFormData.value.filter(item=>item.checked).map(i => i.instanceId);
  if(formQuery.type === '2' && _.isEmpty(checkedList)){
    return ElMessage.warning("请选择导出的数据");
  }
  const query = {
    microAppId:id.value,
    instanceIds:formQuery.type === '2' ? checkedList : []
  }
  const name = formQuery.fileName || appDetail.value.appName
  const res = await exportDataManage(query);
  downloadXls(res.data, `${name}.xlsx`);
  dialogVisible.value = false;
}

</script>

<template>
  <div class="flex flex-col h-full mx-10px">
    <el-card class="mb-10px">
      <div class="inline-flex items-center cursor-pointer" @click="router.back()">
        <el-icon class="mr-5px"><ArrowLeft /></el-icon>
        {{ appDetail.appName }}
      </div>
    </el-card>
    <el-card class="flex-1">
      <avue-form
        @submit="searchHandle"
        @reset-change="searchHandle"
        v-model="query"
        :option="searchOption"
      ></avue-form>
      <div class="flex mb-2">
        <el-button type="primary" @click="dialogVisible = true">导出</el-button>
      </div>

      <el-auto-resizer style="height: 520px">
        <template #default="{ height, width }">
          <el-table-v2
            :columns="v2Columns"
            :data="noFormData"
            :width="width"
            :height="height"
            fixed
            @end-reached="toEnd"
            border
          >
            <template #overlay v-if="loading">
              <div
                class="el-loading-mask"
                style="display: flex; align-items: center; justify-content: center"
              >
                <el-icon class="is-loading" color="var(--el-color-primary)" :size="26">
                  <loading-icon />
                </el-icon>
              </div>
            </template>
          </el-table-v2>
        </template>
      </el-auto-resizer>
    </el-card>
    <print-file v-model="printDrawer" v-model:file="currentFile" :fileType="''"></print-file>
    <ExportDataManageDialog v-model="dialogVisible" @submit-callback="handleExport" />
  </div>
</template>

<style scoped lang="scss"></style>
