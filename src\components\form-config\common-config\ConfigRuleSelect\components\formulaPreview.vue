<template>
    <el-dialog  
        v-model="visible"
        title="公式预览"
        width="700"
    >
      <b-formula v-model="formula" :formulaType="FormulaType.DATA" :fieldList="fieldList" :valText="data.copyLabel"/>  
    </el-dialog>
</template>

<script setup lang="ts">
import BFormula from '@/components/codemirror/BFormula.vue';
import { FormulaType } from '@/api/formula';


withDefaults(
    defineProps<{
        fieldList: any;
        data: any
    }>(),
    {
        fieldList: () => {
            return {}
        },
    }
);

const visible = defineModel('visible', { default: false })
const formula = defineModel('formula')
// const filterList = 


</script>

<style lang="scss" scoped>
::v-deep(.container) {
  max-height: 340px; /* 限制最大高度 */
}
</style>
