<template>
  <el-tree
    ref="listingTreeRef"
    :props="treeProps"
    nodeKey="id"
    show-checkbox
    :data="rackingData"
    check-strictly
    :default-checked-keys="vModel"
    @check="onCheck"
  >
    <template #default="{ data }">
      <div class="flex items-center mt-8px">
        <el-tag size="small" :type="data.sceneGroupFlag ? 'primary' : 'success'">{{
          data.sceneGroupFlag ? '分组' : '任务'
        }}</el-tag>
        <span class="ml-8px">{{ data.groupName || data.taskName }}</span>
      </div>
    </template>
  </el-tree>
</template>
<script setup lang="ts">
import { rackGroupType } from '@/views/mainview/sceneTasks/components/taskManagement/types';

withDefaults(defineProps<{ rackingData?: rackGroupType[] }>(), {
  rackingData: () => [],
});

const treeProps: any = {
  children: 'extendList',
  // 添加 disabled 属性控制父节点是否可选
  disabled: (data: rackGroupType) => {
    // 禁用分组节点（父节点）
    return data.sceneGroupFlag;
  },
};
const vModel = defineModel<string[]>();

const onCheck = (_: any, checkNode: any) => {
  const { checkedNodes } = checkNode;
  vModel.value = checkedNodes
    .filter((item: rackGroupType) => !item.parentId)
    .map((item: rackGroupType) => item.id);
};
</script>
<style lang="scss" scoped></style>
