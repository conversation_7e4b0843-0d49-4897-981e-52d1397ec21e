<template>
  <el-form-item label="标题">
    <el-input
      v-model="data.copyLabel"
      clearable
      placeholder="请输入标题"
      @change="handleChangeTitle"
    ></el-input>
    <el-checkbox v-model="data.showLabel" label="显示标题" />
  </el-form-item>
  <el-form-item label="提示">
    <el-input v-model="data.labelTip" clearable placeholder="请输入提示" type="textarea"></el-input>
  </el-form-item>
  <!-- 表格可导入的组件添加备注,在表格模板里使用 -->
  <el-form-item label="备注" v-if="isRemark">
    <el-input v-model="data.remark" clearable placeholder="请输入备注" type="textarea"></el-input>
  </el-form-item>
</template>

<script setup lang="ts">
import { watchEffect } from 'vue';
import { configTitleType } from './type';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { storeToRefs } from 'pinia';
import { importTypes } from '@/components/form-config/types/FieldTypes';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';
import Data from '@/views/util/data.vue';

const props = defineProps<
  configTitleType
>();

const { taskBase } = storeToRefs(useTaskDetailStore());

const isRemark = computed(() => {
  return _.includes(importTypes, props.data.fixType) && props.data.isDynamic;
});

const handleChangeTitle = ( value:string ) => {
  const { column } = taskBase.value as TaskBase;
  // 判断有没有相同标题的组件
  const sameTextList = column?.filter(item => {
    if (item.fixType === props.data.fixType) {
      return item.copyLabel === value;
    }
  }) || [];
  if (sameTextList?.length >= 2) {
    // ElMessage({
    //   message: '不允许有相同标题的组件',
    //   type: 'warning',
    // });
    // props.data.copyLabel = '';
  }
};

watchEffect(() => {
  // props.data.label = props.data.showLabel ? props.data.copyLabel : '';
});
</script>

<style scoped lang="scss"></style>
