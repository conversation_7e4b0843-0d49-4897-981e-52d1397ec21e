import { ChartTypeEnum, EChartsCommonStyle } from '../../types';
import { MarkNode } from '@antv/g2';

export const labelInit =
  <T extends EChartsCommonStyle>(
    chart: MarkNode,
    chartType: ChartTypeEnum,
    chartStyle: T,
    text: any,
    valueKey?: string,
    nameKey?: string
  ) => {
    if (!chartStyle.showValue) {
      return;
    }

    const valueSize = chartStyle.valueSize ?? 12;

    const others = LabelInitWithType[chartType]?.({
      chartStyle,
      valueSize,
      valueKey,
      nameKey
    }) ?? {};

    chart.label({
      text,
      fontSize: valueSize,
      fill: chartStyle.valueColor,
      ...others
    });

  };

const LabelInitWithType: {
  [K in ChartTypeEnum]?: <T extends EChartsCommonStyle>(params: {
    chartStyle: T,
    valueSize: number,
    valueKey?: string,
    nameKey?: string
  }) => any
} = {
  [ChartTypeEnum.PIE]: (params) => ({
    position: 'outside',
    transform: [{ type: 'overlapDodgeY' }],
    text: (d: any) => `${d[params.nameKey!]}: ${d['percent'] ?? 0}%`
  }),
  [ChartTypeEnum.COLUMN]: (params) => ({
    position: 'top',
    dy: -(params.valueSize + 2)
  }),
  [ChartTypeEnum.BAR]: () => ({
    position: 'right',
    dx: -5
  }),
  [ChartTypeEnum.LINE]: () => ({})
};