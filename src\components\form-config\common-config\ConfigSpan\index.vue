<template>
  <el-form-item label="字段宽度" v-if="!data.isCrud">
    <avue-radio v-model="data.span" :dic="spanDic" button size="small"></avue-radio>
  </el-form-item>
</template>

<script setup lang="ts">
import spanDic from '@/components/form-config/const/spanDic.ts';
import { WidgetPropType } from '@/components/form-config/types/AvueTypes';

defineProps<WidgetPropType>();
</script>

<style scoped lang="scss"></style> 