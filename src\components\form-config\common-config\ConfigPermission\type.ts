import { FieldBaseField, microAppCommonField, singleType, basePermissionType } from '@/components/form-config/types/AvueTypes';


export type showType = {
  hasRequired?: boolean;
  showEdit?: boolean;
  showQuality?: boolean;
  showPath?: boolean;
  showRadio?: boolean;
}
export type configPermissionType = showType &
{ data: Partial<microAppCommonField> & FieldBaseField & Partial<singleType> & Partial<basePermissionType> }  