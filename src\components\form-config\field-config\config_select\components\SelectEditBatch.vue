<template>
  <div class="flex items-center">
    <n-button text :focusable="false" type="primary" @click="handleEditBatch()">批量编辑</n-button>

    <el-dialog
      title="批量编辑"
      v-model="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      class="editBatch"
      width="50%"
      :close-on-click-modal="false"
      @opened="handleOpened"
    >
      <div class="m-10px-0 color-red">每行代表一个选项，可以添加多个选项</div>
      <el-input
        ref="dicInput"
        type="textarea"
        rows="15"
        resize="none"
        placeholder="请输入内容"
        v-model="dicData"
      >
      </el-input>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubDic">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps(['data', 'handleAddFields','updateKey']);

const dialogVisible = ref(false);
const dicData = ref('');

const handleEditBatch = () => {
  const dic = props.data?.dicData ?? [];
  let result = '';
  dic.forEach((item, index) => {
    result += item.label + (index === dic.length - 1 ? '' : '\n');
  });
  dicData.value = result;
  dialogVisible.value = true;
};

const handleSubDic = () => {
  const dic = props.data?.dicData ?? [];
  const split = dicData.value.split('\n');
  split.forEach((item, index) => {
    if (index >= dic.length) {
      props.handleAddFields(false, item);
    } else {
      dic[index].label = item;
    }
  });
  props.data.dicData = dic.slice(0, split.length);
  props.updateKey()
  ElMessage.success('批量编辑成功');
  handleClose();
};
const handleClose = () => {
  dialogVisible.value = false;
  dicData.value = '';
};

const dicInput = ref(null);

const handleOpened = () => {
  dicInput.value.focus();
};
</script>

<style lang="scss">
.editBatch {
  .el-dialog__body {
    padding-top: 0;
  }
}
</style>
