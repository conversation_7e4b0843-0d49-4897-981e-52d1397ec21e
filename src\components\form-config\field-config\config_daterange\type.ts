import { DateFormatTypeEnum,DateRangeType} from '../config_date/const'
import {
  AllDefaultValueType,
  FieldBaseField,
  TitleField,
  basePermissionType,
  RemarkField
} from '@/components/form-config/types/AvueTypes';

/* 时间范围 */
export type DateRangeField = basePermissionType & {
  type: DateRangeType;
  format?: string;
  valueFormat: string;
  dateFormatType: DateFormatTypeEnum;
  startPlaceholder: string;
  endPlaceholder: string;
};
export type DateRangeAllField = FieldBaseField & DateRangeField & TitleField & 
AllDefaultValueType & RemarkField;