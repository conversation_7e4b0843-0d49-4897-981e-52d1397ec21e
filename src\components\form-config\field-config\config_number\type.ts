import { NumberFormatTypeEnum } from './const'
import {
  AllDefaultValueType,
  FieldBaseField,
  TitleField,
  basePermissionType,
  RemarkField
} from '@/components/form-config/types/AvueTypes';

/* 数字 */
export type NumberField = basePermissionType & {
  numberFormat: NumberFormatTypeEnum;
  showPrecision: boolean;
  precision: number;
  min?: number;
  max?: number;
  showPoints: boolean;
};

export type NumberAllField = FieldBaseField & NumberField & TitleField & 
AllDefaultValueType & RemarkField;