
export interface mainFactor {
  id: number;
  industryTypeId: string; // 行业类型
  ban?: boolean;
}

// 使用权方案空间因素
export type spaceFactor ={
  id:number;
  spaceTypeList:string;
  scope: string;
  province:string; // 省
  city: string; // 市
  ban:boolean; // 是否禁用
};
// 时间段
export type slotType = {
  id: number;
  start: number;
  end: number;
  timeType: string;
  ban: boolean;
};

// 使用权方案时间因素
export type timeFactor = {
  id:number;
  number: number; // 时间数量
  timeType: string; // 时间数量
  spaceTypeList:string;
  timePeriodList: slotType[]; // 时间段
  ban:boolean
};

// 使用权方案事件因素
export type controlFactor = {
  id:number;
  eventType:number;
  quantity: number;
  measureType: string;
};

export interface Permission {
  id: number;
  name: string;
  principalFactorList: mainFactor[];
  spaceFactorList: spaceFactor[];
  eventFactorList: controlFactor[];
  timeFactorList: timeFactor[];
}

export interface Model {
  shelvesModel: {
    id: number;
    name: string; // 型号名称
  };

  shelvesPlanDetailList: Permission[]; // 使用权方案
}

export interface Product {
  shelvesProducts: {
    id: number;
    name: string; // 产品名称
    taskGroupIds: string[]; // 功能范围
    description: string; // 介绍
  };
  shelvesModelDTOList: Model[]; // 型号
}
