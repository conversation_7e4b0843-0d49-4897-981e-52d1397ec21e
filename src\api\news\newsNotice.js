import request from '@/axios';

// 消息分页
export const getNewsList = (val, current, data) => {
    return request({
        url: '/message/page',
        method: 'post',
        params: {
            ...val,
            current,
            // ...data,
        },
        data: {
            ...data,
        },
    });
};
// 已读/未读
export const setIsRead = (ids, isHistory, isRead) => {
    return request({
        url: '/message/updateIsRead',
        method: 'post',
        params: {
            ids,
            isHistory,
            isRead
        },

    });
};
// 编辑表分页
export const getEditList = (val, data) => {
    return request({
        url: '/message_edit/page',
        method: 'post',
        params: {
            ...val,
            ...data,
        },
        data: {
            ...data,
        },

    });
};
// 编辑表删除
export const crudDelete = (ids) => {
    return request({
        url: '/message_edit/remove',
        method: 'post',
        params: {
            ids
        },
    });
};
// 撤销
export const restoreData = (id) => {
    return request({
        url: '/message_edit/revoke',
        method: 'post',
        params: {
            id
        },
    });
};
// 恢复
export const recoveryData = (ids) => {
    return request({
        url: '/message_edit/recovery',
        method: 'post',
        params: {
            ids
        },
    });
};
// 编辑表详情
export const editDetail = (id) => {
    return request({
        url: '/message_edit/detail',
        method: 'get',
        params: {
            id
        },
    });
};
// 消息表详情
export const seeDetail = (id) => {
    return request({
        url: '/message/detail',
        method: 'get',
        params: {
            id
        },
    });
};
// 是否可编辑可撤销
export const decide = (id, type) => {
    return request({
        url: '/message_edit/getFlag',
        method: 'post',
        params: {
            id,
            type
        },
        meta:{
            isShowError:false
        }
    });
};
// 模板列表
export const selectTmp = (data) => {
    return request({
        url: '/message_edit/getTemplates',
        method: 'post',
        params: {
            ...data
        }
    });
};
// 编辑表提交
export const subEdit = (messageEdit) => {
    return request({
        url: '/message_edit/submit',
        method: 'post',
        data: { ...messageEdit }
    });
};
// 历史数据文件生成
export const historyFile = (handledId,url) => {
    return request({
        url: '/message_edit/saveByHis',
        method: 'post',
        params:{
            handledId,
            url,
        }
    });
};