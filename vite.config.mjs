import { resolve } from 'path';
import { defineConfig, loadEnv } from 'vite';
import createVitePlugins from './vite/plugins';

// https://vitejs.dev/config/
export default ({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_ENV,VITE_APP_BASE } = env;
  // 判断是打生产环境包
  const isProd = VITE_APP_ENV === 'production'
  // 根据是否生产环境，动态设置压缩配置
  const buildConfig = {
    target: 'esnext',
    minify: isProd ? 'terser' : 'esbuild', // 根据环境选择压缩工具
  };
  // 如果是生产环境，添加Terser的配置
  if (isProd) {
    buildConfig.terserOptions = {
      compress: {
        drop_console: true, // 删除 console
        drop_debugger: true, // 删除 debugger 
      },
      format: {
        comments: false // 删除所有注释
      }
    };
    buildConfig.rollupOptions = {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          '@smallwei/avue': ['@smallwei/avue'],
          'ant-design-vue':['ant-design-vue'],
          'naive-ui':['naive-ui'],
          '@antv/x6':['@antv/x6'],
          '@antv/g2':['@antv/g2']
        },
      }
    }
  }
  return defineConfig({
    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true'
    },
    base: VITE_APP_BASE,
    server: {
      port: 2888,
      proxy: {
        '/api': {
          // target: 'http://localhost',
          // target: 'http://************',
          // target: 'http://************',
          // target: 'http://************',
          // target: 'http://***********',
          target: 'http://***********',
          // target: 'http://************',
          //target: 'https://saber3.bladex.cn/api',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, '')
        }
      }
    },
    resolve: {
      alias: {
        '~': resolve(__dirname, './'),
        '@': resolve(__dirname, './src'),
        components: resolve(__dirname, './src/components'),
        styles: resolve(__dirname, './src/styles'),
        utils: resolve(__dirname, './src/utils'),
        'chart-design': resolve(__dirname, './src/components/chart-design'),
        'xt-component': resolve(__dirname, './src/components/other')
      }
    },
    optimizeDeps: {
      exclude: ['@antv/x6'],
      esbuildOptions: {
        target: 'esnext'
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern',
          silenceDeprecations: ['import']
        }
      }
    },
    plugins: createVitePlugins(env, command === 'build'),
    build: buildConfig
  });
};
