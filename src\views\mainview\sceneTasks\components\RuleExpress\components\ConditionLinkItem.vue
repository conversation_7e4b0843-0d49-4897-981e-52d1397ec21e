<template>
  <div v-if="props.dataPath">
    <h4 class="my-10px" v-if="props.dataPath">筛选条件</h4>
    <n-space vertical :size="20">
      <satisfy-title v-model="fieldRule" btn-width="270px" @add="handleAdd" v-if="props.dataPath" />
      <template v-if="fieldCondition?.length">
        <div v-for="item in fieldCondition" :key="item.id">
          <avue-radio
            v-model="item.filterDataType"
            :dic="dic"
            class="mb-3"
            @change="changeFilterType(item)"
          ></avue-radio>
          <template v-if="item.filterDataType === 1">
            <ConditionOtherTaskItem
              :push-field-list="resFieldList(item)"
              :link-field-list="linkFieldList"
              v-model:data-prop="item.field"
              v-model:symbol="item.conditions"
              field-key="id"
              v-model:linkageProp="item.conditionField"
              :link-task-label="linkTaskLabel"
              :push-task-label="pushTaskLabel"
              @delete="delCondition(item.id)"
            >
            </ConditionOtherTaskItem>
          </template>
          <template v-else>
            <ConditionCustomItem
              :field-list="resFieldList(item)"
              field-key="id"
              :push-task-label="pushTaskLabel"
              v-model:fieldId="item.field"
              v-model:symbol="item.conditions"
              v-model:result="item.value"
              @delete="delCondition(item.id)"
            ></ConditionCustomItem>
          </template>
        </div>
      </template>
    </n-space>
  </div>
</template>
<script setup lang="ts">
import { linkConditionItemType } from '../types';
import { colsToFlatDynamic } from '@/utils/formUtils.ts';
import { randomLenNum } from '@/utils/util';
import ConditionOtherTaskItem from '@/components/conditions/conditionOtherTaskItem.vue';
import ConditionCustomItem from '@/components/conditions/conditionCustomItem.vue';
import SatisfyTitle from '../../../detail/components/common/SatisfyTitle.vue';
import { AvueColumns } from '@/components/form-config/types/AvueTypes';
import {
  createList,
  systemByLinkDataType,
  systemByLinkDataType1,
  filterCurrentField,
  dataLinksType,
  dataLinksDefaultDynamicField,
} from '@/components/form-config/types/FieldTypes';
import { Ref } from 'vue';

const props = defineProps<{
  linkTaskLabel: string;
  fieldList: AvueColumns[];
  linkFieldList: AvueColumns[];
  pathType: number;
  dataPath: string;
  pushTaskLabel: string;
}>();

const dic = [
  { label: '触发任务字段', value: 1 },
  { label: '自定义', value: 0 },
];
//扁平化参数
const params = {
  id: '',
  unFlatDynamic: true,
  unFlatDataCollect: true,
  unFlatDataSelect: true,
  unFlatKnowledgeSelect: true,
  unFlatClubDataCollectType: true,
  unFlatOneByTriggerType: true,
  flatFilterType: dataLinksDefaultDynamicField,
};

const fieldRule = defineModel('fieldRule', { default: 1 });
const fieldCondition = defineModel<linkConditionItemType[]>('fieldCondition', { default: [] });

const systemField = computed(() => (fieldType: number) => {
  const TypeEnumLink: Record<string, string[]> = {
    1: systemByLinkDataType,
    0: systemByLinkDataType,
  };
  const TypeEnumData: Record<string, string[]> = {
    1: systemByLinkDataType1,
    0: systemByLinkDataType1,
  };

  const typeList = props.pathType === 0 ? TypeEnumLink[fieldType] : TypeEnumData[fieldType];

  return createList.filter(item => typeList.includes(item.fixType));
});

const resFieldList = computed(() => (item: linkConditionItemType) => {
  const { filterDataType } = item;
  if (filterDataType === 0) {
    const arr = props.fieldList?.filter(item =>
      _.includes(filterCurrentField, item.fixType)
    ) as AvueColumns[];
    const arr1 = colsToFlatDynamic(arr, params, true);
    return [...arr1, ...systemField.value(item.filterDataType)];
  } else {
    const arr = props.fieldList?.filter(item =>
      _.includes(dataLinksType, item.fixType)
    ) as AvueColumns[];
    return [...arr, ...systemField.value(item.filterDataType)];
  }
});

const handleAdd = () => {
  (fieldCondition.value ??= []).push({
    filterDataType: 1,
    id: randomLenNum(true),
    field: '',
    conditions: undefined,
    value: '',
    conditionField: '',
  });
};
const delCondition = (id: number) => {
  fieldCondition.value = fieldCondition.value.filter(f => f.id !== id);
};

const isFirst = inject<Ref>('isFirst')
const changeFilterType = (item: linkConditionItemType) => {
  if(isFirst?.value) return
  delete item.conditions;
  delete item.field;
  delete item.value;
  delete item.conditionField
};
</script>
<style lang="scss" scoped></style>
