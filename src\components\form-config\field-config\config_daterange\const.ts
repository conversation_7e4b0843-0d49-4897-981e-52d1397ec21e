import {
  DateFormatTypeEnum,
  fullDatetimeFormat,
  minutesFormat,
  minutesTimeFormat,
  DataOptionType,
} from '../config_date/const';

export const daterangeOption: DataOptionType[] = [
  {
    type: 'yearrange',
  },
  {
    type: 'monthrange',
  },
  {
    type: 'daterange',
  },
  {
    type: 'datetimerange',
    format: minutesFormat,
  },
  {
    type: 'datetimerange',
    format: fullDatetimeFormat,
  },
  {
    type: 'timerange',
    format: minutesTimeFormat,
  },
];
export const dateRangeFormatType = [
  {
    label: '年',
    value: DateFormatTypeEnum.YEAR,
  },
  {
    label: '年-月',
    value: DateFormatTypeEnum.MONTH,
  },
  {
    label: '年-月-日',
    value: DateFormatTypeEnum.DAY,
  },
  {
    label: '年-月-日 时:分',
    value: DateFormatTypeEnum.MINUTES,
  },
  {
    label: '年-月-日 时:分:秒',
    value: DateFormatTypeEnum.SECONDS,
  },
  {
    label: '时:分',
    value: DateFormatTypeEnum.MINUTESTIME,
  },
];
