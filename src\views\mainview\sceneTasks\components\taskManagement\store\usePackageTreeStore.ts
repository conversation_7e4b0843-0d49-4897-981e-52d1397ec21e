import { defineStore } from 'pinia';
// import useUserInfo from "@/hooks/useUserInfo.js";

const usePackageTreeStore = defineStore(
  'usePackageTreeStore',
  () => {
    const versionId = ref<string>('');
    const activeTaskPackageId = ref<string>('');
    const expandedKeys = ref<string[]>([]);
    // 分组绑定的工作集合
    const bindWorksByPackage = ref<string[]>([])
    const ruleExpressPackageId = ref<string>('');
    // 工作组最上级id
    const bindWorksByParentId = ref<string>('');

    const resetVersionData = () => {
      versionId.value = ''
      activeTaskPackageId.value = ''
      bindWorksByPackage.value = []
    }
    return {
      versionId,
      activeTaskPackageId,
      expandedKeys,
      bindWorksByPackage,
      ruleExpressPackageId,
      bindWorksByParentId,
      resetVersionData
    };
  },
  { persist: { paths: ['activeTaskPackageId', 'expandedKeys', 'bindWorksByPackage', 'bindWorksByParentId', 'ruleExpressPackageId', 'versionId'] } }
);
export default usePackageTreeStore;
