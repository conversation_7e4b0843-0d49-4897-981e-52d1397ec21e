export const knowableMenu = [
  //应知
  {
    path: '/m/knowable',
    name: 'm应知',
    meta: {
      keepAlive: true,
      isAuth: true,
    },
    component: () => import('@/views/mobileView/knowable/index.vue'),
    children: [
      {
        path: '/m/knowable/duty',
        name: 'm履职看板',
        meta: {
          keepAlive: true,
          isAuth: true,
        },
        component: () => import('@/views/mobileView/knowable/duty/index.vue'),
      },
      {
        path: '/m/knowable/notice',
        name: 'm任务周知',
        meta: {
          keepAlive: true,
          isAuth: true,
        },
        component: () => import('@/views/mobileView/knowable/notice/index.vue'),
      },
      {
        path: '/m/knowable/message',
        name: 'm消息通知',
        meta: {
          keepAlive: true,
          isAuth: true,
        },
        component: () => import('@/views/mobileView/knowable/message/index.vue'),
      },
      {
        path: '/m/knowable/info',
        name: 'm信息查询',
        meta: {
          keepAlive: true,
          isAuth: true,
        },
        component: () => import('@/views/mobileView/knowable/info/index.vue'),
      },
    ],
  },
  {
    path: '/m/knowable/regulations/version',
    name: 'm所有版本',
    meta: {
      keepAlive: true,
      isAuth: true,
    },
    component: () => import('@/views/mobileView/knowable/regulations/version.vue'),
  },
  {
    path: '/m/knowable/proJobInfo/detail/:id',
    name: 'm专岗信息',
    meta: {
      keepAlive: true,
      isAuth: true,
    },
    component: () => import('@/views/mobileView/knowable/proJobInfo/detail.vue'),
  },
];

export const knowableNoMenu = [
  {
    path: '/m/knowable/message/detail',
    name: 'm消息通知详情',
    meta: {
      keepAlive: true,
      isAuth: true,
    },
    component: () => import('@/views/mobileView/knowable/message/detail.vue'),
  },
  {
    path: '/m/knowable/message/addMsg',
    name: 'm添加消息通知',
    meta: {
      keepAlive: true,
      isAuth: true,
    },
    component: () => import('@/views/mobileView/knowable/message/addMsg.vue'),
  },
  {
    path: '/m/knowable/notice/detail',
    name: 'm任务周知详情',
    meta: {
      keepAlive: true,
      isAuth: true,
    },
    component: () => import('@/views/mobileView/knowable/notice/detail.vue'),
  },
  {
    path: '/m/knowable/notice/viewFile',
    name: 'm任务周知查看附件',
    meta: {
      keepAlive: true,
      isAuth: true,
    },
    component: () => import('@/views/mobileView/knowable/notice/viewFile.vue'),
  },
  {
    path: '/m/knowable/regulations/detail',
    name: 'm规章制度详情',
    meta: {
      keepAlive: true,
      isAuth: true,
    },
    component: () => import('@/views/mobileView/knowable/regulations/detail.vue'),
  },
];
