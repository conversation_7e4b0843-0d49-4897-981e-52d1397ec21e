import request from '@/axios';

interface queryParams {
  content: string;
  riseName: string;
  logoUrl: string;
  versionId: string;
}

/**
 * 抬头分页
 */
export const risePage = (
  data: { versionId: string },
  params: { current: number; size: number }
) => {
  return request({
    url: '/riseConfig/page',
    method: 'post',
    data,
    params,
  });
};
/**
 * 抬头详情
 */
export const riseDetail = (data: { id: string; versionId: string }) => {
  return request({
    url: '/riseConfig/detail',
    method: 'post',
    data,
  });
};
/**
 * 新增或修改
 */
export const riseSubmit = (data: queryParams) => {
  return request({
    url: '/riseConfig/submit',
    method: 'post',
    data,
  });
};
/**
 * 删除
 */
export const riseRemove = (params: { ids: string }) => {
  return request({
    url: '/riseConfig/remove',
    method: 'post',
    params,
  });
};
/**
 * 组织下全部抬头
 */
export const riseListByVersion = (params: { versionId: string }) => {
  return request({
    url: '/riseConfig/list',
    method: 'get',
    params,
  });
};

