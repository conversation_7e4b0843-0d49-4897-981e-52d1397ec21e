import { NumberFormatTypeEnum } from './const.ts';
import { NumberField } from './type.ts';
import { ElMessage } from 'element-plus';

export const parseNumberToEmpty = (source: any) => {
  if (_.isNil(source)) {
    return undefined;
  }

  const v = _.toNumber(source);
  return !_.isFinite(v) ? undefined : v;
};

export const formatValue = (
  source: any,
  { showPrecision, precision, showPoints, numberFormat }: NumberField,
  addPercent = false
) => {
  if (_.isNil(source) || source === '') {
    return undefined;
  }
  let v: number | undefined | string = parseNumberToEmpty(source);

  //保留小数
  v = toPrecision(v!, showPrecision ? precision : undefined);

  //添加千分符
  if (showPoints) {
    v = toPoints(v);
  }

  //添加百分符
  if (numberFormat === NumberFormatTypeEnum.PERCENT && addPercent) {
    v += '%';
  }
  return v;
};

export const valueFormat = (source: any, props:any) => {
  if (source === '') return undefined;
  let v = parseNumberToEmpty(source);
  if (_.isNil(v)) {
    return '';
  } else {
    if (props.max && v > props.max) {
      ElMessage(`超过最大值${props.max},请重新填写!`);
      return undefined;
    }
    if (props.min && v < props.min) {
      ElMessage(`小于最小值${props.min},请重新填写!`);
      return undefined;
    }

    // 根据数字格式设置精度限制
    if (props.numberFormat === NumberFormatTypeEnum.PERCENT) {
      // 百分比模式：有设置时用precision+2，没设置时最多2位
      const maxDecimalPlaces = props.showPrecision ? props.precision + 2 : 2;
      return toPrecision(v, maxDecimalPlaces);
    } else {
      // 数值模式：有设置时用precision，没设置时最多4位
      const maxDecimalPlaces = props.showPrecision ? props.precision : 4;
      return toPrecision(v, maxDecimalPlaces);
    }
  }
};

/**
 * 保留小数
 */
export const toPrecision = (num: number, precision?:number) => {
  if (_.isNil(precision)) {
    return num;
  }
  const f = Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision);
  let s = f.toString();
  let rs = s.indexOf('.');
  if (rs < 0 && precision > 0) {
    s += '.';
  }
  for (let i = s.length - s.indexOf('.'); i <= precision; i++) {
    s += '0';
  }
  return s;
};

/**
 * 显示千分符
 */
export const toPoints = (num: number | string) => {
  return num.toString().replace(/\d+/, function (n) {
    // 先提取整数部分
    return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
      return $1 + ',';
    });
  });
};
