import request from '@/axios';
// 组织连接
export const subTname = tname => {
    return request({
        url: '/tenant_authorize/sendMes',
        method: 'post',
        params: {
            tname,
        },
    });
};
// 同意/拒绝
export const isConnection = (tenantId, authorizeId, messageId, isAuthorize,) => {
    return request({
        url: '/tenant_authorize/authorize',
        method: 'post',
        params: {
            tenantId,
            authorizeId,
            messageId,
            isAuthorize,
        },
    });
};
// 组织树
export const getTenantTree = (id) => {
    return request({
        url: '/tenant_authorize/getTree',
        method: 'post',
        params: {
            id,
        },
    });
};
// 组织分类-添加修改
export const addTenantChildren = data => {
    return request({
        url: '/authorize_classification/add',
        method: 'post',
        data: {
            ...data,
        },
    });
};
// 组织分类-删除
export const deleteChildrens = ids => {
    return request({
        url: '/authorize_classification/remove',
        method: 'post',
        params: {
            ids,
        },
    });
};
// 人员添加
export const addUserChildrens = (ids, classId, requestTenant) => {
    return request({
        url: '/authorize_classification_user/add',
        method: 'post',
        params: {
            ids,
            classId,
            requestTenant
        },
    });
};
// 人员分页
export const UserPage = (current, size, params) => {
    return request({
        url: '/authorize_classification_user/page',
        method: 'post',
        params: {
            current,
            size,
            ...params,
        },
        data: {}
    });
};
// 企业协作人员不分页
export const EnterUserNoPage = (params) => {
    return request({
        url: '/authorize_classification_user/list',
        method: 'post',
        params,
        data: {}
    });
};
// 人员删除
export const UserDetele = (userId, classId, requestTenant) => {
    return request({
        url: '/authorize_classification_user/remove',
        method: 'post',
        params: {
            userId,
            classId,
            requestTenant,
        },
    });
};
