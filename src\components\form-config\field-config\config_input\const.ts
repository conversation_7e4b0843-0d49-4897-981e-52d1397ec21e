

export enum InputTypeEnum {
  INPUT = 'input',
  TEXTAREA = 'textarea'
}

export const inputTypeDic = [
  {
    label: '单行文本',
    value: InputTypeEnum.INPUT,
  },
  {
    label: '多行文本',
    value: InputTypeEnum.TEXTAREA,
  },
];

export type InputType = (typeof inputTypeDic)[number]['value'];

export enum InputFormatTypeEnum {
  NONE,
  PHONE,
  TELEPHONE,
  POST_CODE,
  ID_CARD,
  EMAIL,
  URL
}

export const inputFormatType = [
  {
    label: '无',
    value: InputFormatTypeEnum.NONE,
  },
  {
    label: '手机号码',
    value: InputFormatTypeEnum.PHONE,
  },
  {
    label: '电话号码',
    value: InputFormatTypeEnum.TELEPHONE,
  },
  {
    label: '邮政编码',
    value: InputFormatTypeEnum.POST_CODE,
  },
  {
    label: '身份证号码',
    value: InputFormatTypeEnum.ID_CARD,
  },
  {
    label: '邮箱',
    value: InputFormatTypeEnum.EMAIL,
  },
  {
    label: '链接地址',
    value: InputFormatTypeEnum.URL,
  },
];
