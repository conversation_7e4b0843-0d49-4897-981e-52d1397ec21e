@import "./theme.scss";

.avue-form-design {
  height: 100%;
  overflow: hidden;

  //@include formDesign;

  // ::-webkit-scrollbar {
  //   width: 0px !important;
  //   height: 0px !important;
  // }

  //::-webkit-scrollbar-track {
  //  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0) !important;
  //  border-radius: 10px !important;
  //  background-color: rgba(0, 0, 0, 0) !important;
  //}
  //
  //::-webkit-scrollbar-thumb {
  //  border-radius: 10px !important;
  //  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0) !important;
  //  background-color: #fff !important;
  //}

  .widget {
    height: calc(100% - 45px);
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0;

    .widget-list {
      width: 100%;
      padding-bottom: 50px;
      overflow: hidden;

      .widget-item {
        min-height: 30px;
        padding: 5px 10px 18px;
        margin: 0;
        position: relative;
      }

      .widget-dynamic {
        position: relative;
        padding: 2px;
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 8px;

        &__body {
          display: flex;
          padding: 5px;
          padding-top: 10px;
          width: 100%;
          height: 100%;
          overflow: scroll hidden;
          outline: 1px dashed #ccc;
          outline-offset: -1px;
          min-height: 110px;
          box-sizing: border-box;

          @at-root &_form {
            flex-wrap: wrap;
          }


          &::-webkit-scrollbar {
            width: 6px !important;
            height: 6px !important;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #e5e7eb !important;
          }

          .ghost {
            background: white;
            border-left: 5px solid var(--el-color-primary);
            box-sizing: border-box;
            font-size: 0;
            content: "";
            overflow: hidden;
            padding: 0 !important;
            position: relative;
            outline: none 0;
            height: 100%;
            min-height: 90px;
            width: 0 !important;
            min-width: 0 !important;
            max-width: 0 !important;
            margin: 1px 2px 0 2px;
          }
        }

        &__item {
          position: relative;
          height: 100%;
          flex-shrink: 0;

          .wf-table {
            border: 1px solid #ebeef5;
            background: #fff;
            font-size: 14px;

            &__header,
            &__body {
              position: relative;
              width: 100%;
              padding: 12px 10px;
              min-width: 0;
              box-sizing: border-box;
              vertical-align: middle;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: normal;
              word-break: break-all;
            }

            &__body {
              position: static !important;
              padding: 6px 5px;
            }

            &__header {
              color: #909399;
              font-weight: 600;
              border-bottom: 1px solid #ebeef5;
            }
          }
        }
      }

      .widget-group {
        position: relative;
        width: 100%;
        float: left;
        padding: 2px;
        box-sizing: border-box;

        &__head {
          margin: 10px;
        }

        &__body {
          min-height: 150px;
          height: 100%;
          outline: 1px dashed #ccc;
          outline-offset: -1px;
          overflow: hidden;
          padding-bottom: 15px;
          box-sizing: border-box;

          .ghost {
            background: white;
            border-left: 5px solid var(--el-color-primary);
            box-sizing: border-box;
            font-size: 0;
            content: "";
            overflow: hidden;
            padding: 0 !important;
            position: relative;
            outline: none 0;
            height: 100%;
            min-height: 57px;
            width: 0 !important;
            min-width: 0 !important;
            margin: 1px 2px 0 2px;
          }
        }

        &__item {
          padding: 10px;
          margin: 3px;
          position: relative;
          border-left: 5px solid transparent;
          background: white;
        }
      }
    }

    .ghost {
      background: white;
      border-top: 5px solid var(--el-color-primary);
      box-sizing: border-box;
      font-size: 0;
      content: "";
      overflow: hidden;
      padding: 0 !important;
      position: relative;
      outline: none 0;
      height: 0 !important;
      width: 100%;
      margin: 2px 0;
    }
  }

  .required {
    .el-form-item__label::before {
      content: "*";
      color: #f56c6c;
      margin-right: 4px;
    }

    .wf-table__header::before {
      content: "*";
      color: #f56c6c;
      margin-right: 4px;
    }
  }

  .required-title {
    h3::before {
      content: "*";
      color: #f56c6c;
      margin-right: 4px;
    }
  }

  .drag::after {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    display: block;
    z-index: 1001;
    content: "";
  }

  .hover:hover {
    background: $primary-background-color;
    cursor: move;
  }

  .hover-item:hover {
    outline: 1px solid var(--el-color-primary);
    outline-offset: -1px;
    cursor: move;
  }

  .active {
    border-left: 3px solid var(--el-color-primary);
    background: $primary-active-color;
  }

  .active-item {
    outline: 1px solid var(--el-color-primary);
    outline-offset: -1px;
  }

  .danger {
    color: #f56c6c;
  }

  .el-container {
    height: 100%;
  }

  .el-form {
    height: 100%;

    .el-row {
    }
  }

  .avue-date,
  .avue-time,
  .avue-input-map,
  .avue-input-icon,
  .avue-input-color,
  .el-date-editor,
  .el-cascader,
  .el-select,
  .el-input-number {
    width: 100%;
  }

  .editor-fullscreen {
    position: fixed !important;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100% !important;
    z-index: 1003;
    padding: 20px;
    box-sizing: border-box;
    background: #fff;

    svg {
      top: 20px !important;
      left: 25px !important;
    }
  }
}

.afd-drawer {
  .el-drawer__body {
    padding: 0 0 60px 0;
    overflow-y: auto;
  }

  &__footer {
    padding: 10px 16px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    background-color: #fff;
    display: flex;

    .el-button {
      width: 50%;
    }
  }
}
