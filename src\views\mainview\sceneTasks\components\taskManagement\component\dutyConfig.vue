<template>
  <div class="task_overView">
    任务总数共
    <span :style="{ ...numLevelTotal(0) }" class="cursor-pointer" @click="filterNoExecutor(2)">{{
      taskTips.taskCount
    }}</span
    >个；已配执行人任务共<span
      :style="{ ...numLevelTotal(1) }"
      class="cursor-pointer"
      @click="filterNoExecutor(1)"
      >{{ taskTips.hadExecutorTaskCount }}</span
    >个；未匹配执行人任务共<span
      :style="{ ...numLevelTotal(2) }"
      class="cursor-pointer"
      @click="filterNoExecutor(0)"
      >{{ taskTips.noExecutorTaskCount }}</span
    >个
  </div>
  <div class="mt-3">
    <el-table :data="data" style="width: 100%" border>
      <el-table-column prop="taskName" label="任务名称" />
      <el-table-column prop="addExecutorFlag" label="执行人">
        <template #default="scope">
          {{ scope.row.addExecutorFlag === 0 ? scope.row.executorTypeList : '已加' }}
        </template>
      </el-table-column>
      <el-table-column prop="sceneGroupNames" :show-overflow-tooltip="true" label="分组" />
      <el-table-column fixed="right" label="操作" width="500">
        <template #default="scope">
          <el-button text type="primary" @click="toTaskDetail(scope.row)">配置任务</el-button>
          <el-button text @click="emits('updateTask', scope.row)" type="primary">编辑</el-button>
          <el-button text @click="rowDelete(scope.row)" type="primary">删除</el-button>
          <!-- <el-button text type="primary">删除当前后所有</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <div class="package_pagination">
      <el-pagination
        background
        v-model:current-page="page.current"
        v-model:page-size="page.size"
        :page-sizes="[10, 30, 50, 100]"
        layout="total,sizes, prev, pager, next,jumper, ->, "
        :total="page.total"
        @size-change="pageChange"
        @current-change="pageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { getTaskPackagePage } from '@/api/taskManagement/taskPackage.ts';
import { ElMessage, ElMessageBox } from 'element-plus';
import { DutyProps } from '@/piniaStore/banban/useDutyTreeStore.ts';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';

import { delTask } from '@/api/taskManagement/taskDetail';
import { getTaskPageTip } from '@/api/taskManagement/taskPackage';
import { useStore } from 'vuex';
import { storeToRefs } from 'pinia';
import useTaskRuleStore from '@/views/mainview/sceneTasks/detail/store/useTaskRuleStore.ts';
import usePackageTreeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTreeStore.ts';

type taskTipsType = {
  [key: string]: number;
};

const page = ref({
  current: 1,
  size: 50,
  total: 0,
});

const pageChange = () => {
  initTableData();
};
const store = useStore();
const tenantId = computed(() => {
  const userMsg = store.getters.userInfo;
  return userMsg.tenant_id;
});
const emits = defineEmits(['updateTask']);

const vModel = defineModel<string>();
const data = ref<DutyProps[]>([]);
const taskName = defineModel<string>('taskName');
const taskTips = reactive<taskTipsType>({});

const { versionId } = storeToRefs(usePackageTreeStore());

interface ColorEnum {
  color: string;
  fontSize: string;
}

type ColorEnums = {
  [key: number]: ColorEnum;
};

const numLevelTotal = computed(() => (type: number) => {
  const colorEnums: ColorEnums = {
    0: {
      color: 'skyblue',
      fontSize: '20px',
    },
    1: {
      color: 'green',
      fontSize: '20px',
    },
    2: {
      color: 'red',
      fontSize: '20px',
    },
  };
  return { ...colorEnums[type], ...{ marginLeft: '3px', marginRight: '3px' } };
});
// 删除当前
const rowDelete = (row: DutyProps) => {
  ElMessageBox.confirm(`确定删除-${row.taskName}`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    delTask(row.taskId).then(res => {
      ElMessage.success(res.data.msg);
      initTableData();
    });
  });
};

const addExecutorFlag = ref<null | number>(null);

const initTableData = () => {
  if (!vModel.value) {
    ElMessage.warning('请先选择版本');
    return;
  }
  const { current, size } = page.value;
  getTaskPackagePage({
    taskName: taskName.value,
    current,
    size,
    sceneTaskGroupId: vModel.value as string,
    tenantId: tenantId.value,
    versionId: versionId.value,
    addExecutorFlag: addExecutorFlag.value,
  }).then(res => {
    data.value = res.data.data.records;
    page.value.total = res.data.data.total;
    return;
  });
};
const { taskGroupMsg } = storeToRefs(useTaskDetailStore());
const { getTaskDetail } = useTaskDetailStore();
const { getRuleIdsByTaskId } = useTaskRuleStore();
const toTaskDetail = async (row: DutyProps) => {
  try {
    const { taskId, taskName, sceneGroupIds, sceneGroupNames } = row;
    let namesArr = sceneGroupNames.split('/');
    let bindGroupArr = namesArr.map((item: string, index: number) => ({
      label: item,
      value: sceneGroupIds[index],
    }));
    taskGroupMsg.value = bindGroupArr;
    // 调用 getTaskDetail 并等待其完成
    await getTaskDetail(taskId, taskName);
    await getRuleIdsByTaskId(taskId);
  } catch (err) {
    // 处理 getTaskDetail 中可能出现的错误
    console.error('Failed to get task detail:', err);
  }
};

const filterNoExecutor = (type: number) => {
  if (type === 2) {
    addExecutorFlag.value = null;
  } else {
    addExecutorFlag.value = type;
  }

  initTableData();
};

const initTaskPageTip = () => {
  getTaskPageTip({ groupId: vModel.value! }).then(res => {
    Object.entries(res.data.data as taskTipsType).forEach(([key, value]) => {
      taskTips[key] = value;
    });
  });
};
watch(
  vModel,
  () => {
    initTableData();
    initTaskPageTip();
  },
  { immediate: true }
);

defineExpose({
  initTableData,
  addExecutorFlag:addExecutorFlag.value
});
</script>

<style scoped lang="scss">
.package_pagination {
  display: flex;
  justify-content: end;
  padding: 20px;
}
.task_overView {
  height: 38px;
  line-height: 38px;
  border-radius: 4px;
  padding: 5px;
  color: #303133;
  font-size: 14px;
  background-color: rgba(63, 140, 255, 0.13);
}
:deep(.avue-crud__header) {
  margin-bottom: 0;
  align-items: center;
}
:deep(.el-tabs__header) {
  margin: 0;
}
:deep(.el-tabs__nav-wrap) {
  &:after {
    background-color: #ffffff;
  }
}
</style>
