import request from '@/axios';

export const getLedgerTree = (data:object) => {
  return request({
    url: '/infomation/electronicLedgerDir/list',
    method: 'post',
    data: data
  });
};

export const getLinkLedgerTree = () => {//获取关联目录副本tree
  return request({
    url: '/infomation/electronicLedgerDir/listWithConn',
    method: 'post',
    data:{}
  });
};

export const linkLedgerSubmit = (data:object) => {//提交关联目录
  return request({
    url: '/infomation/ledgerConn/submit',
    method: 'post',
    data:data
  });
};

export const getLinkViceId = (originDir:string) => {//获取关联的副本目录id
  return request({
    url: '/infomation/ledgerConn/detail',
    method: 'get',
    params: {
      originDir
    }
  });
};

export const delLinkSubmit = (originDir:string) => {//删除关联
  return request({
    url: '/infomation/ledgerConn/removeByOriginDir',
    method: 'post',
    params: {
      originDir
    }
  });
};


export const move = (data: any) => {
  return request({
    url: '/infomation/electronicLedgerDir/drag',
    method: 'post',
    data: data
  });
};

export const addLedger = (data:object) => {//新增台账目录
  return request({
    url: '/infomation/electronicLedgerDir/submit',
    method: 'post',
    data: data
  });
};
export const delLedgerMenu = (id:string) => {//删除台账目录
  return request({
    url: '/infomation/electronicLedgerDir/remove',
    method: 'post',
    params: {
      id
    }
  });
};

export const addElectronicLedger = (data:object) => {//新增台账
  return request({
    url: '/infomation/electronicLedger/submit',
    method: 'post',
    data: data
  });
};

export const editLedger = (data:object) => {
  return request({
    url: '/infomation/electronicLedgerDir/edit',
    method: 'post',
    data: data
  });
};
export const getLedgerPage = (current:number,size:number,data:object) => {
  return request({
    url: '/infomation/electronicLedger/page',
    method: 'post',
    data:data,
    params:{
      current,
      size
    }
  });
};

// export const getFieldList = (calTableId: string, simple?: boolean) => {
//   return request({
//     url: '/calTableField/list',
//     method: 'post',
//     params: { simple },
//     data: {
//       calTableId
//     }
//   });
// };

export const downloadPrint = (data: any) => {
  return request({
    url: '/infomation/electronicLedgerDir/downloadAndPrint',
    method: 'post',
    data: data
  });
};

export const removeLedger = (ids: string) => {//删除台账
  return request({
    url: '/infomation/electronicLedger/remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const recoverLedger = (ids: string) => {//恢复
  return request({
    url: '/infomation/electronicLedger//recover',
    method: 'post',
    params: {
      ids
    }
  });
};

export const detailField = (id: string) => {
  return request({
    url: '/calTableField/detail',
    method: 'post',
    data: {
      id
    }
  });
};

export const ledgerDrag = (data:any) => {//拖拽
  return request({
    url: '/infomation/electronicLedger/drag',
    method: 'post',
    data: data
  });
};

export const addToWps = (data:any) => {
  return request({
    url: '/webFile/add',
    method: 'post',
    data: data
  });
};


export const ledgerRoll = (data:any) => {//转存
  return request({
    url: '/infomation/electronicLedger/rollover',
    method: 'post',
    data: data
  });
};

export const getCopyId = (dirId:string) => {//获取对应副本id
  return request({
    url: '/infomation/electronicLedgerDir/getCopyId',
    method: 'get',
    params: {dirId}
  });
};

export const getWpsFileInfo = (id: any) => {
  return request({
    url: '/webFile/info',
    method: 'get',
    params: {
      id,
    },
  });
};


