<template>
  <div>
    <n-button @click="handleAdd" quaternary color="#00A870">
      <el-icon>
        <Plus />
      </el-icon>
        添加{{title}}字段
    </n-button>
  </div>
 
  <addGroupItem
    v-for="(item, index) in group"
    :key="item.idx"
    :pathFieldList
    :currentFieldList
    :title
    :item
    :typeList
    :group
    @delete="handleDelete(index)"
  >
  </addGroupItem>

  
</template>

<script setup lang="ts">
import { groupItem } from '@/api/interface/configDataLink';
import { AvueColumns} from '@/components/form-config/types/AvueTypes.ts';
import addGroupItem from './addGroupItem.vue';
import { generateId } from '@/components/form-config/utils';
import { ElMessage } from 'element-plus'

withDefaults(defineProps<{
    title?: string;                                                                            
    pathFieldList: AvueColumns[],
    currentFieldList: AvueColumns[],
    // group: groupItem[],
    typeList: {label: string, value: string}[]
}>(), {
    title: '分组',
    isDynamic: false,
    pathType: 0
});


const group = defineModel<groupItem[]>('group', {default: () => [
  {
    idx: generateId(),
    pathField: '',
    method: '',
    field: ''
  }
  ] 
});

const handleAdd = () => {
  if (_.isArray(group.value)) {
    const haveEmpty = group.value?.some(i => !i.pathField || !i.field || !i.method);
    if (haveEmpty) {
      ElMessage.warning('有空条目请勿添加新的条目');
      return;
    }
  }
  (group.value ??= []).push({
    idx: generateId(),
    pathField: '',
    method: '',
    field: ''
  });
}

const handleDelete = (index: number) => {
  group.value!.splice(index, 1)
}
</script>