import request from '@/axios';
import { formulaCaltype, doLinkageType, doLinkageRuleType } from '@/api/interface/fieldType';

export const formulaCalculate = (data: formulaCaltype) => {
  return request({
    url: '/component/formulaCal',
    method: 'post',
    data
  });
};


//数据联动
export const doLinkage = (data: doLinkageType) => {
  return request({
    url: '/linkage/doLinkage',
    method: 'post',
    data
  });
};

//微应用数据联动
export const doLinkageApp = (data: doLinkageType) => {
  return request({
    url: '/micro-app-linkage/doLinkage',
    method: 'post',
    data
  });
};


//值与值 值与数据表
export const doLinkageRule = (data: doLinkageRuleType) => {
  return request({
    url: '/linkage/doLinkageRule',
    method: 'post',
    data
  });
};

//添加筛选条件
export const doDataFilter = (data: doLinkageType) => {
  return request({
    url: '/linkage/doDataFilter',
    method: 'post',
    data
  });
};


//限制部门/工作组的选择结果
export const getLimitResult = (handledId: string) => {
  return request({
    url: '/staging/queryWorkOrDept',
    method: 'post',
    data: { handledId }
  });
};