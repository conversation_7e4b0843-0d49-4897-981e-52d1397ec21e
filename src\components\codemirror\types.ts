import { Position } from 'codemirror';
import { AvueColumns } from '../form-config/types/AvueTypes.ts';
import { FormulaType } from '@/api/formula';

export type FieldType = Pick<AvueColumns, 'prop' | 'copyLabel' | 'fixType'>;

export type Mark = {
  from: Position;
  to: Position;
  attributes?: any;
};

export type BCmProp = {
  formulaType?: FormulaType;
  fieldList?: FieldType[];
  valText?: string;
  inputHeight?: number;
  templateKey?: string;
  marksKey?: string;
  placeholder?: string;
  filterFormual?: string[];
};

export type ValueType<K1 extends string, K2 extends string> = {
  [key1 in K1]?: string;
} & {
  [key2 in K2]?: Mark[];
};
