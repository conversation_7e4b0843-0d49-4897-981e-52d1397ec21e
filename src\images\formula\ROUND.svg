<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="260px" viewBox="0 0 320 260" version="1.1" xmlns="http://www.w3.org/2000/svg"
>
    <title>编组 38</title>
    <g id="专家端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="82函数示例" transform="translate(-1250.000000, -955.000000)">
            <g id="编组-38" transform="translate(1250.000000, 955.000000)">
                <rect id="矩形" stroke="#DDDFE7" x="0.5" y="0.5" width="319" height="259" rx="4"></rect>
                <line x1="0.5" y1="32.5" x2="320" y2="32.5" id="路径-2" stroke="#DDDFE7"></line>
                <text id="ROUND函数可以将数字四舍五入到指定的" font-family="PingFangSC-Regular, PingFang SC"
                      font-size="14" font-weight="normal" line-spacing="23">
                    <tspan x="10" y="57" fill="#3F70FF">ROUND</tspan>
                    <tspan x="60.148" y="57" fill="#3A3A3A">函数可以将数字四舍五入到指定的位</tspan>
                    <tspan x="10" y="80" fill="#3A3A3A">数。</tspan>
                    <tspan x="10" y="104" fill="#3A3A3A">·用法：</tspan>
                    <tspan x="59" y="104" fill="#3F70FF">ROUND</tspan>
                    <tspan x="109.148" y="104" fill="#3A3A3A">(数字,数字位数)</tspan>
                    <tspan x="10" y="128" fill="#3A3A3A">·示例：</tspan>
                    <tspan x="59" y="128" fill="#3F70FF">ROUND</tspan>
                    <tspan x="109.148" y="128" fill="#3A3A3A">(3.1485,2)返回3.15</tspan>
                </text>
                <text id="ROUND" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal"
                      line-spacing="23" fill="#3A3A3A">
                    <tspan x="11" y="20">ROUND</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>
