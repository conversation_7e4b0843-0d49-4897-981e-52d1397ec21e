<template>
  <div class="w-full">
    <el-card class="box-card" @click="dialogVisible = true">
      <template v-if="modelList.length > 0">
        <el-tag v-for="item in modelList" :key="item.id" style="margin: 0 5px" size="large"
          >{{ item.spaceName }}
        </el-tag>
      </template>
      <div v-else class="select_placeholder">请选择空间</div>
    </el-card>
    <selectSpace v-model:modelList="modelList" v-model:visible="dialogVisible"></selectSpace>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import selectSpace from '@/components/form-component/XTInterspaceCom/component/selectSpace.vue';
import { getSpaceByGroupIds1 } from '@/api/common/index';

type PropItem = {
  id: string;
  spaceName: string;
};

const dialogVisible = ref(false);
const modelList = ref<PropItem[]>([]);
watch(
  () => modelList.value,
  list => {
    vModel.value = list.map(item => item.id);
  },
  { deep: true }
);

const vModel = defineModel<string[]>({ default: [] });

onMounted(async () => {
  if (vModel.value?.length > 0) {
    let res = await getSpaceByGroupIds1(vModel.value);
    modelList.value = res.data.data;
  }
});

defineExpose({
  modelList:modelList
})
</script>
<style lang="scss" scoped>
.box-card {
  cursor: pointer;
  width: 100%;
  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
</style>
