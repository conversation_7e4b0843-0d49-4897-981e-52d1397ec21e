<template>
  <div :class="{ 'pl-30px!': !showMenu }" class="chart-tag">
    <el-dropdown placement="bottom-start" trigger="click">
      <el-button v-if="showMenu" link>
        <el-icon color="white">
          <ArrowDown />
        </el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <template v-for="it in dropdownItems">
            <component :is="it.component({ refType, item })" />
          </template>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <SortAscendingOutlined v-if="item.sort === SortEnum.ASC" />
    <SortDescendingOutlined v-else-if="item.sort === SortEnum.DESC" />
    <span>{{ item.title }}</span>
    <span class="text-12px align-middle">{{ summaryText }}</span>
    <el-button class="ml-0!" link @click="$emit('delete')">
      <el-icon color="white">
        <Close />
      </el-icon>
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons-vue';
import { ArrowDown, Close } from '@element-plus/icons-vue';
import { SummaryDic } from '../../../const/widget.ts';
import { Mark, SortEnum, TopTagType } from '../../../types/widget.ts';

defineOptions({
  inheritAttrs: false
});

const props = withDefaults(defineProps<{
  list?: Mark[],
  item: Mark,
} & Pick<TopTagType, 'dropdownItems' | 'refType'>>(),
  {
    menuGenerators: () => [],
    list: () => []
  }
);

defineEmits<{
  delete: [];
}>();

const showMenu = computed(() => !!props.dropdownItems.length);

const summaryText = computed(() =>
  props.item.summary ?
    `(${SummaryDic.find(dic => dic.value === props.item.summary)?.label})`
    : ''
);
</script>

<style lang="scss">
.chart-tag {
  @apply h-25px flex gap-3px items-center bg-#36AD6A text-white rounded-full px-12px text-14px
}

.chart-ghost-tag {
  @extend .chart-tag;
  @apply mb-0 w-auto px-10px;
}
</style>