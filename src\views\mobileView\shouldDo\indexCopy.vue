<template>
  <xt-m-scroll tabbar-h="50" @scrollend="scrollPageHandle" bodyBg="#F4F9FD">
    <template #top>
      <div>
        <div class="flex flex-item-center py8px px15px">
          <el-input v-model="shouldDoQuery.searchBar"
                    @keyup.enter="filterSubmit"
                    class="h36px"
                    placeholder="输入任务名称" />
          <div @click="filterTaskShow=true"
               class="flex flex-items-center justify-center mx16px">
            <el-icon size="25">
              <Filter />
            </el-icon>
          </div>
          <!--          <div @click="filterSubmit" class="flex flex-items-center justify-center mx16px">-->
          <!--            <el-icon size="25"><Search /></el-icon>-->
          <!--          </div>-->
        </div>
        <div class="todo_new" v-if="!isNotTenant" @click="reToInstantService">
          <el-icon size="25">
            <Document />
          </el-icon>
          <div class="font-size-13px">即时办事</div>
        </div>
        <el-drawer v-model="filterTaskShow"
                   direction="ttb"
                   size="64%"
                   custom-class="drawer_class"
                   title="I am the title"
                   :with-header="false">
          <a-flex vertical justify="space-between" class="h-full">
            <div class="flex flex-item-center py8px px15px">
              <el-input v-model="shouldDoQuery.searchBar"
                        class="h36px"
                        placeholder="输入任务名称" />
              <div @click="filterTaskShow=!filterTaskShow"
                   class="flex flex-items-center justify-center mx16px">
                <el-icon size="25" color="#1989fa">
                  <Filter />
                </el-icon>
              </div>
            </div>
            <a-flex class="flex-1 overflow-y-scroll">
              <el-scrollbar class="overflow-y-scroll">
                <van-sidebar v-model="active">
                  <van-sidebar-item v-for="item in tenantList">
                    <template #title>
                      <a-flex justify="space-between">
                        <div>{{ item.tenantName }}</div>
                        <div>({{ item.atThatDayCount }})</div>
                      </a-flex>
                    </template>
                  </van-sidebar-item>
                </van-sidebar>
              </el-scrollbar>
              <!--              <a-flex class="flex-1" vertical>-->
              <!--                <a-flex justify="space-between"-->
              <!--                        style="border-bottom: 1px solid #F0F0F0"-->
              <!--                        align="center"-->
              <!--                        v-for="item in versionList">-->
              <!--                  <div @click="versionHandler(item.id)" class="version_item"-->
              <!--                       :class="{'active':shouldDoQuery.versionId===item.id}">{{-->
              <!--                      item.groupName-->
              <!--                    }}({{ item.atThatDayCount }})-->
              <!--                  </div>-->
              <!--                  <el-icon color="#3F8CFF" v-if="shouldDoQuery.versionId===item.id">-->
              <!--                    <Check />-->
              <!--                  </el-icon>-->
              <!--                </a-flex>-->
              <!--                <div class="color-#A6A6A6 text-center py10px">-->
              <!--                  没有更多了-->
              <!--                </div>-->
              <!--              </a-flex>-->
            </a-flex>

            <div class="flex px16px py12px">
              <el-text @click="clearFilter" class="refresh_text">
                <el-icon>
                  <Refresh />
                </el-icon>
                重置
              </el-text>
              <el-button class="flex-1 ml15px h45px" type="primary"
                         @click="filterSubmit">确定
              </el-button>
            </div>
          </a-flex>
        </el-drawer>
        <van-tabs v-model:active="activeName"
                  @change="changeTab" background="#F4F9FD">
          <van-tab :name="item.name"
                   v-for="item in tabList"
                   :badge="item.name !== 3?shouldCount[item.name]:null"
                   :title="item.title"></van-tab>
        </van-tabs>
      </div>
    </template>
    <template #body>
      <van-pull-refresh v-model="refreshLoading" @refresh="onRefresh">
        <div class="px-15px py-15px">
          <a-flex justify="center">
            <van-image class="mb-10px" ref="bannerRef" v-if="activeName==='0'&&showBanner"
                       src="/img/mobile/m_todo_banner.png"></van-image>
            <template v-if="dataList.length<=0">
              <van-image class="mt-40% w-50% h-140px" v-if="activeName==='1'"
                         src="/img/mobile/should_do_01.png"></van-image>
              <van-image class="mt-40% w-50% h-140px" v-else-if="activeName==='2'"
                         src="/img/mobile/should_do_02.png"></van-image>
              <van-image class="mt-40% w-50% h-140px" v-else-if="activeName==='3'"
                         src="/img/mobile/should_do_03.png"></van-image>
            </template>
          </a-flex>
          <div v-for="item in dataList">
            <!--              信息审核-->
            <div class="todo_card blue"
                 v-if="item.deptInviteVerify"
                 @click="reToVerifyInfo(item.deptInviteVerify)">
              <div class="py-10px font-size-11px relative">
                <h2 class="my-12px">信息审核</h2>
                <div class="pb-5px">
                  <span class="color-#C6C6CA">用户名：</span>
                  {{ item.deptInviteVerify.nickName }}
                </div>
                <div class="pb-5px">
                  <span class="color-#C6C6CA">组织来源：</span>
                  {{ item.deptInviteVerify.tenantName }}
                </div>
                <div class="pb-5px">
                  <span class="color-#C6C6CA">状态：</span>
                  {{ infoStatusList[item.deptInviteVerify.agreeStatus] }}
                </div>
              </div>
            </div>
            <!--              微应用-->
            <div class="todo_card blue"
                 v-if="item.microAppWaitVerify"
                 @click="reToMicroApp(item.microAppWaitVerify)">
              <div class="py-10px font-size-11px relative">
                <h2 class="my-12px">
                  {{ item.microAppWaitVerify.createBy }}提交的{{ item.microAppWaitVerify.appName }}
                </h2>
                <div class="pb-5px">
                  <span class="color-#C6C6CA">发起时间：</span>
                  {{ item.microAppWaitVerify.createTime }}
                </div>
                <div class="pb-5px">
                  <span class="color-#C6C6CA">组织来源：</span>
                  {{ getTenantName(item.microAppWaitVerify) }}
                </div>
              </div>
            </div>
<!--            应办-->
            <div class="todo_card blue"
                 v-if="item.handledVerify"
                 @click="reToTaskJob(item.handledVerify)"
                 :class="getUrgency(item.handledVerify)">
              <div class="pb-10px" style="border-bottom: 1px solid #F0F0F0">
                <a-flex justify="space-between" class="py-12px font-bold">
                  <div class="font-size-16px">{{ item.handledVerify.name }}</div>
                  <el-dropdown>
                    <el-icon color="#C6C6CA" @click.stop class="mr-10px" style="transform: rotateZ(90deg)">
                      <More />
                    </el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <template
                          v-if="![1, 2].includes(item.handledVerify.agree) && item.handledVerify.viewStatus !== 2">
                          <el-dropdown-item v-if="[0,1].includes(activeName)"
                                            @click="reToPostpone(item.handledVerify)">
                            调
                          </el-dropdown-item>
                          <el-dropdown-item v-if="[0,1].includes(activeName)"
                                            @click="reToTransfer(item.handledVerify)">
                            转
                          </el-dropdown-item>
                          <el-dropdown-item v-if="[0,1].includes(activeName) && item.handledVerify.manualOffline == 1"
                                            @click="confirm(item.handledVerify,1)">
                            下
                          </el-dropdown-item>
                          <el-dropdown-item v-if="[0,1,2].includes(activeName) && item.handledVerify.manualStop == 1"
                                            @click="confirm(item.handledVerify,2)">
                            停
                          </el-dropdown-item>
                        </template>
                        <el-dropdown-item v-if="true" @click="reToDetail(item.handledVerify)">
                          详情
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </a-flex>
                <div class=" font-size-11px pb-5px" v-if="item.handledVerify.operate===2">
                  <span class="color-#C6C6CA">转办人：</span>{{ item.handledVerify.initiatorName }}
                </div>
                <div class=" font-size-11px pb-5px" v-if="item.handledVerify.taskSourceName&&!shouldDoQuery.taskSource">
                  <span class="color-#C6C6CA">组织来源：</span>{{ item.handledVerify.taskSourceName }}
                </div>
                <div class=" font-size-11px" v-if="item.handledVerify.operate===5">
                  <span class="color-#C6C6CA">邀请人：</span>{{ item.handledVerify.initiatorName }}
                </div>
              </div>
              <div class="py-10px font-size-11px relative">
                <div class="pb-5px">
                  <span class="color-#C6C6CA">场景名称：</span>{{ item.handledVerify.groupName }}
                </div>
                <div class="pb-5px">
                  <span class="color-#C6C6CA">触发时间：</span>
                  {{ item.handledVerify.triggerTime }}
                </div>
                <div class="todo_tag" v-if="item.handledVerify.operate">
                  {{ getOperate(item.handledVerify) }}
                </div>
              </div>
              <a-flex justify="end" class="pb-10px px-10px">
                <a-space>
                  <template v-if="item.handledVerify.agree === 1">
                    <a-button @click.stop="transHandle(item.handledVerify,true)" type="primary">同意</a-button>
                    <a-button @click.stop="transHandle(item.handledVerify,false)">拒绝</a-button>
                  </template>
                  <a-button @click.stop="doTaskRevokeHandle(item.handledVerify)" v-if="activeName === 3">恢复
                  </a-button>
                </a-space>
              </a-flex>
            </div>
          </div>
          <a-flex justify="center">
            <div class="text-gray mt-10px">—— 共{{ dataList.length }}条 ——</div>
          </a-flex>
        </div>
      </van-pull-refresh>
    </template>
  </xt-m-scroll>
</template>
<script setup>
import XtMScroll from '@/views/mobileView/components/mScroll.vue';
import { Filter, More, Refresh } from '@element-plus/icons-vue';
import useMPageHook from '@/views/mobileView/hooks/useMPageHook.ts';
import {
  doTaskOffline,
  doTaskRevoke,
  doTaskStop, getMHandleAllDone,
  getShouldDoList, getShouldDoTaskCount, getTaskTenantList,
  getTaskVersion
} from '@/api/banbanMobile/shouldDo';
import { useStore } from 'vuex';
import { showConfirmDialog } from 'vant';
import { ElMessage } from 'element-plus';
import { useShouldDoStore } from '@/views/mobileView/shouldDo/store/useShouldDoStore';
import { storeToRefs } from 'pinia';
import { staticWork } from '@/views/mobileView/shouldDo/const';
import { useBanbanSysStore } from '@/views/mobileView/store/useBanbanSysStore';
import { useElementVisibility } from '@vueuse/core';
import { HandleTypeEnum } from '@/components/form-config/const/commonDic';
import { tabList } from '@/views/banban/option';
import { getCurrentDayThreeInOne, stageChangeApi } from '@/api/banban';
import { ViewMode } from '@/views/banban/application/flowApp/option/enum';


const store = useStore();
const userMsg = computed(() => {
  return store.state.user.userInfo;
});
const { shouldDoQuery, activeName, openTaskIns, transTaskParams } = storeToRefs(useShouldDoStore());

const { isNotTenant } = storeToRefs(useBanbanSysStore());

const router = useRouter();

const tenantList = ref([]);
const active = ref(0);

const filterTaskShow = ref(false);
const showOffLine = ref(false);
const showStopTask = ref(false);
const showBanner = ref(true);
const bannerRef = ref();
const targetIsVisible = useElementVisibility(bannerRef);
const informationList = ref([]);
const refreshLoading = ref(true);
// 下拉刷新
const onRefresh = () => {
  refreshLoading.value = false;
  changeTab(activeName.value);
};
const infoStatusList = ['未审核', '审核成功', '审核失败'];

watch(targetIsVisible, (v) => {
  if (!v) {
    showBanner.value = false;
  }
});

const reToMicroApp = (item) => {
  if (activeName.value !== 4) {
    const { instanceId, microAppId, appName, flwTaskId, startNodeFlag, tenantId } = item;
    router.push({
      path: '/m/shouldDo/microDetail',
      query: {
        instanceId,
        appId: microAppId,
        flwTaskId,
        name: appName,
        type: 3,
        startNodeFlag,
        isExamine: 1,
        tenantId,
      }, //isExamine 是否审核入口 1 是 0 否
    });
  } else {
    const { instanceId, appName, microAppId } = item;
    router.push({
      path: '/m/shouldDo/microDetail',
      query: {
        instanceId,
        appId: microAppId,
        name: appName,
        type: 3,
        disabled: 1,
        viewMode: ViewMode.View,
      },
    });
  }
};

const getTenantName = (item) => {
  return tenantList.value.find((e) => e.tenantId === item.tenantId)?.tenantName;
};

const taskForm = ref({
  name: '和谐劳动用工关系场景任务集',//任务名称
  endDuration: '2024/02/27 09:00-2024/02/29 17:00',//任务结束期限
  isRecall: true,//已办可撤回
  isRedo: false//是否可补办
});

const versionList = ref([]);
const getTenantList = () => {
  getTaskTenantList(userMsg.value.user_id).then((res) => {
    tenantList.value = res.data.data;
    active.value = tenantList.value.findIndex((item) => item.tenantId === shouldDoQuery.value.taskSource);
    versionHandler(shouldDoQuery.value.packageId);
  });
};
if (isNotTenant.value) {
  tenantList.value = [
    {
      tenantName: '办办组织',
      id: '',
      tenantId: ''
    }
  ];
} else {
  getTenantList();
}

const reToVerifyInfo = (item) => {
  router.push({
    path: '/m/deptInviteCheck',
    query: {
      idCard: item.idCard,
      tenantName: item.tenantName,
      nickName: item.nickName,
      attachFiles: item.attachFiles,
      id: item.id,
      agreeStatus: item.agreeStatus
    }
  });
};

const reTo = (path) => {
  router.push({
    path
  });
};

watch(active, (v) => {
  changeTenant(active.value);
});

const getUrgency = (item) => {
  switch (item.urgency) {
    case 1:
      return 'red';
    case 2:
      return 'yellow';
    case 3:
      return 'blue';
  }
  return;
};

const changeTenant = (index) => {
  console.log("tenantId");
  const tenantId = tenantList.value[index]?.tenantId;
  const id = tenantList.value[index]?.id;
  shouldDoQuery.value.taskSource = id;
  shouldDoQuery.value.versionId = '';
  if (!id) {
    return versionList.value = [];
  }
  getTaskVersion(tenantId).then(res => {
    versionList.value = res.data.data;
  });
};


const versionHandler = (id) => {
  shouldDoQuery.value.versionId === id ? shouldDoQuery.value.versionId = '' : shouldDoQuery.value.versionId = id;
};

const doTaskRevokeHandle = (item) => {
  showConfirmDialog({
    title: '提示',
    message:
      `是否确认撤销`
  })
    .then(() => {
      console.log(item, 'ooooo');
      doTaskRevoke(item.id).then(res => {
        if (res.data.success) {
          ElMessage.success(res.data.msg);
          initData(true);
        }
      });
    });
};

//创建组织
const toCreateTenant = () => {
  router.push('/m/tenant/create');
};

const toJoinTenant = () => {
  router.push('/m/tenant/join');
};

const reToPostpone = (item) => {
  openTaskIns.value = item;
  router.push({
    path: '/m/shouldDo/postpone',
    query: {
      id: item.id,
      name: item.taskName
    }
  });
};

const transHandle = (item, flag) => {
  showConfirmDialog({
    title: '提示',
    message:
      `是否${flag ? '同意' : '拒绝'}接收`
  })
    .then(() => {
      console.log('确认');
      if (flag) {
        doTaskStop(item.id).then(res => {
          if (res.data.success) {
            ElMessage.success(res.data.msg);
            initData(true);
          }
        });
      } else {
        doTaskStop(item.id).then(res => {
          if (res.data.success) {
            ElMessage.success(res.data.msg);
            initData(true);
          }
        });
      }
      // on confirm
    })
    .catch(() => {
      console.log('取消');
      // on cancel
    });
};

const reToInstantService = (item) => {
  router.push('/m/shouldDo/instantService');
};

const reToTransfer = (item) => {
  transTaskParams.value = {
    row: { ...item },
    // getTableList: getList,
    api: stageChangeApi
  };
  router.push({
    path: '/m/shouldDo/transfer',
    query: {
      id: item.id,
      taskSource: item.taskSource
    }
  });
};

const confirm = (item, type) => {
  showConfirmDialog({
    title: '提示',
    message:
      `${type === 1 ? '当前任务为下线可补办任务,任务下线后可在补办中继续填写，确认下线？' : '任务终止后将无法填写，可能会对后续触发任务产生影响，确认终止？'}`
  })
    .then(() => {
      if (type === 1) {
        doTaskOffline(item.id).then(res => {
          if (res.data.success) {
            ElMessage.success(res.data.msg);
            initData(true);
          }
        });
      } else {
        doTaskStop(item.id).then(res => {
          if (res.data.success) {
            ElMessage.success(res.data.msg);
            initData(true);
          }
        });
      }
    });
};


const getOperate = (item) => {
  switch (item.operate) {
    case 1:
      return '延';
    case 2:
      return '转';
    case 3:
      return '下';
    case 4:
      return '停';
    case 5:
      return '邀';
  }
};

const reToDetail = (item) => {
  console.log(item);
  router.push({
    path: '/m/shouldDo/taskInfo',
    query: {
      id: item.id,
      name: item.name
    }
  });
};
// 初始化 tabs 数据
const reToTaskJob = (item) => {
  const staticIndex = staticWork.findIndex(s => s.name === item.name);
  if (staticIndex !== -1) {
    const work = staticWork[staticIndex];
    return router.push({
      path: work.path
    });
  }
  router.push({
    path: '/m/shouldDo/taskDetail',
    query: {
      handleId: item.id,
      // taskId: item.taskId,
      // name: item.name,
      // taskPackageId: item.packageId,
      disabled: item.agree === 1 || activeName.value === 3 || item.viewStatus === 2 ? 1 : 0,
      submitType: activeName.value === 2 ? 2 : null,
      // taskSource: item.taskSource,
      // tenantId: item.tenantId,
      type: HandleTypeEnum.HANDLED,
      isControl: 1, // 是否要控制跳转
    }
  });
};

const filterSubmit = () => {
  filterTaskShow.value = false;
  initData(true);
  getShouldDoCount();
};
const clearFilter = () => {
  active.value = 0;
  shouldDoQuery.value.taskSource = '';
  shouldDoQuery.value.versionId = '';
  shouldDoQuery.value.searchBar = '';
};
const cancelOffLine = () => {
  console.log('cancel');
  showOffLine.value = false;
};

const shouldCount = ref([]);
const { dataList, initData, scrollPage, changeApi } = useMPageHook(getShouldDoList, shouldDoQuery);

const getShouldDoCount = () => {
  getShouldDoTaskCount({ ...shouldDoQuery.value, name: shouldDoQuery.value.searchBar }).then(res => {
    const data = res.data.data;
    shouldCount.value = [
      data.atThatDayCount,
      data.planCount,
      data.reissueCount,
      data.revokeCount
    ];
    changeTab(shouldDoQuery.value.handleStatus);
  });
};
const scrollPageHandle = () => {
  scrollPage();
};

const initTriangleData = (current, size, params) => {
  return getCurrentDayThreeInOne({
    current, size,
    executorId: userMsg.value.user_id,
    ...params
  });
};

getShouldDoCount();

watch(() => shouldDoQuery.value.searchBar, (v) => {
  shouldDoQuery.value.name = v;
});

const changeTab = (e) => {
  shouldDoQuery.value.handleStatus = e;
  console.log(e,shouldDoQuery.value,"shouldDoQuery");
  if (e === 0) {
    changeApi(initTriangleData, (res) => {
      return res.data.data?.records || [];
    });
  } else if (e === 4) {
    changeApi(getMHandleAllDone, (res) => {
      return (res.data.data?.records ||[]).map((item) => ({
        handledVerify: item.handled,
        microAppWaitVerify: item.instance,
      }));
    });
  } else {
    changeApi(getShouldDoList, (res) => {
      return (res.data.data?.records || []).map((item) => ({ handledVerify: item }));
    });
  }
  initData(true);
};
</script>

<style scoped lang="scss">
.todo_card {
  background: #fff;
  border-radius: 6px;
  padding-left: 20px;
  position: relative;
  margin-bottom: 10px;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);

  &:after {
    content: '';
    display: block;
    position: absolute;
    border-radius: 5px 0 0 5px;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 98%;
  }

  &.red:after {
    background: #F56C6C;
  }

  &.yellow:after {
    background: #FFCB50;
  }

  &.blue:after {
    background: #3F8CFF;
  }
}

.todo_tag {
  position: absolute;
  right: 25px;
  top: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  color: #3F8CFF;
  padding: 5px;
  font-size: 14px;
  font-weight: bold;
  align-items: center;
  justify-content: center;
  transform: translateY(-50%) rotateZ(-15deg);
  border: 1px solid #3F8CFF;
  border-radius: 50%;

  &:after {
    content: '';
    display: block;
    position: absolute;
    border: 2px solid #3F8CFF;
    border-radius: 50%;
    width: 100%;
    height: 100%;
    padding: 4px;
  }
}

:deep(.el-drawer__body) {
  padding: 0;
}

:deep(.van-sidebar) {
  width: 150px;
}

.version_item {
  padding: 20px 0;
  flex: 1;

  &.active {
    color: #3F8CFF;
  }
}

.todo_new {
  position: absolute;
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
  background: #fff;
  width: 64px;
  height: 64px;
  border-radius: 8px;
  top: 80%;
  left: 80%;
  z-index: 111;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}
</style>