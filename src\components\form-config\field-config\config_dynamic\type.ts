import {
  AvueColumns,
  FieldBaseField,
  TitleField,
  AllDefaultValueType,
  basePermissionType,
} from '@/components/form-config/types/AvueTypes'
import { DynamicDataTypeEnum } from '@/components/form-config/const/commonDic'
import { RowsTypeEnum } from './const'


/* 表格/子表单 */
export type DynamicField = basePermissionType & {
  dynamicType: string;
  children: AvueColumns[];
  fixedColumn?: boolean;
  fixedNumber?: number;
  dynamicMulType?: DynamicDataTypeEnum;//表格组件用的 单选 多选 汇总 
  rowsType?: RowsTypeEnum,//表格组件用的，行数
  rowsMin?: number | string,//行数范围最小值
  rowsMax?: number | string,//行数范围最大值
  rowsNumbeFieldMin?: string;//行数范围最小值组件
  rowsNumbeFieldMax?: string;//行数范围最大值组件
  batchDelete: boolean;//是否批量删除
};

export type DynamicAllField = FieldBaseField & DynamicField & TitleField & AllDefaultValueType