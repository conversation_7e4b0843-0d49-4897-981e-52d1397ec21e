<template>
    <div class="mt-2">
        <n-button @click="handleAdd" quaternary color="#00A870">
            <el-icon>
                <Plus />
            </el-icon>
            {{ title }}
        </n-button>
        <template  class="mt-1 mb-1" v-if="dynamicFields?.length">
          <el-scrollbar max-height="300px">
            <addDynamicItem 
              v-for="(item, index) in dynamicFields" 
              :key="item.idx"
              :data="data"
              v-model:pushTaskId="pushTaskId"
              :dynamicItem="item"
              :dynamicFields="dynamicFields"
              :pushDynamicField="pushDynamicField"
              :taskList="taskList!"
              @delete="handleDelete(index)"
              :isDynamic="isDynamic"
              :pathType="pathType"
              >
            </addDynamicItem>
          </el-scrollbar>
        </template>
    </div>
</template>

<script lang="ts" setup>
import { FieldBaseField, AvueColumns  } from '@/components/form-config/types/AvueTypes.ts';
import { pushTaskListType } from '@/api/interface/task';
import { dynamicType } from '@/api/interface/configDataLink.ts';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import addDynamicItem from './addDynamicItem.vue';
import { generateId } from '@/components/form-config/utils';
import { editKnowType } from '@/api/interface/knowLedgeType'


withDefaults(defineProps<{
    data: FieldBaseField;
    taskList?: pushTaskListType[] | editKnowType[];
    title?: string;
    pushDynamicField: AvueColumns[];
    isDynamic?: boolean;
    pathType?: number
}>(), {
    title: '添加条件',
    isDynamic: false,
    pathType: 0
});


const pushTaskId = defineModel<string>('pushTaskId');
const dynamicFields = defineModel<dynamicType[]>('dynamicFields')
const handleAdd = () => {
  if (_.isArray(dynamicFields.value)) {
    const haveEmpty = dynamicFields.value?.some(i => !i.currentProp || !i.pushProp);
    if (haveEmpty) {
      ElMessage.warning('有空条目请勿添加新的条目');
      return;
    }
  }
  (dynamicFields.value ??= []).push({
    idx: generateId(),
    currentProp: '',
    pushProp: '',
  });
}

const handleDelete = (index: number) => {
  dynamicFields.value!.splice(index, 1)
}


</script>