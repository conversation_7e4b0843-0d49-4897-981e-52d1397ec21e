import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { SelectAllField,SpecificType } from '@/components/form-config/types/AvueTypes.ts';
import { storeToRefs } from 'pinia';
import useHiddenStore from '@/views/mainview/sceneTasks/detail/store/useHiddenStore.ts';

export interface MutualType{
  fieldId:string,
  result:any,
  prop:string,
  conditions?:string,
  mutualFieldId:string,
  mutualResult:any,
  mutualProp:string
  mutualConditions?:string,
  id?:string,
}
export const useMutual = () => {
  const { mutualRef } = storeToRefs(useHiddenStore());

  function isEqual(v1:any, v2:any, type:string) {
    if(type === FixTypeEnum.SELECT){
      return _.isEqual(v1, v2);
    };
    if(type === FixTypeEnum.MUL_SELECT){
      return _.isEqual(v1?.sort(), v2?.sort());

    };
    if(type === FixTypeEnum.MUL_CASCADER){
      return _.isEqual(v1?.flat()?.sort(), v2?.flat()?.sort());
    }
    return false
  }

  function reverseSetting(setting:MutualType){
    const { fieldId, result, prop, mutualFieldId, mutualResult, mutualProp, id } = setting;
    return {
      fieldId:mutualFieldId,
      result:mutualResult,
      prop:mutualProp,
      mutualFieldId:fieldId,
      mutualResult:result,
      mutualProp:prop,
      id
    }

  }

  function disableNodesByPath(options:any[], paths:any[],bool:boolean,id:string) {
    // 创建路径查找表以便快速查找
    const pathMap = new Map();
    paths.forEach(path => {
      const key = path.join('-');
      pathMap.set(key, true);
    });

    // 递归处理树形数据
    function traverse(nodes:any[], currentPath:any[]) {
      nodes.forEach(node => {
        const newPath = [...currentPath, node.label];
        const pathKey = newPath.join('-');

        // 如果当前路径在禁用列表中，设置disabled为true
        if (pathMap.has(pathKey)) {
          setOptionStatus(node,bool,id);
        }

        // 如果有子节点，继续递归处理
        if (node.children && node.children.length) {
          traverse(node.children, newPath);
        }
      });
    }
    traverse(options, []);
    return options;
  }

  // 保存把元素设置为禁用的依赖，
  function setOptionStatus(option:any,bool:boolean,id:string){
    const mutualPath = mutualRef.value?.get(option) as string[];
    // console.log(mutualPath,'mutualPath',bool,id);
    // 如果这个选项没有依赖，就照常设置
    if(_.isEmpty(mutualPath)){
      option.disabled = bool;
      if(bool === true){
        mutualRef.value?.set(option,[id]);
      }
      //   有依赖的话，假如设置为false，就要看看依赖有自己的话，就把自己清了，并且如果是只有自己，就设置为false，还有其他依赖就不改变Disabled
    }else{
      if(bool === false){
        // console.log(id,mutualPath);
        if(mutualPath.includes(id)){
          const final = mutualPath.filter(i => i !== id);
          if(final.length){
          }else{
            option.disabled = false;
          }
          mutualRef.value?.set(option,final)
        }else{
          // console.log('走这里');
          return
        }
        //   有依赖要设置为true，就要看假如依赖里面没有自己，就加上自己，如何有，就不动
      }else {
        if(!mutualPath.includes(id)){
          mutualRef.value?.set(option,[...mutualPath,id]);
          option.disabled = bool;
        }
      }
    }
    // console.log(mutualPath,'结束mutualPath',mutualRef.value);
  }

  // 把disabledList里面的设置给dic
  function formatDicData(dic:any[],disabledList:any[],type:SpecificType['type'],fixType:string,bool:boolean,id:string){
    // fixType     type        disabledList             dic
    // select      select      未命名1             [{ "label": "未命名1", "key": "1" } ]
    // select      radio       未命名1             [{ "label": "未命名1", "key": "1" } ]
    // mulSelect   checkbox    [未命名1]           [{ "label": "未命名1", "isOther": false, "key": "#1747637553587484125" }]
    // mulSelect   select      [未命名1]           [{ "label": "未命名1", "isOther": false, "key": "#1747637553587484125" }]
    // mulSelect   cascader    ['未命名1','1-1']   [{ "label": "未命名1", "isOther": false, "key": "#1747637553587484125", "children": [ { "label": "未命名1-1", "key": "#1747709988423868513" }] }]
    // mulCascader mulCascader [['未命名1','1-1']] [{ "label": "未命名1", "isOther": false, "key": "#1747637553587484125", "children": [ { "label": "未命名1-1", "key": "#1747709988423868513" }] }]
    if(fixType === 'select'){
      // dic.find(i => i.label === disabledList).disabled = bool;
      const option = dic.find(i => i.label === disabledList);
      setOptionStatus(option,bool,id)
    }else if(['checkbox','select'].includes(type as string)){
      disabledList.forEach((item:any) => {
        setOptionStatus(dic.find(i => i.label === item),bool,id)
        // dic.find(i => i.label === item).disabled = bool;
      })
    }else if(type === 'cascader'){
      disableNodesByPath(dic,[disabledList],bool,id)
    }else if((type as string) === 'mulCascader'){
      disableNodesByPath(dic,disabledList,bool,id)
    }
  }

  function filterDisabledPaths(options:any[], paths:any[]) {
    // 创建一个查找表，存储所有被禁用的节点的完整路径
    const disabledPaths = new Set();

    // 递归遍历树形数据，收集所有被禁用的节点的路径
    function traverse(nodes:any[], currentPath:any[]) {
      nodes.forEach(node => {
        const newPath = [...currentPath, node.value];

        // 如果当前节点被禁用，记录它的路径
        if (node.disabled) {
          disabledPaths.add(newPath.join('-'));
        }

        // 如果有子节点，继续递归
        if (node.children && node.children.length) {
          traverse(node.children, newPath);
        }
      });
    }

    traverse(options, []);

    // 过滤掉那些路径中包含被禁用节点的路径
    return paths.filter(path => {
      // 检查路径的每一步，看是否有任何部分是被禁用的
      for (let i = 1; i <= path.length; i++) {
        const partialPath = path.slice(0, i).join('-');
        if (disabledPaths.has(partialPath)) {
          return false;
        }
      }
      return true;
    });
  }

  // undefined和false是允许修改  true是被打回
  function findIsDisabled(value:any,field:SelectAllField){
    const { dicData, type, fixType } = field
    if(fixType === 'select'){
     let res = dicData.find(i => i.label === value)?.disabled;
     return [undefined,false].includes(res) ? value : true
    }else if(['checkbox','select'].includes(type as string)){
      return value?.filter((item:string) => {
        return !dicData.find(i => i.label === item)?.disabled
      })
    }else if(type === 'cascader'){
      return filterDisabledPaths(dicData,[value])?.[0]
    }else if((type as string) === 'mulCascader'){
      return filterDisabledPaths(dicData,value)
    }
  }

  function formatMutualData(data:any[]){
    return {
      groups:data?.map((item:any) => {
        return {
          ...item.rules
        }
      })
    }
  }

  return {
    isEqual,
    formatDicData,
    findIsDisabled,
    formatMutualData,
    reverseSetting
  }
}