// import { dateAllType, TransToXtFieldType } from '../types/FieldTypes.ts';
export enum DateTypeEnum {
  POINT,
  RANGE,
}

export const dateType = [
  {
    label: '日期和时间点',
    value: DateTypeEnum.POINT,
  },
  {
    label: '日期时间范围',
    value: DateTypeEnum.RANGE,
  },
];

export enum DateFormatTypeEnum {
  YEAR,
  MONTH,
  DAY,
  MINUTES,
  SECONDS,
  MINUTESTIME
}


export const dateFormatType = [
  {
    label: '年',
    value: DateFormatTypeEnum.YEAR,
  },
  {
    label: '年-月',
    value: DateFormatTypeEnum.MONTH,
  },
  {
    label: '年-月-日',
    value: DateFormatTypeEnum.DAY,
  },
  {
    label: '年-月-日 时:分',
    value: DateFormatTypeEnum.MINUTES,
  },
  {
    label: '年-月-日 时:分:秒',
    value: DateFormatTypeEnum.SECONDS,
  },
];


// const dateFormat = 'YYYY-MM-DD';
export const fullDatetimeFormat = 'YYYY-MM-DD HH:mm:ss';
export const minutesFormat = 'YYYY-MM-DD HH:mm';
export const minutesTimeFormat = 'HH:mm';


export enum DateRangeType {
  YearRange = 'yearrange',
  MonthRange = 'monthrange',
  DateRange = 'daterange',
  DateTimeRange = 'datetimerange',
  TimeRange = 'timerange',
}
export type DataOptionType = {
  type: any;
  component?: string;
  format?: string;
};
 
export enum DateTypeEnum {
  YEAR = 'year',
  MONTH = 'month',
  DAY = 'date',
  MINUTES = 'datetime',
  SECONDS = 'datetime',
}


export const dateOption: DataOptionType[] = [
  {
    type: 'year',
  },
  {
    type: 'month',
  },
  {
    type: 'date',
  },
  {
    type: 'datetime',
    format: minutesFormat,
  },
  {
    type: 'datetime',
    format: fullDatetimeFormat,
  }
];


export const weekList: { label: string; value: string }[] = [
  {
    label: '星期一',
    value: '1',
  },
  {
    label: '星期二',
    value: '2',
  },
  {
    label: '星期三',
    value: '3',
  },
  {
    label: '星期四',
    value: '4',
  },
  {
    label: '星期五',
    value: '5',
  },
  {
    label: '星期六',
    value: '6',
  },
  {
    label: '星期日',
    value: '7',
  },
]

export const minuteIntervalsList: { label: string; value: string }[] = [
  {
    label: '1分钟',
    value: '1',
  },
  {
    label: '5分钟',
    value: '5',
  },
  {
    label: '10分钟',
    value: '10',
  },
  {
    label: '15分钟',
    value: '15',
  },
  {
    label: '30分钟',
    value: '30',
  },
  {
    label: '60分钟',
    value: '60',
  },
  
]

export enum DateLimitTypeEnum {
  FIXED = 'fixed',
  FORM = 'form',
  DYNAMICS = 'dynamics',
  DEFAULT = 'default',
}

export const dateLimitTypeList: { label: string; value: string }[] = [
  {
    label: '固定值',
    value: DateLimitTypeEnum.FIXED,
  },
  {
    label: '表单值',
    value: DateLimitTypeEnum.FORM,
  },
  {
    label: '动态值',
    value: DateLimitTypeEnum.DYNAMICS,
  },
  {
    label: '自定义',
    value: DateLimitTypeEnum.DEFAULT,
  }
]

// 定义 DynamicsEnum 枚举
export enum DynamicsEnum {
  TODAY = '0',
  YESTERDAY = '-1',
  TOMORROW = '1',
  SEVEN_DAYS_AGO = '-7',
  SEVEN_DAYS_LATER = '7',
  THIRTY_DAYS_AGO = '-30',
  THIRTY_DAYS_LATER = '30',
}


export const dynamicsList: { label: string; value: DynamicsEnum }[] = [
  {
    label: '当天',
    value: DynamicsEnum.TODAY,
  },
  {
    label: '昨天',
    value: DynamicsEnum.YESTERDAY,
  },
  {
    label: '明天',
    value: DynamicsEnum.TOMORROW,
  },
  {
    label: '七天前',
    value: DynamicsEnum.SEVEN_DAYS_AGO,
  },
  {
    label: '七天后',
    value: DynamicsEnum.SEVEN_DAYS_LATER,
  },
  {
    label: '30天前',
    value: DynamicsEnum.THIRTY_DAYS_AGO,
  },
  {
    label: '30天后',
    value: DynamicsEnum.THIRTY_DAYS_LATER,
  },
];


export enum DateDefaultInterEnum {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year',
}

// 
export const dateDefaultInterList: { label: string; value: DateDefaultInterEnum }[] = [
  {
    label: '天',
    value: DateDefaultInterEnum.DAY,
  },
  {
    label: '周',
    value: DateDefaultInterEnum.WEEK,
  },
  {
    label: '月',
    value: DateDefaultInterEnum.MONTH,
  },
  {
    label: '季',
    value: DateDefaultInterEnum.QUARTER,
  },
  {
    label: '年',
    value: DateDefaultInterEnum.YEAR,
  },
];

