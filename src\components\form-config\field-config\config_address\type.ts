import {
  AllDefaultValueType,
  FieldBaseField,
  TitleField,
  basePermissionType,
} from '@/components/form-config/types/AvueTypes';
import { AddressPrecisionEnum } from './const'
/* 文本 */
/* 地址 */
export type AddressField = basePermissionType & {
  addressPrecision: AddressPrecisionEnum;
  addressRequired: boolean;//详细地址必填
  addressDisplay: boolean;//详细地址可见
};
export type AddressAllField = FieldBaseField & AddressField & TitleField & AllDefaultValueType