<template>
  <n-space vertical :size="20">
    <accordTitle :title="title" v-model="andOr" @add="handleAdd" :andOrDisabled="andOrDisabled" />
    <template v-if="condition && condition.length && andOr">
      <!--      <fieldItem-->
      <!--        v-for="(item, index) in condition"-->
      <!--        :key="item.idx"-->
      <!--        :data="item"-->
      <!--        :fieldList="field"-->
      <!--        :current="current"-->
      <!--        :currentField="currentField"-->
      <!--        @delete="handleDelete(index)"-->
      <!--      ></fieldItem>-->
      <conditionCustomItem
        v-for="(item, index) in condition"
        :fieldList="field"
        v-model:symbol="item.operator"
        v-model:fieldId="item.prop"
        v-model:result="item.value"
        :current="current"
        :currentField="currentField"
        @delete="handleDelete(index)"
        :filterSymbolField="filterSymbolField"
        :key="item.idx"
      ></conditionCustomItem>
    </template>
  </n-space>
</template>
<script setup lang="ts">
import accordTitle from './accordTitle/index.vue';
import { ElMessage } from 'element-plus';
import { fieldListType } from '@/api/interface/configDataLink.ts';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { SystemTypeEnum } from '@/components/form-config/types/field';
import {
  dataLinksDefaultField,
  dataLinksDefaultDynamicField,
  systemByCurrentType,
  systemByLinkDataType,
  createList,
} from '@/components/form-config/types/FieldTypes';
import { colsToFlatDynamic } from '@/utils/formUtils.ts';
import { generateId } from '@/components/form-config/utils';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';
import ConditionCustomItem from '@/components/conditions/conditionCustomItem.vue';
import { SymbolListTypes } from '@/components/conditions/types/types.ts';

const props = withDefaults(
  defineProps<{
    conditionTaskId?: string;
    title: string;
    field?: AvueColumns[];
    currentField?: AvueColumns;
    current?: boolean;
    andOrDisabled?: boolean;
    addSystemField?: boolean;
  }>(),
  {
    addSystemField: true,
  }
);

const { taskBase } = storeToRefs(useTaskDetailStore());
const condition = defineModel<fieldListType[]>('condition');
const andOr = defineModel<string>('andOr');

const handleAdd = () => {
  if (!props.conditionTaskId) {
    ElMessage.warning('请先选择任务');
    return;
  }
  if (!andOr.value) {
    ElMessage.warning('请先选择条件的逻辑关系(且/或)');
    return;
  }
  if (_.isArray(condition.value)) {
    const haveEmpty = condition.value?.some(i => _.isEmpty(i.operator) || _.isEmpty(i.prop));
    if (haveEmpty) {
      ElMessage.warning('有空条目请勿添加新的条目');
      return;
    }
  }
  (condition.value ??= []).push({ prop: '', value: '', idx: generateId() });
};
const handleDelete = (index: number) => {
  condition.value?.splice(index, 1);
};

const filterSymbolField: SymbolListTypes[] = [
  //   {
  //     types:[...usersType],
  //     symbols:[
  //       MethodSymbolEnum.INCLUDE,
  //       MethodSymbolEnum.EXCLUDE,
  //       MethodSymbolEnum.BE_INCLUDE,
  //       MethodSymbolEnum.INCLUDE_ANY,
  //       MethodSymbolEnum.BE_INCLUDE_ANY,
  //       ...CommonSymbol
  //     ]
  //   }
];

const params = {
  id: '',
  unFlatDynamic: true,
  unFlatDataCollect: true,
  unFlatDataSelect: true,
  unFlatKnowledgeSelect: true,
  unFlatClubDataCollectType: true,
  unFlatOneByTriggerType: true,
  flatFilterType: dataLinksDefaultDynamicField,
};

//分为当前任务 和其他任务
const field = computed(() => {
  return (props.current ? fieldList1.value : linkFieldList.value) as AvueColumns[];
});

//提交人 提交时间等系统字段
const systemField = computed(() => {
  return createList.filter(item =>
    (systemByLinkDataType as SystemTypeEnum[]).includes(item.fixType as SystemTypeEnum)
  );
});

//提交人 提交时间等系统字段
const curSystemField = computed(() => {
  return createList.filter(item =>
    (systemByCurrentType as SystemTypeEnum[]).includes(item.fixType as SystemTypeEnum)
  );
});

//其他任务的组件
const linkFieldList = computed(() => {
  if (!props.field) return [];
  const arr = props.field
    ?.filter(item => _.includes(dataLinksDefaultField, item.fixType))
    .map(m => {
      return {
        ...m,
        disabled: false,
      };
    });
  const arr1 = colsToFlatDynamic(arr, params, true);
  return props.addSystemField ? [...arr1, ...systemField.value] : arr1;
});

//当前任务的组件
const fieldList1 = computed(() => {
  let column = (taskBase.value as TaskBase).column || [];
  let fieldList = [...column].filter(item => _.includes(dataLinksDefaultField, item.fixType));
  const arr = fieldList.map(v => {
    return {
      ...v,
      label: v.copyLabel,
      contentType: v.fixType,
      fieldType: v.fixType,
      disabled: false,
    };
  });
  return [...colsToFlatDynamic(arr, params, true), ...curSystemField.value];
});
</script>
<style lang="scss" scoped></style>
