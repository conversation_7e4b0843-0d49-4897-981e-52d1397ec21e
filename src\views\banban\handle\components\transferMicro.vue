<script setup lang="ts">
import DialogForm from '@/views/mainview/sceneTasks/components/taskManagement/component/dialogForm.vue';
import { ref, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';
// import SelectUserByDept from '@/components/select/selectUserByDept.vue';
// import { bindMicroCategorize } from "@/api/application/microGroup.ts"
import SelectUser from '@/views/banban/application/flowApp/approvalProcess/scWorkflow/component/selectUser.vue';
import { transferMicroHttp } from '@/api/application/index.ts'

interface PackageProp {
  userIds:any[]
}
interface Emit {
  (e: 'initPage'): void
}
interface userType{
  nickName: string,
  id:string,
  relId:string,
  relType: 0 | 1,
}
provide('disabledUser','')

const emits = defineEmits<Emit>();

const selectVisible = ref(false);

const addMemberRef = useTemplateRef('addMemberRef');
const addMember = ref<PackageProp>({
  userIds: [],
});


const initData = (info:any) => {
  addMember.value = {
    userIds:[]
  };

  const { microAppId, flwTaskId, instanceId } = info;
  addMemberRef.value?.open<PackageProp>(form => {
    const params = {
      transferUserId:form.userIds[0].id,
      transferUserName:form.userIds[0].nickName,
      microAppId,
      flwTaskId,
      instanceId
    }
    transferMicroHttp(params).then(res => {
      res.data.success &&
      addMemberRef.value?.close(() => {
        if (res.data.success) {
          ElMessage.success(res.data.msg);
          emits('initPage');
          addMember.value = {
            userIds: [],
          }
          // initTreeData();
        }
      });
    });
  });
}

defineExpose({
  initData
});
</script>

<template>
  <dialog-form
    ref="addMemberRef"
    dialogType="drawer"
    title="添加成员"
    :rules="{
        groupName: { required: true, message: '请输入分类名称', trigger: 'blur' },
      }"
    v-model="addMember"
  >
    <el-form-item label="接收方" prop="userIds">

      <select-user placeholder="请选择成员"
                   multiple
                   :config="{isRadio:true}"
                   v-model:userList="addMember.userIds"></select-user>

    </el-form-item>
  </dialog-form>
</template>

<style scoped lang="scss">
.group {
  background-color: #eee;
  color: #333;
}

.disabled {
  background: #f6f6f6;
}

.select_placeholder {
  color: #a8abb2;
  font-size: 14px;
}
.box-card {
  cursor: pointer;
  width: 100%;

  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
</style>
