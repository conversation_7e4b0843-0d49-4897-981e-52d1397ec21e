import { ApproveStatus } from '@/views/banban/application/flowApp/option/enum.ts';

//微应用
export namespace Application {
  export interface folderType{
    id?:string,
    folderName:string;
    logo:string;
    folderType:number;
  }
  export interface App {
    id:string
    appName:string
    status:number
    userIds:string[]
    releaseStatus:number //发布状态
    canDo?: boolean
  }
  export interface AppInfo {
    startTime: string;
    microAppName:string;
    approvalStatus:ApproveStatus
    instanceId:string;
    haveFile?:boolean;
    createBy:string;//发起人
  }

  export interface FlowItem {
    operateTime:string;
    taskContent:string;
    remark:string;
  }

  export interface FormState {
    approvalStatus: ApproveStatus | null;
    startTime: string;
    endTime: string;
  }
  
}