<template>
  <div class="w-full">
    <el-card class="box-card">
      <el-tag 
        v-for="(item,index) in dataList" 
        :key="item.id"
        :closable="true"
        style="margin: 0 5px"
        size="large"
        @close="handleClose(index)"
        >
        {{ item.nickName }}
      </el-tag>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { selectUserByProps } from './XTDepartAdmin';

const props = withDefaults(defineProps<selectUserByProps>(), {});
const vModel = defineModel<string[]>();

const handleClose = (index: number) => {
  props.dataList.splice(index, 1);
  vModel.value?.splice(index, 1)
};
</script>

<style lang="scss" scoped>
.group {
  background-color: #eee;
  color: #333;
}

.disabled {
  background: #f6f6f6;
}

.select_placeholder {
  color: #a8abb2;
  font-size: 14px;
}

.box-card {
  cursor: pointer;
  width: 100%;

  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
</style>
