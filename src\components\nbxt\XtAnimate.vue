<template>
  <component :is="comp" v-bind="vBind">
    <slot />
  </component>
</template>

<script setup>
import { Transition, TransitionGroup } from 'vue';
import xtAnimations from '@/const/xtAnimations.js';

const props = defineProps({
  animate: {
    type: [String, Array],
    validator(value) {
      return !_.isEmpty(xtAnimations[value]);
    },
  },
  group: {
    type: Boolean,
    default: () => false,
  },
  enter: {
    type: Boolean,
    default: () => true,
  },
  leave: {
    type: Boolean,
    default: () => true,
  },
});

const comp = computed(() => (props.group ? TransitionGroup : Transition));

const prefix = 'animate__';
const baseClass = `${prefix}animated`;

//处理vbind
const attrs = useAttrs();

const vBind = computed(() => {
  const animate = getAnimate(props.animate);
  return {
    ...attrs,
    enterActiveClass: props.enter ? animate[0] : undefined,
    leaveActiveClass: props.leave ? animate[1] : undefined,
  };
});

const getAnimate = value => {
  if (_.isEmpty(value)) {
    return [null, null];
  }

  if (typeof value === 'string') {
    return xtAnimations[value].map(addPrefix) || [null, null];
  }
  return [value[0], value[1]].map(addPrefix);
};

const addPrefix = animate => {
  return animate ? `${baseClass} ${prefix + animate}` : undefined;
};
</script>

<style lang="scss" scoped></style>
