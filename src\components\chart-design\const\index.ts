import { LegendPositionEnum, TableAlignEnum, TitlePositionEnum } from '../types';

export const TitlePositionDic = [
  {
    label: '左侧',
    value: TitlePositionEnum.LEFT
  },
  {
    label: '居中',
    value: TitlePositionEnum.CENTER
  }
  // {
  //   label: '右侧',
  //   value: TitlePositionEnum.RIGHT
  // }
];

export const LegendPositionDic = [
  {
    label: '底部',
    value: LegendPositionEnum.BOTTOM,
    config: {
      left: 'center',
      bottom: 'bottom'
    }
  },
  {
    label: '顶部',
    value: LegendPositionEnum.TOP,
    config: {
      left: 'center',
      top: 'top'
    }
  },
  {
    label: '左侧',
    value: LegendPositionEnum.LEFT,
    config: {
      orient: 'vertical',
      left: 'left',
      top: 'center'
    }
  },
  {
    label: '右侧',
    value: LegendPositionEnum.RIGHT,
    config: {
      orient: 'vertical',
      right: 'right',
      top: 'center'
    }
  }
];

export const TableAlignDic = [
  {
    label: '左对齐',
    value: TableAlignEnum.LEFT
  },
  {
    label: '右对齐',
    value: TableAlignEnum.RIGHT
  },
  {
    label: '居中对齐',
    value: TableAlignEnum.CENTER
  }
];