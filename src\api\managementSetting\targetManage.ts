import request from '@/axios';

export const getTargetPage = (current:number,size:number,targetName:string) => { //目标管理分页
  return request({
    url: '/taskTarget/page',
    method: 'get',
    params: {
      current,
      size,
      targetName
    }
  });
};

export const addTargetByName = (targetName:string,typeSynchronization:[]) => { //新增目标
  return request({
    url: '/taskTarget/addTargets',
    method: 'post',
    data: {
      targetName,
      typeSynchronization
    }
  });
};

export const editTargetByName = (oldName:string,newName:string) => { //编辑目标
  return request({
    url: '/taskTarget/editTargetName',
    method: 'get',
    params: {
      oldName,
      newName
    }
  });
};

export const delTargetByName = (targetName:string) => { //删除目标
  return request({
    url: '/taskTarget/removeTarget',
    method: 'get',
    params: {
      targetName
    }
  });
};
