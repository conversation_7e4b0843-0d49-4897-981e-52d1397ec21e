import { watchDebounced } from '@vueuse/core';
import {Ref} from 'vue'

type UseSyncRefOptions = {
  deep?: boolean;
  debounce?: number;
};

const useSyncRef = <L>(target: Ref<L>, source: Ref<L>, options: UseSyncRefOptions = {}) => {
  const { deep = false, debounce = 0 } = options;

  onMounted(() => {
    if (!_.isNil(source.value)) {
      target.value = _.cloneDeep(source.value);
    }

    if (debounce > 0) {
      watchDebounced(
        target,
        v => {
          if (!_.isNil(v) && !_.isEqual(v, source.value)) {
            source.value = _.cloneDeep(v);
          }
        },
        {
          deep,
          debounce,
        }
      );
    } else {
      watch(
        target,
        v => {
          if (!_.isNil(v) && !_.isEqual(v, source.value)) {
            source.value = _.cloneDeep(v);
          }
        },
        {
          deep,
        }
      );
    }

    watch(
      source,
      v => {
        if (!_.isNil(v) && !_.isEqual(v, target.value)) {
          target.value = _.cloneDeep(v);
        }
      },
      {
        deep,
      }
    );
  });
};

export default useSyncRef;
