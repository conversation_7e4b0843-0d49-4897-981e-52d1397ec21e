import request from '@/axios';
import { yearBook, archives, ReqPage, files, pageParamsElectric } from './type';
import { RecordForm,lookLimitsType } from '@/views/banban/know/record/types';

//获取年鉴列表
export const getYearList = (data: yearBook.ReqParams) => {
  return request({
    url: '/yearbook/page',
    method: 'post',
    data,
  });
};

//年鉴编辑
export const submitYearBook = (data: yearBook.ReqParams[]) => {
  return request({
    url: '/yearbook/submit',
    method: 'post',
    data,
  });
};

//年鉴删除
export const removeYearBook = (params: { ids: string }) => {
  return request({
    url: '/yearbook/remove',
    method: 'post',
    params,
  });
};

//获取组织标签
export const getLableListByOrg = () => {
  return request({
    url: '/tenantLabel/getLabel',
    method: 'get',
  });
};

//保存组织标签
export const submitLableListByOrg = (data: yearBook.ReqParams) => {
  return request({
    url: '/tenantLabel/submitLabel',
    method: 'post',
    data,
  });
};
//删除组织标签
export const removeLableListByOrg = (data: { id: string }) => {
  return request({
    url: '/tenantLabel/remove',
    method: 'post',
    params: data,
  });
};
//电子档案分页
export const getArchivesPage = (params: archives.archivesParams) => {
  return request({
    url: '/electronic_archives/pageByKey',
    method: 'post',
    params,
    data: {},
  });
};
//电子档案分页
export const getArchivesDetail = (params: { id: string }) => {
  return request({
    url: '/electronic_archives/detail',
    method: 'get',
    params,
  });
};

//电子档案历史
export const getArchivesHistoryPage = (params: archives.archivesParams) => {
  return request({
    url: '/electronic_archives/HistoryByKey',
    method: 'post',
    params,
    data: {},
  });
};
//电子档案恢复
export const getArchivesRecovery = (params: { ids: string }) => {
  return request({
    url: '/electronic_archives/recovery',
    method: 'post',
    params,
  });
};
//电子档案删除
export const getArchivesRemove = (params: { ids: string }) => {
  return request({
    url: '/electronic_archives/remove',
    method: 'post',
    params,
  });
};
//电子档案下载
export const getArchivesDownload = (params: { id: string }) => {
  return request({
    url: '/electronic_archives/download',
    method: 'post',
    params,
  });
};
//电子档案打印
export const getArchivesPrint = (params: { id: string }) => {
  return request({
    url: '/electronic_archives/print',
    method: 'post',
    params,
  });
};
//电子档案打印
export const ArchivesPrintList = (params: { ids: string }) => {
  return request({
    url: '/electronic_archives/printList',
    method: 'post',
    params,
  });
};

//电子档案往上调
export const archivesMoveUp = (params: { newId: string; oldId: string }) => {
  return request({
    url: '/electronic_archives/moveUp',
    method: 'post',
    params,
  });
};
//电子档案往下调
export const archivesMoveDown = (params: { newId: string; oldId: string }) => {
  return request({
    url: '/electronic_archives/moveDown',
    method: 'post',
    params,
  });
};
//电子档案直接上传
export const directUploadByArchives = (
  data: RecordForm & lookLimitsType & { fileUrl: string; fileName: string; tenantId: string },
  params: { labels: string }
) => {
  return request({
    url: '/electronic_archives/save',
    method: 'post',
    data,
    params,
  });
};
//电子档案历史保存
export const hisUploadByArchives = (
  params: { labels: string },
  data: RecordForm & lookLimitsType & { fileUrl: string; fileName: string; tenantId: string }
) => {
  return request({
    url: '/electronic_archives/saveByHis',
    method: 'post',
    params,
    data,
  });
};

//电子存证分页
export const getEvidencePage = (params: archives.archivesParams) => {
  return request({
    url: '/existing_evidence/pageByKey',
    method: 'post',
    params,
    data: {},
  });
};
//电子存证详情
export const getEvidenceDetail = (params: { id: string }) => {
  return request({
    url: '/existing_evidence/detail',
    method: 'get',
    params,
  });
};

//电子存证历史
export const getEvidenceHistoryPage = (params: archives.archivesParams) => {
  return request({
    url: '/existing_evidence/HistoryByKey',
    method: 'post',
    params,
    data: {},
  });
};
//电子存证恢复
export const getEvidenceRecovery = (params: { ids: string }) => {
  return request({
    url: '/existing_evidence/recovery',
    method: 'post',
    params,
  });
};
//电子存证删除
export const getEvidenceRemove = (params: { ids: string }) => {
  return request({
    url: '/existing_evidence/remove',
    method: 'post',
    params,
  });
};
//电子存证下载
export const getEvidenceDownload = (params: { id: string }) => {
  return request({
    url: '/existing_evidence/download',
    method: 'post',
    params,
  });
};
//电子存证打印
export const getEvidencePrint = (params: { id: string }) => {
  return request({
    url: '/existing_evidence/print',
    method: 'post',
    params,
  });
};
//电子存证批量打印
export const EvidencePrintList = (params: { ids: string }) => {
  return request({
    url: '/existing_evidence/printList',
    method: 'post',
    params,
  });
};
//电子存证往上调
export const evidenceMoveUp = (params: { newId: string; oldId: string }) => {
  return request({
    url: '/existing_evidence/moveUp',
    method: 'post',
    params,
  });
};
//电子存证往下调
export const evidenceMoveDown = (params: { newId: string; oldId: string }) => {
  return request({
    url: '/existing_evidence/moveDown',
    method: 'post',
    params,
  });
};
//电子存证直接保存
export const directUploadByEvidence = (
  data: RecordForm & { fileUrl: string; fileName: string; tenantId: string },
  params: { labels: string }
) => {
  return request({
    url: '/existing_evidence/save',
    method: 'post',
    data,
    params,
  });
};
//电子存证历史文件保存
export const directUploadByHis = (
  params: { labels: string },
  data: RecordForm & { fileUrl: string; fileName: string; tenantId: string }
) => {
  return request({
    url: '/existing_evidence/saveByHis',
    method: 'post',
    params,
    data,
  });
};

// 查阅记录
export const lookRecordPage = (params: archives.lookRecordParams & ReqPage) => {
  return request({
    url: '/view_records/page',
    method: 'post',
    params,
    data: {},
  });
};
// 查阅公证
export const inquireNotarizeUrl = (data: { tenantId: string; evidenceNumber: string }) => {
  return request({
    url: '/notarizationFile/storeLetterUrl',
    method: 'post',
    data,
  });
};

//文件列表分页
export const filePage = (params: files.pageParams | pageParamsElectric) => {
  return request({
    url: '/file/page',
    method: 'post',
    params,
    data: {},
  });
};
//文件列表分页
export const fileDetail = (params: { id: string }) => {
  return request({
    url: '/file/detail',
    method: 'get',
    params,
  });
};

//文件列表历史
export const fileHistoryPage = (params: files.pageParams) => {
  return request({
    url: '/file/historyPage',
    method: 'post',
    params,
    data: {},
  });
};

//文件树-新增目录
export const addMenuTop = (params: { ids: string[]; name: string }) => {
  return request({
    url: '/file_menu/addTop',
    method: 'post',
    params,
  });
};
//文件树-新增目录子级
export const addMenu = (params: { id: string; name: string }) => {
  return request({
    url: '/file_menu/add',
    method: 'post',
    params,
  });
};

//文件列表-目录
export const fileMenuTree = (params: { tentantid: string }) => {
  return request({
    url: '/file_menu/getTree',
    method: 'post',
    params,
    data: {},
  });
};
//文件列表-目录排序
export const moveTree = (data: any) => {
  return request({
    url: '/file_menu/move',
    method: 'post',
    data,
  });
};

//文件列表树
export const fileMenu = (params: archives.archivesParams) => {
  return request({
    url: '/file_menu/detail',
    method: 'get',
    params,
    data: {},
  });
};
//重命名-子级
export const updateFileMenu = (params: files.addFileParams) => {
  return request({
    url: '/file_menu/update',
    method: 'post',
    params,
  });
};
//绑定版本-目录
export const updateFileMenuTop = (params: { menuId: string; versionId: string }) => {
  return request({
    url: '/file_menu/updateTop',
    method: 'post',
    params,
  });
};

//删除
export const removeFileMenu = (params: { id: string }) => {
  return request({
    url: '/file_menu/remove',
    method: 'post',
    params,
  });
};

//文件-修改标签
export const updateLabel = (params: { ids: string; id: string }) => {
  return request({
    url: '/file/updateLabel',
    method: 'post',
    params,
  });
};
//文件恢复
export const recoveryFile = (params: { ids: string }) => {
  return request({
    url: '/file/recovery',
    method: 'post',
    params,
  });
};
//文件删除
export const removeFile = (params: { ids: string }) => {
  return request({
    url: '/file/remove',
    method: 'post',
    params,
  });
};
//文件下载
export const downloadFile = (params: { id: string }) => {
  return request({
    url: '/file/download',
    method: 'post',
    params,
  });
};
//文件打印
export const printFile = (params: { id: string }) => {
  return request({
    url: '/file/print',
    method: 'post',
    params,
  });
};
//文件批量打印
export const printFileList = (params: { ids: string }) => {
  return request({
    url: '/file/printList',
    method: 'post',
    params,
  });
};
//文件往上移动
export const fileMoveUp = (params: { oldId: string; newId: string }) => {
  return request({
    url: '/file/moveUp',
    method: 'post',
    params,
  });
};

//文件往下移动
export const fileMoveDown = (params: { oldId: string; newId: string }) => {
  return request({
    url: '/file/moveDown',
    method: 'post',
    params,
  });
};
//文件转存
export const fileMove = (params: { menuid: string; ids: string }) => {
  return request({
    url: '/file/move',
    method: 'post',
    params,
  });
};
//文件直接上传保存
export const directUploadByFile = (data: any, params: { labels: string }) => {
  return request({
    url: '/file/save',
    method: 'post',
    data,
    params,
  });
};
//文件历史上传保存
export const savebyHisFile = (data: any, params: { labels: string }) => {
  return request({
    url: '/file/saveByHis',
    method: 'post',
    data,
    params,
  });
};

//任务生成文件-请求数据
export const getDataByTask = (params: any, data: any) => {
  return request({
    url: '/existing_evidence/getData',
    method: 'post',
    params,
    data,
  });
};
//任务生成文件-获取文件列表
export const getFileList = (params: { ids: string; type: number }) => {
  return request({
    url: '/file/getFileList',
    method: 'post',
    params,
  });
};
//即时办事同时保存存档存证文件
export const fileSaveAll = (data: any, params: any) => {
  return request({
    url: '/file/saveAll',
    method: 'post',
    data,
    params,
  });
};
//电子档案全部删除
export const ArchivesRemoveAll = (params: any) => {
  return request({
    url: '/electronic_archives/removeAll',
    method: 'post',
    data: {},
    params,
  });
};
//电子档案全部打印
export const ArchivesPrintAll = (params: any) => {
  return request({
    url: '/electronic_archives/printAll',
    method: 'post',
    data: {},
    params,
  });
};
//电子档案全部下载
export const ArchivesDownloadAll = (params: any) => {
  return request({
    url: '/electronic_archives/downloadAll',
    method: 'post',
    data: {},
    params,
  });
};
//电子存证全部删除
export const EvidenceRemoveAll = (params: any) => {
  return request({
    url: '/existing_evidence/removeAll',
    method: 'post',
    data: {},
    params,
  });
};
//电子存证全部打印
export const EvidencePrintAll = (params: any) => {
  return request({
    url: '/existing_evidence/printAll',
    method: 'post',
    data: {},
    params,
  });
};
//电子存证全部下载
export const EvidenceDownloadAll = (params: any) => {
  return request({
    url: '/existing_evidence/downloadAll',
    method: 'post',
    data: {},
    params,
  });
};
//文件全部删除
export const fileRemoveAll = (params: any) => {
  return request({
    url: '/file/removeAll',
    method: 'post',
    params,
    data: {},
  });
};
//文件全部打印
export const filePrintAll = (params: any) => {
  return request({
    url: '/file/printAll',
    method: 'post',
    params,
    data: {},
  });
};
//文件全部打印
export const fileDownloadAll = (params: any) => {
  return request({
    url: '/file/downloadAll',
    method: 'post',
    params,
    data: {},
  });
};
