import { 
  AllDefaultValueType, 
  FieldBaseField, 
  TitleField,
  basePermissionType,
  RemarkField,
  SpecificType 
} from '@/components/form-config/types/AvueTypes';
import { DateFormatTypeEnum } from './const'
/* 日期时间 */

export type dataDefaultVal = {
  value: string | number;
  type: string;
};

export type DateField = basePermissionType &  SpecificType & {
  format?: string;
  // type: DateFormatTypeEnum;
  valueFormat: string;
  dateFormatType: DateFormatTypeEnum;
  startPlaceholder: string;
  endPlaceholder: string;
  limitWeek: boolean;//限制可选星期
  limitWeekVal?: string[],//限制可选星期-值
  limitTime?: boolean;//限制可选时间
  limitTimeStart?: string,//限制可选时间起
  limitTimeEnd?: string,//限制可选时间止
  limitMinuteIntervals?: boolean;//预设分钟间隔
  limitMinuteIntervalsVal?: string,//预设分钟间隔-值
  limitStartDate: boolean;//限制开始日期
  limitStartDateType?: string,//限制开始日期-类型
  limitStartDateVal?: dataDefaultVal,//限制开始时间-值
  limitEndDate: boolean;//限制结束时间
  limitEndDateType?: string,//限制结束时间-类型
  limitEndDateVal?: dataDefaultVal,//限制结束时间-值
};
export type DateAllField = FieldBaseField & TitleField & DateField & 
AllDefaultValueType & RemarkField;