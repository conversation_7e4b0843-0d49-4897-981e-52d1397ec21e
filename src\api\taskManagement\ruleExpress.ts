import request from '@/axios';
import { ruleBySheetProp } from '@/views/mainview/sceneTasks/components/RuleExpress/types';

// 添加规则
type addType = {
  ruleName: string;
  ruleType: string;
  versionId: string;
};
export const addRule = (data: addType) => {
  return request({
    url: '/ruleExpress/addRule',
    method: 'post',
    data,
  });
};

export const delRule = (data: { ruleExpressId: string; ruleType: string }) => {
  return request({
    url: '/ruleExpress/remove',
    method: 'post',
    params: data,
  });
};

// 规则速递分页
export const rulePage = (
  params: { current: number; size: number },
  data: { versionId: string; ruleName?: string; ruleType?: string; scope?: string }
) => {
  return request({
    url: '/ruleExpress/page',
    method: 'post',
    params,
    data,
  });
};

// 编辑回显
type ruleShowType = {
  ruleExpressId: string;
  versionId: string;
};
export const ruleEditShow = (params: ruleShowType) => {
  return request({
    url: '/ruleExpress/editShow',
    method: 'get',
    params,
  });
};

// 编辑 值与值
type ruleValueType = {
  fieldId: string;
  formula: string;
  ruleExpressId: string;
  versionId: string;
  taskIdList: string[];
};

export const ruleEditByValue = (data: ruleValueType) => {
  return request({
    url: '/ruleExpress/editValue',
    method: 'post',
    data,
  });
};

// type CustomRule = Omit<ruleBySheetProp, 'groupingField'> & {
//   groupingField: string[];
// };
// 值与报表
export const ruleEditByExport = (
  data: any
) => {
  return request({
    url: '/ruleExpress/editExport',
    method: 'post',
    data,
  });
};

// 编辑 值与文件
export type ruleFileType = {
  fieldId: string;
  dataPath: string;
  fieldCondition: string;
  fieldRule: string;
  files: any;
  showType: string;
  versionId: string;
  ruleExpressId: string;
  taskIdList: string[];
};

export const ruleEditByFile = (data: ruleFileType) => {
  return request({
    url: '/ruleExpress/editFile',
    method: 'post',
    data,
  });
};
export const getFileDetail = (params: { fileId: string }) => {
  return request({
    url: '/ruleExpress/fileDetail',
    method: 'get',
    params,
  });
};

// 编辑 值与数据表
export type ruleTableDataType = {
  dataTableId: string;
  fieldCondition?: string;
  linkageFieldId?: string;
  ruleExpressId: string;
  versionId: string;
  taskIdList: string[];
};

export const ruleEditByTableData = (data: ruleTableDataType) => {
  return request({
    url: '/ruleExpress/editDataTable',
    method: 'post',
    data,
  });
};
// 规章制度保存
export const rulesSubmit = (data: any) => {
  return request({
    url: '/rules/rulesSubmit',
    method: 'post',
    data,
  });
};
export const rulesDetail = (params: { fileId: string }) => {
  return request({
    url: '/rules/rulesDetail',
    method: 'get',
    params,
  });
};
