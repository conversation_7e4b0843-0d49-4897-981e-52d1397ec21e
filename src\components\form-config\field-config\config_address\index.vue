<template>
  <config-title :data="data" />
  <config-permission :data="data">
     <el-checkbox v-model="data.addressRequired" label="详细地址必填" v-if="data.required" />
     <el-checkbox v-model="data.addressDisplay" label="详细地址可见" />
  </config-permission>
  <el-form-item label="格式">
    <xt-select
      v-model="data.addressPrecision"
      :dic="addressPrecisionFilter"
      :clearable="false"
      @change="handlePrecisionChange"
    />
  </el-form-item>
  <config-default-type :data="data"  v-if="formType!== formTypeEnum.KNOWLEDGE"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import { addressPrecision } from './const';
import { AddressAllField } from './type';
import { CustomDefaultType } from '@/components/form-config/types/AvueTypes';

import { formTypeEnum } from '@/components/form-config/const/commonDic';

const formType = inject('formType',null)
const props = defineProps<{
  data: AddressAllField;
}>();

const handlePrecisionChange = () => {
  (props.data as CustomDefaultType).defaultValue = undefined;
};

const addressPrecisionFilter = computed(() => {
  if (props.data.isCrud) {
    // return addressPrecision.slice(1);
  }

  return addressPrecision;
});

watchEffect(() => {
  if (!props.data.required) {
    props.data.addressRequired = false
  }
  // if(!props.data.addressDisplay){
  //   props.data.defaultValue = {
  //     ...(props.data.defaultValue || {}),
  //     address: ''
  //   };
  // }
});
</script>
