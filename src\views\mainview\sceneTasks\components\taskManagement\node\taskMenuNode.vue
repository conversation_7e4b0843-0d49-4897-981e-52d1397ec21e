<template>
    <ul class="menu_box " ref="menuBoxRef">
        <li class="menu__item" v-for="item in nodeData.menu" @click="item.handle(<VueShape<VueShape.Properties>>nodeData.currentNode)">
            {{ item.title }}
        </li>
    </ul>
</template>
<script setup lang="ts">
import {inject} from "vue";
import {VueShape} from "@antv/x6-vue-shape";
export interface TaskMenuProps{
  title: string,
  handle:(node:VueShape)=>void
}
export interface MenuDataProps {
  menu: TaskMenuProps[]
  currentNode: VueShape,
}
const menuBoxRef=ref<HTMLElement>()
//动态监听节点高度变化
watchEffect(()=>{
  if (menuBoxRef.value?.offsetHeight){
    const height=menuBoxRef.value?.offsetHeight||120
    node.resize(120,height)
  }
})
const node = inject<any>("getNode")() as Vue<PERSON>hape
const nodeData=ref<MenuDataProps>(node.getData<MenuDataProps>())

</script>

<style scoped lang="scss">
ul{
    margin: 0;
    padding: 0;
    li{
        list-style: none;
    }
}
.menu_box{
    background: #FFFFFF;
    box-shadow: -1px 2px 13px 0px rgba(220,229,231,0.69);
    border-radius: 5px;
    padding: 5px 10px;
    .menu__item{
        margin: 10px 0;
      white-space: nowrap;
        padding: 10px 20px;
        transition:background .25s;
        font-size: 15px;
        font-weight: 400;
        color: #666666;
        &:hover{
            background: #F2F5F6;
            cursor: pointer;
        }
    }
}
</style>