<template>
  <config-title :data="data" />
  <config-permission :data="data">
    <el-checkbox v-model="data.limitFileType" label="限制文件格式" :disabled="data.archive"/>
    <avue-checkbox
      v-if="data.limitFileType"
      :disabled="data.archive"
      class="file-type-select"
      v-model="data.limitFileTypeList"
      :dic="data.archive? archiveFileType : fileType"
    ></avue-checkbox>
  </config-permission>
  <el-form-item label="限制文件上传个数">
    <div class="flex items-center">
      <el-input-number
        v-model="data.fileNumber"
        controls-position="right"
        placeholder="限制文件上传个数"
        :min="0"
        style="width: 100%"
      ></el-input-number>
      <div class="color-gray ml-2">个</div>
    </div>
  </el-form-item>
  <el-form-item label="上传文件最大体积">
    <div class="flex items-center">
      <el-input-number
        v-model="data.fileSizeMB"
        controls-position="right"
        placeholder="文件大小限制（字节）"
        :min="0"
        style="width: 100%"
      ></el-input-number>
      <div
        style="
          font-size: 14px;
          line-height: 22px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.25);
          margin-left: 15px;
        "
      >
        MB
      </div>
    </div>
  </el-form-item>
  <config-default-type :data="data"  v-if="formType!== formTypeEnum.KNOWLEDGE"/>
  <el-form-item label="存档配置">
     <el-checkbox v-model="data.archive" label="存证存档" v-if="formType !== formTypeEnum.APPLICATION"/>
  </el-form-item>
 
  <config-archive-condition :data="data" v-if="data.archive"/>
  <config-archive-set :data="data" v-if="data.archive"/>
  <config-span :data="data" />
</template>

<script setup lang="ts">
import { fileType,archiveFileType } from './const';
import { UploadAllField } from './type';;
import ConfigDefaultType from '@/components/form-config/common-config/ConfigDefaultType/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import { formTypeEnum } from '@/components/form-config/const/commonDic';
import ConfigArchiveSet from './components/ConfigArchiveSet.vue';
import ConfigArchiveCondition from './components/ConfigArchiveCondition.vue';

const formType = inject('formType',null)
const props = defineProps<{
  data: UploadAllField;
}>();

watchEffect(() => {
  if (props.data.limitFileType) {
    props.data.accept = props.data.limitFileTypeList
      .map(item => item.split(','))
      .flatMap(item => item);
  } else {
    props.data.accept = undefined;
    props.data.limitFileTypeList = [];
  }
});

watchEffect(() => {
  const val = props.data.fileSizeMB;
  if (_.isFinite(val)) {
    props.data.fileSize = val! * 1024;
  } else {
    props.data.fileSize = undefined;
  }
});

watch(
  () => props.data.archive,
  (val) => {
    props.data.limitFileTypeList = [];
    props.data.accept = []
    if(val){
      props.data.limitFileTypeList = [archiveFileType[0].value]
      props.data.limitFileType = true

    }else{
      props.data.limitFileType = false
      props.data.fileSet = null
      props.data.archiveCondition = null
    }
  }
)

</script>
<style lang="scss">
.file-type-select {
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    width: 180px;
    padding: 15px;
    border: 1px solid #e4e7ed;
  }
}
</style>
