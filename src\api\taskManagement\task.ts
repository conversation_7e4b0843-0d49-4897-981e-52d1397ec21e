import request from '@/axios';
import { TaskTreeProps } from '@/views/mainview/sceneTasks/components/taskManagement/hook/useGraphHook.ts';

/**
 * 获取任务列表
 * @param taskPackageId 任务包id
 */
export const getTaskTree = (taskPackageId: string) => {
  return request({
    url: '/nbxt-service-task/task/tree',
    method: 'post',
    params: {
      taskPackageId,
    },
  });
};
/**
 * 获取任务列表平级
 * @param taskPackageId 场景集id
 */
export const getTaskList = (taskPackageId: string) => {
  return request({
    url: '/scene_task_group/getAllTasksBySceneGroupId',
    method: 'get',
    params: {
      sceneTaskGroupId: taskPackageId,
    },
  });
};
/**
 * 获取任务数据
 */
export const getTaskData = (
  params: Partial<{ taskId: string; taskPackageId: string; query?: any }>
) => {
  return request({
    url: '/nbxt-service-task/shouldDo/dataList',
    method: 'post',
    data: {
      ...params,
    },
  });
};
/**
 * 获取任务数据col
 */
export const getTaskDataColumn = (taskPackageId?: string, id?: string) => {
  return request({
    url: '/nbxt-service-task/task/formCol',
    method: 'post',
    params: {
      taskPackageId,
      id,
    },
  });
};
/**
 * 新增或修改任务
 * @param params 任务参数
 */
export const addAndUpdateTask = (params: Partial<TaskTreeProps>) => {
  console.log('ppp',params);
  // if(!params.id){
  //   params.id = Math.random().toString();
  // }
  // return new Promise((resolve)=>{
  //   resolve({data:{data:params}});
  // })
  return request({
    url: '/metrics/saveKanban',
    method: 'post',
    data: {
      node:params,
    },
  });
};
/**
 * 消息列表
 */
export const taskMessageList = (taskPackageId: string) => {
  return request({
    url: '/nbxt-service-task/task/changeMessage/list',
    method: 'post',
    params: {
      taskPackageId,
    },
  });
};
/**
 * 删除消息
 */
export const delTaskMessage = (ids: string) => {
  return request({
    url: '/nbxt-service-task/task/changeMessage/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};
/**
 * 删除任务
 * @param taskPackageId 任务包id
 * @param id 任务id
 * @param deleteAll 是否删除所有
 */
export const removeTask = (taskPackageId: string, id: string, deleteAll: boolean) => {
  return request({
    url: '/nbxt-service-task/task/remove',
    method: 'post',
    params: {
      taskPackageId,
      id,
      deleteAll,
    },
    data: {},
  });
};
export const removeTargetNode = (id: string, isAll: boolean) => {
  return request({
    url: '/metrics/removeNode',
    method: 'post',
    params: {
      id,
      isAll,
    }
  });
};
/**
 * 设置执行人告知人
 * @param params 任务参数
 */
export interface LaunchFormProps {
  selfContent: string[];
  taskIds: string[];
  selfType: 1 | 2 | 3;
  roleId: string[]
}
export const setLaunchNotify = (params: LaunchFormProps) => {
  return request({
    url: '/nbxt-service-task/task/setExecuteBatch',
    method: 'post',
    data: {
      ...params,
    },
  });
};

export const getSignData = (
  params: Partial<{ taskId: string; taskPackageId: string; signField: string; query?: any }>
) => {
  return request({
    url: '/nbxt-service-task/shouldDo/signList',
    method: 'post',
    data: {
      ...params,
    },
  });
};


//获取推送数据列表
export const getPushTaskList = (data: { version: string,type:string }) => {
  return request({
    url: '/linkage/dataPath',
    method: 'post',
    data
  });
};

//根据推数路径获取组件列表
export const getFieldListByTaskId = (data: { type: number | string; path: string }) => {
  return request({
    url: '/linkage/dataPathComp',
    method: 'post',
    data
  });
};