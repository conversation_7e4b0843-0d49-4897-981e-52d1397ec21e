<script setup lang="ts">
import ConfigTitle from '@/components/form-config/common-config/ConfigTitle/index.vue';
import ConfigPermission from '@/components/form-config/common-config/ConfigPermission/index.vue';
import ConfigSpan from '@/components/form-config/common-config/ConfigSpan/index.vue';
import FilterCalcConditionDialog from './components/features/filterCalcConditionDialog.vue';
import { getDataPathByVersionId, getDataPathChildrenByVersionId } from '@/api/taskManagement/componentFetch';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore.ts';
import { ElMessage } from "element-plus";
import { CalculationTableAllType } from '@/components/form-config/types/AvueTypes';

const props = defineProps<{
  data: CalculationTableAllType;
}>();


const { detail } = storeToRefs(useTaskDetailStore());


const dialogVisible = ref(false)

const calcTableList = ref<any[]>([])

const calcTableFieldList = ref([])

const openSelect = () => {
  dialogVisible.value = true
}


// 获取计算表
const getTaskDic = async (versionId:string) => {
  const { data } = await getDataPathByVersionId({version:versionId,type:4});
  if(!_.isEmpty(data?.data)){
    calcTableList.value = data?.data
  }
}

const getFieldDic = async (id:string) => {
  if(!id) return;
  const { data } = await getDataPathChildrenByVersionId({
    type:'2',
    path: id,
    knowConditions:0,
  });
  if(!_.isEmpty(data?.data)){
    calcTableFieldList.value = data?.data
  }
}


onMounted(()=>{
  if((detail.value as any).versionId){
    getTaskDic((detail.value as any).versionId);
  }
})

watch(
  () => props.data.calTableId,
  (v) => {
    if(v){
      getFieldDic(v)
    }
  },
  {
    immediate: true
  }
)

function handleMessage(){
  if(!props.data.calTableId && !props.data.calTableFieldId){
    ElMessage('请选择计算表和字段')
  }else if(!props.data.calTableId){
    ElMessage('请选择计算表')
  }else {
    ElMessage('请选择字段')
  }
}

function changeCal(){
  props.data.calTableFieldId = '';
}


</script>

<template>
  <config-title :data="data" />
  <config-permission :data="data" :show-edit="false" :has-required="false" />
  <el-form-item label='数据路径'>
    <xt-select
      v-model="data.calTableId"
      class="w-full mb-10px"
      :dic="calcTableList"
      placeholder="请选择计算表"
      v-bind="$attrs"
      filterable
      @change="changeCal"
    >
    </xt-select>
    <xt-select
      v-model="data.calTableFieldId"
      class="w-full mb-10px"
      :dic="calcTableFieldList"
      placeholder="请选择字段"
      v-bind="$attrs"
      filterable
    >
    </xt-select>
  </el-form-item>

  <el-form-item label="过滤条件">
    <el-card class="box-card" @click="!(data.calTableId && data.calTableFieldId) ? handleMessage() : openSelect()">
      <div class="text-center font-bold">过滤条件</div>
      <FilterCalcConditionDialog
        v-model:visible="dialogVisible"
        v-model:filter="data.filter"
        :range="data.calTableFieldId"
      />
    </el-card>
  </el-form-item>

  <config-span :data="data" />
</template>

<style scoped lang="scss">
.box-card {
  cursor: pointer;
  width: 100%;

  :deep(.el-card__body) {
    padding: 0;
    border-radius: 0;
    min-height: 24px;
    border: 1px dashed #eee;
  }
}
</style>