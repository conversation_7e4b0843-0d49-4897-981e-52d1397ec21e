//import { pushTaskListType } from './task';
import { MethodSymbolEnum } from '@/views/mainview/sceneTasks/detail/option/field';
import { AvueColumns } from '@/components/form-config/types/AvueTypes.ts'
//当前组件
export interface fieldListType {
  idx: string;
  operator?: MethodSymbolEnum;
  prop: string;
  value?: string | number;
}

export interface filterCondition {
  filterColumnList?: Partial<filterConditionItemType>[]; //子筛选条件
  filterOperator?: string; //筛选条件逻辑运算符
  path: string; //推送任务
  pathType: number; //推送任务类型 数据表还是任务可用值:TASK,DATA_TABLE,CAL_TABLE
  // currentFieldType: string;//当前字段类型
  linkage?: boolean; //空值是否联动
  field?: string; //联动字段
  logicOperator?: string;
  pushFieldList?: AvueColumns[];
}

//
export interface filterConditionItemType {
  idx: string;
  filterType: string; //条件类型
  dataProp: string; //推数路径字段
  linkageProp?: string; //联动任务字段
  currentFormProp?: string; //当前表单字段
  value?: string; //自定义值
  customize?: string; //微应用自定义值
  filterColumn?: string; //微应用自定义值
  operator?: MethodSymbolEnum; //逻辑运算符
}

export interface extremeRuleType {
  extremumDirection: number; //极值方向
  extremumItem: string; //最大最小
  rule?: string; //最大最小--微应用专用
  extremumOption: number; //最新最旧
  extremumSelectDate: string; //具体日期
  extremumSelectTime: string; //极值规则选择(选择时间)(当年/当月/当周/当日)
  extremumType: string; //数字类型
  extremeProp?: string; //数字类型--微应用专用
  extremumSelectDay: number; //天
  extremumSelectHour: number; //小时
  extremumSelectMin: string; //分
}

export interface dataType {
  columnList: fieldListType[]; //当前任务组件
  extremeRule: Partial<extremeRuleType>; //极值规则
  filterCriteriaList: filterCondition[]; //多任务触发联动的筛选条件
  linkagePath?: string; //微应用专用推演路径
  microAppExtreme?: any; //微应用专用极值规则
  microAppFilter?: any; //微应用专用筛选条件
  linkageProp?: any; //微应用专用联动字段
  linkageField: filterCondition; //触发联动和非触发联动的筛选条件
  linkageColumnList: fieldListType[]; //联动任务组件
  linkageOperator: string; //联动条件逻辑条件运算符
  linkageTaskId: string; //联动任务id
  linkageTaskType: number;
  logic: string; //当前任务条件逻辑运算符,可用值:1,2
  logicBetween: string; //当前任务条件逻辑运算符,可用值:1,2
  dynamicFields: dynamicType[] //类表格类型的组件联动
  dynamicField?: dynamicType[] //类表格类型的组件联动
}

export interface dynamicType {
  idx: string;
  currentProp: string;
  pushProp: string
}

export interface FilterItemType {
  linkageField: {
    field?: string;
    path: string; //推送任务
    pathType: number; //推送任务类型 数据表还是任务可用值:TASK,DATA_TABLE,CAL_TABLE

  };
  dynamicFields: dynamicType[]
}

//会办数据汇总/逐一触发的添加筛选
export interface addFilterItemType{
  list: FilterItemType[]
}

//数据联动
export interface dataLinksItem extends Partial<dataType> {
  idx: string;
  method: number; //触发方式
}

export interface dataLinksType {
  list: dataLinksItem[];
}



export interface DataCollectRuleType {
  extremumType: string;
  extremumOption: string |number;
}

export interface sortRuleType {
  componentId: string;
  extremumItem: string | number;
}


export interface showFieldType {
  prop: string;
  label: string;
}

//添加筛选条件
export interface DataCollectItem extends Partial<dataType> {
  idx?:string;
  sortRule?: sortRuleType;
  showField: showFieldType[];
  showFieldIds: string[]
}

export interface DataCollectType {
  list: DataCollectItem[];
}


//分组汇总

export interface groupItem{
  idx: string;
  pathField: string;//分组字段
  method: string;
  field: string;//绑定字段
}

export interface groupSummaryType {
  path: string;
  group?: groupItem[];
  summary?:groupItem[]
}
