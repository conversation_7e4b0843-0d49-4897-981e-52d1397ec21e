import request from '@/axios';
import { Catalog, Agency } from '@/api/interface'

//档案目录列表
export const getCataloglList = (params: Catalog.ReqParams,data:Catalog.ReqSearch = {name:'',code:''}) => {
  return request({
    url: '/archival_catalogue/page',
    method: 'post',
    params,
    data
  });
};

//机构列表
export const getAgencyList = (params: Agency.ReqParams,data:Agency.ReqSearch = {name:'',code:''}) => {
  return request({
    url: '/banban_org_code/page',
    method: 'post',
    params,
    data
  });
}; 