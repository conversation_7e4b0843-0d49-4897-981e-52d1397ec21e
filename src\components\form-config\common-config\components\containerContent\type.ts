import {
  FieldCondition,
  FieldInitiateCondition,
} from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts'
export type insideCondition = {
  type: number;
  leftTaskId: string;
  fieldId: string;
  condition: string;
  rightTaskId?: string;
  result: string;
};
// 内部筛选条件
export type insideScreenType = {
  screenTaskId?: string;
  screenAndOr?: number;
  condition?: insideCondition[];
  peakType?: number;
  peakValue?: number;
  sortFieldId?: number;
  sortType?: number;
};

export type hideGroupType = {
  hideAndOr?: number;
  hideTaskId?: string;
  touchCondition?: FieldInitiateCondition[];
  currentCondition?: FieldInitiateCondition[];
};

export type conditionGroupType = hideGroupType &
  insideScreenType & {
    id: string;
    showField?: string[]; // 展示字段
  };
// 筛选条件
export type screenConditionPropType = {
  data: conditionGroupType[];
};
 