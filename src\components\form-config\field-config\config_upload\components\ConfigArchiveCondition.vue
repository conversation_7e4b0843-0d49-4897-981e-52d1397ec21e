<template>
  <div class="mb-2">
    <el-button class="w-full" size="large" @click="visible = true">条件配置</el-button>
    <el-dialog 
      v-model="visible" 
      title="条件配置" 
      width="800" 
      :destroy-on-close="true"
      >
      <n-space vertical :size="20">
        <accordTitle title="添加条件" v-model="andOr" @add="handleAdd"/>
        <template v-if="archiveCondition && archiveCondition.length ">
          <conditionCustomItem v-for="(item, index) in archiveCondition"
            :fieldList="fieldList"
            v-model:symbol="item.operator"
            v-model:fieldId="item.prop"
            v-model:result="item.value"
            :current="true"
            :currentField="data"
            @delete="handleDelete(index)"
            :filterSymbolField="[]"
            :key="item.idx"
          />
        </template>
      </n-space>
      <template #footer>
        <div class="flex justify-end">
          <el-button type="primary" @click="handleSubmit" round>保存</el-button>
        </div>
      </template>
  
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import accordTitle from '@/components/form-config/common-config/components/accordTitle/index.vue'
import { UploadAllField, conditionType } from '../type'
import { ElMessage } from 'element-plus';
import { generateId } from '@/components/form-config/utils';
import { storeToRefs } from 'pinia';
import useTaskDetailStore from '@/views/mainview/sceneTasks/detail/store/useTaskDetailStore';
import { TaskBase } from '@/views/mainview/sceneTasks/detail/types/TaskDetailType.ts';
import { archiveDefaultField } from '@/components/form-config/types/FieldTypes';
import ConditionCustomItem from '@/components/conditions/conditionCustomItem.vue';

const { taskBase } = storeToRefs(useTaskDetailStore());

const props = defineProps<{
  data: UploadAllField;
}>();
const visible = ref(false);
const andOr = ref('1')
const archiveCondition = ref<conditionType[]>()
const handleAdd = () => {
  if (_.isArray(archiveCondition.value)) {
    const haveEmpty = archiveCondition.value?.some(i => _.isEmpty(i.operator) || _.isEmpty(i.prop));
    if (haveEmpty) {
      ElMessage.warning('有空条目请勿添加新的条目');
      return;
    }
  }
  (archiveCondition.value ??= []).push({ prop: '', value: '', idx: generateId() });
  
};

//当前任务的组件
const fieldList = computed(() => {
  let column = (taskBase.value as TaskBase).column || [];
  let fieldList = [...column].filter(item => _.includes(archiveDefaultField,item.fixType));
  return fieldList.map(v => {
    return {
      ...v,
      label: v.copyLabel,
      contentType: v.fixType,
      fieldType: v.fixType,
      disabled:false
    };
  });
});

const handleDelete = (index: number) => {
  archiveCondition.value?.splice(index,1)
};

const handleSubmit = () => {
   let haveEmpty = true
   if(_.isArray(archiveCondition.value)){
    haveEmpty = archiveCondition.value?.some(i => _.isEmpty(i.operator) || _.isEmpty(i.prop));
   }
    if(haveEmpty) {
      ElMessage.warning('条件不可有空条目');
      return
    }
    props.data.archiveCondition = _.cloneDeep(archiveCondition.value) as conditionType[];
    visible.value = false;
}

watch(
  () => visible.value,
 (val) => {
    if (val) {
      archiveCondition.value = _.cloneDeep(props.data.archiveCondition) || []
    }
  }
);


</script>

<style scoped></style>