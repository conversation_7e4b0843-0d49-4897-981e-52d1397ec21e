<template>
  <component
    :is="getComponent(data.fixType, data.component, data?.showFormat)"
    v-bind="data"
    :dic="(data as unknown as SelectField).dicData"
    :placeholder="noPlaceHolder ? '' : getPlaceholder(data, '默认值')"
    :disabled="false"
  >
  </component>
</template>

<script setup lang="ts">
import useComponents from '../form-config/hooks/useComponents';
import { SelectField, FieldBaseField,InputField } from '@/components/form-config/types/AvueTypes';

defineProps<
  {
    data:FieldBaseField & InputField,
    noPlaceHolder:boolean
  }
>();

const { getComponent, getPlaceholder } = useComponents();
</script>
