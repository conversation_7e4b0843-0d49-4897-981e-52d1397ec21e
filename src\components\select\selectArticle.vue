<template>
    <div>
        <selectComponent title="选择物品" :tab-list="tabList" v-model:modelList="modelList" v-model:visible="visible"
            :submitApi="submitApi" :getTableList="getTableList" :dataCallBack="dataCallBack" :isFilterWork="false">
        </selectComponent>
    </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { getArticleByOrgTree, getArticleByGroup } from '@/api/common/index';
import selectComponent from './selectComponent.vue';
import { useStore } from 'vuex';

const store = useStore();
const userMsg = store.state.user.userInfo;
const modelList = defineModel<any[]>('modelList', { default: () => [] });
const visible = defineModel<boolean>('visible', { default: false });
defineEmits<{
    (e: 'change', value: any[] | string): void;
}>();

interface DrawerProps {
    submitApi?: (params: any) => Promise<any>;//提交的接口
    getTableList?: () => void;//保存后请求表格数据的接口
    dataCallBack?: (data: any) => any,//处理提交的数据
    config?: configType;
}

interface configType {
    tid?: string;
}
const props = defineProps<DrawerProps>();

const tabList = [
    {
        name: "first",
        label: "物品选择",
        api: getArticleByOrgTree,
        apiParams: { tid: props.config?.tid ?? userMsg.tid },
        leftProp: { children: "children", label: "warehouseName", value: "id", leftSearchId: "groupIds" },
        rightProp: { label: "goodsName", id: "id" },
        getDataApi: getArticleByGroup
    }
]
//父组件传过来的数据
watch(
    () => visible.value,
    val => {
        if (val) {



        }
    }
);
</script>