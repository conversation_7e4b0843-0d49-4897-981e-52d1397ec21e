import request from '@/axios';

//个人退休计算器
export const getRetireDetail = (params: { sfz: string }) => {
  return request({
    url: '/retirement_age/getBySfz',
    method: 'get',
    params
  });
};

//批量导入身份证
export const exportRetire = (data: any) => {
  return request({
    url: '/retirement_age/import',
    method: 'post',
    data
  });
};

//新增企业信息
export const addEnterInfo = (params: any) => {
  return request({
    url: '/retirement_age/record',
    method: 'post',
    params
  });
};