import request from '@/axios';
import { deptType, groupType, spaceType, articleType, customerGroupType } from './type';

//根据组织id获取部门列表
export const getDeptByOrgList = (params: deptType.ReqParams) => {
  return request({
    url: '/common/dept/list',
    method: 'get',
    params,
  });
};

//根据组织id获取部门列表tree，嵌套
export const getDeptByOrgTree = (params: deptType.ReqParams) => {
  return request({
    url: '/common/dept/tree',
    method: 'get',
    params,
  });
};

//根据部门id获取用户列表
export const getUserByDept = (params: deptType.ReqUserParams) => {
  return request({
    url: '/common/dept/user/list',
    method: 'get',
    params,
  });
};

// 根据部门ids 获取 用户列表
export const getUserByDeptIds = (data: string[]) => {
  return request({
    url: '/common/getUsersByDeptIds',
    method: 'post',
    data
  });
};


//根据用户id集合获取部门
export const getDeptByUser = (data: any) => {
  return request({
    url: '/common/user/dept/list',
    method: 'post',
    data
  });
};

//根据用户id集合获取部门
export const getDeptDetail = (params: any) => {
  return request({
    url: '/blade-system/dept/detail',
    method: 'get',
    params
  });
};

//根据用户id集合获取工作组
export const getGroupByUser = (ids: any) => {
  return request({
    url: '/common/user/workgroup/list',
    method: 'post',
    data: ids,
  });
};

// 根据工作组ids 获取 用户列表
export const getUserByGroupIds = (data: string[]) => {
  return request({
    url: '/common/getWorkGroupUsersByWorkGroupIds',
    method: 'post',
    data
  });
};

//根据组织id获取工作组列表tree，嵌套
export const getGroupByOrgTree = (params: groupType.ReqParams) => {
  return request({
    url: '/workgroup/tree',
    method: 'get',
    params,
  });
};

//根据工作组id获取用户列表
export const getUserByGroup = (params: groupType.ReqUserParams) => {
  return request({
    url: '/common/workgroup/user/list',
    method: 'get',
    params,
  });
};

//根据组织id获取空间组列表tree，嵌套
export const getSpaceByOrgTree = (params: spaceType.ReqParams) => {
  return request({
    url: '/space_group/tree',
    method: 'get',
    params,
  });
};

//根据空间组id获取空间列表
export const getSpaceByGroup = (data: spaceType.ReqDataParams) => {
  return request({
    url: '/space/getSpaceByGroupIds',
    method: 'put',
    data:[data.ids]
  });
};

//根据空间ids获取空间
export const getSpaceByGroupIds1 = (data: string[]) => {
  return request({
    url: '/space/getSpaceByIds',
    method: 'put',
    data
  });
};

//根据空间ids获取空间
export const getSpaceByGroupIds = (data: {ids:string}) => {
  return request({
    url: '/space/getSpaceByIds',
    method: 'put',
    data:[data.ids]
  });
};

//根据组织id取所有空间
export const getAllSpace = (params: {tenantId: string}) => {
  return request({
    url: '/space/getAllSpaceByTenantId',
    method: 'get',
    params
  });
};

//根据组织id获取物品仓库列表tree，嵌套
export const getArticleByOrgTree = (params: articleType.ReqParams) => {
  return request({
    url: '/warehouse/tree',
    method: 'get',
    params,
  });
};

//根据物品仓库id获取物品列表
export const getArticleByGroup = (data: articleType.ReqDataParams) => {
  return request({
    url: '/goods/getGoodsByGroupIds',
    method: 'put',
    data: [data.groupIds],
  });
};

//根据物品ids获取物品
export const getArticleByIds = (data: {ids:string}) => {
  return request({
    url: '/goods/getGoodsByIds',
    method: 'put',
    data:[data.ids]
  });
};

//根据物品ids获取物品
export const getArticleByIds1 = (data: string[]) => {
  return request({
    url: '/goods/getGoodsByIds',
    method: 'put',
    data
  });
};
//根据组织id取所有物品
export const getAllArticle = (params: {tenantId: string}) => {
  return request({
    url: '/goods/getAllGoodsByTenantId',
    method: 'get',
    params
  });
};


//根据用户ids获取部门管理员
export const getDeptAdminByUserIds = (data:any) => {
  return request({
    url: '/common/dept/leader/list',
    method: 'post',
    data
  });
};
//根据部门ids获取部门管理员
export const getDeptAdminByDeptIds = (params:{ids: string[]}) => {
  return request({
    url: `/common/getDeptLeaderByIds`,
    method: 'get',
    params
  });
};


//根据用户ids获取工作组管理员
export const getGroupAdminByUserIds = (data:any) => {
  return request({
    url: '/common/workgroup/leader/list',
    method: 'post',
    data
  });
};

//根据工作组ids获取部门管理员
export const getGroupAdminByGroupIds = (params:{ids: string[]}) => {
  return request({
    url: '/common/getWorkGroupLeaderByIds',
    method: 'get',
    params
  });
};

//根据部门ids获取部门名称
export const getDeptById = (ids:string) => {
  return request({
    url: '/common/getDeptNameByDeptIds',
    method: 'get',
    params: {
      deptIds: ids
    }
  });
};

//根据部门ids获取部门名称

export const getGroupById = (ids:string) => {
  return request({
    url: '/common/getDeptNameByDeptIds',
    method: 'get',
    params: {
      workGroupIds: ids
    }
  });
};


//根据手机号获取用户信息

export const getUserInfoByPhone = (phone:string) => {
  return request({
    url: '/blade-system/user/getByPhone',
    method: 'get',
    params: {
      phone
    }
  });
};

//获取用户签名

export const getSignature = (params:{userId:string;tenantId:string }) => {
  return request({
    url: '/userTenant/detail',
    method: 'get',
    params
  });
};
//获取资源、任务、工作组为空情况
export const getResourceEmptyCondition = (tenantId:string) => {
  return request({
    url: '/common/getResourceEmptyCondition',
    method: 'get',
    params: {
      tenantId
    }
  });
};

// 获取客服组 /common/getTenantBindCustomerGroup
export const getTenantBindCustomerGroup = (params:customerGroupType.ReqParams) => {
  return request({
    url: '/common/getTenantBindCustomerGroup',
    params
  });
};

//根据工作组id获取用户列表
export const getCustomerServiceRelUserList = (params: customerGroupType.ReqUserParams) => {
  return request({
    url: '/common/getCustomerServiceRelUserList',
    method: 'get',
    params,
  });
};

//获取当前应办的触发时间
export const getTriggerTimeByHandle = (handledId: string) => {
  return request({
    url: '/component/triggerTime',
    method: 'post',
    data: { handledId },
  });
};


//获取内部/外部/全部人员 
export const getAllRangeUser = (params: {tenantId:string;innerType?:number }) => {
  return request({
    url: '/common/getTenantAllUserList',
    method: 'get',
    params,
  });
};
