import { ref } from 'vue';
// hooks/useTableOperations.ts
import { getNamesByIds } from '@/utils/field.js'
import { dataValueFormat } from "@/components/xt-el-crud/tools/crudUtils.js";
import {
  addressType,
  userDeptType,
  usersType,
  userTagType,
  inventoryComType,
  interspaceComType
} from "@/components/form-config/types/FieldTypes.ts";
import { Ref } from 'vue';
export function useTableOperations(
  params:{
    spaceList: Ref<any[]>,
    articleList: Ref<any[]>,
    deptTreeData: Ref<any[]>,
    dutyTreeData: Ref<any[]>,
    allUserList: Ref<any[]>,
  } = { spaceList: ref([]), articleList: ref([]), deptTreeData: ref([]), dutyTreeData: ref([]), allUserList: ref([]) }
  
) {
  const { spaceList, articleList, deptTreeData, dutyTreeData, allUserList } = params
  const formatterRowToText = (rowData: Record<string, any>, col: any) => {
    const row = { ...rowData };
    let resVal = '';
    if (usersType.includes(col.fixType)) {
      if (!_.isArray(row[col.prop])) return ''
      resVal = getNamesByIds(allUserList.value || [], row[col.prop], 'nickName');
    } else if (userDeptType.includes(col.fixType)) {
      if (!_.isArray(row[col.prop])) return ''
      resVal = getNamesByIds(deptTreeData.value || [], row[col.prop], 'deptName');
    } else if (userTagType.includes(col.fixType)) {
      if (!_.isArray(row[col.prop])) return ''
      resVal = getNamesByIds(dutyTreeData.value || [], row[col.prop], 'groupName');
    } else if (addressType.includes(col.fixType)) {
      resVal = row[col.prop] ? [row[col.prop]?.label?.join('/') || '', row[col.prop]?.address || ''].filter(Boolean).join('/') : '';
    } else if (interspaceComType.includes(col.fixType)) {
      if (!_.isArray(row[col.prop])) return ''
      resVal = getNamesByIds(spaceList.value, row[col.prop], 'spaceName');
    } else if (inventoryComType.includes(col.fixType)) {
      if (!_.isArray(row[col.prop])) return ''
      resVal = getNamesByIds(articleList.value, row[col.prop], 'goodsName');
    } else {
      resVal = dataValueFormat(col, row[col.prop]);
    }
    row[`${col.prop}`] = resVal;
    return resVal;
  };


  return {
    formatterRowToText,
  };
}