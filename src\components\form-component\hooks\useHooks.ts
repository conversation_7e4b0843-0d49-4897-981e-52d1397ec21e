import { getLimitResult } from '@/api/banban/field';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';

export function useRequest(handleId:string,item: any) {
   const limitIds = ref<string[]>([])
    //工作组或者部门限制选择范围
    const getLimitDeptOrGroup = async () => { 
      if(!handleId) return
      if(item?.isLimit && item?.limitRange === 1){
       const {data} = await getLimitResult(handleId as string)
       limitIds.value = item.fixType === FixTypeEnum.USER_DEPT? data.data?.deptIdList : data.data?.workIdList
      }
    }
   return { getLimitDeptOrGroup, limitIds }

}