import request from '@/axios';
import { PageStateEnum } from '@/views/banban/know/taskKnowLedge/const';

// 任务周知左边的树
export const getKnowLedgesTreeHttp =  (readStatus:PageStateEnum) => {
  return request({
    url: '/knowledge/tenantMessageNumber',
    params: { readStatus },
  })
}


// 任务周知左边的树的子集
export const getKnowLedgesVersionMessageNumberHttp =  (tenantId:string,readStatus:PageStateEnum) => {
  return request({
    url: '/knowledge/versionMessageNumber',
    params: { tenantId, readStatus},
  })
}




// 任务周知列表
export const getKnowLedgesPageHttp =  (current:number, size:number, data: any) => {
  return request({
    url: '/knowledge/page',
    method: 'post',
    data,
    params:{current,size},
  })
}

// 更新阅读状态
export const updateKnowLedgeReadStatusHttp =  (ids: string,readStatus:number) => {
  return request({
    url: '/knowledge/updateReadStatus',
    method: 'post',
    params:{ids,readStatus},
  })
}


// 任务消息流程
export const getKnowLedgesMessageFlowHttp =  (id:string) => {
  return request({
    url: '/knowledge/taskMessage',
    params: { id },
  })
}


// 通过应办id获取应办任务的详情
export const getHandleDetailHttp =  (id:string) => {
  return request({
    url: '/staging/getOne',
    params: { id },
  })
}
// 告知任务链接是否可操作
export const getHandleAuthority =  (id:string) => {
  return request({
    url: '/staging/isOperate',
    method: 'get',
    params: { id },
  })
}



