import request from '@/axios';


export const getAuthStatus = (tenantid:string) => {//获取认证信息
  return request({
    url: '/enterprise_information/detail',
    method: 'get',
    params: {tenantid}
  });
};

export const getAuthStatusList = (tenantid:string) => {//获取认证流程记录
  return request({
    url: '/enterprise_flag_log/getByTenant',
    method: 'post',
    params: {tenantid}
  });
};


export const authSubmit = (form:any) => {//认证提交
  return request({
    url: '/enterprise_information/submit',
    method: 'post',
    data: form
  });
};

export const firmBack = (id:string) => {//企业认证回退
  return request({
    url: '/enterprise_information/back',
    method: 'post',
    params: {id}
  });
};

export const financeBack = (id:string) => {//财务认证回退
  return request({
    url: '/finance_manage/back',
    method: 'post',
    params: {id}
  });
};

export const getFinanceList = (current:number,size:number,params:any) => {//财务管理列表
  return request({
    url: "/finance_manage/page",
    method: 'post',
    data:{},
    params:{
      ...params,
      current,
      size
    }
  });
};

export const addPoints = (tenantId:string,amount:string) => {//增加积分
  return request({
    url: "/point_order_record/recharge",
    method: 'post',
    data:{tenantId,amount}
  });
};

export const financeConfirm = (id:string,amount:string) => {//财务确认信息汇款
  return request({
    url: "/finance_manage/submit",
    method: 'post',
    params:{id,amount}
  });
};

export const firmConfirm = (id:string,amount:string) => {//企业输入收款金额比对
  return request({
    url: "/enterprise_information/authentication",
    method: 'post',
    params:{id,amount}
  });
};


