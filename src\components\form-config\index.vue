<template>
  <div class="avue-form-design">
    <el-container>
      <el-aside :width="asideLeftWidth" class="overflow-hidden flex flex-col gap-4">
        <el-scrollbar 
          wrap-class="bg-white" 
          v-if="formType === formTypeEnum.FORM"
        >
          <TemplateField 
            :task-package-id="taskPackageId" 
            :fieldClick="handleFieldClickTemplate" 
            @change="selectTemp"
          />
        </el-scrollbar>
        <el-scrollbar wrap-class="bg-white position-relative">
          <!-- 左侧字段区 -->
          <h3 class="m-0  pl-5 mt-2">组件库</h3>
          <afd-field 
            :fields="leftFields" 
            @field-click="handleFieldClick">
          </afd-field>
        </el-scrollbar>
        <submit-btn-config 
          v-model="submitText" 
          v-if="showSubmitText" 
        />
      </el-aside>
      <el-container 
        direction="vertical" 
        class="flex items-center h-full pt-20px"
      >
        <div class="w-750px bg-white h-full">
          <form-top-line />
          <!-- 主设计区 -->
          <afd-widget 
            ref="widgetForm" 
            :data="widget.option" 
            v-model:select="widget.select"
            v-model:delList="delList" 
            :form-type="formType">
          </afd-widget>
        </div>
      </el-container>
      <el-aside :width="asideRightWidth">
        <!-- 右侧设计区 -->
        <afd-config 
          :widget-select="widget.select" 
          :key="configKey" 
          :column="widget.option">
        </afd-config>
      </el-aside>
    </el-container>

    <!-- 预览弹窗 -->
    <afd-preview-drawer ref="previewDrawer" :submit-text="submitText"></afd-preview-drawer>
  </div>
</template>

<script setup lang="ts">
import FormTopLine from './components/FormTopLine.vue';
import SubmitBtnConfig from './components/SubmitBtnConfig.vue';
import AfdConfig from './AfdConfig/index.vue'; // 右侧配置区
import AfdField from './AfdField/index.vue'; // 左侧字段区
import AfdPreviewDrawer from './AfdWidget/drawer/preview.vue'; // 预览弹窗
import AfdWidget from './AfdWidget/index.vue';
import TemplateField from './AfdField/template/index.vue'//字段库
import { OptionInjectKey, PackageIdInjectKey, ResetConfigInjectKey } from './utils/injectKeys.ts'; // 中间主设计区
import { AvueColumns, DesignField, DynamicAllField } from './types/AvueTypes.ts';
import { ElMessage } from 'element-plus';
import useFormDesign from '../../hooks/useFormDesign.js';
import { generateId } from '@/components/form-config/utils';
import { formTypeEnum } from '@/components/form-config/const/commonDic';

const props = withDefaults(
  defineProps<{
    asideLeftWidth?: string;
    asideRightWidth?: string;
    leftFields: DesignField[];
    taskPackageId?: string;
    showSubmitText?: boolean;
  }>(),
  {
    asideLeftWidth: () => '300px',
    asideRightWidth: () => '360px',
    showSubmitText: () => true,
  }
);

const taskPackageId = toRef(props, 'taskPackageId');

provide(PackageIdInjectKey, taskPackageId);
const formType = inject('formType', 2)

const widget = reactive<{
  option: {
    column: AvueColumns[];
  };
  select: Partial<AvueColumns>;
}>({
  option: {
    column: [],
  },
  select: {},
});

const configKey = ref(Math.random());

watch(
  () => widget.select.prop,
  () => {
    configKey.value = Math.random();
  }
);

const column = defineModel<AvueColumns[]>('column', { required: true });
const delList = defineModel<string[]>('delList', { required: false });


// 初始化数据
const handleLoadStorage = () => {
  widget.option.column = _.cloneDeep(column.value);
};

// 同步内外
watch(
  column,
  val => {
    if (!_.isEqual(val, widget.option.column)) {
      handleLoadStorage();
      console.log(column.value)
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

watch(
  () => widget.option.column,
  val => {
    if (!_.isEqual(val, column.value)) {
      column.value = _.cloneDeep(val);
    }
  },
  {
    deep: true,
  }
);

const submitText = defineModel<string>('submitText', {
  required: false,
  default: () => '提交',
});

provide(OptionInjectKey, () => widget.option);
provide(ResetConfigInjectKey, () => {
  configKey.value = Math.random();
});
const selectTemp = (column: AvueColumns[]) => {
  const fieldIds = widget.option.column.map(v => v.mid);
  const filterColumn = column.filter(col=>{
    return !fieldIds.includes(col.mid)
  })
  if (!filterColumn.length) return ElMessage.warning('该模板里的字段已全部加入');
  filterColumn.reverse();
  filterColumn.forEach(v => {
    handleFieldClickTemplate(v)
  })
}

const widgetForm = ref<InstanceType<typeof AfdWidget>>();

//模板点击过来的要加上模板的标识,字段库里选来的字段才有选规则
const handleFieldClickTemplate = (item: AvueColumns) => {
  if (item.fixType === 'dynamic' && (item as DynamicAllField).children?.length) {
    (item as DynamicAllField).children.forEach((v:AvueColumns) => {
      v.prop = generateId(v.fixType);
      let startIndex = v.prop.indexOf('#');
      v.id = v.prop.slice(startIndex + 1);
      v.visibleBool = true
    })
  }
  item = { ...item}
  handleFieldClick(item)
}

// 左侧字段点击
const handleFieldClick = (item: AvueColumns) => {
  const fieldIds = widget.option.column.map(v => v.mid)
  if (item?.mid && fieldIds.includes(item?.mid)) {
    ElMessage.warning('模板字段只能使用一次');
    return;
  }
  const activeIndex = widget.option.column.findIndex(c => c.prop === widget.select.prop) + 1;
  let newIndex;
  if (activeIndex === -1) {
    //字段库的页面
    widget.option.column.push(item);
    newIndex = widget.option.column.length - 1;
  } else {
    widget.option.column.splice(activeIndex, 0, item);
    newIndex = activeIndex;
  }

  widgetForm.value?.handleWidgetAdd({ newIndex });
};

// 预览
const previewDrawer = ref<any>();
const handlePreview = () => {
  if (!widget.option.column || widget.option.column.length === 0) {
    ElMessage.error('没有需要展示的内容');
  } else {
    previewDrawer.value?.show(_.cloneDeep(widget.option.column));
  }
};
let { setPreview } = useFormDesign();
setPreview(handlePreview);
</script>

<style lang="scss">
@import './styles/index.scss';
</style>
