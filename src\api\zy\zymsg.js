import request from '@/axios';

export const submitForm = (data) => {
    return request({
        url: '/common/tenant/update',
        method: 'post',
        data: {
            ...data
        }
    });
};
export const getAreas = (parentId) => {
    return request({
        url: '/getAreas',
        method: 'get',
        params: {
            parentId
        }
    });
};
export const getdetail = () => {
    return request({
        url: '/blade-system/tenant/detail',
        method: 'get',
        params: {

        }
    });
};
export const gettype = () => {
    return request({
        url: '/getChild',
        method: 'get',
        params: {

        }
    });
};
export const getOfficialSeal = (tenantId) => {
    return request({
        url: '/seal/officialSeal',
        method: 'get',
        params: {
            tenantId
        }
    });
};

