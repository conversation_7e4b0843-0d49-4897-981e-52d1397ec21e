import { defineStore } from 'pinia';
import { Mark } from '../types/widget.ts';
import { ChartStyle, ChartTypeEnum, DesignProps, PreviewDataType } from '../types';
import { watchPausable } from '@vueuse/core';
import { ChartConfInit } from '../const/echarts.ts';

const useChartDesignStore = defineStore('chartDesign', () => {
  const dimensionsList = ref<Mark[]>([]);
  const metricsList = ref<Mark[]>([]);
  const auxiliaryList = ref<Mark[]>([]);
  const chartStyle = ref<ChartStyle>({});
  const chartType = ref(ChartTypeEnum.TEXT);

  const previewData = ref<PreviewDataType>({});

  const { pause: chartWatchPause, resume: chartWatchResume } = watchPausable(chartType, () => {
    dimensionsList.value = [];
    metricsList.value = [];
    auxiliaryList.value = [];
    chartStyle.value = {
      backgroundColor: chartStyle.value.backgroundColor,
      title: chartStyle.value.title,
      titleColor: chartStyle.value.titleColor,
      titlePosition: chartStyle.value.titlePosition,
      ...(ChartConfInit[chartType.value]?.() ?? {})
    };
  });

  // 向内同步数据
  const syncData = (data: DesignProps) => {
    // 先暂停内部监听
    chartWatchPause();

    chartType.value = data.chartType || ChartTypeEnum.TEXT;
    dimensionsList.value = _.cloneDeep(data.dimensions || []);
    metricsList.value = _.cloneDeep(data.metrics || []);
    auxiliaryList.value = _.cloneDeep(data.auxiliary || []);
    chartStyle.value = _.cloneDeep(data.chartStyle || {});

    // 重新启动监听
    nextTick(() => {
      chartWatchResume();
    });
  };

  const resetList = () => {
    dimensionsList.value = [];
    metricsList.value = [];
    auxiliaryList.value = [];
  };

  const $reset = () => {
    resetList();
    chartStyle.value = {};
    chartType.value = ChartTypeEnum.TEXT;
    previewData.value = {};
  };

  return {
    dimensionsList,
    metricsList,
    auxiliaryList,
    chartType,
    chartStyle,
    chartWatchPause,
    chartWatchResume,
    syncData,
    previewData,
    $reset,
    resetList
  };
});

export default useChartDesignStore;