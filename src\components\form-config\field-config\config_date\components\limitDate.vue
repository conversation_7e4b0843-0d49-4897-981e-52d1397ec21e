<template>
  <!-- <el-form-item label="限制可选范围"> -->
    <el-form-item  v-if="!isShowWeek">
      <el-checkbox v-model="data.limitWeek">限定可选星期</el-checkbox>
    </el-form-item>
    <el-form-item v-if="data.limitWeek">
      <el-select v-model="data.limitWeekVal" multiple>
        <el-option
          v-for="item in weekList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <template v-if="isTimeFormat">
      <el-form-item>
        <el-checkbox v-model="data.limitTime">限定可选时段</el-checkbox>
      </el-form-item>
      <el-form-item  v-if="data.limitTime">
        <div class="flex">
          <el-time-select
            start="00:00"
            end="23:59"
            step="00:01"
            v-model="data.limitTimeStart"
            :max-time="data.limitTimeEnd"
            class="mr-4 flex-1"
            placeholder="开始"
          />
          <el-time-select
            start="00:00"
            end="23:59"
            step="00:01"
            v-model="data.limitTimeEnd"
            :min-time="data.limitTimeStart"
            placeholder="结束"
            class="flex-1"
          />
        </div>
      </el-form-item>
    </template>
    
    <!-- <el-form-item>
      <el-checkbox v-model="data.limitMinuteIntervals">预设分钟间隔</el-checkbox>
    </el-form-item>
    <el-form-item  v-if="data.limitMinuteIntervals">
      <el-select v-model="data.limitMinuteIntervalsVal">
        <el-option
          v-for="item in minuteIntervalsList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item> -->
  <!-- </el-form-item> -->
  
  <config-limit-date-item
    title="开始日期"
    :data
    v-model:limitDate="data.limitStartDate"
    v-model:limitDateType="data.limitStartDateType"
    v-model:limitValue="data.limitStartDateVal"
  />
  <config-limit-date-item
    title="结束日期"
    :data
    v-model:limitDate="data.limitEndDate"
    v-model:limitDateType="data.limitEndDateType"
    v-model:limitValue="data.limitEndDateVal"
  />

</template>

<script setup lang="ts">
import { DateAllField } from '../type.ts';
import { weekList, DateFormatTypeEnum } from '../const.ts';
import configLimitDateItem from './limitDateItem.vue';
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic';

const props = defineProps<{
  data: DateAllField;
}>();

const isTimeFormat = computed(() => [DateFormatTypeEnum.MINUTES, DateFormatTypeEnum.SECONDS].includes(props.data?.dateFormatType));//有带时间的格式才显示时间范围限制
const isShowWeek = computed(() => _.includes([DefaultTypeEnum.FORMULA, DefaultTypeEnum.TRIGGERTIME],props.data?.defaultType));//公式编辑/触发时间不显示限制日期

watchEffect(() => {
  //如果没有选择星期，则清空星期数据
  if(!props.data?.limitWeek){
    props.data.limitWeekVal = [];
  }
  //如果没有选择时间，则清空时间数据
  if(!props.data?.limitTime){
    props.data.limitTimeStart = '';
    props.data.limitTimeEnd = '';
  }
  // 如果没有选择开始日期，则清空开始日期数据
  if(!props.data?.limitStartDate){
    props.data.limitStartDateType = '';
    props.data.limitStartDateVal = {value:'', type:''};
  }
  // 如果没有选择结束日期，则清空结束日期数据
  if(!props.data?.limitEndDate){
    props.data.limitEndDateType = '';
    props.data.limitEndDateVal = {value:'', type:''};
  }
  //格式时分/时分秒才能才能有时间段控制
  if(!_.includes([DateFormatTypeEnum.MINUTES,DateFormatTypeEnum.SECONDS], props.data?.dateFormatType)){
    props.data.limitTime = false
  }
 
  if(isShowWeek.value){
    props.data.limitWeek = false
  }

})



</script>