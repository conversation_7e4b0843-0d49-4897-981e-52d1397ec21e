import { getLedgerTree } from '@/api/banban/ledger';
import { fileMenuTree } from '@/api/know/index';
import { useStore } from 'vuex'


export function useArchiveSet() {
  const ledgerTree = ref<any[]>([]);//电子台账目录
  const allLedgerTree = ref<any[]>([]);
  const fileManageTree = ref<any[]>([]);//文件目录
  const store = useStore();

  const tenantId = computed(() => {
    const userMsg = store.getters.userInfo;
    return userMsg.tenant_id;
  });
  //获取电子台账
  const initLedgerTree = () => {
    getLedgerTree({ originalStatus: 1 }).then(res => {
      allLedgerTree.value = res.data.data;
      ledgerTree.value = res.data.data.map((f: any) => {
        if (f.parentId == 0) {
          return {
            ...f,
            disabled: true,
          };
        } else {
          return {
            ...f,
          };
        }
      });
    });
  };
  //获取文件目录
  const initFileManageTree = () => {
    fileMenuTree({ tentantid: tenantId.value }).then(({ data }) => {
      fileManageTree.value = data.data;
    });
  };

  return {
    ledgerTree,
    allLedgerTree,
    initLedgerTree,
    fileManageTree,
    initFileManageTree
  }


}