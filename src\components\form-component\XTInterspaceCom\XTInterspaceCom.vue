<template>
  <div class="w-full">
    <slot :data="modelList">
      <el-card
        class="box-card"
        :class="{ disabled: disabled }"
        @click="disabled ? void 0 : openSelect()"
      >
        <template v-if="modelList.length > 0">
          <el-tag
            v-for="(item, index) in modelList"
            :key="item.id"
            :closable="!disabled && closable"
            @close="unCheckUser(index)"
            style="margin: 0 5px"
            size="large"
            >{{ item.spaceName }}
          </el-tag>
        </template>
        <div v-else class="select_placeholder">{{ placeholder }}</div>
      </el-card>
    </slot>
    <selectSpace
      :modelList="modelList"
      @submit="handleSubmit"
      v-model:visible="dialogVisible"
      :config="{ tid: taskSource || userMsg?.tid }"
    >
    </selectSpace>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import selectSpace from './component/selectSpace.vue';
import { selectUserBySpace } from './XTInterspaceCom';
import { getSpaceByGroupIds1 } from '@/api/common/index';
import { taskSourceInjectKey } from '@/components/xt-el-form/constant.ts';
import { useStore } from 'vuex';
import { isEmptyValue } from '@/utils/field';
import { useFormItem } from 'element-plus';
import { spaceType } from '@/api/common/type';
import { ElMessage } from 'element-plus';
const { formItem } = useFormItem();

const store = useStore();

const props = withDefaults(defineProps<selectUserBySpace>(), {
  dataType: 'array',
  multiple: false,
  placeholder: '请选择',
  initData: false,
  taskPackageId: '',
  closable: true,
  disabled: false,
});

const userMsg = store.state.user.userInfo;
const dialogVisible = ref(false);
const vModel = defineModel<string[] | string>();
const modelList = ref<spaceType.ResDataParams[]>([]);
const taskSource = inject(taskSourceInjectKey, null); //任务组织来源

//选中空间
const handleSubmit = (n: spaceType.ResDataParams[]) => {
  if (props?.isRadio && n.length > 1) {
    ElMessage.warning('只能选择一个数据，请重新选择');
    return;
  }
  dialogVisible.value = false;
  modelList.value = n;
  vModel.value = props.dataType === 'array' ? n.map(t => t.id) : n.map(t => t.id).join(',');
};

const openSelect = () => {
  dialogVisible.value = true;
};

const unCheckUser = (index: number) => {
  modelList.value.splice(index, 1);
  vModel.value =
    props.dataType === 'array'
      ? modelList.value.map(t => t.id)
      : modelList.value.map(t => t.id).join(',');
};

watch(
  () => vModel.value,
  async val => {
    formItem?.validate('change');
    if (!isEmptyValue(val)) {
      const ids = props.dataType === 'array' ? val : (val as string)?.split(',');
      if (!_.isArray(ids)) return;
      let res = await getSpaceByGroupIds1(ids);
      modelList.value = res.data.data;
    } else {
      modelList.value = [];
    }
  },
  {
    immediate: true,
  }
);
</script>

<style lang="scss" scoped>
.disabled {
  background: #f6f6f6;
}

.select_placeholder {
  color: #a8abb2;
  font-size: 14px;
}

.box-card {
  cursor: pointer;
  width: 100%;

  :deep(.el-card__body) {
    padding: 10px;
    border-radius: 0;
    min-height: 40px;
    border: 1px dashed #eee;
  }
}
</style>
