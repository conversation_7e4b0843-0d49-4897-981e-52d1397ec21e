import useChartTypeStyleAndStore from '../../../../hooks/useChartTypeStyleAndStore.ts';
import { TableStyle } from '../../../../types';
import { TableAlignDic } from '../../../../const';


export default [
  {
    header: '表格类图样',
    render: defineComponent(() => {
      const { chartStyle } = useChartTypeStyleAndStore<TableStyle>();

      return () => (
        <div>
          <el-form-item label="表头文字">
            <el-color-picker
              v-model={chartStyle.value.tableHeaderColor}
            />
          </el-form-item>
          <el-form-item label="内容文字">
            <el-color-picker
              v-model={chartStyle.value.tableContentColor}
            />
          </el-form-item>
          <el-form-item label="对齐方式">
            <el-select-v2
              v-model={chartStyle.value.tableAlign}
              options={TableAlignDic}
            />
          </el-form-item>
        </div>
      );
    })
  }
];