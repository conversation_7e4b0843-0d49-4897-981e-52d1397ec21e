import { dayFormat, formatDate, monthFormat, yearFormat } from '@/const/dateFormat.js';
import { selectAllType,mulCascaderType } from "@/components/form-config/types/FieldTypes.ts";
import {formatValue} from "@/components/form-config/utils";
const formatFunctions = (item, value) => {
  const type = item.fixType || 'input';
  //选择
  if (selectAllType.includes(type)) {
    if (_.isArray(value)) {
      return _.join(value, ',');
    } else {
      return value;
    }
  }
  //级联多选
  if (mulCascaderType.includes(type)) {
    if (Array.isArray(value) && value.every(arr => Array.isArray(arr))) {
      return value.map(arr => arr.join('/')).join(',');
    }
  }

  switch (type) {
    //时间
    case 'year':
    case 'yearrange':
      return formatDate(value, yearFormat);
    case 'month':
    case 'monthrange':
      return formatDate(value, monthFormat);
    case 'date':
    case 'daterange':
      return formatDate(value, dayFormat);
    case 'datetime':
    case 'datetimerange':
      return formatDate(value, item.format);
    //数字
    case 'number':
      return formatValue(value, item, true);
    //地址
    case 'address':
      return _.join(value.label, '') + (value.address || '');
    default:
      return value;
  }
};

export const dataValueFormat = (item, value) => {
  if (_.isEmpty(value) && !_.isNumber(value)) {
    // console.log('value is empty')
    return null;
  }
  try {
    return formatFunctions(item, value);
  } catch (e) {
    console.log('ssss', item, value, e);
    return value;
  }
};
