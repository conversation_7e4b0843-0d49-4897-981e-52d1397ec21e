#K8S_NAMESPACE: nbxtx-banban-web-prod22
#APPLICATION_NAME: nbxtx-banban-web-pc
kind: Deployment
apiVersion: apps/v1
metadata:
  name: ${WEB_APPLICATION_NAME}
  namespace: ${K8S_NAMESPACE}
  labels:
    app: ${WEB_APPLICATION_NAME}
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: ${WEB_APPLICATION_NAME}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%
      maxSurge: 50%
  template:
    metadata:
      labels:
        app: ${WEB_APPLICATION_NAME}
    spec:
      serviceAccountName: default
      volumes:
        - name: host-time #同步时区
          hostPath:
            path: /etc/localtime
            type: ''
      containers:
        - name: app
          image: registry-vpc.cn-shanghai.aliyuncs.com/nbxt-k8s/${WEB_APPLICATION_NAME}:${CI_COMMIT_ID}
          ports:
            - containerPort: 80
              protocol: TCP
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
---
kind: Service
apiVersion: v1
metadata:
  name: ${WEB_APPLICATION_NAME}
  namespace: ${K8S_NAMESPACE}
  labels:
    app: ${WEB_APPLICATION_NAME}
spec:
  ports:
    - name: http-80
      protocol: TCP
      port: 80
      targetPort: 80
  selector:
    app: ${WEB_APPLICATION_NAME}
  type: ClusterIP
  sessionAffinity: None
---
kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: ${WEB_APPLICATION_NAME}
  namespace: ${K8S_NAMESPACE}
  annotations:
    kubesphere.io/alias-name: ${WEB_APPLICATION_NAME}生产环境pc端外部访问
    kubesphere.io/creator: admin
    nginx.ingress.kubernetes.io/proxy-body-size: 100000m
    nginx.ingress.kubernetes.io/ssl-redirect: 'true'
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - ${WEB_URL}
  rules:
    - host: ${WEB_URL}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ${WEB_APPLICATION_NAME}
                port:
                  number: 80
