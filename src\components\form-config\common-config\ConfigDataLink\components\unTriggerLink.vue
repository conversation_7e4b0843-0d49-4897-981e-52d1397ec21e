<template>
    <n-card class="mb-2">
        <!-- 联动条件 -->
        <div class="mb-2">
            <h3>联动条件</h3>
            <span>触发任务的联动条件与当前表单的联动条件之间为</span>
            <avue-select 
                class="w-110px! px-10px satisfy-select" 
                v-model="linkItem.logicBetween" 
                :dic="isAlsoEnum1" 
                :clearable="false">
            </avue-select>
            <span>的关系</span>
        </div>

        <n-card class="mb-2">
            <el-form-item label="选择联动任务">
                <selectTask 
                    :taskType="1" 
                    v-model:taskId="linkItem.linkageTaskId" 
                    v-model:fieldList="linkFieldList" 
                    v-model:type="linkItem.linkageTaskType" 
                    :taskList="taskList"
                />
            </el-form-item>
            <fieldConditionConfig
                :field="linkFieldList" 
                :conditionTaskId="linkItem.linkageTaskId"
                v-model:condition="linkItem.linkageColumnList"  
                v-model:andOr="linkItem.linkageOperator" 
                title="添加触发任务的联动条件" 
            />
        </n-card>

        <n-card class="mb-2">
            <fieldConditionConfig 
                :field="fieldList1" 
                :conditionTaskId="taskId"
                v-model:condition="linkItem.columnList" 
                v-model:andOr="linkItem.logic"
                :current-field="data" 
                :current="true"
                title="添加当前任务的联动条件" 
            />
        </n-card>

    </n-card>
    <!-- 筛选条件 -->
    <n-card class="mb-2">
        <h3>筛选条件</h3>
        <div>
            <el-form-item label="选择推数路径" required>
                <selectTask 
                    :taskType="2" 
                    v-model:taskId="linkItem.linkageField!.path" 
                    v-model:fieldList="pushFieldList"
                    v-model:type="linkItem.linkageField!.pathType"
                    :taskList="taskList"
                    @click="handleChangeTask1()"

                />
            </el-form-item>
            <accordTitle 
                title="添加条件" 
                v-model="linkItem.linkageField!.filterOperator" 
                @add="handleAdd"
             />
            <filterConditionItem 
                class="mb-3"
                v-for="(filterColumn, index) in linkItem.linkageField!.filterColumnList" 
                :key="filterColumn.idx"
                :filterColumn="filterColumn" 
                v-model:linkTaskId="linkItem.linkageTaskId"
                v-model:pushTaskId="linkItem.linkageField!.path" 
                :conditionTaskId="taskId"
                :data="data"
                :taskList="taskList"
                :pathType="linkItem.linkageField!.pathType"
                v-model:pushFieldList="pushFieldList"
                v-model:linkFieldList="linkFieldList"
                @delete="handleDelete(index)"
            />
        </div>
        <n-card class="mt-10">
            <matchField 
                :pathType="linkItem.linkageField!.pathType" 
                v-model:field="linkItem.linkageField!.field"
                v-model:linkage="linkItem.linkageField!.linkage"
                :data="data" 
                :fieldList="pushFieldList">
            </matchField>
            <template  v-if="data.fixType === 'dynamic' && linkItem.linkageField!.field"  >
                <addDynamicLink
                    :data="data" 
                    :taskList="taskList"
                    v-model:pushTaskId="linkItem.linkageField!.path" 
                    :pushDynamicField="dynamicFieldList"
                    v-model:dynamicFields="linkItem.dynamicFields"
                    >
                </addDynamicLink>
            </template>
        </n-card>

    </n-card>
    <!-- 极值规则 -->
    <extremeRule 
        v-model="linkItem.extremeRule"
        :fieldList="pushFieldList"
        :pathType="linkItem.linkageField!.pathType"
        >
    </extremeRule>
</template>

<script setup lang="ts">
import fieldConditionConfig from '../../components/fieldConditionConfig.vue';
import matchField from '../../components/matchField.vue'
import { configDataLinkType } from '../type';
import selectTask from '../../components/selectTask.vue';
import filterConditionItem from '../../components/filterConditionItem.vue';
import accordTitle from '../../components/accordTitle/index.vue';
import extremeRule from '../../components/extremeRule.vue';
import { isAlsoEnum1 } from '@/components/form-config/utils/option';
import { dataLinksItem } from '@/api/interface/configDataLink.ts';
import { ElMessage } from 'element-plus';
import useFieldList from '@/views/mainview/sceneTasks/detail/hooks/useFieldList.ts';
import { pushTaskListType } from '@/api/interface/task'
import addDynamicLink from '../../components/addDynamicLink/index.vue';
import { generateId } from '@/components/form-config/utils';
import { AvueColumns,DynamicAllField } from '@/components/form-config/types/AvueTypes'

const props = defineProps<{
    taskId: string;
    data: configDataLinkType['data'];
    taskList:pushTaskListType[]
}>();

const linkItem = defineModel<dataLinksItem>('linkItem', { default: {} });
const pushFieldList = ref<AvueColumns[]>([])
const linkFieldList = ref<AvueColumns[]>([])

const dynamicFieldList = computed(() => {
  const filteredItem = pushFieldList.value.find(item => item.prop === linkItem.value.linkageField!.field);
  return filteredItem ? (filteredItem as DynamicAllField).children : [];
});

//推数路径改动清除选项
const handleChangeTask1 = () => {
  linkItem.value.linkageField!.field= '';
  linkItem.value.dynamicFields= [];
  linkItem.value.linkageField!.filterColumnList = [{
    idx: generateId(),
    dataProp: '',
    filterType: 'TRIGGER_TASK_FIELD',
  }]
  linkItem.value.linkageField!.filterOperator = '1'
}; 


const handleAdd = () => {
    if(!linkItem.value.linkageField?.path){
        ElMessage.warning('请先选择推数路径');
        return
    }
    if(!linkItem.value.linkageField?.filterOperator){
        ElMessage.warning('请先选择条件的逻辑关系(且/或)');
        return
    }

    if (_.isArray(linkItem.value.linkageField.filterColumnList)) {
    const haveEmpty = linkItem.value.linkageField.filterColumnList?.some(
      i =>
        _.isEmpty(i.dataProp) ||
        _.isEmpty(i.operator) 
    );
    if (haveEmpty) {
      ElMessage.warning('有空条目请勿添加新的条目');
      return;
    }
  }
    // if (_.isArray(linkItem.value.linkageField.filterColumnList)) {
    //     const haveEmpty = linkItem.value.linkageField.filterColumnList?.some(i => _.isEmpty(i.dataProp) || _.isEmpty(i.operator) || (_.isEmpty(i.linkageProp) && _.isEmpty(i.currentFormProp)));
    //     if (haveEmpty) {
    //     ElMessage.warning('有空条目请勿添加新的条目');
    //     return;
    //     }
    // }
    (linkItem.value.linkageField.filterColumnList ??= []).push({dataProp:'',filterType:'TRIGGER_TASK_FIELD',idx: generateId()});
}

const handleDelete = (index: number) => {
    linkItem.value.linkageField?.filterColumnList?.splice(index, 1);
};

const fieldList1 = computed(() => {
  return (fieldList.value as any[]).map(v =>{
    return {
      ...v,
      disabled:false
    }
  }) 
})
//字段
const { getFiledList, fieldList } = useFieldList();

getFiledList({
  id:  props.taskId || '',
  unFlatDynamic: false,
  changeToAble: true,
  unFlatDataCollect: false,
  unFlatDataSelect: false,
  unFlatKnowledgeSelect: false,
});

</script>