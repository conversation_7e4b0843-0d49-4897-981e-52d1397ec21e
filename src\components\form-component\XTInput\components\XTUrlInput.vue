<template>
    <div class="flex-(~ col) w-full items-start">
        <el-input v-model="model" style="max-width: 600px" v-bind="$attrs" placeholder="请输入链接" class="input-with-select">
            <template #append>
                <el-button :icon="Search" @click="handleClick"/>
            </template>
        </el-input>
    </div>
</template>

<script setup lang="ts">

import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';



const model = defineModel<string>();

const handleClick = () => {
    if(!model.value){
        ElMessage.warning('请输入链接地址');
        return
    }
    window.open(model.value, '_blank');
    // if (urlPattern.test(model.value)) {
    //     window.open(model.value, '_blank');
    // }else{
    //     ElMessage.warning('请输入正确的链接地址https://');
    // }
};

</script>

<style scoped lang="scss"></style>