import { AxisType, ChartStyle, ChartTypeEnum, EChartsAxisStyle, EChartsCommonStyle, LineStyle } from '../types';

const AxisTooltip = {
  trigger: 'axis',
  axisPointer: {
    type: 'shadow'
  }
};

export const ChartConfInit: {
  [K in ChartTypeEnum]?: () => ChartStyle
} = {
  [ChartTypeEnum.LINE]: () => ({
    lineWidth: 1,
    showSymbol: true,
    symbolSize: 1
  })
};

export const TooltipTypes: {
  [K in ChartTypeEnum]: any
} = {
  [ChartTypeEnum.TEXT]: {},
  [ChartTypeEnum.PIE]: {
    tooltip: {
      trigger: 'item'
    }
  },
  [ChartTypeEnum.COLUMN]: AxisTooltip,
  [ChartTypeEnum.BAR]: AxisTooltip,
  [ChartTypeEnum.LINE]: AxisTooltip,
  [ChartTypeEnum.TABLE]: {}
};

const AxisLabel = (chartStyle: EChartsCommonStyle) => {
  const r: any = {
    show: chartStyle.showValue,
    position: 'top'
  };
  r['color'] = chartStyle.valueColor;
  r['fontSize'] = chartStyle.valueSize;
  return r;
};

export const SeriesLabelTypes: {
  [K in ChartTypeEnum]: (chartStyle: EChartsCommonStyle) => any
} = {
  [ChartTypeEnum.TEXT]: () => ({}),
  [ChartTypeEnum.PIE]: (chartStyle) => {
    const r: any = {};
    r['formatter'] = chartStyle.showValue ? '{b}: {d}%' : '{b}';
    r['color'] = chartStyle.valueColor;
    r['fontSize'] = chartStyle.valueSize;
    return r;
  },
  [ChartTypeEnum.COLUMN]: AxisLabel,
  [ChartTypeEnum.BAR]: (chartStyle) => {
    const r = AxisLabel(chartStyle);
    r['position'] = 'right';
    return r;
  },
  [ChartTypeEnum.LINE]: AxisLabel,
  [ChartTypeEnum.TABLE]: () => ({})
};

export const AxisMain: {
  [K in ChartTypeEnum]?: 'xAxis' | 'yAxis'
} = {
  [ChartTypeEnum.COLUMN]: 'yAxis',
  [ChartTypeEnum.BAR]: 'xAxis',
  [ChartTypeEnum.LINE]: 'yAxis'
};

export const AxisProp = (chartStyle: EChartsAxisStyle, axis: AxisType) => {
  return {
    name: chartStyle[`${axis}Label`],
    axisLabel: {
      show: true,
      rotate: chartStyle[`${axis}LabelRotate`] ?? 0,
      fontSize: chartStyle[`${axis}LabelSize`] ?? 12,
      color: chartStyle[`${axis}LabelColor`] ?? '#333'
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: chartStyle[`${axis}LineColor`] ?? '#333'
      }
    }
  };
};

export type AxisInitParams<T extends EChartsAxisStyle = EChartsAxisStyle> = {
  chartStyle: T
  maxTargetValue?: number
}

const computedMax = (value: { min?: number, max?: number }, maxTargetValue?: number) => {
  return _.max([maxTargetValue, value.max]);
};

export const AxisInit: {
  [K in ChartTypeEnum]?: {
    xAxis: (params: AxisInitParams) => any
    yAxis: (params: AxisInitParams) => any
  }
} = {
  [ChartTypeEnum.COLUMN]: {
    xAxis: (params) => ({
      type: 'category',
      nameLocation: 'middle',
      nameGap: 40,
      ...AxisProp(params.chartStyle, 'xAxis')
    }),
    yAxis: (params) => ({
      type: 'value',
      nameGap: 40,
      max: (value: any) => computedMax(value, params.maxTargetValue),
      ...AxisProp(params.chartStyle, 'yAxis')
    })
  },
  [ChartTypeEnum.BAR]: {
    xAxis: (params) => ({
      type: 'value',
      nameLocation: 'middle',
      nameGap: 30,
      max: (value: any) => computedMax(value, params.maxTargetValue),
      ...AxisProp(params.chartStyle, 'xAxis')
    }),
    yAxis: (params) => ({
      type: 'category',
      ...AxisProp(params.chartStyle, 'yAxis')
    })
  },
  [ChartTypeEnum.LINE]: {
    xAxis: (params: AxisInitParams<LineStyle>) => ({
      type: 'category',
      nameLocation: 'middle',
      nameGap: 40,
      boundaryGap: !params.chartStyle.showArea,
      ...AxisProp(params.chartStyle, 'xAxis')
    }),
    yAxis: (params) => ({
      type: 'value',
      nameGap: 40,
      max: (value: any) => computedMax(value, params.maxTargetValue),
      ...AxisProp(params.chartStyle, 'yAxis')
    })
  }
};