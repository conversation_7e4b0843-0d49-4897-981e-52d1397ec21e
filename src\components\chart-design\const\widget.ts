import { Mark, SortEnum, SummaryEnum, TopTagType, WidgetGroupEnum } from '../types/widget.ts';
import { ChartTypeEnum } from '../types';
import { storeToRefs } from 'pinia';
import useChartDesignStore from '../store/useChartDesignStore.ts';
import {
  CollectGenerator,
  RenameGenerator,
  SortGenerator
} from '@/components/chart-design/components/widget/components/dropdownItemGenerators';

export const MARK_DEFAULT_GROUP_NAME = 'mark_default_group';
export const AUXILIARY_GROUP_NAME = 'auxiliary_group_name';

const { metricsList, dimensionsList } = storeToRefs(useChartDesignStore());

export const SummaryDic = [
  {
    label: '计数',
    value: SummaryEnum.COUNT
  },
  {
    label: '去重',
    value: SummaryEnum.DISTINCT
  }
];

export const NumberSummaryDic = [
  {
    label: '求和',
    value: SummaryEnum.SUM
  },
  {
    label: '平均值',
    value: SummaryEnum.AVG
  },
  {
    label: '最大值',
    value: SummaryEnum.MAX
  },
  {
    label: '最小值',
    value: SummaryEnum.MIN
  },
  {
    label: '中位数',
    value: SummaryEnum.MEDIAN
  }
];

export const FormulaSummaryDic = [...NumberSummaryDic];

export const SortDic = [
  {
    label: '默认',
    value: SortEnum.NONE
  },
  {
    label: '升序',
    value: SortEnum.ASC
  },
  {
    label: '降序',
    value: SortEnum.DESC
  }
];

const metricsTransClone = (item: Mark) => {
  if (!item.summary) {
    item.summary = SummaryEnum.COUNT;
  }
  return item;
};

const columnTag: TopTagType[] = [
  {
    label: '维度',
    dropdownItems: [RenameGenerator, SortGenerator],
    dragCondition: (_, oldList) => [
      // 若现在已经有两个维度了，则直接返回
      {
        condition: oldList.length >= 2,
        message: '最多只能添加两个维度'
      },// 若现在只有一个维度，检查是否有两个指标
      {
        condition: metricsList.value.length >= 2 && !!oldList.length,
        message: '有多个指标时只能添加一个维度'
      }
    ],
    refType: WidgetGroupEnum.DIMENSIONS
  },
  {
    label: '指标',
    dropdownItems: [RenameGenerator, CollectGenerator, SortGenerator],
    transClone: metricsTransClone,
    dragCondition: (_, oldList) => [
      // 若现在已经有两个维度了，则不能添加第二个
      {
        condition: dimensionsList.value.length >= 2 && !!oldList.length,
        message: '有两个维度时不能添加多个指标'
      }
    ],
    refType: WidgetGroupEnum.METRICS
  },
  {
    label: '辅助指标',
    dragGroup: AUXILIARY_GROUP_NAME,
    refType: WidgetGroupEnum.AUXILIARY_METRICS,
    dropdownItems: []
  }
];

export const TopTags: {
  [K in ChartTypeEnum]: TopTagType[];
} = {
  [ChartTypeEnum.TEXT]: [
    {
      label: '指标',
      transClone: metricsTransClone,
      dropdownItems: [CollectGenerator],
      dragCondition: (_, oldList) => [
        {
          condition: !!oldList.length,
          message: '只能添加一个指标'
        }
      ],
      refType: WidgetGroupEnum.METRICS
    }
  ],
  [ChartTypeEnum.PIE]: [
    {
      label: '维度',
      dropdownItems: [RenameGenerator],
      dragCondition: (_, oldList) => [
        {
          condition: !!oldList.length,
          message: '只能添加一个维度'
        }
      ],
      refType: WidgetGroupEnum.DIMENSIONS
    },
    {
      label: '指标',
      dropdownItems: [RenameGenerator, CollectGenerator],
      transClone: metricsTransClone,
      dragCondition: (_, oldList) => [
        {
          condition: !!oldList.length,
          message: '只能添加一个指标'
        }
      ],
      refType: WidgetGroupEnum.METRICS
    }
  ],
  [ChartTypeEnum.COLUMN]: columnTag,
  [ChartTypeEnum.BAR]: columnTag,
  [ChartTypeEnum.LINE]: columnTag,
  [ChartTypeEnum.TABLE]: [
    {
      label: '指标',
      refType: WidgetGroupEnum.METRICS,
      dropdownItems: []
    }
  ]
};

const CommonShowChart = (dimensions?: Mark[], metrics?: Mark[]) => !!dimensions?.length && !!metrics?.length;

export const ShowChart: {
  [K in ChartTypeEnum]: (
    dimensions?: Mark[],
    metrics?: Mark[]
  ) => boolean;
} = {
  [ChartTypeEnum.TEXT]: (_, metrics) => !!metrics?.length,
  [ChartTypeEnum.PIE]: CommonShowChart,
  [ChartTypeEnum.COLUMN]: CommonShowChart,
  [ChartTypeEnum.BAR]: CommonShowChart,
  [ChartTypeEnum.LINE]: CommonShowChart,
  [ChartTypeEnum.TABLE]: (_, metrics) => !!metrics?.length
};