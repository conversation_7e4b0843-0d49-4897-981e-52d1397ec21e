import { getSelectInit } from '@/const/BasicInfoOption';

// 单位选择
export const handleBasic = () => {
  let list = _.omit(getSelectInit(), [
    'manageStatus',
    'staffSize',
    'logo',
    'officialSealStatus',
    'securityCode',
  ]);
  let childs: any = [];
  Object.entries(list).forEach(([key, value]: any) => {
    childs.push({
      prop: key,
      copyLabel: value.label,
      type: value.label === '上传logo' ? 'basicLogo' : undefined,
    });
  });
  return childs;
};
