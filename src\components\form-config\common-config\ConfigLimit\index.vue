<template>
  <div>
    <el-form-item label="选择范围">
      <div class="w-full">
        <el-checkbox v-model="data.isLimit" label="控制范围"/>
      </div>
      <el-radio-group v-model="data.limitRange" v-if="data.isLimit" @change="changeLimit">
        <el-radio :value="item.value"  v-for="item in dic">{{ item.label }}</el-radio>
      </el-radio-group>
      <div class="w-full" v-if="data.limitRange === 0">
        <component
          v-model="data.limitIds"
          :is="getComponent(data.fixType, data.component)"
          v-bind="data"
          placeholder="选择可选范围"
          :disabled="false"
          :limitIds="[]"
          :workParentId="bindWorksByParentId"
        >
        </component>
      </div>
    </el-form-item>
  </div>
</template>    

<script setup lang="ts">
import { DataLinkField,FormulaField,UserTagAllField,UserDeptAllField } from '@/components/form-config/types/AvueTypes';
import useComponents from '@/components/form-config/hooks/useComponents.ts';
import { storeToRefs } from 'pinia';
import usePackageTreeStore from '@/views/mainview/sceneTasks/components/taskManagement/store/usePackageTreeStore.ts';
import { FixTypeEnum } from '@/components/form-config/types/field.ts';
import { DefaultTypeEnum } from '@/components/form-config/const/commonDic.ts';

const props = defineProps<{
  data: UserTagAllField | UserDeptAllField;
}>();

const { getComponent } = useComponents();
const { bindWorksByParentId } = storeToRefs(usePackageTreeStore());

const dic = [
  {
    label: '自定义',
    value: 0,
  },
  {
    label: `当前用户所在${props.data.fixType === FixTypeEnum.USER_DEPT ? '部门' : '工作组'}`,
    value: 1,
  },
]

const changeLimit = () => {
  props.data.limitIds = []
  props.data.defaultType = DefaultTypeEnum.CUSTOM
  props.data.defaultValue = [];
  (props.data as DataLinkField).dataLinks = { list: [] };
  (props.data as FormulaField).formula = {};
}

watchEffect(() => { 
  if(!props.data.isLimit){
    props.data.limitRange = undefined
    props.data.limitIds = []
  }
});

watch(
  () => props.data.limitIds, 
  (val) => { 
    if(!val?.length) return
    props.data.defaultValue = []
  },

);

</script>