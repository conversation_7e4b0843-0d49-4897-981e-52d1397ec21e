<template>
  <!-- 中间的组件配置 -->
  <div v-if="isDynamic && source === fieldSource.SET" class="w-full">
    <el-button type="text">签名汇总</el-button>
  </div>
  <div
    v-else-if="isDynamic && (source === fieldSource.PREVIEW || source === fieldSource.HANDLED)"
    class="w-full"
  >
    <el-button type="text" @click="dialogVisible = true">编辑</el-button>
    <el-dialog
      class="xtDataSelect"
      v-model="dialogVisible"
      title="签名汇总"
      width="70%"
      append-to-body
      destroy-on-close
    >
      <n-space v-if="vModel?.length" wrap>
        <n-card v-for="(item, index) in vModel" :key="item" closable @close="handleClose(index)">
          <el-image :src="item" class="w-200px h-100px" />
        </n-card>
      </n-space>
      <template #footer>
        <el-button type="primary" @click="dialogVisible = false">确定</el-button>
      </template>
    </el-dialog>
  </div>
  <div class="w-full" v-else>
    <n-space v-if="vModel?.length" wrap>
      <n-card v-for="(item, index) in vModel" :key="item" closable @close="handleClose(index)">
        <el-image :src="item" class="w-200px h-100px" />
      </n-card>
    </n-space>
    <el-empty v-else />
  </div>
</template>

<script setup lang="ts">
import { fieldSource } from '@/components/form-config/const/commonDic';

defineOptions({
  inheritAttrs: false,
});

defineProps<{
  disabled?: boolean;
  taskPackageId?: string;
  source?: number;
  isDynamic: boolean;
}>();

const vModel = defineModel<string[]>({ default: () => [] });
const dialogVisible = ref(false);

const handleClose = (index: number) => {
  _.pullAt(vModel.value, index);
};
</script>

<style scoped></style>
