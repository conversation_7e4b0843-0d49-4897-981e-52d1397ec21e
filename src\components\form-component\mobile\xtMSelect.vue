<template>
  <van-field
    v-model="modelValue"
    is-link
    readonly
    v-bind="$attrs"
    label=""
    placeholder="选择城市"
    @click="open"
  />
  <van-popup
    v-model:show="showPicker"
    position="bottom"
    round
    :style="{ height: '50%' }"
  >
    <div class="h-full flex flex-col justify-between">
      <div class="flex justify-between px-25px py-10px">
        <div></div>
        <div>{{ $attrs.label }}</div>
        <div>
          <van-icon @click="showPicker=false" name="cross" />
        </div>
      </div>
      <el-scrollbar class="flex-1">
        <van-cell class=" flex items-center"
                  v-for="item in dicts" is-link
                  @click="selectHandle(item)"
                  :title-class="{'color-#024FE5':inCheck(item)}"
                  :key="item[prop.props.value]"
                  :title="item[prop.props.value]">
          <template #right-icon>
            <van-icon name="success" v-if="inCheck(item)" class="color-#024FE5" />
          </template>
        </van-cell>
      </el-scrollbar>
      <div class="m-10px">
        <van-button type="primary" @click="confirm" block>确定</van-button>
      </div>
    </div>
  </van-popup>

</template>

<script setup>

import { stringToArray } from '@/views/mobileView/components/mFormDesign/utils/util';

defineOptions({
  name: 'xt-m-select'
});
const prop = defineProps({
  dic: {
    type: Array,
    default: () => []
  },
  dicData: {
    type: Array,
    default: () => []
  },
  props: {
    default: () => {
      return {
        label: 'label',
        value: 'value'
      };
    }
  },
  placeholder: {
    type: String,
    default: () => '请选择'
  },
  clearable: {
    type: Boolean,
    default: () => true
  }
});

const modelValue = defineModel();

const copyModelValue = ref(modelValue.value);

const showPicker = ref(false);

const emit = defineEmits(['select']);

const open = () => {
  copyModelValue.value = stringToArray(modelValue.value).filter(s=>s);
  showPicker.value = true;
};

const inCheck=(item)=>{
  return _.includes(copyModelValue.value,item[prop.props.value])
}

const attrs=useAttrs()

const selectHandle = (item) => {
  if (attrs.multiple){
    if (_.includes(copyModelValue.value,item[prop.props.value])){
      copyModelValue.value = _.without(copyModelValue.value,item[prop.props.value])
    }else{
      copyModelValue.value.push(item[prop.props.value])
    }
  }else{
    copyModelValue.value = stringToArray(item[prop.props.value])
  }
};

const confirm = () => {
  showPicker.value = false;
  modelValue.value = copyModelValue.value;
};


const dicts = computed(() => (!_.isEmpty(prop.dic) ? prop.dic : prop.dicData));
</script>

<style scoped lang="scss"></style>
